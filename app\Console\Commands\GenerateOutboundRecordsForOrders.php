<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\Order;
use App\Inventory\Models\OutboundDocument;
use App\Inventory\Models\OutboundItem;
use App\Inventory\Models\InventoryTransaction;
use App\Inventory\Models\InventoryTransactionType;
use App\Product\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateOutboundRecordsForOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:generate-outbound-records 
                            {--dry-run : 只显示需要处理的订单，不实际创建记录}
                            {--order-id= : 只处理指定的订单ID}
                            {--status=* : 只处理指定状态的订单，可多选（默认：paid,shipped,delivered）}
                            {--limit=100 : 限制处理的订单数量}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '为已完成的订单生成出库记录，确保库存数据的完整性';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始为订单生成出库记录...');
        
        $dryRun = $this->option('dry-run');
        $orderId = $this->option('order-id');
        $statuses = $this->option('status');
        $limit = (int) $this->option('limit');
        
        // 默认处理的订单状态
        if (empty($statuses)) {
            $statuses = ['paid', 'shipped', 'delivered'];
        }
        
        $this->info("处理模式: " . ($dryRun ? '预览模式（不会实际创建记录）' : '执行模式'));
        $this->info("订单状态: " . implode(', ', $statuses));
        
        // 构建查询
        $query = Order::with(['items.product', 'user'])
            ->whereDoesntHave('outboundDocuments') // 没有出库记录的订单
            ->whereIn('status', $statuses);
            
        if ($orderId) {
            $query->where('id', $orderId);
        }
        
        $query->orderBy('created_at', 'desc')
              ->limit($limit);
              
        $orders = $query->get();
        
        $this->info("找到 {$orders->count()} 个需要处理的订单");
        
        if ($orders->isEmpty()) {
            $this->info('没有需要处理的订单。');
            return 0;
        }
        
        // 显示预览
        $this->table(
            ['订单ID', '订单号', '状态', '商品数量', '总金额', '创建时间'],
            $orders->map(function ($order) {
                return [
                    $order->id,
                    $order->order_no,
                    $order->status,
                    $order->items->count(),
                    '¥' . number_format($order->total, 2),
                    $order->created_at->format('Y-m-d H:i:s')
                ];
            })->toArray()
        );
        
        if ($dryRun) {
            $this->info('预览模式完成，没有实际创建记录。');
            return 0;
        }
        
        if (!$this->confirm('确认要为以上订单创建出库记录吗？')) {
            $this->info('操作已取消。');
            return 0;
        }
        
        $successCount = 0;
        $errorCount = 0;
        $progressBar = $this->output->createProgressBar($orders->count());
        $progressBar->start();
        
        foreach ($orders as $order) {
            try {
                $this->generateOutboundForOrder($order);
                $successCount++;
                $this->line("\n✓ 订单 {$order->order_no} 出库记录创建成功");
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("\n✗ 订单 {$order->order_no} 出库记录创建失败: " . $e->getMessage());
                Log::error('生成出库记录失败', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'error' => $e->getMessage()
                ]);
            }
            
            $progressBar->advance();
        }
        
        $progressBar->finish();
        
        $this->info("\n\n处理完成:");
        $this->info("成功: {$successCount} 个订单");
        $this->info("失败: {$errorCount} 个订单");
        
        return 0;
    }
    
    /**
     * 为订单生成出库记录
     */
    private function generateOutboundForOrder(Order $order)
    {
        DB::beginTransaction();
        
        try {
            // 获取默认仓库
            $defaultWarehouseId = $this->getDefaultWarehouse();
            
            // 创建出库单
            $outboundDocument = OutboundDocument::create([
                'document_no' => $this->generateDocumentNo(),
                'document_type' => 'sales', // 销售出库
                'reference_type' => 'order',
                'reference_id' => $order->id,
                'order_id' => $order->id,
                'warehouse_id' => $order->warehouse_id ?? $defaultWarehouseId,
                'status' => 'completed', // 直接设为已完成
                'confirmed_at' => $order->updated_at,
                'completed_at' => $order->updated_at,
                'confirmed_by' => 1, // 系统用户
                'completed_by' => 1, // 系统用户
                'created_by' => 1, // 系统用户
                'updated_by' => 1, // 系统用户
                'notes' => "系统为订单 {$order->order_no} 补充生成的出库记录",
                'created_at' => $order->updated_at,
                'updated_at' => now(),
            ]);
            
            $totalCost = 0;
            $totalItems = 0;
            
            // 创建出库明细
            foreach ($order->items as $orderItem) {
                $product = $orderItem->product;
                if (!$product) {
                    $this->warn("订单项 {$orderItem->id} 对应的商品不存在，跳过");
                    continue;
                }
                
                // 计算成本价（使用商品的当前成本价或订单价格）
                $unitCost = $product->cost_price ?? $orderItem->price;
                $itemTotalCost = $orderItem->quantity * $unitCost;
                
                // 获取单位ID，如果订单项没有单位ID，则使用商品的默认销售单位
                $unitId = $orderItem->unit_id;
                if (!$unitId) {
                    // 尝试获取商品的默认销售单位
                    $defaultUnit = $product->getSaleDefaultUnit();
                    if ($defaultUnit) {
                        $unitId = $defaultUnit->id;
                    } else {
                        // 如果没有默认单位，尝试获取第一个可用单位
                        $firstUnit = DB::table('units')->first();
                        if ($firstUnit) {
                            $unitId = $firstUnit->id;
                        } else {
                            $this->warn("商品 {$product->name} 没有可用的单位，跳过");
                            continue;
                        }
                    }
                }
                
                $outboundItem = OutboundItem::create([
                    'outbound_document_id' => $outboundDocument->id,
                    'product_id' => $orderItem->product_id,
                    'product_name' => $orderItem->product_name,
                    'product_sku' => $orderItem->product_sku ?? $product->sku,
                    'planned_quantity' => $orderItem->quantity,
                    'actual_quantity' => $orderItem->quantity,
                    'unit_id' => $unitId,
                    'unit_cost' => $unitCost,
                    'total_cost' => $itemTotalCost,
                    'order_item_id' => $orderItem->id,
                    'notes' => "订单项出库",
                    'created_at' => $order->updated_at,
                    'updated_at' => now(),
                ]);
                
                $totalCost += $itemTotalCost;
                $totalItems += $orderItem->quantity;
                
                // 创建库存事务记录
                $this->createInventoryTransaction($outboundItem, $order, $unitId);
            }
            
            // 更新出库单总计
            $outboundDocument->update([
                'total_cost' => $totalCost,
                'total_items' => $totalItems,
            ]);
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 创建库存事务记录
     */
    private function createInventoryTransaction(OutboundItem $outboundItem, Order $order, $unitId)
    {
        // 获取或创建销售出库事务类型
        $transactionType = InventoryTransactionType::firstOrCreate(
            ['code' => 'sales_out'],
            [
                'name' => '销售出库',
                'description' => '销售订单出库',
                'direction' => 'out',
                'is_active' => true,
            ]
        );
        
        InventoryTransaction::create([
            'transaction_type_id' => $transactionType->id,
            'reference_type' => OutboundDocument::class,
            'reference_id' => $outboundItem->outbound_document_id,
            'product_id' => $outboundItem->product_id,
            'warehouse_id' => $outboundItem->outboundDocument->warehouse_id,
            'quantity' => -$outboundItem->actual_quantity, // 负数表示出库
            'unit_id' => $unitId,
            'unit_price' => $outboundItem->unit_cost,
            'total_amount' => $outboundItem->total_cost,
            'notes' => "订单 {$order->order_no} 销售出库",
            'status' => 'completed',
            'created_by' => 1, // 系统用户
            'updated_by' => 1, // 系统用户
            'created_at' => $order->updated_at,
            'updated_at' => now(),
        ]);
    }
    
    /**
     * 获取默认仓库ID
     */
    private function getDefaultWarehouse(): int
    {
        // 尝试获取第一个活跃仓库
        $warehouse = DB::table('warehouses')
            ->where('status', 'active')
            ->first();
            
        if ($warehouse) {
            return $warehouse->id;
        }
        
        // 如果没有活跃仓库，获取第一个仓库
        $warehouse = DB::table('warehouses')->first();
        if ($warehouse) {
            return $warehouse->id;
        }
        
        // 如果没有仓库，抛出异常
        throw new \Exception('系统中没有可用的仓库');
    }
    
    /**
     * 生成出库单号
     */
    private function generateDocumentNo(): string
    {
        $prefix = 'OUT';
        $date = now()->format('Ymd');
        
        // 获取今日最大序号
        $lastDocument = OutboundDocument::where('document_no', 'like', "{$prefix}{$date}%")
            ->orderBy('document_no', 'desc')
            ->first();
            
        if ($lastDocument) {
            $lastNumber = (int) substr($lastDocument->document_no, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return $prefix . $date . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }
} 