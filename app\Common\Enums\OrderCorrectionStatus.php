<?php

namespace App\Common\Enums;

/**
 * 订单更正状态统一管理
 */
class OrderCorrectionStatus
{
    // 🔥 更正状态
    const PENDING = 'pending';       // 待确认
    const CONFIRMED = 'confirmed';   // 已确认
    const CANCELLED = 'cancelled';   // 已取消

    // 🔥 结算状态
    const SETTLEMENT_PENDING = 'pending';       // 待结算
    const SETTLEMENT_PROCESSING = 'processing'; // 结算中
    const SETTLEMENT_COMPLETED = 'completed';   // 已完成
    const SETTLEMENT_FAILED = 'failed';         // 失败
    const SETTLEMENT_CANCELLED = 'cancelled';   // 已取消

    // 🔥 更正类型
    const TYPE_INCREASE = 'increase';    // 增加金额
    const TYPE_DECREASE = 'decrease';    // 减少金额
    const TYPE_NO_CHANGE = 'no_change';  // 无金额变化

    /**
     * 更正状态映射
     */
    public static function getStatusMap(): array
    {
        return [
            self::PENDING => '待确认',
            self::CONFIRMED => '已确认',
            self::CANCELLED => '已取消',
        ];
    }

    /**
     * 结算状态映射
     */
    public static function getSettlementStatusMap(): array
    {
        return [
            self::SETTLEMENT_PENDING => '待结算',
            self::SETTLEMENT_PROCESSING => '结算中',
            self::SETTLEMENT_COMPLETED => '已完成',
            self::SETTLEMENT_FAILED => '失败',
            self::SETTLEMENT_CANCELLED => '已取消',
        ];
    }

    /**
     * 更正类型映射
     */
    public static function getTypeMap(): array
    {
        return [
            self::TYPE_INCREASE => '增加金额',
            self::TYPE_DECREASE => '减少金额',
            self::TYPE_NO_CHANGE => '无金额变化',
        ];
    }

    /**
     * 获取状态文本
     */
    public static function getStatusText(string $status): string
    {
        return self::getStatusMap()[$status] ?? '未知状态';
    }

    /**
     * 获取结算状态文本
     */
    public static function getSettlementStatusText(string $settlementStatus): string
    {
        return self::getSettlementStatusMap()[$settlementStatus] ?? '未知状态';
    }

    /**
     * 获取更正类型文本
     */
    public static function getTypeText(string $type): string
    {
        return self::getTypeMap()[$type] ?? '未知类型';
    }

    /**
     * 检查是否可以确认
     */
    public static function canConfirm(string $status): bool
    {
        return $status === self::PENDING;
    }

    /**
     * 检查是否可以取消
     */
    public static function canCancel(string $status): bool
    {
        return $status === self::PENDING;
    }

    /**
     * 检查是否需要结算
     */
    public static function needsSettlement(string $status, string $type): bool
    {
        return $status === self::CONFIRMED && $type !== self::TYPE_NO_CHANGE;
    }

    /**
     * 检查是否可以标记结算方式
     */
    public static function canMarkSettlement(string $status, string $type, ?string $settlementStatus): bool
    {
        return self::needsSettlement($status, $type) && 
               in_array($settlementStatus, [self::SETTLEMENT_PENDING, self::SETTLEMENT_FAILED, null]);
    }

    /**
     * 检查是否可以完成结算
     */
    public static function canCompleteSettlement(?string $settlementMethod, ?string $settlementStatus): bool
    {
        return $settlementMethod !== null && 
               in_array($settlementStatus, [self::SETTLEMENT_PENDING, self::SETTLEMENT_PROCESSING]);
    }

    /**
     * 检查结算是否已完成
     */
    public static function isSettlementCompleted(?string $settlementStatus): bool
    {
        return $settlementStatus === self::SETTLEMENT_COMPLETED;
    }

    /**
     * 根据金额变化确定更正类型
     */
    public static function determineType(float $originalAmount, float $newAmount): string
    {
        if ($newAmount > $originalAmount) {
            return self::TYPE_INCREASE;
        } elseif ($newAmount < $originalAmount) {
            return self::TYPE_DECREASE;
        } else {
            return self::TYPE_NO_CHANGE;
        }
    }

    /**
     * 获取用户端显示信息
     */
    public static function getUserDisplayInfo(string $status, string $type, float $amountDifference): array
    {
        $statusText = self::getStatusText($status);
        $typeText = self::getTypeText($type);
        
        $displayClass = match($status) {
            self::PENDING => 'warning',
            self::CONFIRMED => 'success',
            self::CANCELLED => 'danger',
            default => 'secondary'
        };

        $amountText = '';
        if ($type === self::TYPE_INCREASE) {
            $amountText = '+¥' . number_format(abs($amountDifference), 2);
        } elseif ($type === self::TYPE_DECREASE) {
            $amountText = '-¥' . number_format(abs($amountDifference), 2);
        } else {
            $amountText = '¥0.00';
        }

        return [
            'status' => $statusText,
            'type' => $typeText,
            'amount_text' => $amountText,
            'display_class' => $displayClass,
            'is_pending' => $status === self::PENDING,
            'is_confirmed' => $status === self::CONFIRMED,
            'is_cancelled' => $status === self::CANCELLED,
        ];
    }

    /**
     * 获取管理端显示信息
     */
    public static function getAdminDisplayInfo(
        string $status, 
        string $type, 
        float $amountDifference,
        ?string $settlementStatus = null,
        ?string $settlementMethod = null
    ): array {
        $userInfo = self::getUserDisplayInfo($status, $type, $amountDifference);
        
        $settlementInfo = [];
        if (self::needsSettlement($status, $type)) {
            $settlementInfo = [
                'settlement_status' => self::getSettlementStatusText($settlementStatus ?? self::SETTLEMENT_PENDING),
                'settlement_method' => $settlementMethod,
                'can_mark_settlement' => self::canMarkSettlement($status, $type, $settlementStatus),
                'can_complete_settlement' => self::canCompleteSettlement($settlementMethod, $settlementStatus),
                'is_settlement_completed' => self::isSettlementCompleted($settlementStatus),
            ];
        }

        return array_merge($userInfo, $settlementInfo, [
            'can_confirm' => self::canConfirm($status),
            'can_cancel' => self::canCancel($status),
            'needs_settlement' => self::needsSettlement($status, $type),
        ]);
    }

    /**
     * 获取状态流转规则
     */
    public static function getStatusFlow(): array
    {
        return [
            self::PENDING => [self::CONFIRMED, self::CANCELLED],
            self::CONFIRMED => [], // 终态（但可能有结算流程）
            self::CANCELLED => [], // 终态
        ];
    }

    /**
     * 检查状态转换是否合法
     */
    public static function canTransitionTo(string $fromStatus, string $toStatus): bool
    {
        $allowedTransitions = self::getStatusFlow()[$fromStatus] ?? [];
        return in_array($toStatus, $allowedTransitions);
    }
}
