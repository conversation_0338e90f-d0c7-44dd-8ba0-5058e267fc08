<?php

namespace App\Billing\Http\Controllers\Api;

use App\Billing\Services\PaymentLinkService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * 🔥 新增：统一支付回调控制器
 * 由账单系统统一处理所有支付回调，包括微信、支付宝等
 */
class PaymentCallbackController extends Controller
{
    protected PaymentLinkService $paymentLinkService;

    public function __construct(PaymentLinkService $paymentLinkService)
    {
        $this->paymentLinkService = $paymentLinkService;
    }

    /**
     * 🔥 统一微信支付回调处理
     * 替代所有模块中的微信支付回调处理
     */
    public function wechatCallback(Request $request)
    {
        try {
            Log::info('账单系统接收微信支付回调', [
                'content_type' => $request->header('Content-Type'),
                'user_agent' => $request->header('User-Agent'),
                'ip' => $request->ip()
            ]);

            // 获取XML数据
            $xml = $request->getContent();
            if (empty($xml)) {
                return $this->returnWechatXml([
                    'return_code' => 'FAIL',
                    'return_msg' => '回调数据为空'
                ]);
            }

            // 检查是否为V3回调（JSON格式）还是V2回调（XML格式）
            $contentType = $request->header('Content-Type', '');

            if (str_contains($contentType, 'application/json') || $this->isJsonString($xml)) {
                // V3回调处理
                $notifyData = $this->handleV3Callback($request, $xml);
            } else {
                // V2回调处理（XML格式）
                $notifyData = $this->parseWechatXml($xml);
            }

            Log::info('微信支付回调数据解析', [
                'callback_version' => str_contains($contentType, 'application/json') ? 'V3' : 'V2',
                'out_trade_no' => $notifyData['out_trade_no'] ?? '',
                'transaction_id' => $notifyData['transaction_id'] ?? '',
                'return_code' => $notifyData['return_code'] ?? '',
                'result_code' => $notifyData['result_code'] ?? '',
                'total_fee' => $notifyData['total_fee'] ?? '',
                'time_end' => $notifyData['time_end'] ?? ''
            ]);

            // 通过账单系统统一处理
            $result = $this->paymentLinkService->handleWechatCallback($notifyData);

            // 根据回调版本返回不同格式的响应
            if (str_contains($request->header('Content-Type', ''), 'application/json')) {
                // V3回调返回HTTP 200状态码，无需响应体
                return response('', 200);
            } else {
                // V2回调返回XML格式
                return $this->returnWechatXml([
                    'return_code' => 'SUCCESS',
                    'return_msg' => 'OK'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('账单系统微信支付回调处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'xml_content' => $request->getContent()
            ]);

            return $this->returnWechatXml([
                'return_code' => 'FAIL',
                'return_msg' => $e->getMessage()
            ]);
        }
    }

    /**
     * 🔥 统一支付宝回调处理
     */
    public function alipayCallback(Request $request): JsonResponse
    {
        try {
            Log::info('账单系统接收支付宝回调', [
                'content' => $request->all()
            ]);

            // TODO: 实现支付宝回调处理逻辑
            
            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('账单系统支付宝回调处理失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json(['success' => false], 500);
        }
    }

    /**
     * 🔥 付款链接支付成功回调
     */
    public function paymentLinkCallback(Request $request): JsonResponse
    {
        try {
            $linkNo = $request->input('link_no');
            $paymentData = $request->only([
                'payment_method', 'amount', 'transaction_id', 
                'external_payment_no', 'received_by'
            ]);

            Log::info('接收付款链接支付成功回调', [
                'link_no' => $linkNo,
                'payment_method' => $paymentData['payment_method'] ?? ''
            ]);

            $this->paymentLinkService->handlePaymentSuccess($linkNo, $paymentData);

            return response()->json([
                'success' => true,
                'message' => '付款处理成功'
            ]);

        } catch (\Exception $e) {
            Log::error('付款链接回调处理失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 🔥 手动标记付款成功
     * 供管理员或配送员手动确认收款使用
     */
    public function markPaymentSuccess(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'bill_id' => 'required|exists:bills,id',
                'payment_method' => 'required|string',
                'amount' => 'required|numeric|min:0.01',
                'received_by' => 'required|integer',
                'notes' => 'string|max:500'
            ]);

            // 创建手动支付记录
            $paymentData = [
                'payment_method' => $request->payment_method,
                'amount' => $request->amount,
                'received_by' => $request->received_by,
                'notes' => $request->notes ?? '手动确认收款',
                'manual_confirmation' => true
            ];

            // 直接通过账单系统创建支付记录
            $bill = \App\Billing\Models\Bill::findOrFail($request->bill_id);
            $billingService = app(\App\Billing\Services\BillingService::class);
            
            $paymentRecord = $billingService->processPayment($bill, [
                'payment_method' => $request->payment_method,
                'payment_amount' => $request->amount,
                'received_by' => $request->received_by,
                'notes' => $request->notes ?? '手动确认收款'
            ]);

            return response()->json([
                'success' => true,
                'message' => '收款确认成功',
                'data' => [
                    'payment_record_id' => $paymentRecord->id,
                    'bill_status' => $bill->fresh()->status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('手动确认收款失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 解析微信XML数据
     */
    private function parseWechatXml(string $xml): array
    {
        try {
            libxml_disable_entity_loader(true);

            // 记录原始XML数据
            Log::info('微信支付回调原始XML', ['xml' => $xml]);

            $xmlObject = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xmlObject === false) {
                throw new \Exception('XML解析失败');
            }

            $data = json_decode(json_encode($xmlObject), true);

            // 记录解析后的数据
            Log::info('微信支付回调解析结果', [
                'parsed_data' => $data,
                'transaction_id_exists' => isset($data['transaction_id']),
                'transaction_id_value' => $data['transaction_id'] ?? 'null'
            ]);

            return $data ?: [];
        } catch (\Exception $e) {
            Log::error('微信支付回调XML解析失败', [
                'error' => $e->getMessage(),
                'xml' => $xml
            ]);
            return [];
        }
    }

    /**
     * 返回微信XML格式响应
     */
    private function returnWechatXml(array $data)
    {
        $xml = '<xml>';
        foreach ($data as $key => $value) {
            $xml .= "<{$key}><![CDATA[{$value}]]></{$key}>";
        }
        $xml .= '</xml>';

        return response($xml, 200, ['Content-Type' => 'application/xml']);
    }

    /**
     * 检查是否为JSON字符串
     */
    private function isJsonString($string): bool
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * 处理V3回调（JSON格式，包含加密数据）
     */
    private function handleV3Callback($request, $jsonData): array
    {
        try {
            Log::info('处理微信支付V3回调', ['json_data' => $jsonData]);

            $callbackData = json_decode($jsonData, true);
            if (!$callbackData) {
                throw new \Exception('V3回调JSON解析失败');
            }

            // 验证必要字段
            if (!isset($callbackData['resource']['ciphertext'])) {
                throw new \Exception('V3回调缺少加密数据');
            }

            // 解密回调数据
            $decryptedData = $this->decryptV3CallbackData($callbackData['resource']);

            Log::info('V3回调解密成功', [
                'transaction_id' => $decryptedData['transaction_id'] ?? 'missing',
                'out_trade_no' => $decryptedData['out_trade_no'] ?? 'missing',
                'trade_state' => $decryptedData['trade_state'] ?? 'missing'
            ]);

            // 转换为统一格式（兼容V2格式）
            return [
                'return_code' => 'SUCCESS',
                'result_code' => $decryptedData['trade_state'] === 'SUCCESS' ? 'SUCCESS' : 'FAIL',
                'transaction_id' => $decryptedData['transaction_id'] ?? '',
                'out_trade_no' => $decryptedData['out_trade_no'] ?? '',
                'total_fee' => isset($decryptedData['amount']['total']) ? $decryptedData['amount']['total'] : 0,
                'time_end' => $decryptedData['success_time'] ?? '',
                'trade_state' => $decryptedData['trade_state'] ?? '',
                'v3_raw_data' => $decryptedData
            ];

        } catch (\Exception $e) {
            Log::error('V3回调处理失败', [
                'error' => $e->getMessage(),
                'json_data' => $jsonData
            ]);

            // 返回失败格式，但不抛出异常，让上层继续处理
            return [
                'return_code' => 'FAIL',
                'return_msg' => 'V3回调解密失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 解密V3回调数据
     */
    private function decryptV3CallbackData($resource): array
    {
        try {
            // 获取解密所需参数
            $algorithm = $resource['algorithm'] ?? '';
            $ciphertext = $resource['ciphertext'] ?? '';
            $nonce = $resource['nonce'] ?? '';
            $associatedData = $resource['associated_data'] ?? '';

            Log::info('开始V3回调解密', [
                'algorithm' => $algorithm,
                'nonce' => $nonce,
                'ciphertext_length' => strlen($ciphertext),
                'associated_data' => $associatedData
            ]);

            // 验证算法类型
            if ($algorithm !== 'AEAD_AES_256_GCM') {
                throw new \Exception("不支持的加密算法: {$algorithm}");
            }

            // 获取APIv3密钥
            $apiV3Key = $this->getApiV3Key();
            if (empty($apiV3Key)) {
                throw new \Exception('APIv3密钥未配置');
            }

            // Base64解码密文
            $encryptedData = base64_decode($ciphertext);
            if ($encryptedData === false) {
                throw new \Exception('密文Base64解码失败');
            }

            // 使用AES-256-GCM解密
            $decryptedJson = $this->aesGcmDecrypt(
                $encryptedData,
                $apiV3Key,
                $nonce,
                $associatedData
            );

            if ($decryptedJson === false) {
                throw new \Exception('AES-GCM解密失败');
            }

            // 解析JSON
            $decryptedData = json_decode($decryptedJson, true);
            if (!$decryptedData) {
                throw new \Exception('解密后JSON解析失败');
            }

            Log::info('V3回调解密成功', [
                'transaction_id' => $decryptedData['transaction_id'] ?? 'missing',
                'out_trade_no' => $decryptedData['out_trade_no'] ?? 'missing',
                'trade_state' => $decryptedData['trade_state'] ?? 'missing'
            ]);

            return $decryptedData;

        } catch (\Exception $e) {
            Log::error('V3回调解密失败', [
                'error' => $e->getMessage(),
                'resource' => $resource
            ]);
            throw $e;
        }
    }

    /**
     * AES-256-GCM解密
     */
    private function aesGcmDecrypt($encryptedData, $key, $nonce, $associatedData = ''): string|false
    {
        try {
            // 分离密文和认证标签
            // GCM模式下，最后16字节是认证标签
            if (strlen($encryptedData) < 16) {
                throw new \Exception('加密数据长度不足');
            }

            $ciphertext = substr($encryptedData, 0, -16);
            $tag = substr($encryptedData, -16);

            // 使用openssl_decrypt解密
            $decrypted = openssl_decrypt(
                $ciphertext,
                'aes-256-gcm',
                $key,
                OPENSSL_RAW_DATA,
                $nonce,
                $tag,
                $associatedData
            );

            if ($decrypted === false) {
                throw new \Exception('OpenSSL解密失败: ' . openssl_error_string());
            }

            return $decrypted;

        } catch (\Exception $e) {
            Log::error('AES-GCM解密异常', [
                'error' => $e->getMessage(),
                'data_length' => strlen($encryptedData),
                'nonce_length' => strlen($nonce),
                'key_length' => strlen($key)
            ]);
            return false;
        }
    }

    /**
     * 获取APIv3密钥
     */
    private function getApiV3Key(): string
    {
        // 优先从环境变量获取
        $apiV3Key = env('WECHAT_PAY_V3_KEY', '');

        Log::info('获取APIv3密钥', [
            'env_key_length' => strlen($apiV3Key),
            'env_key_exists' => !empty($apiV3Key)
        ]);

        if (empty($apiV3Key)) {
            // 从数据库获取
            try {
                $provider = \App\WechatPayment\Models\WechatServiceProvider::where('is_active', true)->first();
                if ($provider && !empty($provider->api_v3_key)) {
                    $apiV3Key = $provider->api_v3_key;
                    Log::info('从数据库获取到APIv3密钥', [
                        'provider_id' => $provider->id,
                        'provider_name' => $provider->name,
                        'key_length' => strlen($apiV3Key)
                    ]);
                } else {
                    Log::warning('数据库中未找到有效的APIv3密钥', [
                        'provider_exists' => $provider ? true : false,
                        'provider_v3_key_empty' => $provider ? empty($provider->api_v3_key) : 'no_provider'
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('从数据库获取APIv3密钥失败', ['error' => $e->getMessage()]);
            }
        }

        // 验证密钥长度（APIv3密钥应该是32字节）
        if (!empty($apiV3Key) && strlen($apiV3Key) !== 32) {
            Log::warning('APIv3密钥长度不正确', [
                'expected_length' => 32,
                'actual_length' => strlen($apiV3Key)
            ]);
        }

        if (empty($apiV3Key)) {
            Log::error('APIv3密钥未配置或为空', [
                'env_checked' => true,
                'db_checked' => true
            ]);
        } else {
            Log::info('APIv3密钥获取成功', [
                'key_length' => strlen($apiV3Key),
                'source' => !empty(env('WECHAT_PAY_V3_KEY')) ? 'environment' : 'database'
            ]);
        }

        return $apiV3Key;
    }
}