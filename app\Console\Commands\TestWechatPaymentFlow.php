<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Product\Models\Product;
use App\Billing\Models\Bill;
use App\Billing\Models\PaymentRecord;
use App\Billing\Services\PaymentLinkService;
use App\Inventory\Models\InventoryTransaction;
use Illuminate\Support\Facades\DB;

class TestWechatPaymentFlow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:wechat-payment 
                            {--check : 只检查现有订单状态}
                            {--create : 创建新的测试订单}
                            {--order-id= : 指定订单ID进行测试}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试微信支付订单流程和分单打印功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 微信支付订单流程测试');
        $this->info('========================');

        if ($this->option('check')) {
            return $this->checkExistingOrders();
        }

        if ($this->option('create')) {
            return $this->createAndTestOrder();
        }

        if ($this->option('order-id')) {
            return $this->testSpecificOrder($this->option('order-id'));
        }

        // 默认显示菜单
        $this->showMenu();
    }

    protected function showMenu()
    {
        $choice = $this->choice('请选择测试类型', [
            'check' => '检查现有订单状态',
            'create' => '创建新订单并测试',
            'specific' => '测试指定订单'
        ]);

        switch ($choice) {
            case 'check':
                $this->checkExistingOrders();
                break;
            case 'create':
                $this->createAndTestOrder();
                break;
            case 'specific':
                $orderId = $this->ask('请输入订单ID');
                $this->testSpecificOrder($orderId);
                break;
        }
    }

    protected function checkExistingOrders()
    {
        $this->info('🔍 检查现有订单状态...');

        // 检查微信支付订单
        $wechatOrders = Order::where('payment_method', 'wechat')
            ->whereIn('status', ['paid', 'confirmed'])
            ->with(['items'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        if ($wechatOrders->count() > 0) {
            $this->info("\n📊 微信支付订单状态:");
            
            $headers = ['订单号', '状态', '库存已处理', '商品数', '库存事务数', '状态'];
            $rows = [];

            foreach ($wechatOrders as $order) {
                $inventoryCount = InventoryTransaction::where('reference_type', 'order')
                    ->where('reference_id', $order->id)
                    ->count();
                
                $itemsCount = $order->items->count();
                $status = $inventoryCount >= $itemsCount ? '✅' : '❌';

                $rows[] = [
                    $order->order_no,
                    $order->status,
                    $order->inventory_processed ? '是' : '否',
                    $itemsCount,
                    $inventoryCount,
                    $status
                ];
            }

            $this->table($headers, $rows);
        } else {
            $this->warn('未找到微信支付订单');
        }

        // 统计信息
        $this->showStatistics();
    }

    protected function createAndTestOrder()
    {
        $this->info('🧪 创建测试订单...');

        if (!$this->confirm('此操作会创建测试数据，确认继续?')) {
            return;
        }

        DB::beginTransaction();
        
        try {
            // 1. 创建测试订单
            $order = Order::create([
                'order_no' => 'TEST_WX_' . date('YmdHis') . rand(1000, 9999),
                'user_id' => 1,
                'status' => 'pending',
                'payment_method' => 'wechat',
                'payment_status' => 'pending',
                'total_amount' => 100.00,
                'delivery_fee' => 5.00,
                'final_amount' => 105.00,
                'delivery_address' => '测试地址',
                'delivery_phone' => '13800138000',
                'delivery_name' => '测试用户',
                'notes' => '微信支付测试订单'
            ]);

            $this->info("✅ 订单创建成功: {$order->order_no}");

            // 2. 添加订单商品
            $product = Product::first();
            if (!$product) {
                throw new \Exception('没有找到测试商品');
            }

            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'unit_id' => 1,
                'warehouse_id' => 1,
                'quantity' => 2,
                'unit_price' => 50.00,
                'total_price' => 100.00,
                'product_name' => $product->name,
                'product_image' => $product->image ?? ''
            ]);

            $this->info("✅ 订单商品添加成功");

            // 3. 创建账单
            $bill = Bill::create([
                'bill_no' => 'BILL_' . $order->order_no,
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'total_amount' => $order->final_amount,
                'paid_amount' => 0,
                'payment_status' => 'pending',
                'status' => 'active'
            ]);

            $this->info("✅ 账单创建成功");

            // 4. 模拟支付成功
            $this->info("💳 模拟微信支付成功...");

            $paymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                'payment_method' => 'wechat',
                'amount' => $bill->total_amount,
                'status' => PaymentRecord::STATUS_SUCCESS,
                'transaction_id' => 'wx_test_' . time(),
                'confirmed_at' => now()
            ]);

            $bill->update([
                'payment_status' => Bill::PAYMENT_STATUS_PAID,
                'paid_amount' => $bill->total_amount,
                'paid_at' => now()
            ]);

            // 5. 触发支付回调
            $paymentLinkService = new PaymentLinkService();
            $paymentLinkService->syncOrderStatusFromBill($bill);

            $this->info("✅ 支付回调处理完成");

            // 6. 检查结果
            $this->checkOrderResult($order->id);

            // 询问是否保留数据
            if ($this->confirm('是否保留测试数据?')) {
                DB::commit();
                $this->info("✅ 测试数据已保留");
            } else {
                DB::rollBack();
                $this->info("🗑️ 测试数据已清理");
            }

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("❌ 测试失败: " . $e->getMessage());
        }
    }

    protected function testSpecificOrder($orderId)
    {
        $order = Order::with(['items'])->find($orderId);
        
        if (!$order) {
            $this->error("❌ 未找到订单 ID: {$orderId}");
            return;
        }

        $this->info("🔍 检查订单: {$order->order_no}");
        $this->checkOrderResult($orderId);
    }

    protected function checkOrderResult($orderId)
    {
        $order = Order::with(['items'])->find($orderId);
        
        $inventoryCount = InventoryTransaction::where('reference_type', 'order')
            ->where('reference_id', $order->id)
            ->count();

        $this->info("\n📊 订单检查结果:");
        $this->line("   - 订单号: {$order->order_no}");
        $this->line("   - 订单状态: {$order->status}");
        $this->line("   - 支付状态: {$order->payment_status}");
        $this->line("   - 库存已处理: " . ($order->inventory_processed ? '是' : '否'));
        $this->line("   - 库存处理方式: " . ($order->inventory_method ?? '未设置'));
        $this->line("   - 商品数量: " . $order->items->count());
        $this->line("   - 库存事务数: {$inventoryCount}");

        // 检查分单打印条件
        $canPrint = $inventoryCount >= $order->items->count();
        
        if ($canPrint) {
            $this->info("✅ 分单打印条件满足");
        } else {
            $this->error("❌ 分单打印条件不满足");
        }

        // 显示库存事务详情
        if ($inventoryCount > 0) {
            $transactions = InventoryTransaction::where('reference_type', 'order')
                ->where('reference_id', $order->id)
                ->get();

            $this->info("\n📋 库存事务记录:");
            foreach ($transactions as $transaction) {
                $this->line("   - ID: {$transaction->id}, 商品: {$transaction->product_id}, 数量: {$transaction->quantity}");
            }
        }
    }

    protected function showStatistics()
    {
        $this->info("\n📈 统计信息:");
        
        $totalOrders = Order::whereIn('status', ['paid', 'confirmed'])->count();
        $wechatOrders = Order::where('payment_method', 'wechat')
            ->whereIn('status', ['paid', 'confirmed'])
            ->count();
        $codOrders = Order::where('payment_method', 'cod')
            ->where('status', 'confirmed')
            ->count();

        $this->line("   - 总订单数: {$totalOrders}");
        $this->line("   - 微信支付订单: {$wechatOrders}");
        $this->line("   - 货到付款订单: {$codOrders}");

        // 检查配置
        $printOnPaid = config('flycloud.print_triggers.on_order_paid', false);
        $printOnConfirmed = config('flycloud.print_triggers.on_order_confirmed', true);

        $this->info("\n⚙️ 打印配置:");
        $this->line("   - 支付时打印: " . ($printOnPaid ? '开启' : '关闭'));
        $this->line("   - 确认时打印: " . ($printOnConfirmed ? '开启' : '关闭'));

        if (!$printOnPaid) {
            $this->warn("⚠️ 建议开启支付时打印配置");
        }
    }
}
