<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\OrderCorrection;
use Illuminate\Support\Facades\DB;

class ResetOrderCorrectionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:reset-correction-status {id} {--force} {--reason=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '重置订单更正记录的状态为待处理';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');
        $force = $this->option('force');
        $reason = $this->option('reason') ?: '管理员重置状态';

        $correction = OrderCorrection::with(['order'])->find($id);
        
        if (!$correction) {
            $this->error("未找到ID为 {$id} 的订单更正记录");
            return 1;
        }

        $this->info("=== 订单更正记录 #{$id} ===");
        $this->displayCorrectionInfo($correction);

        // 检查当前状态
        if ($correction->status === 'pending') {
            $this->info('✅ 记录状态已经是待处理，无需重置');
            return 0;
        }

        // 安全检查
        if (!$force && !$this->confirmReset($correction)) {
            $this->info('操作已取消');
            return 0;
        }

        // 执行重置
        $this->resetCorrectionStatus($correction, $reason);

        return 0;
    }

    private function displayCorrectionInfo(OrderCorrection $correction)
    {
        $this->table(
            ['字段', '值'],
            [
                ['ID', $correction->id],
                ['订单ID', $correction->order_id],
                ['订单号', $correction->order->order_no ?? 'N/A'],
                ['当前状态', $correction->status],
                ['更正类型', $correction->correction_type],
                ['创建时间', $correction->created_at],
                ['确认时间', $correction->confirmed_at ?? 'N/A'],
                ['取消时间', $correction->cancelled_at ?? 'N/A'],
                ['确认人', $correction->confirmed_by ?? 'N/A'],
            ]
        );
    }

    private function confirmReset(OrderCorrection $correction): bool
    {
        $this->warn('⚠️  重置状态是一个危险操作，可能会影响业务数据！');
        
        if ($correction->status === 'confirmed') {
            $this->warn('该记录已确认，重置后可能需要重新处理库存和财务数据');
        }

        $this->line('重置操作将：');
        $this->line('1. 将状态改为 pending');
        $this->line('2. 清空确认时间和确认人');
        $this->line('3. 清空取消时间');
        $this->line('4. 记录操作日志');

        return $this->confirm('确定要重置此订单更正记录的状态吗？');
    }

    private function resetCorrectionStatus(OrderCorrection $correction, string $reason)
    {
        DB::transaction(function () use ($correction, $reason) {
            $originalStatus = $correction->status;
            $originalConfirmedAt = $correction->confirmed_at;
            $originalConfirmedBy = $correction->confirmed_by;
            $originalCancelledAt = $correction->cancelled_at;

            // 重置状态
            $correction->update([
                'status' => 'pending',
                'confirmed_at' => null,
                'confirmed_by' => null,
                'cancelled_at' => null,
                'updated_at' => now(),
            ]);

            // 记录操作日志
            \Illuminate\Support\Facades\Log::warning('订单更正状态被重置', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'original_status' => $originalStatus,
                'new_status' => 'pending',
                'original_confirmed_at' => $originalConfirmedAt,
                'original_confirmed_by' => $originalConfirmedBy,
                'original_cancelled_at' => $originalCancelledAt,
                'reset_reason' => $reason,
                'reset_by' => 'console_command',
                'reset_at' => now(),
            ]);

            $this->info("✅ 订单更正记录 #{$correction->id} 状态已重置为待处理");
            $this->info("原状态: {$originalStatus} → 新状态: pending");
            $this->info("重置原因: {$reason}");
        });
    }
}
