<?php

namespace App\FlyCloud\Http\Controllers;

use App\Http\Controllers\Controller;
use App\FlyCloud\Services\FlyCloudService;
use App\FlyCloud\Models\FlyCloudPrinter;
use App\FlyCloud\Models\WarehousePrinterBinding;
use App\FlyCloud\Models\PrintTask;
use App\Order\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class FlyCloudController extends Controller
{
    protected FlyCloudService $flyCloudService;

    public function __construct(FlyCloudService $flyCloudService)
    {
        $this->flyCloudService = $flyCloudService;
    }

    /**
     * 飞蛾云打印文本
     */
    public function printText(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string',
                'printer_sn' => 'nullable|string',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $content = $request->input('content');
            $options = [
                'printer_sn' => $request->input('printer_sn'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printText($content, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印任务已发送成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印失败，请检查打印机状态'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print text API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '飞蛾云打印服务异常: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 飞蛾云打印HTML
     */
    public function printHtml(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string',
                'printer_sn' => 'nullable|string',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $content = $request->input('content');
            $options = [
                'printer_sn' => $request->input('printer_sn'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printHtml($content, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印任务已发送成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印失败，请检查打印机状态'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print HTML API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '飞蛾云打印服务异常: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 飞蛾云打印订单小票（分仓库）
     */
    public function printOrderReceipt(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'printer_sn' => 'nullable|string',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $orderId = $request->input('order_id');
            // 🔥 修复：加载配送员信息，确保小票能显示配送员
            $order = Order::with(['items.product', 'delivery.deliverer.employee', 'user.crmAgent'])->findOrFail($orderId);

            $options = [
                'printer_sn' => $request->input('printer_sn'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printOrderReceipt($order, $options);

            if ($result) {
                Log::info('FlyCloud order receipt printed successfully', [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no
                ]);

                return response()->json([
                    'success' => true,
                    'message' => '订单小票已发送到飞蛾云打印机'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '订单小票打印失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print order receipt API error', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '订单小票打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 🔥 新增：飞蛾云打印整单小票（不分仓库）
     */
    public function printWholeOrderReceipt(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'printer_sn' => 'nullable|string',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $orderId = $request->input('order_id');
            $order = Order::with(['items.product'])->findOrFail($orderId);

            $options = [
                'printer_sn' => $request->input('printer_sn'),
                'copies' => $request->input('copies', 1),
                'whole_order' => true // 标记为整单打印
            ];

            $result = $this->flyCloudService->printWholeOrderReceipt($order, $options);

            if ($result) {
                Log::info('FlyCloud whole order receipt printed successfully', [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no
                ]);

                return response()->json([
                    'success' => true,
                    'message' => '整单小票已发送到飞蛾云打印机'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '整单小票打印失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print whole order receipt API error', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '整单小票打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 🔥 新增：手动触发订单确认后的小票打印
     */
    public function printReceiptOnConfirm(Request $request): JsonResponse
    {
        try {
            $orderId = $request->input('order_id');
            if (!$orderId) {
                return response()->json([
                    'success' => false,
                    'message' => '订单ID不能为空'
                ], 400);
            }

            $order = Order::with(['items.product'])->findOrFail($orderId);

            // 检查订单状态
            if ($order->status !== 'confirmed') {
                return response()->json([
                    'success' => false,
                    'message' => '只有已确认的订单才能打印小票'
                ], 400);
            }

            // 检查是否已经打印过
            if (\App\Printing\Models\PrintRecord::isPrinted($order, \App\Printing\Models\PrintRecord::TYPE_RECEIPT)) {
                return response()->json([
                    'success' => false,
                    'message' => '该订单小票已打印过'
                ], 400);
            }

            // 调度小票打印任务
            \App\FlyCloud\Jobs\AutoPrintReceiptJob::dispatch($order->id, '手动触发')
                ->onQueue('printing');

            Log::info('手动触发订单小票打印', [
                'order_id' => $orderId,
                'order_no' => $order->order_no,
                'operator' => auth()->user()->name ?? 'unknown'
            ]);

            return response()->json([
                'success' => true,
                'message' => '小票打印任务已加入队列，请稍后查看打印结果'
            ]);

        } catch (\Exception $e) {
            Log::error('手动触发小票打印失败', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '触发小票打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 按仓库分单打印订单
     */
    public function printOrderByWarehouses(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'print_type' => 'nullable|string|in:order,picking,delivery',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $orderId = $request->input('order_id');
            // 🔥 修复：加载配送员信息，确保分仓小票能显示配送员
            $order = Order::with(['items.product', 'delivery.deliverer.employee', 'user.crmAgent'])->findOrFail($orderId);

            $options = [
                'print_type' => $request->input('print_type', 'order'),
                'copies' => $request->input('copies', 1)
            ];

            $results = $this->flyCloudService->printOrderByWarehouses($order, $options);

            // 统计结果
            $successCount = 0;
            $failCount = 0;
            $totalWarehouses = count($results);

            foreach ($results as $result) {
                if (isset($result['success']) && $result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return response()->json([
                'success' => $failCount === 0,
                'message' => "分单打印完成，共 {$totalWarehouses} 个仓库，成功 {$successCount} 个，失败 {$failCount} 个",
                'data' => [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'total_warehouses' => $totalWarehouses,
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud print order by warehouses API error', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '分单打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 打印指定仓库的订单分单
     */
    public function printWarehouseOrder(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'warehouse_id' => 'required|integer|exists:warehouses,id',
                'print_type' => 'nullable|string|in:order,picking,delivery',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $orderId = $request->input('order_id');
            $warehouseId = $request->input('warehouse_id');
            $order = Order::with(['items.product'])->findOrFail($orderId);

            $options = [
                'print_type' => $request->input('print_type', 'order'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printWarehouseOrder($order, $warehouseId, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => "仓库 {$warehouseId} 的订单分单已发送到飞蛾云打印机"
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => "仓库 {$warehouseId} 的订单分单打印失败"
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print warehouse order API error', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id'),
                'warehouse_id' => $request->input('warehouse_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '仓库分单打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取飞蛾云打印机列表
     */
    public function getPrinters(): JsonResponse
    {
        try {
            $printers = $this->flyCloudService->getPrinters();

            return response()->json([
                'success' => true,
                'data' => $printers
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get printers API error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印机列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取飞蛾云打印机状态
     */
    public function getPrinterStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'printer_sn' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $printerSn = $request->input('printer_sn');
            $status = $this->flyCloudService->getPrinterStatus($printerSn);

            return response()->json([
                'success' => true,
                'data' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get printer status API error', [
                'error' => $e->getMessage(),
                'printer_sn' => $request->input('printer_sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印机状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 添加飞蛾云打印机
     */
    public function addPrinter(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sn' => 'required|string',
                'key' => 'required|string',
                'name' => 'nullable|string',
                'location' => 'nullable|string',
                'description' => 'nullable|string',
                'is_default' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sn = $request->input('sn');
            $key = $request->input('key');
            $name = $request->input('name', '');

            $options = [
                'location' => $request->input('location'),
                'description' => $request->input('description'),
                'is_default' => $request->input('is_default', false),
                'created_by' => auth()->id()
            ];

            $result = $this->flyCloudService->addPrinter($sn, $key, $name, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印机添加成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印机添加失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud add printer API error', [
                'error' => $e->getMessage(),
                'sn' => $request->input('sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '添加打印机失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新飞蛾云打印机
     */
    public function updatePrinter(Request $request, $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'location' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:500',
                'is_default' => 'nullable|boolean',
                'status' => 'nullable|string|in:active,inactive,maintenance'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $printer = FlyCloudPrinter::findOrFail($id);
            
            // 更新打印机信息
            if ($request->has('name')) {
                $printer->name = $request->input('name');
            }
            if ($request->has('location')) {
                $printer->location = $request->input('location');
            }
            if ($request->has('description')) {
                $printer->description = $request->input('description');
            }
            if ($request->has('is_default')) {
                $printer->is_default = $request->input('is_default');
            }
            if ($request->has('status')) {
                $printer->status = $request->input('status');
            }
            
            $printer->updated_by = auth()->id();
            $printer->save();

            return response()->json([
                'success' => true,
                'message' => '飞蛾云打印机更新成功',
                'data' => $printer
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud update printer API error', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => '更新打印机失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除飞蛾云打印机
     */
    public function deletePrinter(Request $request, $id): JsonResponse
    {
        try {
            $printer = FlyCloudPrinter::findOrFail($id);
            $sn = $printer->sn;
            
            $result = $this->flyCloudService->deletePrinter($sn);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印机删除成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印机删除失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud delete printer API error', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => '删除打印机失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清空飞蛾云打印队列
     */
    public function clearPrintQueue(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sn' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sn = $request->input('sn');
            $result = $this->flyCloudService->clearPrintQueue($sn);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '打印队列清空成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '打印队列清空失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud clear print queue API error', [
                'error' => $e->getMessage(),
                'sn' => $request->input('sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '清空打印队列失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取订单涉及的仓库列表
     */
    public function getOrderWarehouses(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::with(['items.product'])->findOrFail($orderId);
            $warehouseIds = $this->flyCloudService->getOrderWarehouses($order);

            return response()->json([
                'success' => true,
                'data' => [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'warehouse_ids' => $warehouseIds,
                    'warehouse_count' => count($warehouseIds)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get order warehouses API error', [
                'error' => $e->getMessage(),
                'order_id' => $orderId
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取订单仓库信息失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 绑定仓库和打印机
     */
    public function bindWarehousePrinter(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'warehouse_id' => 'required|integer|exists:warehouses,id',
                'flycloud_printer_id' => 'nullable|integer|exists:flycloud_printers,id',
                'print_type' => 'nullable|string|in:order,picking,delivery',
                'is_default' => 'nullable|boolean',
                'priority' => 'nullable|integer|min:0',
                'settings' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $warehouseId = $request->input('warehouse_id');
            $flyCloudPrinterId = $request->input('flycloud_printer_id');
            $printType = $request->input('print_type') ?: 'order'; // 🔥 修复：确保printType不为null

            $options = [
                'is_default' => $request->input('is_default', false),
                'priority' => $request->input('priority', 0),
                'settings' => $request->input('settings', []),
                'created_by' => auth()->id()
            ];

            $binding = WarehousePrinterBinding::bindWarehousePrinter(
                $warehouseId,
                $flyCloudPrinterId,
                $printType,
                $options
            );

            return response()->json([
                'success' => true,
                'message' => '仓库打印机绑定成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'warehouse_id' => $warehouseId,
                    'printer_id' => $flyCloudPrinterId,
                    'print_type' => $printType,
                    'is_default' => $binding->is_default
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud bind warehouse printer API error', [
                'error' => $e->getMessage(),
                'warehouse_id' => $request->input('warehouse_id'),
                'printer_id' => $request->input('flycloud_printer_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '仓库打印机绑定失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量绑定仓库打印机
     */
    public function batchBindWarehousePrinters(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'bindings' => 'required|array|min:1',
                'bindings.*.warehouse_id' => 'required|integer|exists:warehouses,id',
                'bindings.*.flycloud_printer_id' => 'required|integer|exists:flycloud_printers,id',
                'bindings.*.print_type' => 'nullable|string|in:order,picking,delivery',
                'bindings.*.options' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $bindings = $request->input('bindings');

            // 为每个绑定添加创建者信息
            foreach ($bindings as &$binding) {
                if (!isset($binding['options'])) {
                    $binding['options'] = [];
                }
                $binding['options']['created_by'] = auth()->id();
            }

            $results = $this->flyCloudService->bindWarehousePrinters($bindings);

            $successCount = count(array_filter($results, function($result) {
                return $result['success'];
            }));
            $totalCount = count($results);

            return response()->json([
                'success' => $successCount === $totalCount,
                'message' => "批量绑定完成，共 {$totalCount} 个，成功 {$successCount} 个",
                'data' => [
                    'total_count' => $totalCount,
                    'success_count' => $successCount,
                    'fail_count' => $totalCount - $successCount,
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud batch bind warehouse printers API error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '批量绑定失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取仓库打印机绑定信息
     */
    public function getWarehousePrinterBindings(Request $request, $warehouseId): JsonResponse
    {
        try {
            $bindings = $this->flyCloudService->getWarehousePrinterBindings($warehouseId);

            return response()->json([
                'success' => true,
                'data' => [
                    'warehouse_id' => $warehouseId,
                    'bindings' => $bindings
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get warehouse printer bindings API error', [
                'error' => $e->getMessage(),
                'warehouse_id' => $warehouseId
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取仓库打印机绑定信息失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取打印任务列表
     */
    public function getPrintTasks(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'nullable|string|in:pending,printing,completed,failed',
                'printer_sn' => 'nullable|string',
                'task_type' => 'nullable|string|in:text,html,order,receipt',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date',
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $query = PrintTask::with(['printer', 'creator']);

            // 按状态筛选
            if ($request->has('status')) {
                $query->withStatus($request->input('status'));
            }

            // 按打印机筛选
            if ($request->has('printer_sn')) {
                $query->withPrinter($request->input('printer_sn'));
            }

            // 按任务类型筛选
            if ($request->has('task_type')) {
                $query->withType($request->input('task_type'));
            }

            // 按时间范围筛选
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->withinDateRange($request->input('start_date'), $request->input('end_date'));
            }

            // 分页
            $perPage = $request->input('per_page', 15);
            $tasks = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $tasks
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get print tasks API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印任务列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取打印任务详情
     */
    public function getPrintTask(Request $request, $taskId): JsonResponse
    {
        try {
            $task = PrintTask::with(['printer', 'creator'])
                ->where('task_id', $taskId)
                ->firstOrFail();

            return response()->json([
                'success' => true,
                'data' => $task
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get print task API error', [
                'error' => $e->getMessage(),
                'task_id' => $taskId
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印任务详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重试打印任务
     */
    public function retryPrintTask(Request $request, $taskId): JsonResponse
    {
        try {
            $task = PrintTask::where('task_id', $taskId)->firstOrFail();

            if (!$task->canRetry()) {
                return response()->json([
                    'success' => false,
                    'message' => '该任务不允许重试'
                ], 400);
            }

            // 重置任务状态
            $task->status = PrintTask::STATUS_PENDING;
            $task->error_message = null;
            $task->incrementRetryCount();

            // 调用打印服务重新执行
            $result = $this->flyCloudService->retryPrintTask($task);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '打印任务重试成功',
                    'data' => [
                        'task_id' => $taskId,
                        'retry_count' => $task->retry_count
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '打印任务重试失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud retry print task API error', [
                'error' => $e->getMessage(),
                'task_id' => $taskId
            ]);

            return response()->json([
                'success' => false,
                'message' => '重试打印任务失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 取消打印任务
     */
    public function cancelPrintTask(Request $request, $taskId): JsonResponse
    {
        try {
            $task = PrintTask::where('task_id', $taskId)->firstOrFail();

            if ($task->status !== PrintTask::STATUS_PENDING) {
                return response()->json([
                    'success' => false,
                    'message' => '只能取消等待中的任务'
                ], 400);
            }

            $task->markAsFailed('任务已取消');

            return response()->json([
                'success' => true,
                'message' => '打印任务已取消',
                'data' => [
                    'task_id' => $taskId,
                    'status' => $task->status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud cancel print task API error', [
                'error' => $e->getMessage(),
                'task_id' => $taskId
            ]);

            return response()->json([
                'success' => false,
                'message' => '取消打印任务失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取打印任务统计
     */
    public function getPrintTaskStats(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date',
                'printer_sn' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $query = PrintTask::query();

            // 按时间范围筛选
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->withinDateRange($request->input('start_date'), $request->input('end_date'));
            }

            // 按打印机筛选
            if ($request->has('printer_sn')) {
                $query->withPrinter($request->input('printer_sn'));
            }

            // 统计各状态的任务数量
            $stats = [
                'total' => $query->count(),
                'pending' => (clone $query)->withStatus(PrintTask::STATUS_PENDING)->count(),
                'printing' => (clone $query)->withStatus(PrintTask::STATUS_PRINTING)->count(),
                'completed' => (clone $query)->withStatus(PrintTask::STATUS_COMPLETED)->count(),
                'failed' => (clone $query)->withStatus(PrintTask::STATUS_FAILED)->count(),
            ];

            // 成功率计算
            $stats['success_rate'] = $stats['total'] > 0 
                ? round(($stats['completed'] / $stats['total']) * 100, 2) 
                : 0;

            // 按任务类型统计
            $typeStats = PrintTask::selectRaw('task_type, count(*) as count')
                ->groupBy('task_type')
                ->pluck('count', 'task_type')
                ->toArray();

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => $stats,
                    'type_stats' => $typeStats
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get print task stats API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印任务统计失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取所有仓库打印机绑定关系
     */
    public function getAllWarehousePrinterBindings(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'warehouse_id' => 'nullable|integer|exists:warehouses,id',
                'print_type' => 'nullable|string|in:order,picking,delivery',
                'is_active' => 'nullable|boolean',
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $query = WarehousePrinterBinding::with(['flyCloudPrinter', 'warehouse']);

            // 按仓库筛选
            if ($request->has('warehouse_id')) {
                $query->byWarehouse($request->input('warehouse_id'));
            }

            // 按打印类型筛选
            if ($request->has('print_type')) {
                $query->byPrintType($request->input('print_type'));
            }

            // 按激活状态筛选
            if ($request->has('is_active')) {
                $query->where('is_active', $request->input('is_active'));
            }

            // 分页
            $perPage = $request->input('per_page', 15);
            $bindings = $query->orderByPriority()->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $bindings
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get all warehouse printer bindings API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取仓库打印机绑定关系失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取FlyCloud全局配置
     */
    public function getGlobalConfig(): JsonResponse
    {
        try {
            $configService = app(\App\Shop\Services\ConfigService::class);
            
            $config = [
                'api_url' => $configService->get('flycloud_api_url', 'http://api.feieyun.cn/Api/Open/'),
                'user' => $configService->get('flycloud_user', ''),
                'ukey' => $configService->get('flycloud_ukey', ''),
                'timeout' => (int)$configService->get('flycloud_timeout', 30),
                'debug' => (bool)$configService->get('flycloud_debug', false),
                'auto_print_enabled' => (bool)$configService->get('flycloud_auto_print_enabled', true),
                'auto_print_on_create' => (bool)$configService->get('flycloud_auto_print_on_create', true),
                'auto_print_on_paid' => (bool)$configService->get('flycloud_auto_print_on_paid', false),
                'default_print_type' => $configService->get('flycloud_default_print_type', 'order'),
                'default_copies' => (int)$configService->get('flycloud_default_copies', 1),
                'max_copies' => (int)$configService->get('flycloud_max_copies', 10),
                'retry_attempts' => (int)$configService->get('flycloud_retry_attempts', 3),
                'retry_delay' => (int)$configService->get('flycloud_retry_delay', 10),
                'notify_on_failure' => (bool)$configService->get('flycloud_notify_on_failure', true),
            ];

            return response()->json([
                'success' => true,
                'data' => $config
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get global config API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取全局配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 保存FlyCloud全局配置
     */
    public function saveGlobalConfig(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'api_url' => 'required|url',
                'user' => 'required|string',
                'ukey' => 'required|string',
                'timeout' => 'required|integer|min:5|max:120',
                'debug' => 'boolean',
                'auto_print_enabled' => 'boolean',
                'auto_print_on_create' => 'boolean',
                'auto_print_on_paid' => 'boolean',
                'default_print_type' => 'required|string|in:order,picking,delivery',
                'default_copies' => 'required|integer|min:1|max:10',
                'max_copies' => 'required|integer|min:1|max:20',
                'retry_attempts' => 'required|integer|min:0|max:10',
                'retry_delay' => 'required|integer|min:1|max:300',
                'notify_on_failure' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $configService = app(\App\Shop\Services\ConfigService::class);
            
            // 保存全局配置到系统配置表
            $configs = [
                'flycloud_api_url' => $request->input('api_url'),
                'flycloud_user' => $request->input('user'),
                'flycloud_ukey' => $request->input('ukey'),
                'flycloud_timeout' => $request->input('timeout'),
                'flycloud_debug' => $request->input('debug', false),
                'flycloud_auto_print_enabled' => $request->input('auto_print_enabled', true),
                'flycloud_auto_print_on_create' => $request->input('auto_print_on_create', true),
                'flycloud_auto_print_on_paid' => $request->input('auto_print_on_paid', false),
                'flycloud_default_print_type' => $request->input('default_print_type'),
                'flycloud_default_copies' => $request->input('default_copies'),
                'flycloud_max_copies' => $request->input('max_copies'),
                'flycloud_retry_attempts' => $request->input('retry_attempts'),
                'flycloud_retry_delay' => $request->input('retry_delay'),
                'flycloud_notify_on_failure' => $request->input('notify_on_failure', true),
            ];

            foreach ($configs as $key => $value) {
                $configService->set($key, $value, [
                    'group' => 'flycloud',
                    'title' => $this->getConfigTitle($key),
                    'description' => $this->getConfigDescription($key),
                    'type' => $this->getConfigType($key, $value),
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'FlyCloud全局配置保存成功'
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud save global config API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '保存全局配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 测试FlyCloud连接
     */
    public function testGlobalConfig(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'api_url' => 'required|url',
                'user' => 'required|string',
                'ukey' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            // 创建临时配置进行测试
            $testConfig = [
                'api_url' => $request->input('api_url'),
                'user' => $request->input('user'),
                'ukey' => $request->input('ukey'),
                'timeout' => 30,
                'debug' => false
            ];
            
            // 创建测试服务实例
            $testService = new \App\FlyCloud\Services\FlyCloudService();
            $testService->updateGlobalConfig($testConfig);
            
            // 尝试获取打印机列表来测试连接
            $printers = $testService->getPrinters();

            return response()->json([
                'success' => true,
                'message' => 'FlyCloud连接测试成功',
                'data' => [
                    'printers_count' => count($printers),
                    'connection_status' => 'connected'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud test global config API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'FlyCloud连接测试失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取配置项标题
     */
    private function getConfigTitle(string $key): string
    {
        $titles = [
            'flycloud_api_url' => 'API地址',
            'flycloud_user' => '用户账号',
            'flycloud_ukey' => '用户密钥',
            'flycloud_timeout' => '请求超时时间',
            'flycloud_debug' => '调试模式',
            'flycloud_auto_print_enabled' => '启用自动打印',
            'flycloud_auto_print_on_create' => '订单创建时打印',
            'flycloud_auto_print_on_paid' => '订单付款时打印',
            'flycloud_default_print_type' => '默认打印类型',
            'flycloud_default_copies' => '默认打印份数',
            'flycloud_max_copies' => '最大打印份数',
            'flycloud_retry_attempts' => '重试次数',
            'flycloud_retry_delay' => '重试延迟',
            'flycloud_notify_on_failure' => '失败时通知',
        ];

        return $titles[$key] ?? $key;
    }

    /**
     * 获取配置项描述
     */
    private function getConfigDescription(string $key): string
    {
        $descriptions = [
            'flycloud_api_url' => '飞蛾云API接口地址',
            'flycloud_user' => '飞蛾云后台注册的用户账号',
            'flycloud_ukey' => '飞蛾云后台生成的用户密钥',
            'flycloud_timeout' => 'API请求超时时间（秒）',
            'flycloud_debug' => '是否开启调试模式，开启后会记录详细日志',
            'flycloud_auto_print_enabled' => '是否启用订单自动打印功能',
            'flycloud_auto_print_on_create' => '订单创建时是否自动打印',
            'flycloud_auto_print_on_paid' => '订单付款完成时是否自动打印',
            'flycloud_default_print_type' => '默认的打印类型：order订单、picking拣货、delivery配送',
            'flycloud_default_copies' => '默认的打印份数',
            'flycloud_max_copies' => '允许的最大打印份数',
            'flycloud_retry_attempts' => '打印失败时的重试次数',
            'flycloud_retry_delay' => '重试间隔时间（秒）',
            'flycloud_notify_on_failure' => '打印失败时是否发送通知',
        ];

        return $descriptions[$key] ?? '';
    }

    /**
     * 获取配置项类型
     */
    private function getConfigType(string $key, $value): string
    {
        if (is_bool($value)) {
            return 'switch';
        }
        
        if (is_int($value)) {
            return 'number';
        }
        
        if (in_array($key, ['flycloud_ukey'])) {
            return 'password';
        }
        
        if (in_array($key, ['flycloud_api_url'])) {
            return 'url';
        }
        
        if (in_array($key, ['flycloud_default_print_type'])) {
            return 'select';
        }
        
        return 'text';
    }

    /**
     * 调试打印机添加问题
     */
    public function debugAddPrinter(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sn' => 'required|string',
                'key' => 'required|string',
                'name' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sn = $request->input('sn');
            $key = $request->input('key');
            $name = $request->input('name', '');

            // 1. 验证格式
            $formatCheck = [
                'sn_valid' => FlyCloudPrinter::validateSn($sn),
                'key_valid' => FlyCloudPrinter::validateKey($key),
                'sn_length' => strlen($sn),
                'key_length' => strlen($key),
                'sn_pattern' => preg_match('/^[a-zA-Z0-9]{8,15}$/', $sn),
                'key_pattern' => preg_match('/^[a-zA-Z0-9]{8,32}$/', $key)
            ];

            // 2. 检查数据库中是否已存在
            $existsInDb = FlyCloudPrinter::where('sn', $sn)->exists();

            // 3. 构造API请求参数
            $printerContent = $sn . '#' . $key . '#' . $name . '#';
            
            // 4. 记录详细的API请求信息
            Log::info('FlyCloud debug add printer', [
                'sn' => $sn,
                'key' => $key,
                'name' => $name,
                'printer_content' => $printerContent,
                'format_check' => $formatCheck,
                'exists_in_db' => $existsInDb,
                'api_url' => $this->flyCloudService->config['api_url'] ?? 'unknown',
                'user' => $this->flyCloudService->config['user'] ?? 'unknown'
            ]);

            // 5. 尝试通过服务层添加到飞蛾云
            $result = $this->flyCloudService->addPrinter($sn, $key, $name);
            
            // 获取最后的API响应（需要在服务层记录）
            $response = ['result' => $result];

            return response()->json([
                'success' => true,
                'debug_info' => [
                    'format_check' => $formatCheck,
                    'exists_in_db' => $existsInDb,
                    'printer_content' => $printerContent,
                    'api_response' => $response,
                    'config' => [
                        'api_url' => $this->flyCloudService->config['api_url'] ?? 'unknown',
                        'user' => $this->flyCloudService->config['user'] ?? 'unknown',
                        'ukey_length' => strlen($this->flyCloudService->config['ukey'] ?? '')
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud debug add printer error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '调试失败: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 测试打印机连接（不添加到账户）
     */
    public function testPrinterConnection(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sn' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sn = $request->input('sn');

            // 尝试查询打印机状态（这个API不需要打印机在账户中）
            $statusResult = $this->flyCloudService->getPrinterStatus($sn);

            return response()->json([
                'success' => true,
                'message' => '打印机连接测试完成',
                'data' => [
                    'sn' => $sn,
                    'status_result' => $statusResult,
                    'can_connect' => isset($statusResult['status']),
                    'error_message' => $statusResult['message'] ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud test printer connection error', [
                'error' => $e->getMessage(),
                'sn' => $request->input('sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '测试打印机连接失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取FlyCloud配置（保持向后兼容）
     * @deprecated 请使用 getGlobalConfig() 方法
     */
    public function getConfig(): JsonResponse
    {
        return $this->getGlobalConfig();
    }

    /**
     * 保存FlyCloud配置（保持向后兼容）
     * @deprecated 请使用 saveGlobalConfig() 方法
     */
    public function saveConfig(Request $request): JsonResponse
    {
        return $this->saveGlobalConfig($request);
    }

    /**
     * 测试FlyCloud连接（保持向后兼容）
     * @deprecated 请使用 testGlobalConfig() 方法
     */
    public function testConnection(Request $request): JsonResponse
    {
        return $this->testGlobalConfig($request);
    }

    /**
     * 查询商品库存 - 简单直接查库存表
     */
    public function getProductStock(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $productId = $request->input('product_id');
            $stockData = $this->flyCloudService->getProductStock($productId);

            return response()->json([
                'success' => true,
                'message' => '查询成功',
                'data' => [
                    'product_id' => $productId,
                    'warehouses' => $stockData,
                    'total_warehouses' => count($stockData)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('查询商品库存失败', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '查询失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量查询商品库存
     */
    public function getBatchProductStock(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_ids' => 'required|array',
                'product_ids.*' => 'integer|exists:products,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $productIds = $request->input('product_ids');
            $stockData = $this->flyCloudService->getBatchProductStock($productIds);

            return response()->json([
                'success' => true,
                'message' => '批量查询成功',
                'data' => [
                    'products' => $stockData,
                    'total_products' => count($productIds)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('批量查询商品库存失败', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '批量查询失败：' . $e->getMessage()
            ], 500);
        }
    }
}