<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Product\Models\Product;
use App\Product\Services\PriceCalculationService;
use App\Models\User;
use App\Region\Models\Region;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MonitorPricePerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'price:monitor 
                            {--samples=50 : 测试样本数量}
                            {--batch-size=10 : 批量测试大小}
                            {--region-id= : 指定区域ID}
                            {--user-id= : 指定用户ID}
                            {--clear-cache : 清除缓存后测试}
                            {--report : 生成详细报告}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '监控价格计算性能，分析瓶颈和优化效果';

    /**
     * 价格计算服务
     */
    protected $priceService;

    /**
     * Create a new command instance.
     */
    public function __construct(PriceCalculationService $priceService)
    {
        parent::__construct();
        $this->priceService = $priceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始价格计算性能监控...');
        
        if ($this->option('clear-cache')) {
            $this->info('清除价格缓存...');
            Cache::flush();
        }
        
        $samples = (int) $this->option('samples');
        $batchSize = (int) $this->option('batch-size');
        $regionId = $this->option('region-id') ? (int) $this->option('region-id') : null;
        $userId = $this->option('user-id') ? (int) $this->option('user-id') : null;
        
        // 获取测试用户
        $user = $userId ? User::find($userId) : null;
        if ($userId && !$user) {
            $this->error("用户 ID {$userId} 不存在");
            return 1;
        }
        
        // 获取测试商品
        $products = Product::where('status', 1)
                          ->inRandomOrder()
                          ->limit($samples)
                          ->get();
        
        if ($products->isEmpty()) {
            $this->error('没有找到可用的商品进行测试');
            return 1;
        }
        
        $this->info("测试配置:");
        $this->info("- 商品样本: {$samples}");
        $this->info("- 批量大小: {$batchSize}");
        $this->info("- 区域ID: " . ($regionId ?? '无'));
        $this->info("- 用户ID: " . ($userId ?? '游客'));
        $this->newLine();
        
        $results = [
            'single_calculation' => $this->testSingleCalculation($products, $user, $regionId),
            'batch_calculation' => $this->testBatchCalculation($products, $user, $regionId, $batchSize),
            'cache_performance' => $this->testCachePerformance($products, $user, $regionId),
            'database_queries' => $this->analyzeDatabaseQueries($products, $user, $regionId)
        ];
        
        $this->displayResults($results);
        
        if ($this->option('report')) {
            $this->generateDetailedReport($results);
        }
        
        return 0;
    }
    
    /**
     * 测试单个价格计算性能
     */
    private function testSingleCalculation($products, $user, $regionId)
    {
        $this->info('测试单个价格计算性能...');
        
        $times = [];
        $errors = 0;
        
        $progressBar = $this->output->createProgressBar($products->count());
        $progressBar->start();
        
        foreach ($products as $product) {
            $startTime = microtime(true);
            
            try {
                $this->priceService->calculatePrice($product, $user, $regionId);
                $endTime = microtime(true);
                $times[] = ($endTime - $startTime) * 1000; // 转换为毫秒
            } catch (\Exception $e) {
                $errors++;
                Log::warning('单个价格计算失败', [
                    'product_id' => $product->id,
                    'error' => $e->getMessage()
                ]);
            }
            
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->newLine();
        
        return [
            'total_products' => $products->count(),
            'successful_calculations' => count($times),
            'errors' => $errors,
            'avg_time_ms' => count($times) > 0 ? round(array_sum($times) / count($times), 2) : 0,
            'min_time_ms' => count($times) > 0 ? round(min($times), 2) : 0,
            'max_time_ms' => count($times) > 0 ? round(max($times), 2) : 0,
            'total_time_ms' => round(array_sum($times), 2)
        ];
    }
    
    /**
     * 测试批量价格计算性能
     */
    private function testBatchCalculation($products, $user, $regionId, $batchSize)
    {
        $this->info('测试批量价格计算性能...');
        
        $productChunks = $products->chunk($batchSize);
        $times = [];
        $errors = 0;
        $totalProducts = 0;
        
        $progressBar = $this->output->createProgressBar($productChunks->count());
        $progressBar->start();
        
        foreach ($productChunks as $chunk) {
            $productIds = $chunk->pluck('id')->toArray();
            $startTime = microtime(true);
            
            try {
                $results = $this->priceService->calculateBatchPrices($productIds, $user, $regionId);
                $endTime = microtime(true);
                
                $times[] = ($endTime - $startTime) * 1000; // 转换为毫秒
                $totalProducts += count($productIds);
                
                // 检查是否有错误结果
                foreach ($results as $result) {
                    if (isset($result['error'])) {
                        $errors++;
                    }
                }
            } catch (\Exception $e) {
                $errors++;
                Log::warning('批量价格计算失败', [
                    'product_ids' => $productIds,
                    'error' => $e->getMessage()
                ]);
            }
            
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->newLine();
        
        return [
            'total_batches' => $productChunks->count(),
            'batch_size' => $batchSize,
            'total_products' => $totalProducts,
            'successful_batches' => count($times),
            'errors' => $errors,
            'avg_batch_time_ms' => count($times) > 0 ? round(array_sum($times) / count($times), 2) : 0,
            'avg_per_product_ms' => count($times) > 0 ? round(array_sum($times) / $totalProducts, 2) : 0,
            'total_time_ms' => round(array_sum($times), 2)
        ];
    }
    
    /**
     * 测试缓存性能
     */
    private function testCachePerformance($products, $user, $regionId)
    {
        $this->info('测试缓存性能...');
        
        // 选择前10个商品进行缓存测试
        $testProducts = $products->take(10);
        
        // 第一次计算（无缓存）
        $coldTimes = [];
        foreach ($testProducts as $product) {
            // 清除该商品的缓存
            $this->priceService->clearPriceCache($product->id, $user?->id);
            
            $startTime = microtime(true);
            $this->priceService->calculatePrice($product, $user, $regionId);
            $endTime = microtime(true);
            
            $coldTimes[] = ($endTime - $startTime) * 1000;
        }
        
        // 第二次计算（有缓存）
        $hotTimes = [];
        foreach ($testProducts as $product) {
            $startTime = microtime(true);
            $this->priceService->calculatePrice($product, $user, $regionId);
            $endTime = microtime(true);
            
            $hotTimes[] = ($endTime - $startTime) * 1000;
        }
        
        return [
            'test_products' => $testProducts->count(),
            'cold_cache_avg_ms' => round(array_sum($coldTimes) / count($coldTimes), 2),
            'hot_cache_avg_ms' => round(array_sum($hotTimes) / count($hotTimes), 2),
            'cache_speedup' => round((array_sum($coldTimes) / array_sum($hotTimes)), 2),
            'cache_hit_improvement_percent' => round((1 - array_sum($hotTimes) / array_sum($coldTimes)) * 100, 1)
        ];
    }
    
    /**
     * 分析数据库查询
     */
    private function analyzeDatabaseQueries($products, $user, $regionId)
    {
        $this->info('分析数据库查询...');
        
        // 启用查询日志
        DB::enableQueryLog();
        
        // 测试单个商品的查询
        $testProduct = $products->first();
        $this->priceService->clearPriceCache($testProduct->id, $user?->id);
        $this->priceService->calculatePrice($testProduct, $user, $regionId);
        
        $singleQueries = DB::getQueryLog();
        DB::flushQueryLog();
        
        // 测试批量查询
        $testProductIds = $products->take(5)->pluck('id')->toArray();
        foreach ($testProductIds as $productId) {
            $this->priceService->clearPriceCache($productId, $user?->id);
        }
        $this->priceService->calculateBatchPrices($testProductIds, $user, $regionId);
        
        $batchQueries = DB::getQueryLog();
        DB::disableQueryLog();
        
        // 安全计算，避免除零错误
        $singleQueriesCount = count($singleQueries);
        $batchQueriesCount = count($batchQueries);
        $testProductsCount = count($testProductIds);
        
        $queriesPerProductBatch = $testProductsCount > 0 ? round($batchQueriesCount / $testProductsCount, 2) : 0;
        
        // 计算查询减少百分比，避免除零
        $queryReductionPercent = 0;
        if ($singleQueriesCount > 0 && $testProductsCount > 0) {
            $batchQueriesPerProduct = $batchQueriesCount / $testProductsCount;
            $queryReductionPercent = round((1 - $batchQueriesPerProduct / $singleQueriesCount) * 100, 1);
        }
        
        return [
            'single_calculation_queries' => $singleQueriesCount,
            'batch_calculation_queries' => $batchQueriesCount,
            'batch_products_tested' => $testProductsCount,
            'queries_per_product_single' => $singleQueriesCount,
            'queries_per_product_batch' => $queriesPerProductBatch,
            'query_reduction_percent' => $queryReductionPercent
        ];
    }
    
    /**
     * 显示测试结果
     */
    private function displayResults($results)
    {
        $this->newLine();
        $this->info('=== 性能测试结果 ===');
        
        // 单个计算结果
        $single = $results['single_calculation'];
        $this->info('单个价格计算:');
        $this->line("  成功: {$single['successful_calculations']}/{$single['total_products']}");
        $this->line("  错误: {$single['errors']}");
        $this->line("  平均耗时: {$single['avg_time_ms']}ms");
        $this->line("  最小耗时: {$single['min_time_ms']}ms");
        $this->line("  最大耗时: {$single['max_time_ms']}ms");
        
        // 批量计算结果
        $batch = $results['batch_calculation'];
        $this->newLine();
        $this->info('批量价格计算:');
        $this->line("  批次数: {$batch['total_batches']}");
        $this->line("  批量大小: {$batch['batch_size']}");
        $this->line("  总商品数: {$batch['total_products']}");
        $this->line("  平均批次耗时: {$batch['avg_batch_time_ms']}ms");
        $this->line("  平均单品耗时: {$batch['avg_per_product_ms']}ms");
        
        // 性能对比
        if ($single['avg_time_ms'] > 0 && $batch['avg_per_product_ms'] > 0) {
            $speedup = round($single['avg_time_ms'] / $batch['avg_per_product_ms'], 2);
            $improvement = round((1 - $batch['avg_per_product_ms'] / $single['avg_time_ms']) * 100, 1);
            $this->line("  性能提升: {$speedup}x ({$improvement}%)");
        }
        
        // 缓存性能
        $cache = $results['cache_performance'];
        $this->newLine();
        $this->info('缓存性能:');
        $this->line("  冷缓存平均: {$cache['cold_cache_avg_ms']}ms");
        $this->line("  热缓存平均: {$cache['hot_cache_avg_ms']}ms");
        $this->line("  缓存加速: {$cache['cache_speedup']}x");
        $this->line("  性能提升: {$cache['cache_hit_improvement_percent']}%");
        
        // 数据库查询
        $db = $results['database_queries'];
        $this->newLine();
        $this->info('数据库查询优化:');
        $this->line("  单个计算查询数: {$db['single_calculation_queries']}");
        $this->line("  批量计算查询数: {$db['batch_calculation_queries']}");
        $this->line("  批量测试商品数: {$db['batch_products_tested']}");
        $this->line("  单品查询数(单个): {$db['queries_per_product_single']}");
        $this->line("  单品查询数(批量): {$db['queries_per_product_batch']}");
        $this->line("  查询减少: {$db['query_reduction_percent']}%");
    }
    
    /**
     * 生成详细报告
     */
    private function generateDetailedReport($results)
    {
        $reportPath = storage_path('logs/price_performance_' . date('Y-m-d_H-i-s') . '.json');
        
        $report = [
            'timestamp' => now()->toISOString(),
            'test_configuration' => [
                'samples' => $this->option('samples'),
                'batch_size' => $this->option('batch-size'),
                'region_id' => $this->option('region-id'),
                'user_id' => $this->option('user-id'),
                'cache_cleared' => $this->option('clear-cache')
            ],
            'results' => $results,
            'recommendations' => $this->generateRecommendations($results)
        ];
        
        file_put_contents($reportPath, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->newLine();
        $this->info("详细报告已保存到: {$reportPath}");
    }
    
    /**
     * 生成优化建议
     */
    private function generateRecommendations($results)
    {
        $recommendations = [];
        
        // 基于单个计算性能的建议
        if ($results['single_calculation']['avg_time_ms'] > 100) {
            $recommendations[] = '单个价格计算耗时较长，建议优化数据库查询或增加索引';
        }
        
        // 基于批量计算性能的建议
        $batchImprovement = $results['single_calculation']['avg_time_ms'] / $results['batch_calculation']['avg_per_product_ms'];
        if ($batchImprovement < 2) {
            $recommendations[] = '批量计算优化效果不明显，建议检查批量查询逻辑';
        }
        
        // 基于缓存性能的建议
        if ($results['cache_performance']['cache_speedup'] < 5) {
            $recommendations[] = '缓存加速效果不明显，建议优化缓存策略或增加缓存时间';
        }
        
        // 基于数据库查询的建议
        if ($results['database_queries']['queries_per_product_single'] > 5) {
            $recommendations[] = '单个商品价格计算查询数过多，建议优化查询逻辑或使用预加载';
        }
        
        if ($results['database_queries']['query_reduction_percent'] < 50) {
            $recommendations[] = '批量查询优化效果不佳，建议进一步优化批量查询策略';
        }
        
        return $recommendations;
    }
} 