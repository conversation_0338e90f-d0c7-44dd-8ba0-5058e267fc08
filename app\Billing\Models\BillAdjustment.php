<?php

namespace App\Billing\Models;

use App\Employee\Models\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BillAdjustment extends Model
{
    use HasFactory;

    protected $fillable = [
        'bill_id',
        'adjustment_no',
        'adjustment_type',
        'adjustment_amount',
        'reason',
        'description',
        'operator_id',
        'status',
        'applied_at',
    ];

    protected $casts = [
        'adjustment_amount' => 'decimal:2',
        'applied_at' => 'datetime',
    ];

    // 调整类型常量
    const TYPE_PRICE_INCREASE = 'price_increase';
    const TYPE_PRICE_DECREASE = 'price_decrease';
    const TYPE_QUANTITY_CHANGE = 'quantity_change';
    const TYPE_ITEM_ADD = 'item_add';
    const TYPE_ITEM_REMOVE = 'item_remove';
    const TYPE_DISCOUNT_ADJUST = 'discount_adjust';
    const TYPE_DISCOUNT = 'discount';  // 🔥 新增：抹零优惠
    const TYPE_FEE_ADD = 'fee_add';
    const TYPE_MANUAL_ADJUST = 'manual_adjust';

    // 调整状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_APPLIED = 'applied';

    /**
     * 生成唯一调整单号
     */
    public static function generateAdjustmentNo(): string
    {
        return 'BA' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 关联账单
     */
    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class);
    }

    /**
     * 关联调整操作员
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'operator_id');
    }

    /**
     * 获取调整类型文本
     */
    public function getAdjustmentTypeTextAttribute(): string
    {
        return match($this->adjustment_type) {
            self::TYPE_PRICE_INCREASE => '价格上调',
            self::TYPE_PRICE_DECREASE => '价格下调',
            self::TYPE_QUANTITY_CHANGE => '数量变更',
            self::TYPE_ITEM_ADD => '新增项目',
            self::TYPE_ITEM_REMOVE => '移除项目',
            self::TYPE_DISCOUNT_ADJUST => '优惠调整',
            self::TYPE_DISCOUNT => '抹零优惠',  // 🔥 新增：抹零优惠
            self::TYPE_FEE_ADD => '新增费用',
            self::TYPE_MANUAL_ADJUST => '人工调整',
            default => '其他调整'
        };
    }

    /**
     * 获取调整状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => '待审批',
            self::STATUS_APPROVED => '已批准',
            self::STATUS_REJECTED => '已拒绝',
            self::STATUS_APPLIED => '已应用',
            default => '未知状态'
        };
    }

    /**
     * 检查是否为增加金额的调整
     */
    public function isIncreasing(): bool
    {
        return $this->adjustment_amount > 0;
    }

    /**
     * 检查是否为减少金额的调整
     */
    public function isDecreasing(): bool
    {
        return $this->adjustment_amount < 0;
    }

    /**
     * 检查是否可以审批
     */
    public function canBeApproved(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 检查是否可以应用
     */
    public function canBeApplied(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * 批准调整
     */
    public function approve(): void
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
        ]);
    }

    /**
     * 拒绝调整
     */
    public function reject(): void
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
        ]);
    }

    /**
     * 应用调整
     */
    public function apply(): void
    {
        $this->update([
            'status' => self::STATUS_APPLIED,
            'applied_at' => now(),
        ]);
    }

    /**
     * 作用域：按账单筛选
     */
    public function scopeByBill($query, int $billId)
    {
        return $query->where('bill_id', $billId);
    }

    /**
     * 作用域：按调整类型筛选
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('adjustment_type', $type);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待审批的调整
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：已批准的调整
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * 作用域：增加金额的调整
     */
    public function scopeIncreasing($query)
    {
        return $query->where('adjustment_amount', '>', 0);
    }

    /**
     * 作用域：减少金额的调整
     */
    public function scopeDecreasing($query)
    {
        return $query->where('adjustment_amount', '<', 0);
    }
} 