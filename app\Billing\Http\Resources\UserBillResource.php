<?php

namespace App\Billing\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

/**
 * 用户端账单资源类
 * 为小程序用户端优化的账单数据格式
 */
class UserBillResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'bill_no' => $this->bill_no,
            'bill_type' => $this->bill_type,
            'bill_type_text' => $this->getBillTypeText(),
            'status' => $this->status,
            'status_text' => $this->getStatusText(),
            'payment_status' => $this->payment_status,
            'payment_status_text' => $this->getPaymentStatusText(),
            
            // 金额信息
            'amounts' => [
                'original' => $this->original_amount,
                'final' => $this->final_amount,
                'paid' => $this->paid_amount,
                'remaining' => $this->final_amount - $this->paid_amount,
                'adjustment' => $this->final_amount - $this->original_amount,
            ],
            
            // 订单信息
            'order' => $this->when($this->order, [
                'id' => $this->order?->id,
                'order_no' => $this->order?->order_no,
                'status' => $this->order?->status,
                'delivery_time' => $this->order?->delivery_time,
            ]),
            
            // 更正信息
            'correction_info' => [
                'has_correction' => $this->hasCorrection(),
                'correction_count' => $this->getCorrectionCount(),
                'latest_correction_at' => $this->getLatestCorrectionTime(),
                'correction_reason' => $this->getLatestCorrectionReason(),
            ],
            
            // 时间信息
            'dates' => [
                'created_at' => $this->created_at,
                'due_date' => $this->due_date,
                'paid_at' => $this->getLatestPaymentTime(),
                'days_until_due' => $this->getDaysUntilDue(),
                'is_overdue' => $this->isOverdue(),
            ],
            
            // 支付信息
            'payment_info' => [
                'preferred_method' => $this->getPreferredPaymentMethod(),
                'available_methods' => $this->getAvailablePaymentMethods(),
                'last_payment_method' => $this->getLastPaymentMethod(),
            ],
            
            // 显示标签
            'display_tags' => $this->getDisplayTags(),
            
            // 操作权限
            'actions' => [
                'can_pay' => $this->canPay(),
                'can_view_detail' => true,
                'can_contact_service' => true,
            ],
        ];
    }

    /**
     * 获取账单类型文本
     */
    private function getBillTypeText(): string
    {
        $types = [
            'order' => '订单账单',
            'adjustment' => '调整账单',
            'refund' => '退款账单',
            'supplement' => '补款账单',
        ];
        
        return $types[$this->bill_type] ?? '未知类型';
    }

    /**
     * 获取状态文本
     */
    private function getStatusText(): string
    {
        $statuses = [
            'draft' => '草稿',
            'pending' => '待付款',
            'paid' => '已付款',
            'cancelled' => '已取消',
            'consolidated' => '已合并',
        ];
        
        return $statuses[$this->status] ?? '未知状态';
    }

    /**
     * 获取支付状态文本
     */
    private function getPaymentStatusText(): string
    {
        $statuses = [
            'unpaid' => '未付款',
            'partial' => '部分付款',
            'paid' => '已付款',
            'refunded' => '已退款',
        ];
        
        return $statuses[$this->payment_status] ?? '未知状态';
    }

    /**
     * 检查是否有更正
     */
    private function hasCorrection(): bool
    {
        return $this->order && $this->order->corrections()->exists();
    }

    /**
     * 获取更正次数
     */
    private function getCorrectionCount(): int
    {
        return $this->order ? $this->order->corrections()->count() : 0;
    }

    /**
     * 获取最新更正时间
     */
    private function getLatestCorrectionTime(): ?string
    {
        $latestCorrection = $this->order?->corrections()->latest()->first();
        return $latestCorrection?->created_at;
    }

    /**
     * 获取最新更正原因
     */
    private function getLatestCorrectionReason(): ?string
    {
        $latestCorrection = $this->order?->corrections()->latest()->first();
        return $latestCorrection?->correction_reason;
    }

    /**
     * 获取距离到期天数
     */
    private function getDaysUntilDue(): ?int
    {
        if (!$this->due_date) {
            return null;
        }
        
        return Carbon::now()->diffInDays(Carbon::parse($this->due_date), false);
    }

    /**
     * 检查是否逾期
     */
    private function isOverdue(): bool
    {
        if (!$this->due_date || $this->status === 'paid') {
            return false;
        }
        
        return Carbon::now()->isAfter(Carbon::parse($this->due_date));
    }

    /**
     * 获取最新支付时间
     */
    private function getLatestPaymentTime(): ?string
    {
        $latestPayment = $this->paymentRecords()->where('status', 'success')->latest()->first();
        return $latestPayment?->paid_at;
    }

    /**
     * 获取推荐支付方式
     */
    private function getPreferredPaymentMethod(): string
    {
        // 根据金额和用户历史偏好推荐支付方式
        if ($this->final_amount > 500) {
            return 'bank_transfer'; // 大额推荐银行转账
        }
        
        return 'wechat'; // 默认推荐微信支付
    }

    /**
     * 获取可用支付方式
     */
    private function getAvailablePaymentMethods(): array
    {
        return [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            'bank_transfer' => '银行转账',
        ];
    }

    /**
     * 获取最后使用的支付方式
     */
    private function getLastPaymentMethod(): ?string
    {
        $lastPayment = $this->paymentRecords()->latest()->first();
        return $lastPayment?->payment_method;
    }

    /**
     * 获取显示标签
     */
    private function getDisplayTags(): array
    {
        $tags = [];
        
        if ($this->hasCorrection()) {
            $tags[] = [
                'text' => '已更正',
                'type' => 'warning',
                'color' => '#ff9500'
            ];
        }
        
        if ($this->isOverdue()) {
            $tags[] = [
                'text' => '已逾期',
                'type' => 'danger',
                'color' => '#ff3b30'
            ];
        } elseif ($this->getDaysUntilDue() !== null && $this->getDaysUntilDue() <= 3) {
            $tags[] = [
                'text' => '即将到期',
                'type' => 'warning',
                'color' => '#ff9500'
            ];
        }
        
        if ($this->payment_status === 'partial') {
            $tags[] = [
                'text' => '部分付款',
                'type' => 'info',
                'color' => '#007aff'
            ];
        }
        
        return $tags;
    }

    /**
     * 检查是否可以支付
     */
    private function canPay(): bool
    {
        return $this->status === 'pending' && 
               in_array($this->payment_status, ['unpaid', 'partial']) &&
               $this->final_amount > $this->paid_amount;
    }
}
