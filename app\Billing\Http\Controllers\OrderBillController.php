<?php

namespace App\Billing\Http\Controllers;

use App\Billing\Services\BillingService;
use App\Billing\Http\Resources\BillResource;
use App\Http\Controllers\Controller;
use App\Order\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class OrderBillController extends Controller
{
    protected BillingService $billingService;

    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * 从订单创建账单
     */
    public function createFromOrder(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'adjustment_amount' => 'nullable|numeric',
            'adjustment_reason' => 'nullable|string|max:500',
            'due_date' => 'nullable|date|after:today',
            'created_by' => 'nullable|integer|exists:employees,id',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
        ]);

        try {
            $options = [
                'adjustment_amount' => $request->adjustment_amount ?? 0,
                'adjustment_reason' => $request->adjustment_reason,
                'due_date' => $request->due_date ? \Carbon\Carbon::parse($request->due_date) : now()->addDays(7),
                'created_by' => $request->created_by,
                'notes' => $request->notes ?? '',
                'metadata' => $request->metadata ?? [],
            ];

            $bill = $this->billingService->createBillFromOrder($order, $options);

            return response()->json([
                'success' => true,
                'message' => '账单创建成功',
                'data' => new BillResource($bill)
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '账单创建失败: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * 获取订单相关的账单
     */
    public function getOrderBills(Order $order): JsonResponse
    {
        try {
            $bills = $order->bills()->with(['items', 'paymentRecords', 'adjustments'])->get();

            return response()->json([
                'success' => true,
                'data' => BillResource::collection($bills)
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取订单账单失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 