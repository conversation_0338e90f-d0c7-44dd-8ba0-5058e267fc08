{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__CDD03FB", "name": "客户管理", "version": {"name": "1.0.0", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#FFFFFF"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"icons": {"android": {"hdpi": "C:/Users/<USER>/Desktop/CRM.png"}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#FFFFFF", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#8a8a8a", "selectedColor": "#1976D2", "borderStyle": "rgba(0,0,0,0.4)", "backgroundColor": "#FFFFFF", "list": [{"pagePath": "pages/index/index", "text": "工作台", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-active.png"}, {"pagePath": "pages/analytics/analytics", "text": "数据分析", "iconPath": "static/tabbar/analytics.png", "selectedIconPath": "static/tabbar/analytics-active.png"}, {"pagePath": "pages/clients/clients", "text": "客户管理", "iconPath": "static/tabbar/clients.png", "selectedIconPath": "static/tabbar/clients-active.png"}, {"pagePath": "pages/orders/orders", "text": "订单管理", "iconPath": "static/tabbar/orders.png", "selectedIconPath": "static/tabbar/orders-active.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "static/tabbar/profile.png", "selectedIconPath": "static/tabbar/profile-active.png"}], "height": "50px"}, "launch_path": "__uniappview.html"}}