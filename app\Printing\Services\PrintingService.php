<?php

namespace App\Printing\Services;

use App\Printing\Contracts\PrintDriverInterface;
use App\Printing\Models\PrintRecord;
use Illuminate\Support\Facades\Log;

class PrintingService
{
    protected PrintDriverInterface $driver;
    protected array $drivers = [];

    /**
     * 模板打印设置配置
     */
    protected const TEMPLATE_CONFIGS = [
        'delivery' => [
            'paper_width' => 170, // 170mm宽度
            'paper_height' => 140, // 140mm高度
            'orientation' => 'portrait',
            'margin_top' => 5,
            'margin_left' => 0,
            'margin_right' => 0,
            'margin_bottom' => 5,
            'font_size' => 12,
            'font_name' => '宋体'
        ],
        'receipt' => [
            'paper_width' => 80, // 80mm热敏纸
            'orientation' => 'portrait',
            'margin_top' => 5,
            'margin_left' => 5,
            'margin_right' => 5,
            'margin_bottom' => 5,
            'font_size' => 11,
            'font_name' => '宋体'
        ],
        'whole_order_receipt' => [
            'paper_width' => 80, // 80mm热敏纸
            'orientation' => 'portrait',
            'margin_top' => 5,
            'margin_left' => 5,
            'margin_right' => 5,
            'margin_bottom' => 5,
            'font_size' => 11,
            'font_name' => '宋体'
        ],
        'normal' => [
            'paper_size' => 'A4',
            'orientation' => 'portrait',
            'margin_top' => 15,
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_bottom' => 15,
            'font_size' => 12,
            'font_name' => '宋体'
        ]
    ];

    public function __construct(PrintDriverInterface $driver)
    {
        $this->driver = $driver;
    }

    /**
     * 设置打印驱动
     */
    public function setDriver(string $driverName): void
    {
        // 根据驱动名称创建对应的驱动实例
        switch ($driverName) {
            case 'lodop':
            case 'clodop':
                $this->driver = app(\App\Printing\Services\Drivers\CLodopDriver::class);
                $this->driver->initialize(config('printing.drivers.lodop', []));
                break;
            case 'browser':
            default:
                $this->driver = app(\App\Printing\Services\Drivers\BrowserDriver::class);
                $this->driver->initialize(config('printing.drivers.browser', []));
                break;
        }
    }

    /**
     * 打印文本
     */
    public function printText(string $content, array $options = []): bool
    {
        return $this->driver->printText($content, $options);
    }

    /**
     * 打印HTML
     */
    public function printHtml(string $html, array $options = []): bool
    {
        return $this->driver->printHtml($html, $options);
    }

    /**
     * 预览打印内容
     */
    public function preview(string $content, array $options = []): string
    {
        return $this->driver->preview($content, $options);
    }

    /**
     * 生成打印脚本
     */
    public function generatePrintScript(string $content, array $options = []): string
    {
        return $this->driver->generatePrintScript($content, $options);
    }

    /**
     * 生成小票打印脚本
     */
    public function generateReceiptScript(string $content, array $options = []): string
    {
        return $this->driver->generateReceiptScript($content, $options);
    }

    /**
     * 打印订单
     */
    public function printOrder($order, array $options = []): array
    {
        try {
            // 创建打印记录
            $printRecord = PrintRecord::createRecord($order, array_merge($options, [
                'print_type' => $options['type'] ?? 'normal', // 确保print_type正确设置
                'print_content' => null, // 会在生成HTML后更新
                'driver' => $this->getCurrentDriverName()
            ]));

            // 浏览器打印：点击即完成，CLodop：标记为打印中
            $driverName = $this->getCurrentDriverName();
            if ($driverName === 'browser') {
                // 浏览器打印：直接标记为已完成
                $printRecord->markAsCompleted();
            } else {
                // CLodop打印：标记为打印中，等待回调
                $printRecord->markAsPrinting();
            }

            // 根据类型选择不同的模板
            $type = $options['type'] ?? 'normal';

            // 🔥 添加日志以便调试模板选择
            Log::info('Selecting print template', [
                'order_id' => $order->id,
                'print_type' => $type,
                'is_whole_order_receipt' => $type === 'whole_order_receipt',
                'print_mode' => $options['print_mode'] ?? ''
            ]);

            if ($type === 'delivery') {
                $html = $this->generateDeliveryTemplate($order, array_merge($options, ['printRecord' => $printRecord]));
            } elseif ($type === 'whole_order_receipt') {
                // 🔥 新增：整单小票打印
                Log::info('Generating whole order receipt HTML', ['order_id' => $order->id]);

                if (env('WHOLE_ORDER_PRINT_LOG', false)) {
                    Log::channel('whole_order_print')->debug('开始生成整单小票HTML', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'items_count' => $order->items ? $order->items->count() : 0,
                        'total_amount' => $order->total ?? 0
                    ]);
                }

                $html = $this->generateWholeOrderReceiptHtml($order, $options);

                if (env('WHOLE_ORDER_PRINT_LOG', false)) {
                    Log::channel('whole_order_print')->debug('整单小票HTML生成完成', [
                        'order_id' => $order->id,
                        'html_length' => strlen($html),
                        'contains_whole_order_mark' => strpos($html, '整单小票') !== false
                    ]);
                }
            } else {
                $html = $this->generateOrderHtml($order);
            }

            // 更新打印记录的内容
            $printRecord->update(['print_content' => $html]);

            // 检查是否使用浏览器驱动
            $driverName = $this->getCurrentDriverName();
            $isBrowserDriver = $driverName === 'browser';
            
            // 获取模板默认配置
            $templateConfig = self::TEMPLATE_CONFIGS[$type] ?? self::TEMPLATE_CONFIGS['normal'];

            // 从HTML模板中提取打印设置
            $templateSettings = $this->extractPrintSettings($html);

            // 初始化打印选项（从传入的options开始）
            $printOptions = $options;

            // 🔥 整单打印：使用默认打印机，不依赖仓库绑定
            if ($type === 'whole_order_receipt') {
                $printOptions = $this->getWholeOrderPrintOptions($printOptions, $options);

                // 详细的整单打印日志
                if (env('WHOLE_ORDER_PRINT_LOG', false)) {
                    Log::channel('whole_order_print')->info('整单打印开始', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'printer_name' => $printOptions['printer_name'] ?? 'default',
                        'driver' => $this->getCurrentDriverName(),
                        'print_options' => $printOptions,
                        'timestamp' => now()->toDateTimeString()
                    ]);
                }

                Log::info('Using default printer for whole order receipt', [
                    'order_id' => $order->id,
                    'printer_name' => $printOptions['printer_name'] ?? 'default',
                    'driver' => $this->getCurrentDriverName()
                ]);
            }

            // 合并设置：模板配置 < HTML设置 < 传入参数
            $printOptions = array_merge($templateConfig, $templateSettings, $printOptions);

            if ($type === 'receipt' || $type === 'whole_order_receipt') {
                $script = $this->driver->generateReceiptScript($html, $printOptions);
            } else {
                $script = $this->driver->generatePrintScript($html, $printOptions);
            }

            // 注意：这里不能直接标记为已完成，需要等待前端回调
            // $printRecord->markAsCompleted();

            return [
                'success' => true,
                'print_record_id' => $printRecord->id,
                'html' => $html,
                'script' => $script,
                'preview_url' => $this->generatePreviewUrl($html),
                'template_type' => $type,
                'template_config' => $templateConfig,
                'html_settings' => $templateSettings,
                'final_settings' => $printOptions
            ];
        } catch (\Exception $e) {
            // 如果有打印记录，标记为失败
            if (isset($printRecord)) {
                $printRecord->markAsFailed($e->getMessage());
            }

            Log::error('Print order failed', [
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 从HTML模板中提取打印设置
     */
    protected function extractPrintSettings(string $html): array
    {
        $settings = [];
        
        // 使用正则表达式提取data-*属性
        if (preg_match('/data-paper-size=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['paper_size'] = $matches[1];
        }
        
        if (preg_match('/data-paper-width=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['paper_width'] = (int)$matches[1];
        }
        
        if (preg_match('/data-orientation=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['orientation'] = $matches[1];
        }
        
        if (preg_match('/data-margin-top=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_top'] = (int)$matches[1];
        }
        
        if (preg_match('/data-margin-left=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_left'] = (int)$matches[1];
        }
        
        if (preg_match('/data-margin-right=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_right'] = (int)$matches[1];
        }
        
        if (preg_match('/data-margin-bottom=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_bottom'] = (int)$matches[1];
        }
        
        // 从CSS @page规则中提取设置（备用方案）
        if (preg_match('/@page\s*\{[^}]*size:\s*([^;]+);/', $html, $matches)) {
            if (empty($settings['paper_size']) && empty($settings['paper_width'])) {
                $pageSize = trim($matches[1]);
                if (preg_match('/(\d+)mm/', $pageSize, $widthMatch)) {
                    $settings['paper_width'] = (int)$widthMatch[1];
                } else {
                    $settings['paper_size'] = $pageSize;
                }
            }
        }
        
        return $settings;
    }

    /**
     * 生成订单HTML
     */
    protected function generateOrderHtml($order): string
    {
        $html = "
<style>
    /* 小票打印设置 */
    @page {
        size: 80mm auto;
        margin: 5mm;
    }
    
    .receipt {
        width: 70mm;
        font-family: '宋体', SimSun, serif;
        font-size: 11px;
        line-height: 1.3;
        margin: 0;
        padding: 5mm;
        box-sizing: border-box;
        /* 小票专用设置 */
        --paper-width: 80;
        --orientation: portrait;
        --margin-top: 5mm;
        --margin-bottom: 5mm;
        --margin-left: 5mm;
        --margin-right: 5mm;
    }
    .header {
        text-align: center;
        margin-bottom: 10px;
    }
    .store-name {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 3px;
    }
    .store-info {
        font-size: 12px;
        margin-bottom: 5px;
    }
    .info {
        margin-bottom: 8px;
    }
    .info-item {
        margin: 1px 0;
        font-size: 10px;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 5px 0;
        font-size: 10px;
    }
    th, td {
        padding: 1px 2px;
        text-align: left;
        border-bottom: 1px dashed #ccc;
    }
    th {
        font-weight: bold;
        font-size: 9px;
    }
    .total {
        font-weight: bold;
        font-size: 12px;
        text-align: right;
        margin-top: 8px;
    }
    .footer {
        text-align: center;
        margin-top: 10px;
        font-size: 9px;
        border-top: 1px dashed #ccc;
        padding-top: 3px;
    }
    .divider {
        border-top: 1px dashed #ccc;
        margin: 8px 0;
    }

    /* 🔥 新增：备注样式 */
    .notes-section, .delivery-notes-section {
        margin: 5px 0;
        padding: 3px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 2px;
    }
    .notes-title {
        font-size: 9px;
        font-weight: bold;
        margin-bottom: 2px;
        color: #666;
    }
    .notes-content {
        font-size: 10px;
        line-height: 1.4;
        word-wrap: break-word;
        white-space: pre-wrap;
    }

    @media print {
        .receipt {
            padding: 0;
            margin: 0;
        }
        .notes-section, .delivery-notes-section {
            background-color: transparent;
            border: 1px solid #000;
        }
    }
</style>

<div class='receipt' 
     data-paper-width='80' 
     data-orientation='portrait' 
     data-margin-top='5' 
     data-margin-left='5' 
     data-margin-right='5' 
     data-margin-bottom='5'>
    <div class='header'>
        <div class='store-name'>万家生鲜</div>
        <div class='store-info'>订单小票</div>
    </div>
    
    <div class='divider'></div>
    
    <div class='info'>
        <div class='info-item'>订单号: {$order->order_no}</div>
        <div class='info-item'>下单时间: " . date('Y-m-d H:i:s', strtotime($order->created_at)) . "</div>
        <div class='info-item'>收货人: {$order->contact_name}</div>
        <div class='info-item'>联系电话: {$order->contact_phone}</div>
        <div class='info-item'>收货地址: {$order->shipping_address}</div>
    </div>
    
    <div class='divider'></div>
    
    <table>
        <thead>
            <tr>
                <th>商品</th>
                <th>单价</th>
                <th>数量</th>
                <th>小计</th>
            </tr>
        </thead>
        <tbody>";

        $totalAmount = 0;
        if ($order->items && count($order->items) > 0) {
            foreach ($order->items as $item) {
                $productName = $item->product_name ?? ($item->product->name ?? '商品');
                $price = floatval($item->price ?? 0);
                $quantity = floatval($item->quantity ?? 1);
                $subtotal = $price * $quantity;
                $totalAmount += $subtotal;
                
                // 获取单位信息
                $unit = '';
                if (isset($item->unit) && $item->unit) {
                    $unit = $item->unit->symbol ?? $item->unit->name ?? '';
                } elseif (isset($item->unit_symbol)) {
                    $unit = $item->unit_symbol;
                } elseif (isset($item->unit_name)) {
                    $unit = $item->unit_name;
                }
                
                $displayQuantity = $unit ? "{$quantity}{$unit}" : $quantity;
                
                $html .= "
            <tr>
                <td>" . htmlspecialchars($productName) . "</td>
                <td>¥" . number_format($price, 2) . "</td>
                <td>{$displayQuantity}</td>
                <td>¥" . number_format($subtotal, 2) . "</td>
            </tr>";
            }
        } else {
            $totalAmount = $order->total ?? 0;
        }

        // 使用计算的总金额或订单总金额，确保转换为浮点数
        $finalTotal = floatval($totalAmount > 0 ? $totalAmount : ($order->total ?? 0));
        $paymentMethod = $this->getPaymentMethodName($order->payment_method ?? '');

        $html .= "
        </tbody>
    </table>
    
    <div class='divider'></div>
    
    <div class='total'>
        <div>商品总额: ¥" . number_format($finalTotal, 2) . "</div>
        <div>配送费: ¥0.00</div>
        <div style='font-size: 16px; margin-top: 5px;'>实付金额: ¥" . number_format($finalTotal, 2) . "</div>
    </div>
    
    <div class='divider'></div>
    
    <div class='info'>
        <div class='info-item'>支付方式: {$paymentMethod}</div>
        <div class='info-item'>订单状态: " . $this->getOrderStatusName($order->status ?? '') . "</div>
    </div>";

        // 🔥 新增：添加订单备注显示
        if (!empty($order->notes)) {
            $html .= "
    <div class='divider'></div>

    <div class='notes-section'>
        <div class='notes-title'>订单备注:</div>
        <div class='notes-content'>" . htmlspecialchars($order->notes) . "</div>
    </div>";
        }



        $html .= "
    <div class='footer'>
        <div>感谢您的惠顾！</div>
        <div>客服电话: 400-123-4567</div>
        <div>打印时间: " . date('Y-m-d H:i:s') . "</div>
    </div>
</div>";

        return $html;
    }

    /**
     * 生成配送单HTML（增强版 - 支持分页、优惠明细、实发数量）
     */
    protected function generateDeliveryTemplate($order, $options = [])
    {
        $isPrintPreview = $options['preview'] ?? false;
        $companyName = config('app.company_name', '天心食品');
        
        try {
            $items = $order->items ?? collect([]);
            $itemsPerPage = 6; // 每页显示6行商品
            $totalPages = max(1, ceil($items->count() / $itemsPerPage));
            
            // 预先计算一次总计，避免重复计算
            $orderTotals = [
                'subtotal' => $order->subtotal ?? 0,
                'discount' => $order->discount ?? 0,
                'payment_discount' => $order->payment_discount ?? 0,
                'total' => $order->total ?? 0,
                'original_total' => $order->original_total ?? $order->total ?? 0,
            ];
            
            $allPagesHtml = '';
            
            for ($page = 1; $page <= $totalPages; $page++) {
                $startIndex = ($page - 1) * $itemsPerPage;
                $pageItems = $items->slice($startIndex, $itemsPerPage);
                $isLastPage = ($page === $totalPages);
                
                $allPagesHtml .= $this->generateDeliveryPageTemplate(
                    $order,
                    $pageItems,
                    $page,
                    $totalPages,
                    $isLastPage,
                    $orderTotals,
                    $companyName,
                    $options
                );
                
                // 页面之间添加分页符（除了最后一页）
                if ($page < $totalPages) {
                    $allPagesHtml .= '<div style="page-break-after: always;"></div>';
                }
            }
            
            return $allPagesHtml;
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('配送单模板生成失败', [
                'order_id' => $order->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return '<div style="color: red; padding: 20px;">配送单生成失败: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }

    private function generateDeliveryPageTemplate($order, $pageItems, $currentPage, $totalPages, $isLastPage, $orderTotals, $companyName, $options = [])
    {


        $html = "
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: \"Microsoft YaHei\", \"SimSun\", \"宋体\", Arial, sans-serif;
                margin: 0;
                padding: 0;
            }
            .delivery-container {
                width: 170mm;
                height: 140mm;
                margin: 0 auto;
                padding: 5mm 0; /* 无左右边距，内容占满170mm */
                font-size: 14px;
                line-height: 1.4;
                box-sizing: border-box;
                page-break-inside: avoid;
                overflow: hidden;
                border: 1px solid #ddd; /* 预览时显示边框 */
            }

            @media print {
                * { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
                @page {
                    margin: 0;
                    size: 170mm 140mm;
                }
                body { margin: 0; padding: 0; }
                .delivery-container {
                    border: none; /* 打印时隐藏边框 */
                    margin: 0;
                    width: 170mm;
                    height: 140mm;
                    padding: 5mm 0; /* 无左右边距，内容占满170mm */
                }
                .no-print { display: none; }
            }
            @media screen {
                body { background: #f5f5f5; padding: 20px; }
                .print-button {
                    text-align: center;
                    margin-bottom: 20px;
                }
                .print-btn {
                    background: #007cba;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                }
                .print-btn:hover { background: #005a87; }
            }
        </style>
        <div class='print-button no-print'>
            <button class='print-btn' onclick='window.print()'>打印配送单</button>
        </div>
        <div class='delivery-container'>
            <!-- 页眉 -->
            <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; border-bottom: 2px solid #000; padding-bottom: 4px;'>
                <div style='flex: 1; font-size: 12px;'>
                    <div><strong>订单号:</strong> " . htmlspecialchars($order->order_no ?? '') . "</div>
                </div>
                <div style='text-align: center; flex: 2;'>
                    <div style='font-size: 20px; font-weight: bold; margin-bottom: 2px;'>成都天心食品有限公司</div>
                    <div style='font-size: 18px; font-weight: bold;'>发货单</div>
                </div>
                <div style='flex: 1; text-align: right;'>
                    " . $this->generateBillPaymentQrCodeWithConfig($order) . "
                </div>
            </div>

            <!-- 订单信息 -->
            <div style='margin-bottom: 3px; font-size: 13px;'>
                <!-- 第一行：商家名称 vs 系统编号+打印编号 -->
                <div style='display: flex; justify-content: space-between; align-items: center; line-height: 1.3; margin-bottom: 1px;'>
                    <div><strong>商家名称:</strong> " . htmlspecialchars(mb_substr($order->user->merchant_name ?? $order->user->name ?? '', 0, 15)) . "（" . htmlspecialchars(mb_substr($order->contact_name ?? '', 0, 8)) . "）</div>
                    <div style='display: flex; gap: 15px;'>
                        <div><strong>系统编号:</strong> ___________</div>
                        <div><strong>打印编号:</strong> " . htmlspecialchars($order->id) . "</div>
                    </div>
                </div>
                <!-- 第二行：联系电话 vs 付款方式 -->
                <div style='display: flex; justify-content: space-between; align-items: center; line-height: 1.3; margin-bottom: 1px;'>
                    <div><strong>联系电话:</strong> " . htmlspecialchars(mb_substr($order->contact_phone ?? '', 0, 15)) . "</div>
                    <div><strong>付款方式:</strong> " . htmlspecialchars(mb_substr($this->getPaymentMethodName($order->payment_method ?? ''), 0, 10)) . "</div>
                </div>
                <!-- 第三行：收货地址 vs 制单日期 -->
                <div style='display: flex; justify-content: space-between; align-items: center; line-height: 1.3; margin-bottom: 1px;'>
                    <div><strong>收货地址:</strong> " . htmlspecialchars(mb_substr($order->shipping_address ?? '', 0, 35)) . "</div>
                    <div><strong>制单日期:</strong> " . date('Y年m月d日') . "</div>
                </div>
            </div>


            
            <!-- 商品表格 - 占满170mm纸张宽度 -->
            <table style='width: 100%; border-collapse: collapse; margin-bottom: 3px; font-size: 12px;'>
                <thead>
                    <tr style='background-color: #f0f0f0;'>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 32%;'>商品名称</th>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 9%;'>数量</th>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 8%;'>单位</th>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 12%;'>单价</th>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 12%;'>订货小计</th>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 10%;'>实发数量</th>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 12%;'>实发小计</th>
                        <th style='border: 1px solid #000; padding: 2px; text-align: center; width: 5%;'>备注</th>
                    </tr>
                </thead>
                <tbody>";

        // 商品行
        $pageSubtotal = 0;
        foreach ($pageItems as $item) {
            // 商品名称处理：170mm宽度适配
            $productName = $item->product_name ?? ($item->product->name ?? '商品');
            $productName = mb_substr($productName, 0, 16) . (mb_strlen($productName) > 16 ? '...' : '');
            
            // 获取单位信息 - 优先使用名称
            $unit = '';
            if (isset($item->unit) && $item->unit) {
                $unit = $item->unit->name ?? $item->unit->symbol ?? '';
            } elseif (isset($item->unit_name)) {
                $unit = $item->unit_name;
            } elseif (isset($item->unit_symbol)) {
                $unit = $item->unit_symbol;
            }
            
            // 直接使用订单项的数据，确保转换为浮点数
            $quantity = floatval($item->quantity ?? 0);
            $price = floatval($item->price ?? 0);
            $itemTotal = floatval($item->total ?? 0); // 直接使用订单项的总金额

            $pageSubtotal += $itemTotal;

            $html .= "
                <tr>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>" . htmlspecialchars($productName) . "</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px; text-align: center;'>" . number_format($quantity, 2) . "</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px; text-align: center;'>" . htmlspecialchars($unit) . "</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px; text-align: right;'>¥" . number_format($price, 2) . "</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px; text-align: right;'>¥" . number_format($itemTotal, 2) . "</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px; text-align: center;'></td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px; text-align: center;'></td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'></td>
                </tr>";
        }
        
        // 填充空行到6行
        $currentRows = $pageItems->count();
        for ($i = $currentRows; $i < 6; $i++) {
            $html .= "
                <tr>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 3px; height: 18px;'>&nbsp;</td>
                </tr>";
        }
        
        // 汇总行
        $html .= "
                <tr>
                    <td style='border: 1px solid #000; padding: 6px; text-align: left; width: 15%;'><strong>优惠信息</strong></td>
                    <td style='border: 1px solid #000; padding: 6px; text-align: left; border-left: 2px solid #000;' colspan='7'>" . htmlspecialchars($this->getDiscountInfo($order)) . "</td>
                </tr>
                <tr>
                    <td colspan='4' style='border: 1px solid #000; padding: 2px; text-align: right;'><strong>订货合计:</strong></td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'><strong>¥" . number_format(floatval($orderTotals['total']), 2) . "</strong></td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'><strong>实付</strong></td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'><strong>运费: ¥0.00</strong></td>
                </tr>
            </tbody>
        </table>";

        // 🔥 优化：订单备注显示
        if (!empty($order->notes)) {
            $html .= "
        <!-- 订单备注 -->
        <div style='margin: 3px 0; padding: 2px; border: 1px solid #ddd; background-color: #f9f9f9; font-size: 12px;'>
            <strong>订单备注:</strong> " . htmlspecialchars($order->notes) . "
        </div>";
        }



        $html .= "
        <!-- 订单信息 -->
        <div style='margin-top: 4px; font-size: 11px;'>
            <div style='display: flex; justify-content: space-between;'>
                <div>订货电话: " . htmlspecialchars($order->contact_phone ?? '028-88703177') . "</div>
                <div>夜间值班电话: 18382013323</div>
                <div>售后电话: 18000540440</div>
                    </div>
            " . $this->generateUserDebtInfo($order) . "
            <div style='margin-top: 2px; margin-bottom: 3px; font-size: 9px; word-wrap: break-word; white-space: normal;'>温馨提示: ☆请在验收明细确认无误后，并付款。如有差异或质量问题请通知配送人员当面核准。☆特殊情况不能现场付款的，请确认签字，并于3日内付清货款。☆若超期付款，公司将按照欠款总额的1%收取滞纳金。</div>
                    </div>

        <!-- 签名区域 -->
        <div style='margin-top: 4px; font-size: 12px; display: flex; justify-content: space-between;'>
            <div>制单员: " . htmlspecialchars($this->getPrintedByName($options)) . "</div>
            <div>配送员: " . htmlspecialchars($this->getDeliveryPersonInfo($order)) . "</div>
            <div>业务经理: " . htmlspecialchars($this->getCrmManagerInfo($order)) . "</div>
            <div>客户签字: _____________</div>
                </div>
        
        <!-- 页码 -->
        <div style='margin-top: 4px; font-size: 10px; text-align: center;'>
            第{$currentPage}页/共{$totalPages}页
            </div>
        

        </div>";
        
        return $html;
    }

    /**
     * 获取支付方式名称
     */
    protected function getPaymentMethodName(string $method): string
    {
        $methods = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            'bank' => '银行转账',
            'bank_transfer' => '银行转账',
            'cod' => '货到付款',
            'pos' => 'POS机刷卡',
            'credit' => '信用支付'
        ];

        return $methods[$method] ?? $method;
    }

    /**
     * 获取订单状态名称
     */
    protected function getOrderStatusName(string $status): string
    {
        $statuses = [
            'pending' => '待付款',
            'paid' => '已付款',
            'shipped' => '已发货',
            'delivered' => '已送达',
            'cancelled' => '已取消'
        ];

        return $statuses[$status] ?? $status;
    }

    /**
     * 生成预览URL
     */
    protected function generatePreviewUrl(string $html): string
    {
        // 根据当前驱动类型生成不同的预览
        $driverName = $this->getCurrentDriverName();

        if ($driverName === 'clodop' || $driverName === 'lodop') {
            // 使用CLodop预览，确保预览和打印一致
            $previewHtml = $this->driver->generatePreviewScript($html, [
                'paper_width' => 1700,   // 170mm = 1700 (单位：0.1mm)
                'paper_height' => 1400,  // 140mm = 1400 (单位：0.1mm)
                'orientation' => 1,      // 1=纵向
                'margin_top' => 50,      // 5mm = 50 (单位：0.1mm)
                'margin_left' => 200,    // 20mm = 200 (单位：0.1mm)
                'margin_right' => 200,   // 20mm = 200 (单位：0.1mm)
                'margin_bottom' => 50,   // 5mm = 50 (单位：0.1mm)
            ]);
        } else {
            // 浏览器预览
            $previewHtml = $html;
        }

        // 保存预览文件
        $filename = 'print_preview_' . time() . '_' . uniqid() . '.html';
        $path = storage_path('app/temp/' . $filename);

        // 确保目录存在
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        file_put_contents($path, $previewHtml);

        return url('api/print/preview/' . $filename);
    }

    /**
     * 获取打印机列表
     */
    public function getPrinters(): array
    {
        return $this->driver->getPrinters();
    }

    /**
     * 设置默认打印机
     */
    public function setDefaultPrinter(string $printerName): bool
    {
        return $this->driver->setDefaultPrinter($printerName);
    }

    /**
     * 检查打印机状态
     */
    public function getPrinterStatus(string $printerName): array
    {
        return $this->driver->getPrinterStatus($printerName);
    }

    /**
     * 获取当前驱动名称
     */
    protected function getCurrentDriverName(): string
    {
        $driverClass = get_class($this->driver);
        
        if (str_contains($driverClass, 'CLodopDriver')) {
            return PrintRecord::DRIVER_CLODOP;
        } elseif (str_contains($driverClass, 'BrowserDriver')) {
            return PrintRecord::DRIVER_BROWSER;
        }
        
        return PrintRecord::DRIVER_CLODOP; // 默认
    }

    /**
     * 打印完成回调
     */
    public function markPrintCompleted(int $printRecordId): bool
    {
        try {
            $printRecord = PrintRecord::findOrFail($printRecordId);
            return $printRecord->markAsCompleted();
        } catch (\Exception $e) {
            Log::error('Mark print completed failed', [
                'print_record_id' => $printRecordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 打印失败回调
     */
    public function markPrintFailed(int $printRecordId, string $errorMessage = ''): bool
    {
        try {
            $printRecord = PrintRecord::findOrFail($printRecordId);
            return $printRecord->markAsFailed($errorMessage);
        } catch (\Exception $e) {
            Log::error('Mark print failed failed', [
                'print_record_id' => $printRecordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取打印历史
     */
    public function getPrintHistory($printable, string $printType = null): \Illuminate\Database\Eloquent\Collection
    {
        return PrintRecord::getPrintHistory($printable, $printType);
    }

    /**
     * 检查是否已打印过
     */
    public function isPrinted($printable, string $printType = null): bool
    {
        return PrintRecord::isPrinted($printable, $printType);
    }

    /**
     * 获取打印统计
     */
    public function getPrintStats($printable, string $printType = null): array
    {
        $totalCount = PrintRecord::countPrints($printable, $printType);
        $lastRecord = PrintRecord::getLastPrintRecord($printable, $printType);
        
        return [
            'total_prints' => $totalCount,
            'last_printed_at' => $lastRecord ? $lastRecord->printed_at : null,
            'last_printed_by' => $lastRecord && $lastRecord->printedBy ? $lastRecord->printedBy->name : null,
            'is_printed' => $totalCount > 0
        ];
    }

    /**
     * 获取待打印列表
     */
    public function getPendingPrints(): \Illuminate\Database\Eloquent\Collection
    {
        return PrintRecord::pending()
            ->with(['printable', 'printedBy'])
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * 重新打印
     */
    public function reprintOrder($order, array $options = []): array
    {
        // 检查是否允许重复打印
        $allowReprint = $options['allow_reprint'] ?? true;
        
        if (!$allowReprint && $this->isPrinted($order, $options['type'] ?? null)) {
            return [
                'success' => false,
                'error' => '该订单已经打印过，不允许重复打印'
            ];
        }

        // 添加重印标识
        $options['is_reprint'] = true;
        
        return $this->printOrder($order, $options);
    }

    /**
     * 获取订单优惠信息明细
     */
    protected function getDiscountInfo($order): string
    {
        $discountInfo = [];
        
        // 获取订单优惠金额
        $orderDiscount = $order->discount ?? 0;
        if ($orderDiscount > 0) {
            $discountInfo[] = "订单优惠：¥" . number_format($orderDiscount, 2);
        }
        
        // 获取支付优惠金额
        $paymentDiscount = $order->payment_discount ?? 0;
        if ($paymentDiscount > 0) {
            $discountInfo[] = "支付优惠：¥" . number_format($paymentDiscount, 2);
        }
        
        // 获取优惠券信息
        $couponDiscount = $order->coupon_discount ?? 0;
        if ($couponDiscount > 0) {
            $discountInfo[] = "优惠券：¥" . number_format($couponDiscount, 2);
        }
        
        // 获取满减活动信息
        $promotionDiscount = $order->promotion_discount ?? 0;
        if ($promotionDiscount > 0) {
            $discountInfo[] = "满减活动：¥" . number_format($promotionDiscount, 2);
        }
        
        // 会员优惠
        $memberDiscount = $order->member_discount ?? 0;
        if ($memberDiscount > 0) {
            $discountInfo[] = "会员优惠：¥" . number_format($memberDiscount, 2);
        }
        
        // 区域优惠
        $regionDiscount = $order->region_discount ?? 0;
        if ($regionDiscount > 0) {
            $discountInfo[] = "区域优惠：¥" . number_format($regionDiscount, 2);
        }
        
        // 首单优惠
        $firstOrderDiscount = $order->first_order_discount ?? 0;
        if ($firstOrderDiscount > 0) {
            $discountInfo[] = "首单优惠：¥" . number_format($firstOrderDiscount, 2);
        }
        
        // 季节性优惠
        $seasonalDiscount = $order->seasonal_discount ?? 0;
        if ($seasonalDiscount > 0) {
            $discountInfo[] = "季节优惠：¥" . number_format($seasonalDiscount, 2);
        }
        
        // 特殊客户优惠
        $vipDiscount = $order->vip_discount ?? 0;
        if ($vipDiscount > 0) {
            $discountInfo[] = "特殊客户优惠：¥" . number_format($vipDiscount, 2);
        }
        
        // 如果有优惠备注，也显示出来
        $discountRemark = $order->discount_remark ?? '';
        if (!empty($discountRemark)) {
            $discountInfo[] = "备注：" . $discountRemark;
        }
        
        // 如果没有任何优惠信息，显示默认文本
        if (empty($discountInfo)) {
            return "无优惠";
        }
        
        return implode("，", $discountInfo);
    }

    /**
     * 获取配送员信息（姓名和电话）
     */
    protected function getDeliveryPersonInfo($order): string
    {
        try {
            // 优先从配送记录获取配送员
            if (isset($order->delivery) && $order->delivery && $order->delivery->deliverer && $order->delivery->deliverer->employee) {
                $employee = $order->delivery->deliverer->employee;
                $info = $employee->name ?? '';
                if (!empty($employee->phone)) {
                    $info .= "（" . $employee->phone . "）";
                }
                return $info;
            }

            // 其次使用用户的默认配送员
            if (isset($order->user) && $order->user && $order->user->default_employee_deliverer_id) {
                $employee = \App\Employee\Models\Employee::find($order->user->default_employee_deliverer_id);
                if ($employee) {
                    $info = $employee->name ?? '';
                    if (!empty($employee->phone)) {
                        $info .= "（" . $employee->phone . "）";
                    }
                    return $info;
                }
            }

            return "未指定";
        } catch (\Exception $e) {
            \Log::warning('获取配送员信息失败', [
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage()
            ]);
            return "未指定";
        }
    }
    
    /**
     * 获取CRM专员/业务经理信息
     */
    protected function getCrmManagerInfo($order): string
    {
        try {
            // 直接从用户的crm_agent_id获取CRM专员信息
            if (isset($order->user) && $order->user && $order->user->crm_agent_id) {
                $employee = \App\Employee\Models\Employee::find($order->user->crm_agent_id);
                if ($employee) {
                    $info = $employee->name ?? '';
                    if (!empty($employee->phone)) {
                        $info .= "（" . $employee->phone . "）";
                    }
                    return $info;
                }
            }

            return "未指定";
        } catch (\Exception $e) {
            \Log::warning('获取业务经理信息失败', [
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage()
            ]);
            return "未指定";
        }
    }

    /**
     * 获取制单员姓名（打印操作人）
     */
    protected function getPrintedByName($options): string
    {
        try {
            $printRecord = $options['printRecord'] ?? null;
            if (!$printRecord || !$printRecord->printed_by) {
                return '';
            }

            // 从员工表中获取打印操作人信息
            $employee = \App\Employee\Models\Employee::find($printRecord->printed_by);
            return $employee->name ?? '';
        } catch (\Exception $e) {
            \Log::warning('获取制单员信息失败', [
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * 🔥 新增：生成整单小票HTML（不分仓库）
     */
    protected function generateWholeOrderReceiptHtml($order, array $options = []): string
    {
        $storeName = config('app.name', '天心食品');
        $currentTime = now()->format('Y-m-d H:i:s');

        // 计算订单汇总信息
        $totalQuantity = 0;
        $totalAmount = 0;
        $itemsHtml = '';

        if ($order->items && count($order->items) > 0) {
            foreach ($order->items as $item) {
                $productName = $item->product_name ?? ($item->product->name ?? '商品');
                $price = (float)($item->price ?? 0);
                $quantity = (int)($item->quantity ?? 1);
                $subtotal = $price * $quantity;

                $totalQuantity += $quantity;
                $totalAmount += $subtotal;

                $itemsHtml .= "
                    <tr>
                        <td style='padding: 5px 0; border-bottom: 1px dashed #ccc;'>{$productName}</td>
                        <td style='padding: 5px 0; border-bottom: 1px dashed #ccc; text-align: center;'>{$quantity}</td>
                        <td style='padding: 5px 0; border-bottom: 1px dashed #ccc; text-align: right;'>¥" . number_format($price, 2) . "</td>
                        <td style='padding: 5px 0; border-bottom: 1px dashed #ccc; text-align: right;'>¥" . number_format($subtotal, 2) . "</td>
                    </tr>";
            }
        }

        // 获取客户信息
        $customerName = $this->getCustomerName($order);
        $customerPhone = $this->getCustomerPhone($order);

        // 🔥 新增：获取配送员信息
        $deliveryPersonInfo = $this->getDeliveryPersonInfo($order);

        // 订单备注
        $notesHtml = '';
        if (!empty($order->notes)) {
            $notesHtml = "
                <div style='margin-top: 15px; padding-top: 10px; border-top: 1px dashed #ccc;'>
                    <div style='font-weight: bold; margin-bottom: 5px;'>订单备注：</div>
                    <div style='color: #666;'>{$order->notes}</div>
                </div>";
        }

        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>整单小票 - {$order->order_no}</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                    margin: 0;
                    padding: 10px;
                    width: 80mm;
                    max-width: 80mm;
                }
                .header { text-align: center; margin-bottom: 15px; }
                .store-name { font-size: 16px; font-weight: bold; margin-bottom: 5px; }
                .receipt-type { font-size: 14px; margin-bottom: 10px; }
                .divider { border-top: 1px solid #000; margin: 10px 0; }
                .info-row { margin: 3px 0; }
                .items-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                .items-table th {
                    padding: 5px 0;
                    border-bottom: 1px solid #000;
                    font-weight: bold;
                    text-align: left;
                }
                .summary { margin-top: 10px; }
                .total-row { font-weight: bold; font-size: 13px; }
                .footer { text-align: center; margin-top: 15px; font-size: 11px; color: #666; }
            </style>
        </head>
        <body>
            <div class='header'>
                <div class='store-name'>{$storeName}</div>
                <div class='receipt-type'>整单小票</div>
            </div>

            <div class='divider'></div>

            <div class='info-row'><strong>订单号：</strong>{$order->order_no}</div>
            <div class='info-row'><strong>下单时间：</strong>" . ($order->created_at ? $order->created_at->format('Y-m-d H:i:s') : '') . "</div>";

        if ($customerName) {
            $html .= "<div class='info-row'><strong>下单人：</strong>{$customerName}</div>";
        }
        if ($customerPhone) {
            $html .= "<div class='info-row'><strong>联系电话：</strong>{$customerPhone}</div>";
        }

        // 🔥 新增：显示配送员信息
        if ($deliveryPersonInfo) {
            $html .= "<div class='info-row'><strong>配送员：</strong>{$deliveryPersonInfo}</div>";
        }

        $html .= "
            <div class='divider'></div>

            <table class='items-table'>
                <thead>
                    <tr>
                        <th>商品名称</th>
                        <th style='text-align: center; width: 15%;'>数量</th>
                        <th style='text-align: right; width: 20%;'>单价</th>
                        <th style='text-align: right; width: 20%;'>小计</th>
                    </tr>
                </thead>
                <tbody>
                    {$itemsHtml}
                </tbody>
            </table>

            <div class='divider'></div>

            <div class='summary'>
                <div class='info-row'><strong>商品总数：</strong>{$totalQuantity} 件</div>
                <div class='info-row'><strong>商品总额：</strong>¥" . number_format($totalAmount, 2) . "</div>
                <div class='info-row'><strong>配送费：</strong>¥0.00</div>
                <div class='info-row total-row'><strong>实付金额：</strong>¥" . number_format((float)($order->total ?? 0), 2) . "</div>
            </div>

            <div class='divider'></div>

            <div class='info-row'><strong>收货人：</strong>" . ($order->contact_name ?? '') . "</div>
            <div class='info-row'><strong>联系电话：</strong>" . ($order->contact_phone ?? '') . "</div>
            <div class='info-row'><strong>收货地址：</strong>" . ($order->shipping_address ?? '') . "</div>

            {$notesHtml}

            <div class='divider'></div>

            <div class='footer'>
                <div>整单小票 - 不分仓库</div>
                <div>感谢您的惠顾！</div>
                <div>打印时间：{$currentTime}</div>
            </div>
        </body>
        </html>";

        return $html;
    }

    /**
     * 获取客户名称
     */
    protected function getCustomerName($order): string
    {
        if (!empty($order->contact_name)) {
            return $order->contact_name;
        }

        if ($order->user && !empty($order->user->name)) {
            return $order->user->name;
        }

        return '';
    }

    /**
     * 获取客户电话
     */
    protected function getCustomerPhone($order): string
    {
        if (!empty($order->contact_phone)) {
            return $order->contact_phone;
        }

        if ($order->user && !empty($order->user->phone)) {
            return $order->user->phone;
        }

        return '';
    }

    /**
     * 🔥 新增：获取整单打印的打印机选项（使用默认打印机）
     */
    protected function getWholeOrderPrintOptions(array $currentOptions, array $originalOptions): array
    {
        // 整单打印使用默认打印机，不依赖仓库绑定
        $wholeOrderOptions = $currentOptions;

        // 如果没有指定打印机，使用系统默认打印机
        if (empty($wholeOrderOptions['printer_name'])) {
            $defaultPrinter = $this->getDefaultPrinter();
            if ($defaultPrinter) {
                $wholeOrderOptions['printer_name'] = $defaultPrinter;
                Log::info('Using system default printer for whole order', [
                    'printer_name' => $defaultPrinter
                ]);
            }
        }

        // 整单打印的特殊设置
        $wholeOrderOptions = array_merge($wholeOrderOptions, [
            'paper_width' => 80,  // 80mm热敏纸
            'font_size' => 12,
            'copies' => $originalOptions['copies'] ?? 1,
            'print_type' => 'whole_order_receipt'
        ]);

        return $wholeOrderOptions;
    }

    /**
     * 获取系统默认打印机
     */
    protected function getDefaultPrinter(): ?string
    {
        try {
            $defaultPrinter = null;
            $source = '';

            // 1. 从配置文件获取默认打印机
            $configPrinter = config('printing.drivers.clodop.default_printer');
            if (!empty($configPrinter)) {
                $defaultPrinter = $configPrinter;
                $source = 'config_file';
            }

            // 2. 从环境变量获取
            if (empty($defaultPrinter)) {
                $envPrinter = env('CLODOP_DEFAULT_PRINTER');
                if (!empty($envPrinter)) {
                    $defaultPrinter = $envPrinter;
                    $source = 'environment';
                }
            }

            // 3. 从数据库配置获取（如果有配置表）
            if (empty($defaultPrinter) && class_exists('\App\Shop\Services\ConfigService')) {
                $configService = app(\App\Shop\Services\ConfigService::class);
                $dbPrinter = $configService->get('default_printer_name');
                if (!empty($dbPrinter)) {
                    $defaultPrinter = $dbPrinter;
                    $source = 'database';
                }
            }

            // 🔥 详细日志记录
            if (env('WHOLE_ORDER_PRINT_LOG', false)) {
                if ($defaultPrinter) {
                    Log::channel('whole_order_print')->info('找到默认打印机', [
                        'printer_name' => $defaultPrinter,
                        'source' => $source,
                        'timestamp' => now()->toDateTimeString()
                    ]);
                } else {
                    Log::channel('whole_order_print')->warning('未找到默认打印机配置', [
                        'checked_sources' => ['config_file', 'environment', 'database'],
                        'timestamp' => now()->toDateTimeString()
                    ]);
                }
            }

            if (empty($defaultPrinter)) {
                Log::warning('No default printer configured for whole order receipt');
                return null;
            }

            return $defaultPrinter;

        } catch (\Exception $e) {
            Log::error('Failed to get default printer', [
                'error' => $e->getMessage()
            ]);

            if (env('WHOLE_ORDER_PRINT_LOG', false)) {
                Log::channel('whole_order_print')->error('获取默认打印机失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            return null;
        }
    }

    /**
     * 🔥 新增：生成用户欠款信息HTML（已禁用 - 避免冗余显示）
     */
    protected function generateUserDebtInfo($order): string
    {
        // 🚫 已禁用总欠款描述显示，避免与其他账单信息冗余
        // 如需重新启用，请取消注释下面的代码
        return '';

        /*
        try {
            if (!$order->user_id) {
                return '';
            }

            // 获取用户所有待付款账单（排除子账单，考虑累计账单）
            $bills = \App\Billing\Models\Bill::where('user_id', $order->user_id)
                ->whereIn('status', ['pending', 'partial_paid'])
                ->whereNull('parent_bill_id') // 排除已合并的子账单
                ->where('pending_amount', '>', 0)
                ->where(function($query) use ($order) {
                    // 排除当前订单的账单，但保留累计账单（order_id为null）
                    $query->where('order_id', '!=', $order->id)
                          ->orWhereNull('order_id');
                })
                ->get();

            if ($bills->isEmpty()) {
                return '';
            }

            $totalDebtAmount = $bills->sum('pending_amount');
            $debtBillCount = $bills->count();
            $overdueAmount = $bills->where('due_date', '<', now())->sum('pending_amount');

            // 确定显示样式
            $debtLevel = '';
            $alertIcon = '';
            if ($overdueAmount > 0) {
                $debtLevel = 'high-debt';
                $alertIcon = '⚠️ ';
            } elseif ($totalDebtAmount > 1000) {
                $debtLevel = 'high-debt';
                $alertIcon = '⚠️ ';
            } elseif ($totalDebtAmount > 500) {
                $debtLevel = 'medium-debt';
                $alertIcon = '💰 ';
            } else {
                $debtLevel = 'low-debt';
                $alertIcon = 'ℹ️ ';
            }

            // 检查是否有累计账单
            $hasConsolidatedBill = $bills->where('order_id', null)->count() > 0;
            $consolidatedText = $hasConsolidatedBill ? ' (含累计账单)' : '';

            return "
            <div class='debt-info {$debtLevel}' style='margin: 2px 0; padding: 3px; border: 1px solid #ddd; background-color: #fff3cd; font-size: 10px; font-weight: bold;'>
                {$alertIcon}客户总欠款: ¥" . number_format($totalDebtAmount, 2) . " ({$debtBillCount}个账单){$consolidatedText}" .
                ($overdueAmount > 0 ? " | 逾期: ¥" . number_format($overdueAmount, 2) : '') . "
            </div>";

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('生成用户欠款信息失败', [
                'order_id' => $order->id ?? 'unknown',
                'user_id' => $order->user_id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return '';
        }
        */
    }

    /**
     * 🔥 新增：根据配置生成账单支付二维码
     */
    protected function generateBillPaymentQrCodeWithConfig($order): string
    {
        // 🔥 总开关：检查是否启用配送单账单信息显示
        $showBillInfo = \App\Models\ShopConfig::getConfig('delivery.show_bill_info', true);

        if (!$showBillInfo) {
            return ''; // 总开关关闭，不显示任何账单相关内容
        }

        // 检查是否启用付款码显示
        $showBillPaymentQr = \App\Models\ShopConfig::getConfig('delivery.show_bill_payment_qr', true);

        if (!$showBillPaymentQr) {
            return $this->generateBillInfoWithoutQr($order); // 显示账单信息但不显示二维码
        }

        return $this->generateBillPaymentQrCode($order);
    }

    /**
     * 🔥 新增：生成不带二维码的账单信息
     */
    protected function generateBillInfoWithoutQr($order): string
    {
        try {
            // 检查是否显示账单金额
            $showBillAmount = \App\Models\ShopConfig::getConfig('delivery.show_bill_amount', true);

            if (!$showBillAmount) {
                return '';
            }

            if (!$order->user_id) {
                return '';
            }

            // 获取配置
            $showConsolidatedOnly = \App\Models\ShopConfig::getConfig('delivery.show_consolidated_bills_only', false);
            $minAmount = \App\Models\ShopConfig::getConfig('delivery.bill_qr_min_amount', 1);
            $maxAmount = \App\Models\ShopConfig::getConfig('delivery.bill_qr_max_amount', 50000);

            // 获取用户所有待付款账单
            $billsQuery = \App\Billing\Models\Bill::where('user_id', $order->user_id)
                ->whereIn('status', ['pending', 'partial_paid'])
                ->whereNull('parent_bill_id')
                ->where('pending_amount', '>', 0)
                ->where(function($query) use ($order) {
                    $query->where('order_id', '!=', $order->id)
                          ->orWhereNull('order_id');
                });

            if ($showConsolidatedOnly) {
                $billsQuery->whereNull('order_id');
            }

            $bills = $billsQuery->orderBy('pending_amount', 'desc')->first();

            if (!$bills || $bills->pending_amount < $minAmount || $bills->pending_amount > $maxAmount) {
                return '';
            }

            $isConsolidated = $bills->order_id === null;
            $billTypeText = $isConsolidated ? '累计账单' : '账单';

            return "
            <div style='display: flex; align-items: center; gap: 8px; font-size: 9px; line-height: 1.2;'>
                <div style='font-weight: bold;'>待付款账单</div>
                <div style='font-size: 11px; font-weight: bold;'>¥" . number_format($bills->pending_amount, 2) . "</div>
                <div style='color: #666;'>{$billTypeText}</div>
            </div>";

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('生成账单信息失败', [
                'order_id' => $order->id ?? 'unknown',
                'user_id' => $order->user_id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * 🔥 更新：生成账单支付二维码（支持配置控制）
     */
    protected function generateBillPaymentQrCode($order): string
    {
        try {
            if (!$order->user_id) {
                return '';
            }

            // 🔥 再次检查总开关（双重保险）
            $showBillInfo = \App\Models\ShopConfig::getConfig('delivery.show_bill_info', true);
            if (!$showBillInfo) {
                return '';
            }

            // 🔥 更新：使用配送单配置而不是账单配置
            $showConsolidatedOnly = \App\Models\ShopConfig::getConfig('delivery.show_consolidated_bills_only', false);

            // 获取用户所有待付款账单
            $billsQuery = \App\Billing\Models\Bill::where('user_id', $order->user_id)
                ->whereIn('status', ['pending', 'partial_paid'])
                ->whereNull('parent_bill_id') // 排除已合并的子账单
                ->where('pending_amount', '>', 0)
                ->where(function($query) use ($order) {
                    // 排除当前订单的账单，但保留累计账单（order_id为null）
                    $query->where('order_id', '!=', $order->id)
                          ->orWhereNull('order_id');
                });

            // 🔥 新增：如果配置为仅显示累计账单，则只查询累计账单
            if ($showConsolidatedOnly) {
                $billsQuery->whereNull('order_id'); // 累计账单的order_id为null
            }

            $bills = $billsQuery->orderBy('pending_amount', 'desc') // 优先显示金额大的账单
                ->first();

            if (!$bills) {
                return '';
            }

            // 🔥 更新：使用配送单配置的金额限制
            $minAmount = \App\Models\ShopConfig::getConfig('delivery.bill_qr_min_amount', 1);
            $maxAmount = \App\Models\ShopConfig::getConfig('delivery.bill_qr_max_amount', 50000);

            if ($bills->pending_amount < $minAmount || $bills->pending_amount > $maxAmount) {
                return '';
            }

            // 生成支付链接
            $paymentUrl = route('bill.pay.h5', [
                'bill_id' => $bills->id,
                'signature' => $this->generateBillSignature($bills),
                'expires' => now()->addHours(config('billing.qr_code_payment.expire_hours', 24))->timestamp
            ]);

            // 生成二维码（使用简单的二维码生成）
            $qrCodeData = $this->generateSimpleQrCode($paymentUrl);

            // 检查是否有累计账单
            $isConsolidated = $bills->order_id === null;
            $billTypeText = $isConsolidated ? '累计账单' : '账单';

            return "
            <div style='display: flex; align-items: center; gap: 8px; font-size: 9px; line-height: 1.2;'>
                <div style='font-weight: bold;'>扫码支付</div>
                <div>¥" . number_format($bills->pending_amount, 2) . "</div>
                <div style='color: #666;'>{$billTypeText}</div>
                <div style='width: 50px; height: 50px; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; background: white; padding: 1px;'>
                    {$qrCodeData}
                </div>
            </div>";

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('生成支付二维码失败', [
                'order_id' => $order->id ?? 'unknown',
                'user_id' => $order->user_id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * 生成账单签名
     */
    protected function generateBillSignature($bill): string
    {
        $data = [
            'bill_id' => $bill->id,
            'bill_no' => $bill->bill_no,
            'amount' => $bill->pending_amount,
            'user_id' => $bill->user_id,
        ];

        return hash_hmac('sha256', json_encode($data), config('app.key'));
    }

    /**
     * 🔥 更新：生成真正的二维码
     */
    protected function generateSimpleQrCode($url): string
    {
        try {
            // 使用 SimpleSoftwareIO/simple-qrcode 生成二维码
            $qrCode = \SimpleSoftwareIO\QrCode\Facades\QrCode::size(58)
                ->format('svg')
                ->generate($url);

            return $qrCode;

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('二维码生成失败', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            // 如果二维码生成失败，使用文本占位符
            return "<div style='font-size: 8px; word-break: break-all; padding: 2px;'>QR码<br/>支付链接</div>";
        }
    }

}