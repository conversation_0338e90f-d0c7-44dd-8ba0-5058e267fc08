<?php

namespace App\Inventory\Services;

use App\Inventory\Models\OutboundDocument;
use App\Inventory\Models\OutboundItem;
use App\Inventory\Models\OutboundOperation;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryTransaction;
use App\Inventory\Models\InventoryTransactionType;
use App\Inventory\Models\InventoryBatch;
use App\Order\Models\Order;
use App\Product\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class OutboundService
{
    /**
     * 获取出库单列表
     *
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getOutboundDocuments(array $params): LengthAwarePaginator
    {
        $query = OutboundDocument::with(['warehouse', 'toWarehouse', 'order', 'creator', 'items.product']);

        // 状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 出库类型筛选
        if (isset($params['document_type'])) {
            $query->where('document_type', $params['document_type']);
        }

        // 仓库筛选
        if (isset($params['warehouse_id'])) {
            $query->where('warehouse_id', $params['warehouse_id']);
        }

        // 订单筛选
        if (isset($params['order_id'])) {
            $query->where('order_id', $params['order_id']);
        }

        // 日期范围筛选
        if (isset($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date']);
        }

        if (isset($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date']);
        }

        // 关键字搜索
        if (isset($params['keyword'])) {
            $query->where(function ($q) use ($params) {
                $q->where('document_no', 'like', "%{$params['keyword']}%")
                  ->orWhere('notes', 'like', "%{$params['keyword']}%")
                  ->orWhereHas('order', function ($orderQuery) use ($params) {
                      $orderQuery->where('order_no', 'like', "%{$params['keyword']}%");
                  });
            });
        }

        // 排序
        $orderBy = $params['order_by'] ?? 'created_at';
        $direction = $params['direction'] ?? 'desc';
        $query->orderBy($orderBy, $direction);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }

    /**
     * 创建出库单
     *
     * @param array $data
     * @return OutboundDocument
     */
    public function createOutbound(array $data): OutboundDocument
    {
        DB::beginTransaction();

        try {
            // 创建出库单主记录
            $outboundDocument = OutboundDocument::create([
                'document_type' => $data['document_type'],
                'warehouse_id' => $data['warehouse_id'],
                'to_warehouse_id' => $data['to_warehouse_id'] ?? null,
                'expected_at' => $data['expected_at'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => $data['status'] ?? OutboundDocument::STATUS_DRAFT,
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ]);

            // 如果直接确认，设置确认信息
            if ($outboundDocument->status === OutboundDocument::STATUS_CONFIRMED) {
                $outboundDocument->update([
                    'confirmed_at' => now(),
                    'confirmed_by' => Auth::id(),
                ]);
            }

            // 创建出库明细
            foreach ($data['items'] as $itemData) {
                OutboundItem::create([
                    'outbound_document_id' => $outboundDocument->id,
                    'product_id' => $itemData['product_id'],
                    'product_name' => Product::find($itemData['product_id'])->name ?? '',
                    'product_sku' => Product::find($itemData['product_id'])->sku ?? '',
                    'planned_quantity' => $itemData['planned_quantity'],
                    'actual_quantity' => $itemData['planned_quantity'], // 初始值相同
                    'unit_id' => $itemData['unit_id'],
                    'unit_cost' => $itemData['unit_cost'],
                    'total_cost' => $itemData['planned_quantity'] * $itemData['unit_cost'],
                    'notes' => $itemData['notes'] ?? null,
                ]);
            }

            // 更新出库单总计
            $this->updateOutboundTotals($outboundDocument);

            // 记录操作
            $outboundDocument->recordOperation(
                OutboundOperation::TYPE_CREATE,
                '手动创建出库单'
            );

            // 如果是确认状态，直接执行库存扣减
            if ($outboundDocument->status === OutboundDocument::STATUS_CONFIRMED) {
                $this->processConfirmedOutbound($outboundDocument);
            }

            DB::commit();

            return $outboundDocument->load(['warehouse', 'toWarehouse', 'items.product']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建出库单失败', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 从订单创建出库单
     *
     * @param Order $order
     * @return OutboundDocument
     */
    public function createOutboundFromOrder(Order $order): OutboundDocument
    {
        DB::beginTransaction();

        try {
            // 检查订单状态
            if (!in_array($order->status, ['pending', 'paid', 'shipped'])) {
                throw new \Exception('只有待付款、已付款或已发货的订单才能创建出库单');
            }

            // 检查是否已存在出库单
            $existingOutbound = OutboundDocument::where('order_id', $order->id)->first();
            if ($existingOutbound) {
                throw new \Exception('订单已存在出库单：' . $existingOutbound->document_no);
            }

            // 创建出库单
            $outboundDocument = OutboundDocument::create([
                'document_type' => OutboundDocument::TYPE_SALES,
                'reference_type' => OutboundDocument::REFERENCE_TYPE_ORDER,
                'reference_id' => $order->id,
                'order_id' => $order->id,
                'warehouse_id' => $order->warehouse_id ?? $this->getDefaultWarehouse(),
                'status' => OutboundDocument::STATUS_CONFIRMED, // 订单确认后直接确认出库
                'confirmed_at' => now(),
                'confirmed_by' => Auth::id(),
                'notes' => "订单 {$order->order_no} 自动创建出库单",
            ]);

            // 创建出库明细
            foreach ($order->items as $orderItem) {
                // 智能批次选择
                $batchSelections = $this->selectOptimalBatches(
                    $orderItem->product_id,
                    $outboundDocument->warehouse_id,
                    $orderItem->quantity,
                    $orderItem->unit_id
                );

                if (empty($batchSelections)) {
                    throw new \Exception("商品 {$orderItem->product->name} 库存不足");
                }

                // 为每个批次创建出库明细
                foreach ($batchSelections as $batchSelection) {
                    $batch = $batchSelection['batch'];
                    $quantity = $batchSelection['quantity'];
                    
                    OutboundItem::create([
                        'outbound_document_id' => $outboundDocument->id,
                        'product_id' => $orderItem->product_id,
                        'product_name' => $orderItem->product->name,
                        'product_sku' => $orderItem->product->sku,
                        'planned_quantity' => $quantity,
                        'actual_quantity' => $quantity,
                        'unit_id' => $orderItem->unit_id,
                        'batch_id' => $batch->id,
                        'batch_code' => $batch->batch_code,
                        'unit_cost' => $batch->cost_price,
                        'total_cost' => $quantity * $batch->cost_price,
                        'order_item_id' => $orderItem->id,
                    ]);
                }
            }

            // 更新出库单总成本和总数量
            $this->updateOutboundTotals($outboundDocument);

            // 记录操作
            $outboundDocument->recordOperation(
                OutboundOperation::TYPE_CREATE,
                "从订单 {$order->order_no} 自动创建出库单"
            );

            DB::commit();

            return $outboundDocument->load(['warehouse', 'order', 'items.product', 'items.batch']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('从订单创建出库单失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 确认出库（执行库存扣减）
     *
     * @param int $id
     * @return OutboundDocument
     */
    public function confirmOutbound(int $id): OutboundDocument
    {
        DB::beginTransaction();

        try {
            $document = OutboundDocument::findOrFail($id);

            if (!$document->canConfirm()) {
                throw new \Exception('当前状态下不允许确认出库');
            }

            // 执行库存扣减
            foreach ($document->items as $item) {
                $this->executeStockReduction($item);
                $this->createInventoryTransaction($item);
            }

            // 更新出库单状态
            $document->update([
                'status' => OutboundDocument::STATUS_COMPLETED,
                'completed_at' => now(),
                'completed_by' => Auth::id(),
            ]);

            // 记录操作
            $document->recordOperation(
                OutboundOperation::TYPE_COMPLETE,
                '确认出库，库存已扣减'
            );

            // 如果是订单出库，更新订单状态
            if ($document->order) {
                $this->updateOrderStatus($document->order);

                // 🔥 如果是代客下单订单，更新配送状态
                if ($document->order->source === 'proxy') {
                    $this->updateProxyOrderDeliveryStatus($document->order);
                }
            }

            DB::commit();

            return $document->load(['warehouse', 'order', 'items.product', 'items.batch']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('确认出库失败', [
                'outbound_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 取消出库单
     *
     * @param int $id
     * @param string|null $reason
     * @return OutboundDocument
     */
    public function cancelOutbound(int $id, ?string $reason = null): OutboundDocument
    {
        $document = OutboundDocument::findOrFail($id);

        if (!$document->canCancel()) {
            throw new \Exception('当前状态下不允许取消出库单');
        }

        $document->update([
            'status' => OutboundDocument::STATUS_CANCELLED,
        ]);

        $document->recordOperation(
            OutboundOperation::TYPE_CANCEL,
            $reason ?? '取消出库单'
        );

        return $document;
    }

    /**
     * 处理已确认的出库单（执行库存扣减和批次分配）
     *
     * @param OutboundDocument $document
     * @return void
     */
    private function processConfirmedOutbound(OutboundDocument $document): void
    {
        foreach ($document->items as $item) {
            // 为手动创建的出库单分配批次
            if (!$item->batch_id) {
                $batchSelections = $this->selectOptimalBatches(
                    $item->product_id,
                    $document->warehouse_id,
                    $item->planned_quantity,
                    $item->unit_id
                );

                if (empty($batchSelections)) {
                    // 🔥 修复：为没有批次的商品创建默认批次
                    $defaultBatch = $this->createDefaultBatchForProduct($item->product_id, $document->warehouse_id);

                    if (!$defaultBatch) {
                        throw new \Exception("商品 {$item->product_name} 无法创建默认批次");
                    }

                    $item->update([
                        'batch_id' => $defaultBatch->id,
                        'batch_code' => $defaultBatch->batch_code,
                        'unit_cost' => $defaultBatch->cost_price,
                        'total_cost' => $item->planned_quantity * $defaultBatch->cost_price,
                    ]);
                } else {
                    // 使用第一个最优批次
                    $firstBatch = $batchSelections[0];
                    $item->update([
                        'batch_id' => $firstBatch['batch']->id,
                        'batch_code' => $firstBatch['batch']->batch_code,
                        'unit_cost' => $firstBatch['batch']->cost_price,
                        'total_cost' => $item->planned_quantity * $firstBatch['batch']->cost_price,
                    ]);
                }
            }

            // 执行库存扣减
            $this->executeStockReduction($item);
            $this->createInventoryTransaction($item);
        }

        // 更新为已完成状态
        $document->update([
            'status' => OutboundDocument::STATUS_COMPLETED,
            'completed_at' => now(),
            'completed_by' => Auth::id(),
        ]);

        // 记录操作
        $document->recordOperation(
            OutboundOperation::TYPE_COMPLETE,
            '确认出库，库存已扣减'
        );
    }

    /**
     * 智能批次选择
     *
     * @param int $productId
     * @param int $warehouseId
     * @param float $requiredQuantity
     * @param int $unitId
     * @return array
     */
    private function selectOptimalBatches(int $productId, int $warehouseId, float $requiredQuantity, int $unitId): array
    {
        // 使用现有的批次管理服务
        $batchService = app(BatchManagementService::class);
        
        $result = $batchService->selectOutboundBatches(
            $productId,
            $warehouseId,
            $requiredQuantity,
            ['unit_id' => $unitId]
        );

        return $result['batches'] ?? [];
    }

    /**
     * 执行库存扣减
     *
     * @param OutboundItem $item
     * @return void
     */
    private function executeStockReduction(OutboundItem $item): void
    {
        $batch = $item->batch;
        if (!$batch) {
            throw new \Exception("出库明细缺少批次信息");
        }

        // 执行批次出库
        $result = $batch->outbound($item->actual_quantity, [
            'outbound_document_id' => $item->outbound_document_id,
            'order_id' => $item->outboundDocument->order_id,
            'reason' => '销售出库',
        ]);

        if (!$result) {
            throw new \Exception("批次出库失败：库存不足");
        }

        // 更新总库存
        $item->product->updateTotalStock();

        Log::info('出库扣减完成', [
            'product_id' => $item->product_id,
            'batch_id' => $item->batch_id,
            'quantity' => $item->actual_quantity,
            'remaining_stock' => $batch->fresh()->available_quantity,
        ]);
    }

    /**
     * 创建库存事务记录
     *
     * @param OutboundItem $item
     * @return InventoryTransaction
     */
    private function createInventoryTransaction(OutboundItem $item): InventoryTransaction
    {
        $transactionType = InventoryTransactionType::where('code', 'sales_out')->first();
        if (!$transactionType) {
            throw new \Exception('未找到销售出库事务类型');
        }

        return InventoryTransaction::create([
            'transaction_type_id' => $transactionType->id,
            'reference_type' => OutboundDocument::class,
            'reference_id' => $item->outbound_document_id,
            'product_id' => $item->product_id,
            'warehouse_id' => $item->outboundDocument->warehouse_id,
            'quantity' => -$item->actual_quantity, // 负数表示出库
            'unit_id' => $item->unit_id,
            'unit_price' => $item->unit_cost,
            'total_amount' => $item->total_cost,
            'batch_id' => $item->batch_id,
            'notes' => "出库单 {$item->outboundDocument->document_no} 销售出库",
            'status' => 'completed',
            'created_by' => Auth::id(),
            'updated_by' => Auth::id(),
        ]);
    }

    /**
     * 更新出库单总计
     *
     * @param OutboundDocument $document
     * @return void
     */
    private function updateOutboundTotals(OutboundDocument $document): void
    {
        $totalCost = $document->items->sum('total_cost');
        $totalItems = $document->items->sum('actual_quantity');

        $document->update([
            'total_cost' => $totalCost,
            'total_items' => $totalItems,
        ]);
    }

    /**
     * 更新订单状态
     *
     * @param Order $order
     * @return void
     */
    private function updateOrderStatus(Order $order): void
    {
        // 如果订单还未发货，更新为已发货状态
        if ($order->status === 'paid') {
            $order->update([
                'status' => 'shipped',
                'shipped_at' => now(),
            ]);
        }
    }

    /**
     * 获取默认仓库
     *
     * @return int
     */
    private function getDefaultWarehouse(): int
    {
        $defaultWarehouse = \App\Warehouse\Models\Warehouse::where('is_default', true)->first();
        if (!$defaultWarehouse) {
            $defaultWarehouse = \App\Warehouse\Models\Warehouse::first();
        }
        
        if (!$defaultWarehouse) {
            throw new \Exception('未找到可用的仓库');
        }

        return $defaultWarehouse->id;
    }

    /**
     * 获取出库统计数据
     *
     * @return array
     */
    public function getOutboundStats(): array
    {
        return [
            'total' => OutboundDocument::count(),
            'draft' => OutboundDocument::where('status', OutboundDocument::STATUS_DRAFT)->count(),
            'confirmed' => OutboundDocument::where('status', OutboundDocument::STATUS_CONFIRMED)->count(),
            'completed' => OutboundDocument::where('status', OutboundDocument::STATUS_COMPLETED)->count(),
            'cancelled' => OutboundDocument::where('status', OutboundDocument::STATUS_CANCELLED)->count(),
        ];
    }

    /**
     * 🔥 新增：更新代客下单订单的配送状态
     *
     * @param Order $order
     * @return void
     */
    private function updateProxyOrderDeliveryStatus(Order $order): void
    {
        try {
            // 查找该订单的配送记录
            $delivery = \App\Delivery\Models\Delivery::where('order_id', $order->id)->first();

            if ($delivery && $delivery->status === 'warehouse_pending') {
                // 更新配送状态为待配送
                $delivery->update([
                    'status' => 'pending'
                ]);

                // 标记订单库存已处理
                $order->update([
                    'inventory_processed' => true,
                    'inventory_method' => 'outbound_document_confirmed'
                ]);

                Log::info('代客下单出库确认，配送状态已更新', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'delivery_id' => $delivery->id,
                    'old_status' => 'warehouse_pending',
                    'new_status' => 'pending',
                    'deliverer_id' => $delivery->deliverer_id,
                    'next_step' => $delivery->deliverer_id ? '已分配配送员，可以开始配送' : '等待分配配送员'
                ]);
            } else {
                Log::warning('代客下单出库确认，但配送记录状态异常', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'delivery_id' => $delivery?->id,
                    'delivery_status' => $delivery?->status,
                    'expected_status' => 'warehouse_pending'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('更新代客下单配送状态失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}