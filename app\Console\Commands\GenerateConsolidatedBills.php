<?php

namespace App\Console\Commands;

use App\Billing\Services\ConsolidatedBillingService;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateConsolidatedBills extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'billing:generate-consolidated 
                            {--period=monthly : 累计周期 (weekly, monthly, quarterly)}
                            {--min-orders=2 : 最少订单数量}
                            {--user-id= : 指定用户ID，留空则处理所有用户}';

    /**
     * The console command description.
     */
    protected $description = '自动为用户生成累计账单';

    protected ConsolidatedBillingService $consolidatedBillingService;

    public function __construct(ConsolidatedBillingService $consolidatedBillingService)
    {
        parent::__construct();
        $this->consolidatedBillingService = $consolidatedBillingService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $period = $this->option('period');
        $minOrders = (int) $this->option('min-orders');
        $userId = $this->option('user-id');

        $this->info("开始生成{$this->getPeriodText($period)}累计账单...");
        $this->info("最少订单数量: {$minOrders}");

        if ($userId) {
            $this->info("处理指定用户: {$userId}");
            $users = User::where('id', $userId)->get();
        } else {
            $this->info("处理所有用户");
            $users = User::all();
        }

        $processedUsers = 0;
        $generatedBills = 0;
        $errors = 0;

        $progressBar = $this->output->createProgressBar($users->count());
        $progressBar->start();

        foreach ($users as $user) {
            try {
                // 检查用户是否有可累计的订单
                if (!$this->consolidatedBillingService->hasConsolidatableOrders($user, $minOrders)) {
                    $progressBar->advance();
                    continue;
                }

                // 生成累计账单
                $consolidatedBill = $this->consolidatedBillingService->createPeriodicConsolidatedBill($user, $period);
                
                if ($consolidatedBill) {
                    $generatedBills++;
                    $this->line("");
                    $this->info("✓ 用户 {$user->name} (ID: {$user->id}) 生成累计账单: {$consolidatedBill->bill_no}");
                    $this->line("  包含订单数: " . count($consolidatedBill->getConsolidatedOrderIds()));
                    $this->line("  账单金额: ¥{$consolidatedBill->final_amount}");
                }

                $processedUsers++;
                
            } catch (\Exception $e) {
                $errors++;
                $this->line("");
                $this->error("✗ 处理用户 {$user->name} (ID: {$user->id}) 时出错: " . $e->getMessage());
                
                Log::error('累计账单生成失败', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->line("");
        $this->line("");

        // 输出统计信息
        $this->info("=== 累计账单生成完成 ===");
        $this->table([
            '项目', '数量'
        ], [
            ['处理用户总数', $users->count()],
            ['有效用户数', $processedUsers],
            ['生成账单数', $generatedBills],
            ['错误数', $errors],
        ]);

        if ($generatedBills > 0) {
            $this->info("✓ 成功生成 {$generatedBills} 个累计账单");
        } else {
            $this->warn("没有生成任何累计账单");
        }

        Log::info('累计账单生成任务完成', [
            'period' => $period,
            'min_orders' => $minOrders,
            'processed_users' => $processedUsers,
            'generated_bills' => $generatedBills,
            'errors' => $errors
        ]);

        return Command::SUCCESS;
    }

    /**
     * 获取周期文本
     */
    protected function getPeriodText(string $period): string
    {
        return match($period) {
            'weekly' => '本周',
            'monthly' => '本月',
            'quarterly' => '本季度',
            default => '本月'
        };
    }
} 