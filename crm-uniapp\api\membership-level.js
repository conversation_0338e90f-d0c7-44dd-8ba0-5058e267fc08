import request from '../utils/request.js'

// 会员等级相关API
export default {
	// 获取会员等级列表
	getMembershipLevels(params = {}) {
		return request.get('/crm/membership-levels', params)
	},
	
	// 获取会员等级详情
	getMembershipLevelDetail(levelId) {
		return request.get(`/crm/membership-levels/${levelId}`)
	},
	
	// 创建会员等级
	createMembershipLevel(data) {
		return request.post('/crm/membership-levels', data)
	},
	
	// 更新会员等级
	updateMembershipLevel(levelId, data) {
		return request.put(`/crm/membership-levels/${levelId}`, data)
	},
	
	// 删除会员等级
	deleteMembershipLevel(levelId) {
		return request.delete(`/crm/membership-levels/${levelId}`)
	},
	
	// 设置默认会员等级
	setDefaultLevel(levelId) {
		return request.put(`/crm/membership-levels/${levelId}/default`)
	}
}
