<?php

use Illuminate\Support\Facades\Route;
use App\Billing\Http\Controllers\BillPaymentController;

// 🔥 新增：账单支付H5页面路由（无需认证）
Route::get('/bill/pay/{bill_id}', [BillPaymentController::class, 'show'])
    ->name('bill.pay.h5');

// 🔥 新增：支付返回页面（H5支付完成后的跳转页面）
Route::get('/bill/pay/return', [BillPaymentController::class, 'paymentReturn'])
    ->name('bill.pay.return');

// 🔥 新增：支付成功页面
Route::get('/bill/pay/success', [BillPaymentController::class, 'paymentSuccess'])
    ->name('bill.pay.success');

// 账单管理后台路由（如果需要Web界面）
Route::prefix('admin/bills')->middleware(['auth:admin'])->group(function () {
    // 这里可以添加后台管理界面的路由
    // 例如：账单列表页面、账单详情页面等
});