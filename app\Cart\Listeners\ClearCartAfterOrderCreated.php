<?php

namespace App\Cart\Listeners;

use App\Order\Events\OrderCreated;
use App\Cart\Services\CartService;
use App\Cart\Models\CartItem;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ClearCartAfterOrderCreated
{
    /**
     * 购物车服务实例
     *
     * @var \App\Cart\Services\CartService
     */
    protected $cartService;

    /**
     * 创建监听器实例
     *
     * @param  \App\Cart\Services\CartService  $cartService
     * @return void
     */
    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * 处理事件
     *
     * @param  \App\Order\Events\OrderCreated  $event
     * @return void
     */
    public function handle(OrderCreated $event)
    {
        $order = $event->order;
        $cartItemIds = $event->cartItemIds;

        if (empty($cartItemIds)) {
            Log::info('订单创建后无需清理购物车商品', [
                'order_id' => $order->id,
                'order_no' => $order->order_no
            ]);
            return;
        }

        Log::info('开始清理订单关联的购物车商品', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'cart_item_ids' => $cartItemIds,
            'user_id' => $order->user_id // 记录用户ID，确保我们知道是谁的购物车
        ]);

        $successCount = 0;
        $failedIds = [];

        try {
            // 直接使用模型删除购物车项，不依赖于Auth::id()
            $deletedCount = CartItem::whereIn('id', $cartItemIds)
                ->whereHas('cart', function($query) use ($order) {
                    $query->where('user_id', $order->user_id);
                })
                ->delete();
            
            Log::info('批量删除购物车商品结果', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'requested_count' => count($cartItemIds),
                'deleted_count' => $deletedCount
            ]);
            
            $successCount = $deletedCount;
            
            // 检查是否有未删除的项目
            if ($deletedCount < count($cartItemIds)) {
                $failedIds = $cartItemIds; // 简化处理，实际应该计算差集
                Log::warning('部分购物车商品未能删除', [
                    'order_id' => $order->id,
                    'requested_count' => count($cartItemIds),
                    'deleted_count' => $deletedCount
                ]);
            }
        } catch (\Exception $e) {
            Log::error('批量删除购物车商品失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $failedIds = $cartItemIds;
        }

        Log::info('购物车商品清理完成', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'success_count' => $successCount,
            'failed_ids' => $failedIds
        ]);
    }
} 