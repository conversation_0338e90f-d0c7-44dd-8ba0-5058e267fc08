<?php

namespace App\Billing\Http\Controllers;

use App\Billing\Models\Bill;
use App\Billing\Services\BillingService;
use App\Billing\Http\Resources\BillResource;
use App\Billing\Http\Requests\CreateBillRequest;
use App\Billing\Http\Requests\ProcessPaymentRequest;
use App\Billing\Http\Requests\AdjustBillRequest;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BillController extends Controller
{
    protected BillingService $billingService;

    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * 获取账单列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'bill_type' => $request->bill_type,
                'status' => $request->status,
                'payment_status' => $request->payment_status,
                'payment_method' => $request->payment_method, // 🔥 新增：支付方式筛选
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
                'user_id' => $request->user_id,
                'is_overdue' => $request->is_overdue,
                'search' => $request->search,
            ];

            $perPage = $request->get('per_page', 15);
            $bills = Bill::query()
                ->with(['user', 'order', 'items', 'paymentRecords', 'adjustments']) // 🔥 新增：加载订单关联
                ->where('bill_type', '!=', 'consolidated') // 🔥 排除累计账单
                ->when($filters['bill_type'], fn($q) => $q->where('bill_type', $filters['bill_type']))
                ->when($filters['status'], fn($q) => $q->where('status', $filters['status']))
                ->when($filters['payment_status'], fn($q) => $q->where('payment_status', $filters['payment_status']))
                ->when($filters['payment_method'], function($q, $paymentMethod) {
                    // 🔥 新增：根据原始付款方式筛选
                    Log::info('账单支付方式筛选', [
                        'payment_method' => $paymentMethod,
                        'filter_applied' => true
                    ]);

                    // 主要通过关联订单的付款方式进行筛选
                    $q->whereHas('order', function($orderQuery) use ($paymentMethod) {
                        $orderQuery->where('payment_method', $paymentMethod);
                    });
                })
                ->when($filters['date_from'], fn($q) => $q->whereDate('created_at', '>=', $filters['date_from']))
                ->when($filters['date_to'], fn($q) => $q->whereDate('created_at', '<=', $filters['date_to']))
                ->when($filters['user_id'], fn($q) => $q->where('user_id', $filters['user_id']))
                ->when($filters['is_overdue'], fn($q) => $q->where('due_date', '<', now()))
                ->when($filters['search'], function($q, $search) {
                    $q->where(function($query) use ($search) {
                        $query->where('bill_no', 'like', "%{$search}%")
                              ->orWhereHas('user', function($userQuery) use ($search) {
                                  $userQuery->where('name', 'like', "%{$search}%")
                                           ->orWhere('phone', 'like', "%{$search}%");
                              });
                    });
                })
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => BillResource::collection($bills),
                'meta' => [
                    'total' => $bills->total(),
                    'per_page' => $bills->perPage(),
                    'current_page' => $bills->currentPage(),
                    'last_page' => $bills->lastPage(),
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个账单详情
     */
    public function show(Bill $bill): JsonResponse
    {
        try {
            $bill->load(['user', 'order', 'items.product', 'paymentRecords', 'adjustments', 'balanceTransactions']);

            return response()->json([
                'success' => true,
                'data' => new BillResource($bill)
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理账单支付
     */
    public function processPayment(ProcessPaymentRequest $request, Bill $bill): JsonResponse
    {
        try {
            $paymentRecord = $this->billingService->processPayment($bill, $request->validated());

            return response()->json([
                'success' => true,
                'message' => '账单支付处理成功',
                'data' => [
                    'payment_record_id' => $paymentRecord->id,
                    'payment_no' => $paymentRecord->payment_no,
                    'bill' => new BillResource($bill->fresh())
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '账单支付处理失败: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * 调整账单
     */
    public function adjust(AdjustBillRequest $request, Bill $bill): JsonResponse
    {
        try {
            // 添加详细的请求日志
            Log::info('账单调整请求开始', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'request_data' => $request->all(),
                'validated_data' => $request->validated(),
                'client_ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            
            // 检查账单状态
            if (!$bill->canBeAdjusted()) {
                Log::warning('账单状态不允许调整', [
                    'bill_id' => $bill->id,
                    'bill_status' => $bill->status,
                    'payment_status' => $bill->payment_status
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => '账单状态不允许调整'
                ], 400);
            }
            
            $adjustment = $this->billingService->createAdjustment(
                $bill,
                $request->adjustment_amount,
                $request->reason,
                $request->only(['description', 'operator_id', 'adjustment_type'])
            );
            
            Log::info('账单调整成功', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'adjustment_id' => $adjustment->id,
                'adjustment_amount' => $request->adjustment_amount,
                'reason' => $request->reason
            ]);

            return response()->json([
                'success' => true,
                'message' => '账单调整成功',
                'data' => [
                    'adjustment_id' => $adjustment->id,
                    'adjustment_no' => $adjustment->adjustment_no,
                    'bill' => new BillResource($bill->fresh())
                ]
            ]);
        } catch (Exception $e) {
            Log::error('账单调整失败', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '账单调整失败: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * 取消账单
     */
    public function cancel(Request $request, Bill $bill): JsonResponse
    {
        try {
            $this->billingService->cancelBill(
                $bill,
                $request->reason ?? '用户取消',
                $request->only(['operator_id'])
            );

            return response()->json([
                'success' => true,
                'message' => '账单取消成功',
                'data' => new BillResource($bill->fresh())
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '账单取消失败: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * 账单退款
     */
    public function refund(Request $request, Bill $bill): JsonResponse
    {
        $request->validate([
            'refund_amount' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:500',
            'refund_method' => 'nullable|string|in:balance,cash,bank_transfer,other',
            'operator_id' => 'nullable|integer|exists:employees,id'
        ]);

        try {
            $refundRecord = $this->billingService->refundBill(
                $bill,
                $request->refund_amount,
                $request->reason,
                $request->only(['refund_method', 'operator_id'])
            );

            return response()->json([
                'success' => true,
                'message' => '账单退款处理成功',
                'data' => [
                    'refund_record_id' => $refundRecord->id,
                    'refund_no' => $refundRecord->payment_no,
                    'bill' => new BillResource($bill->fresh())
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '账单退款处理失败: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * 🔥 新增：批量退款
     */
    public function batchRefund(Request $request): JsonResponse
    {
        // 🔒 权限验证
        if (!auth()->user()->can('batch_refund_bills')) {
            return response()->json([
                'success' => false,
                'message' => '没有批量退款权限'
            ], 403);
        }

        $request->validate([
            'bill_ids' => 'required|array|min:1|max:50', // 🔒 限制批量数量
            'bill_ids.*' => 'required|integer|exists:bills,id',
            'reason' => 'required|string|max:500', // 🔒 必须提供退款原因
        ]);

        try {
            $billIds = $request->bill_ids;
            $reason = $request->reason;
            $operatorId = auth()->id(); // 🔒 从认证用户获取操作员ID

            // 🔒 检查批量退款限制
            if (count($billIds) > 50) {
                return response()->json([
                    'success' => false,
                    'message' => '单次批量退款不能超过50个账单'
                ], 400);
            }

            // 🔒 预检查总退款金额
            $totalRefundAmount = $this->billingService->calculateBatchRefundAmount($billIds);
            $maxBatchRefundAmount = config('billing.max_batch_refund_amount', 100000); // 默认10万元限制

            if ($totalRefundAmount > $maxBatchRefundAmount) {
                return response()->json([
                    'success' => false,
                    'message' => "批量退款总金额 ¥{$totalRefundAmount} 超过限制 ¥{$maxBatchRefundAmount}"
                ], 400);
            }

            $results = $this->billingService->batchRefundBills(
                $billIds,
                $reason,
                $operatorId
            );

            return response()->json([
                'success' => true,
                'message' => '批量退款处理完成',
                'data' => $results
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量退款处理失败: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * 获取用户账单列表
     */
    public function userBills(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $filters = $request->only(['bill_type', 'status', 'payment_status', 'date_from', 'date_to']);
            
            $perPage = $request->get('per_page', 15);
            $bills = $this->billingService->getUserBills($user, $filters)->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => BillResource::collection($bills),
                'meta' => [
                    'total' => $bills->total(),
                    'per_page' => $bills->perPage(),
                    'current_page' => $bills->currentPage(),
                    'last_page' => $bills->lastPage(),
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取用户账单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取逾期账单
     */
    public function overdue(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['user_id', 'overdue_days']);
            $perPage = $request->get('per_page', 15);
            $bills = $this->billingService->getOverdueBills($filters)->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => BillResource::collection($bills),
                'meta' => [
                    'total' => $bills->total(),
                    'per_page' => $bills->perPage(),
                    'current_page' => $bills->currentPage(),
                    'last_page' => $bills->lastPage(),
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取逾期账单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取账单统计
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['user_id', 'date_from', 'date_to']);
            $statistics = $this->billingService->getBillStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单统计失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户欠款概览（只计算已确认订单的账单）
     */
    public function getUserDebtSummary(Request $request, int $userId)
    {
        try {
            $user = User::findOrFail($userId);
            
            // 获取欠款统计（只统计已确认订单的账单）
            $debtSummary = Bill::where('user_id', $userId)
                ->whereIn('status', ['pending', 'partial_paid'])
                ->whereHas('order', function($query) {
                    $query->where('status', 'delivered')
                        ->where(function($q) {
                            // 没有更正记录 OR 更正已确认
                            $q->whereDoesntHave('corrections')
                              ->orWhere('correction_status', 'confirmed');
                        });
                })
                ->selectRaw('
                    COUNT(*) as total_bills,
                    SUM(final_amount) as total_debt,
                    SUM(pending_amount) as outstanding_debt,
                    SUM(paid_amount) as paid_amount,
                    MIN(due_date) as earliest_due_date,
                    COUNT(CASE WHEN due_date < NOW() THEN 1 END) as overdue_bills,
                    SUM(CASE WHEN due_date < NOW() THEN pending_amount ELSE 0 END) as overdue_amount
                ')
                ->first();

            // 获取最近的欠款账单（只包含已确认订单）
            $recentBills = Bill::where('user_id', $userId)
                ->whereIn('status', ['pending', 'partial_paid'])
                ->whereHas('order', function($query) {
                    $query->where('status', 'delivered')
                        ->where(function($q) {
                            // 没有更正记录 OR 更正已确认
                            $q->whereDoesntHave('corrections')
                              ->orWhere('correction_status', 'confirmed');
                        });
                })
                ->with(['items', 'order'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            // 统计待确认的订单（这些不计入正式欠款）
            $pendingOrdersCount = \App\Order\Models\Order::where('user_id', $userId)
                ->where('status', 'delivered')
                ->where(function($q) {
                    $q->whereHas('corrections', function($sub) {
                        $sub->where('status', 'pending');
                    })->orWhere('correction_status', 'pending');
                })
                ->count();

            $pendingOrdersAmount = \App\Order\Models\Order::where('user_id', $userId)
                ->where('status', 'delivered')
                ->where(function($q) {
                    $q->whereHas('corrections', function($sub) {
                        $sub->where('status', 'pending');
                    })->orWhere('correction_status', 'pending');
                })
                ->sum('final_payment_amount');

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'debt_summary' => $debtSummary,
                    'recent_bills' => $recentBills,
                    'pending_confirmation' => [
                        'orders_count' => $pendingOrdersCount,
                        'orders_amount' => $pendingOrdersAmount,
                        'description' => '待确认订单金额（不计入正式欠款）'
                    ]
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取用户欠款概览失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户欠款明细列表（只显示已确认订单的账单）
     */
    public function getUserDebtDetails(Request $request, int $userId)
    {
        try {
            $query = Bill::where('user_id', $userId)
                ->whereIn('status', ['pending', 'partial_paid'])
                ->whereHas('order', function($orderQuery) {
                    $orderQuery->where('status', 'delivered')
                        ->where(function($q) {
                            // 没有更正记录 OR 更正已确认
                            $q->whereDoesntHave('corrections')
                              ->orWhere('correction_status', 'confirmed');
                        });
                })
                ->with(['items.product', 'order', 'paymentRecords']);

            // 筛选条件
            if ($request->filled('bill_type')) {
                $query->where('bill_type', $request->bill_type);
            }

            if ($request->filled('overdue_only') && $request->overdue_only) {
                $query->where('due_date', '<', now());
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $bills = $query->orderBy('due_date', 'asc')
                          ->orderBy('created_at', 'desc')
                          ->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'message' => '用户欠款明细（仅包含已确认订单）',
                'data' => $bills->items(),
                'meta' => [
                    'total' => $bills->total(),
                    'per_page' => $bills->perPage(),
                    'current_page' => $bills->currentPage(),
                    'last_page' => $bills->lastPage(),
                    'note' => '只显示已确认订单的账单，待确认订单不计入欠款明细'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取用户欠款明细失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单张账单的详细欠款信息
     */
    public function getBillDebtDetails(Request $request, int $billId)
    {
        try {
            $bill = Bill::with([
                'user',
                'order',
                'items.product',
                'paymentRecords' => function($query) {
                    $query->orderBy('payment_time', 'desc');
                },
                'adjustments' => function($query) {
                    $query->orderBy('created_at', 'desc');
                }
            ])->findOrFail($billId);

            // 计算详细信息
            $debtDetails = [
                'bill_info' => $bill,
                'debt_analysis' => [
                    'original_amount' => $bill->original_amount,
                    'adjustments' => $bill->adjustment_amount,
                    'final_amount' => $bill->final_amount,
                    'paid_amount' => $bill->paid_amount,
                    'outstanding_amount' => $bill->pending_amount,
                    'payment_progress' => $bill->final_amount > 0 ? 
                        round(($bill->paid_amount / $bill->final_amount) * 100, 2) : 0,
                ],
                'item_breakdown' => $bill->items->map(function($item) {
                    return [
                        'item_name' => $item->item_name,
                        'quantity' => $item->quantity,
                        'unit' => $item->unit,
                        'unit_price' => $item->unit_price,
                        'total_price' => $item->total_price,
                        'discount_amount' => $item->discount_amount,
                        'final_amount' => $item->final_amount,
                        'product' => $item->product
                    ];
                }),
                'payment_history' => $bill->paymentRecords,
                'adjustment_history' => $bill->adjustments
            ];

            return response()->json([
                'success' => true,
                'data' => $debtDetails
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单欠款详情失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取所有用户的欠款排行榜（只计算已确认订单的账单）
     */
    public function getDebtRanking(Request $request)
    {
        try {
            $query = DB::table('bills')
                ->join('users', 'bills.user_id', '=', 'users.id')
                ->join('orders', 'bills.order_id', '=', 'orders.id')
                ->where('bills.status', 'pending')
                ->orWhere('bills.status', 'partial_paid')
                ->where('orders.status', 'delivered')
                ->where(function($q) {
                    // 只计算已确认的订单（没有更正记录或更正已确认）
                    $q->whereNull('orders.correction_status')
                      ->orWhere('orders.correction_status', 'confirmed');
                })
                ->groupBy('bills.user_id', 'users.name', 'users.phone')
                ->select([
                    'bills.user_id',
                    'users.name as user_name',
                    'users.phone as user_phone',
                    DB::raw('SUM(bills.pending_amount) as total_debt'),
                    DB::raw('COUNT(bills.id) as bill_count'),
                    DB::raw('SUM(CASE WHEN bills.due_date < NOW() THEN bills.pending_amount ELSE 0 END) as overdue_amount'),
                    DB::raw('COUNT(CASE WHEN bills.due_date < NOW() THEN 1 END) as overdue_bills'),
                    DB::raw('MIN(bills.due_date) as earliest_due_date')
                ]);

            // 排序
            $sortBy = $request->get('sort_by', 'total_debt');
            $sortOrder = $request->get('sort_order', 'desc');
            
            switch ($sortBy) {
                case 'bill_count':
                    $query->orderBy('bill_count', $sortOrder);
                    break;
                case 'overdue_amount':
                    $query->orderBy('overdue_amount', $sortOrder);
                    break;
                case 'earliest_due_date':
                    $query->orderBy('earliest_due_date', $sortOrder);
                    break;
                default:
                    $query->orderBy('total_debt', $sortOrder);
            }

            $rankings = $query->paginate($request->get('per_page', 50));

            return response()->json([
                'success' => true,
                'message' => '欠款排行榜（仅包含已确认订单）',
                'data' => $rankings->items(),
                'meta' => [
                    'total' => $rankings->total(),
                    'per_page' => $rankings->perPage(),
                    'current_page' => $rankings->currentPage(),
                    'last_page' => $rankings->lastPage(),
                    'note' => '只统计已确认订单的欠款，待确认订单不计入排行榜'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取欠款排行榜失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取欠款概览统计
     */
    public function getDebtOverview(Request $request): JsonResponse
    {
        try {
            // 只统计已确认订单的账单
            $confirmedBillsQuery = Bill::whereIn('status', ['pending', 'partial_paid'])
                ->whereHas('order', function($query) {
                    $query->where('status', 'delivered')
                        ->where(function($q) {
                            $q->whereDoesntHave('corrections')
                              ->orWhere('correction_status', 'confirmed');
                        });
                });

            $totalDebt = $confirmedBillsQuery->sum('pending_amount');
            $totalUsers = $confirmedBillsQuery->distinct('user_id')->count();
            
            $overdueDebt = $confirmedBillsQuery->where('due_date', '<', now())->sum('pending_amount');
            $overdueUsers = $confirmedBillsQuery->where('due_date', '<', now())->distinct('user_id')->count();

            // 统计待确认订单金额（不计入正式欠款）
            $pendingConfirmation = \App\Order\Models\Order::where('status', 'delivered')
                ->where(function($q) {
                    $q->whereHas('corrections', function($sub) {
                        $sub->where('status', 'pending');
                    })->orWhere('correction_status', 'pending');
                })
                ->sum('final_payment_amount');

            // 本月新增欠款（已确认订单）
            $monthlyNew = $confirmedBillsQuery
                ->whereMonth('created_at', now()->month)
                ->sum('pending_amount');

            // 计算增长率（对比上月）
            $lastMonthDebt = Bill::whereIn('status', ['pending', 'partial_paid'])
                ->whereHas('order', function($query) {
                    $query->where('status', 'delivered')
                        ->where(function($q) {
                            $q->whereDoesntHave('corrections')
                              ->orWhere('correction_status', 'confirmed');
                        });
                })
                ->whereMonth('created_at', now()->subMonth()->month)
                ->sum('pending_amount');

            $growthRate = $lastMonthDebt > 0 ? 
                round((($monthlyNew - $lastMonthDebt) / $lastMonthDebt) * 100, 2) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'total_debt' => $totalDebt,
                    'total_users' => $totalUsers,
                    'overdue_debt' => $overdueDebt,
                    'overdue_users' => $overdueUsers,
                    'pending_confirmation' => $pendingConfirmation,
                    'monthly_new' => $monthlyNew,
                    'growth_rate' => $growthRate,
                    'note' => '统计数据仅包含已确认订单，待确认订单单独统计不计入正式欠款'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取欠款概览失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 发送催款通知
     */
    public function sendDebtNotification(Request $request, int $userId): JsonResponse
    {
        $request->validate([
            'methods' => 'required|array',
            'methods.*' => 'in:sms,phone,wechat',
            'message' => 'required|string|max:500'
        ]);

        try {
            $user = User::findOrFail($userId);
            
            // 这里应该集成实际的通知服务
            // 比如短信服务、微信推送等
            
            // 模拟发送成功
            $sentMethods = [];
            foreach ($request->methods as $method) {
                switch ($method) {
                    case 'sms':
                        // 发送短信
                        $sentMethods[] = '短信通知已发送';
                        break;
                    case 'phone':
                        // 电话通知（可能是自动语音或人工）
                        $sentMethods[] = '电话通知已安排';
                        break;
                    case 'wechat':
                        // 微信推送
                        $sentMethods[] = '微信通知已发送';
                        break;
                }
            }

            // 记录通知历史
            DB::table('debt_notifications')->insert([
                'user_id' => $userId,
                'methods' => json_encode($request->methods),
                'message' => $request->message,
                'sent_at' => now(),
                'sent_by' => auth()->id(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => '催款通知发送成功',
                'data' => [
                    'user' => $user->name,
                    'methods' => $sentMethods,
                    'sent_at' => now()->toDateTimeString()
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '发送催款通知失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 搜索用户
     */
    public function searchUsers(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', '');
            $limit = $request->get('limit', 10);

            if (empty($query)) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            $users = User::where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('phone', 'like', "%{$query}%")
                      ->orWhere('email', 'like', "%{$query}%");
                })
                ->select(['id', 'name', 'phone', 'email'])
                ->limit($limit)
                ->get();

            // 为每个用户添加统计信息
            $usersWithStats = $users->map(function($user) {
                $orderCount = \App\Order\Models\Order::where('user_id', $user->id)->count();
                $debtAmount = Bill::where('user_id', $user->id)
                    ->whereIn('status', ['pending', 'partial_paid'])
                    ->sum('pending_amount');

                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'phone' => $user->phone,
                    'email' => $user->email,
                    'order_count' => $orderCount,
                    'debt_amount' => $debtAmount
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $usersWithStats
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '搜索用户失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取账单报告
     */
    public function reports(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'start_date' => 'required|date',
                'end_date' => 'required|date',
                'chart_type' => 'string|in:day,week,month',
                'bill_type' => 'string',
            ]);

            $reports = $this->billingService->getBillReports($params);

            return response()->json([
                'success' => true,
                'data' => $reports
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单报告失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取合并账单统计
     */
    public function consolidatedStats(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'start_date' => 'date',
                'end_date' => 'date',
                'user_id' => 'integer',
            ]);

            $stats = $this->billingService->getConsolidatedStats($params);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取合并账单统计失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取债务排名
     */
    public function debtRanking(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'limit' => 'integer|min:1|max:100',
                'start_date' => 'date',
                'end_date' => 'date',
            ]);

            $ranking = $this->billingService->getDebtRanking($params);

            return response()->json([
                'success' => true,
                'data' => $ranking
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取债务排名失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 支付账单
     */
    public function pay(Request $request, $id): JsonResponse
    {
        try {
            // 🔥 修复：支持前端PaymentModal发送的数据格式
            $params = $request->validate([
                'payment_method' => 'required|string',
                'payment_amount' => 'required|numeric|min:0.01',
                'balance_used' => 'nullable|numeric|min:0',
                'transaction_id' => 'nullable|string|max:100',
                'notes' => 'nullable|string|max:500',
                'payment_type' => 'nullable|string|in:full,partial',
            ]);

            Log::info('账单支付请求', [
                'bill_id' => $id,
                'params' => $params,
                'user_agent' => $request->header('User-Agent'),
                'ip' => $request->ip()
            ]);

            $result = $this->billingService->payBill($id, $params);

            Log::info('账单支付成功', [
                'bill_id' => $id,
                'result' => $result
            ]);

            return response()->json([
                'success' => true,
                'message' => '账单支付成功',
                'data' => $result
            ]);
        } catch (Exception $e) {
            Log::error('账单支付失败', [
                'bill_id' => $id,
                'params' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '账单支付失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取账单项目
     */
    public function items($id): JsonResponse
    {
        try {
            $bill = Bill::with('items')->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $bill->items
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单项目失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 🔥 新增：获取累计账单列表
     */
    public function consolidated(Request $request): JsonResponse
    {
        try {
            $filters = [
                'status' => $request->status,
                'payment_status' => $request->payment_status,
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
                'user_id' => $request->user_id,
                'is_overdue' => $request->is_overdue,
                'search' => $request->search,
            ];

            $perPage = $request->get('per_page', 15);
            $bills = Bill::query()
                ->with(['user', 'items', 'paymentRecords', 'adjustments'])
                ->where('bill_type', 'consolidated') // 只获取累计账单
                ->when($filters['status'], fn($q) => $q->where('status', $filters['status']))
                ->when($filters['payment_status'], fn($q) => $q->where('payment_status', $filters['payment_status']))
                ->when($filters['date_from'], fn($q) => $q->whereDate('created_at', '>=', $filters['date_from']))
                ->when($filters['date_to'], fn($q) => $q->whereDate('created_at', '<=', $filters['date_to']))
                ->when($filters['user_id'], fn($q) => $q->where('user_id', $filters['user_id']))
                ->when($filters['is_overdue'], fn($q) => $q->where('due_date', '<', now()))
                ->when($filters['search'], function($q, $search) {
                    $q->where(function($query) use ($search) {
                        $query->where('bill_no', 'like', "%{$search}%")
                              ->orWhereHas('user', function($userQuery) use ($search) {
                                  $userQuery->where('name', 'like', "%{$search}%")
                                           ->orWhere('phone', 'like', "%{$search}%");
                              });
                    });
                })
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => BillResource::collection($bills),
                'meta' => [
                    'total' => $bills->total(),
                    'per_page' => $bills->perPage(),
                    'current_page' => $bills->currentPage(),
                    'last_page' => $bills->lastPage(),
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取累计账单列表失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 