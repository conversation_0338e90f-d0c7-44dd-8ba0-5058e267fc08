<?php

use App\Order\Http\Controllers\OrderController;
use App\Order\Http\Controllers\OrderMergeController;
use App\Order\Http\Controllers\OrderCorrectionController;
use Illuminate\Support\Facades\Route;

// 需要用户认证的订单路由（普通用户商城系统）
Route::group(['prefix' => 'api/orders', 'middleware' => ['api', 'auth:sanctum']], function () {
    Route::get('/', [OrderController::class, 'index']); // 用户查看自己的订单列表
    Route::post('/', [OrderController::class, 'create']);
    Route::get('/stats', [OrderController::class, 'getStats']);
    Route::get('/{id}', [OrderController::class, 'show']);
    Route::put('/{id}/status', [OrderController::class, 'updateStatus']);
    Route::post('/{id}/confirm', [OrderController::class, 'confirm']); // 🔥 客户确认收货API
    Route::post('/{id}/auto-confirm', [OrderController::class, 'autoConfirm']); // 🤖 手动触发自动确认API
    Route::post('/{id}/cancel', [OrderController::class, 'cancel']);

    // 🔥 新增：支付相关路由
    Route::post('/{id}/payment-success', [OrderController::class, 'paymentSuccess']); // 支付成功回调
    Route::post('/{id}/payment-failed', [OrderController::class, 'paymentFailed']); // 支付失败回调
});

// 需要员工权限的订单管理路由（员工系统）
Route::group(['prefix' => 'api/admin/orders', 'middleware' => ['api', 'auth:sanctum', 'employee.role:admin,manager,staff,warehouse_manager,crm_agent']], function () {
    Route::get('/', [OrderController::class, 'index']); // 员工查看所有订单列表
    Route::get('/stats', [OrderController::class, 'getStats']); // 员工查看订单统计
    Route::get('/{id}', [OrderController::class, 'show']); // 员工查看单个订单详情
    Route::post('/{id}/cancel', [OrderController::class, 'cancel']); // 管理员取消订单
    Route::get('/{id}/payment-records', [OrderController::class, 'getPaymentRecords']); // 获取订单支付记录

    // 🔥 新增：获取待确认订单列表（代客下单和货到付款订单）
    Route::get('/pending-confirmation', [OrderController::class, 'getPendingConfirmationOrders'])->middleware('employee.role:admin,manager,staff');

    // 🔥 新增：管理员人工确认订单（创建出库单）
    Route::post('/{id}/manual-confirm', [OrderController::class, 'manualConfirm'])->middleware('employee.role:admin,manager,staff');

    // 🤖 自动确认相关路由（仅管理员和经理）
    Route::post('/batch-auto-confirm', [OrderController::class, 'batchAutoConfirm'])->middleware('employee.role:admin,manager');
    Route::get('/auto-confirm-config', [OrderController::class, 'getAutoConfirmConfig'])->middleware('employee.role:admin,manager');
});

// 需要员工认证和CRM权限的代客下单路由
Route::group(['prefix' => 'api/orders', 'middleware' => ['api', 'auth:sanctum', 'employee.role:admin,manager,crm_agent']], function () {
    Route::post('/proxy', [OrderController::class, 'createForClient']);
    
    // 订单合并相关路由
    Route::prefix('merge')->group(function () {
        Route::post('/preview', [OrderMergeController::class, 'preview']);
        Route::post('/execute', [OrderMergeController::class, 'execute']);
        Route::get('/candidates', [OrderMergeController::class, 'candidates']);
        Route::post('/{merge}/revert', [OrderMergeController::class, 'revert']);
        Route::get('/history', [OrderMergeController::class, 'history']);
        Route::post('/auto-merge-today', [OrderMergeController::class, 'autoMergeToday']);
    });
});

// 订单更正管理 - 添加api前缀，调整路由顺序
Route::group(['prefix' => 'api'], function () {
    // 更正页面专用接口 - 放在前面，避免被通用路由拦截
    Route::get('corrections/orders/correctable', [OrderCorrectionController::class, 'getCorrectableOrders']);
    Route::get('corrections/filter-options', [OrderCorrectionController::class, 'getCorrectionFilterOptions']);
    Route::get('corrections/statistics', [OrderCorrectionController::class, 'statistics']);
    Route::get('corrections/export', [OrderCorrectionController::class, 'export']);

    // 🔥 新增：商品添加相关接口
    Route::get('corrections/products-for-correction', [OrderCorrectionController::class, 'getProductsForCorrection']);
    Route::get('corrections/category-tree', [OrderCorrectionController::class, 'getCategoryTree']);
    
    // 批量操作
    Route::post('corrections/batch-confirm', [OrderCorrectionController::class, 'batchConfirm']);
    Route::post('corrections/batch-cancel', [OrderCorrectionController::class, 'batchCancel']);
    
    // 基础CRUD - 放在后面
    Route::get('corrections', [OrderCorrectionController::class, 'index']);
    Route::post('corrections', [OrderCorrectionController::class, 'store']);
    Route::get('corrections/{id}', [OrderCorrectionController::class, 'show']);
    Route::put('corrections/{id}', [OrderCorrectionController::class, 'update']);
    Route::post('corrections/{id}/confirm', [OrderCorrectionController::class, 'confirm']);
    Route::post('corrections/{id}/cancel', [OrderCorrectionController::class, 'cancel']);
    Route::post('corrections/{id}/reverse', [OrderCorrectionController::class, 'reverse']);

    // 订单更正相关
    Route::get('orders/{orderId}/for-correction', [OrderCorrectionController::class, 'orderForCorrection']);
    Route::get('orders/{orderId}/correction-history', [OrderCorrectionController::class, 'orderHistory']);
}); 