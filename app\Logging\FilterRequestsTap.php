<?php

namespace App\Logging;

use Illuminate\Log\Logger;
use Monolog\Handler\HandlerInterface;
use Monolog\LogRecord;

/**
 * 过滤请求日志的 Tap 类
 * 正确实现 Laravel 日志 tap 接口
 */
class FilterRequestsTap
{
    /**
     * 需要过滤的路径模式
     */
    protected array $filteredPaths = [
        // 健康检查和监控
        '/health',
        '/ping',
        '/status',
        '/metrics',
        
        // 静态资源
        '/favicon.ico',
        '/robots.txt',
        '/sitemap.xml',
        
        // API心跳检测
        '/api/ping',
        '/api/health',
        '/api/status',
        
        // 微信小程序频繁请求
        '/api/wechat-mp/config',
        '/api/wechat-mp/user/info',
        
        // 其他频繁请求
        '/api/categories',
        '/api/banners',
    ];

    /**
     * 需要过滤的HTTP方法
     */
    protected array $filteredMethods = [
        'OPTIONS', // 预检请求
    ];

    /**
     * 需要过滤的状态码
     */
    protected array $filteredStatusCodes = [
        200, // 正常请求不记录
        304, // 未修改
    ];

    /**
     * 需要过滤的日志消息模式
     */
    protected array $filteredMessages = [
        'local.INFO: GET',
        'local.INFO: POST',
        'local.INFO: PUT',
        'local.INFO: DELETE',
        'production.INFO: GET',
        'production.INFO: POST',
        'production.INFO: PUT',
        'production.INFO: DELETE',
        'Matched route',
        'Route matched',
        'Request handled',
    ];

    /**
     * 自定义 Logger 实例
     * 这是 Laravel tap 的正确接口
     */
    public function __invoke(Logger $logger): void
    {
        // 添加一个自定义处理器来过滤日志
        $logger->pushProcessor(function (LogRecord $record): LogRecord {
            return $this->processRecord($record);
        });
    }

    /**
     * 处理日志记录
     */
    protected function processRecord(LogRecord $record): LogRecord
    {
        // 只过滤INFO级别的日志
        if ($record->level->value !== 200) { // INFO = 200
            return $record;
        }

        $message = $record->message;
        $context = $record->context;

        // 🔥 过滤请求相关的日志消息
        if ($this->shouldFilterMessage($message)) {
            // 返回一个空的日志记录，实际上不会被记录
            return $record->with(message: '');
        }

        // 🔥 过滤基于上下文的请求日志
        if ($this->shouldFilterByContext($context)) {
            return $record->with(message: '');
        }

        return $record;
    }

    /**
     * 检查是否应该过滤消息
     */
    protected function shouldFilterMessage(string $message): bool
    {
        foreach ($this->filteredMessages as $pattern) {
            if (str_contains($message, $pattern)) {
                return true;
            }
        }

        // 过滤包含URL路径的消息
        foreach ($this->filteredPaths as $path) {
            if (str_contains($message, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否应该基于上下文过滤
     */
    protected function shouldFilterByContext(array $context): bool
    {
        // 检查请求路径
        if (isset($context['url']) || isset($context['path'])) {
            $path = $context['url'] ?? $context['path'] ?? '';
            
            foreach ($this->filteredPaths as $filteredPath) {
                if (str_contains($path, $filteredPath)) {
                    return true;
                }
            }
        }

        // 检查HTTP方法
        if (isset($context['method'])) {
            if (in_array(strtoupper($context['method']), $this->filteredMethods)) {
                return true;
            }
        }

        // 检查状态码
        if (isset($context['status_code']) || isset($context['status'])) {
            $statusCode = $context['status_code'] ?? $context['status'] ?? null;
            
            if ($statusCode && in_array($statusCode, $this->filteredStatusCodes)) {
                return true;
            }
        }

        return false;
    }
}
