<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\UserAddress;
use Carbon\Carbon;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MigrateUserAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:user-addresses
                            {--dry-run : 只显示将要迁移的数据，不实际执行}
                            {--limit=100 : 限制迁移的地址数量}
                            {--offset=0 : 跳过的记录数}
                            {--batch-size=100 : 批量处理的数量}
                            {--all : 迁移所有地址数据}
                            {--fix : 修复之前迁移的问题}
                            {--user-id= : 只迁移指定用户ID的地址}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '迁移老数据库的用户地址到新系统';

    /**
     * 用户映射缓存
     */
    protected $userMapping = [];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始用户地址数据迁移...');
        
        $dryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');
        $offset = (int) $this->option('offset');
        $batchSize = (int) $this->option('batch-size');
        $all = $this->option('all');
        $fix = $this->option('fix');
        $userId = $this->option('user-id');
        
        if ($dryRun) {
            $this->warn('这是预览模式，不会实际修改数据');
        }
        
        try {
            // 测试老数据库连接
            $this->info('测试老数据库连接...');
            $oldConnection = DB::connection('mysql_old');
            $oldConnection->getPdo();
            $this->info('✅ 老数据库连接成功');
            
            // 预先加载用户映射关系
            $this->loadUserMapping();
            
            if ($userId) {
                // 只迁移指定用户的地址
                $this->info("仅迁移用户ID: {$userId} 的地址");
                $this->migrateAddressesForUser($userId, $dryRun);
            } else if ($all) {
                // 批量迁移所有地址
                $this->batchMigrateAllAddresses($dryRun, $batchSize);
            } else {
                // 迁移部分地址
                $addresses = $this->getAddressesFromOldSystem($limit, $offset);
                
                if (!empty($addresses)) {
                    $this->info("找到 " . count($addresses) . " 条地址记录需要迁移");
                    
                    if ($dryRun) {
                        $this->previewAddressMigration($addresses);
                    } else {
                        $this->migrateAddresses($addresses);
                    }
                } else {
                    $this->warn("没有找到需要迁移的地址数据");
                }
            }
            
            if ($fix && !$dryRun) {
                $this->fixPreviousMigrationIssues();
            }
            
            $this->info('✅ 用户地址数据迁移完成');
            
        } catch (\Exception $e) {
            $this->error("迁移失败: " . $e->getMessage());
            $this->error("错误位置: " . $e->getFile() . " 第 " . $e->getLine() . " 行");
            $this->error("堆栈跟踪: " . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 批量迁移所有地址
     */
    private function batchMigrateAllAddresses($dryRun, $batchSize)
    {
        $this->info('准备批量迁移所有用户地址数据...');
        
        // 获取总地址数量
        $totalCount = DB::connection('mysql_old')->table('zjhj_bd_address')
            ->where('is_delete', 0)
            ->count();
        
        if ($totalCount === 0) {
            $this->warn('没有找到需要迁移的地址数据');
            return;
        }
        
        $this->info("总共找到 {$totalCount} 条地址记录需要迁移");
        
        if ($dryRun) {
            $this->warn('预览模式下不执行批量迁移，请使用 --limit 选项预览部分数据');
            return;
        }
        
        if (!$this->confirm("确定要迁移所有 {$totalCount} 条地址记录吗？")) {
            $this->info('已取消迁移操作');
            return;
        }
        
        $offset = 0;
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        $bar = $this->output->createProgressBar($totalCount);
        $bar->start();
        
        // 创建错误日志文件
        $errorLogFile = storage_path('logs/address_migration_errors_'.date('Y-m-d_H-i-s').'.log');
        $errorLog = fopen($errorLogFile, 'w');
        fwrite($errorLog, "地址迁移错误日志 - ".date('Y-m-d H:i:s')."\n");
        fwrite($errorLog, "--------------------------------------\n\n");
        
        while ($offset < $totalCount) {
            $addresses = $this->getAddressesFromOldSystem($batchSize, $offset);
            
            if (empty($addresses)) {
                break;
            }
            
            foreach ($addresses as $address) {
                try {
                    $result = $this->migrateAddress($address);
                    
                    if ($result) {
                        $successCount++;
                    } else {
                        $skippedCount++;
                        // 记录跳过的地址
                        fwrite($errorLog, "跳过地址 ID {$address->id}: 未找到对应用户\n");
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    // 记录错误
                    fwrite($errorLog, "迁移地址 ID {$address->id} 失败: {$e->getMessage()}\n");
                    fwrite($errorLog, "堆栈跟踪: {$e->getTraceAsString()}\n\n");
                }
                
                $bar->advance();
            }
            
            $offset += $batchSize;
        }
        
        $bar->finish();
        $this->line('');
        
        fclose($errorLog);
        
        $this->info("✅ 地址迁移完成");
        $this->info("成功迁移: {$successCount}");
        $this->info("跳过迁移: {$skippedCount}");
        $this->info("迁移失败: {$errorCount}");
        
        if ($errorCount > 0) {
            $this->warn("错误详情请查看: {$errorLogFile}");
        }
    }
    
    /**
     * 修复之前迁移的问题
     */
    private function fixPreviousMigrationIssues()
    {
        $this->info('开始修复之前的迁移问题...');
        
        // 1. 检查并更新空地址问题
        $this->info('检查空地址字段...');
        $emptyCount = UserAddress::whereNull('address')
            ->orWhere('address', '')
            ->count();
            
        if ($emptyCount > 0) {
            $this->warn("发现 {$emptyCount} 条地址数据中的详细地址为空");
            if ($this->confirm('是否使用省市区信息填充空地址？')) {
                $updated = UserAddress::whereNull('address')
                    ->orWhere('address', '')
                    ->update([
                        'address' => DB::raw("CONCAT(IFNULL(province, ''), ' ', IFNULL(city, ''), ' ', IFNULL(district, ''))")
                    ]);
                $this->info("✅ 已更新 {$updated} 条地址记录");
            }
        }
        
        // 2. 检查重复地址
        $this->info('检查重复地址...');
        $duplicateAddresses = DB::table('user_addresses')
            ->select(DB::raw('user_id, contact_name, contact_phone, province, city, district, address, COUNT(*) as count'))
            ->groupBy('user_id', 'contact_name', 'contact_phone', 'province', 'city', 'district', 'address')
            ->having('count', '>', 1)
            ->get();
            
        if ($duplicateAddresses->count() > 0) {
            $this->warn("发现 {$duplicateAddresses->count()} 组重复地址");
            if ($this->confirm('是否删除重复地址，只保留最新的一条？')) {
                foreach ($duplicateAddresses as $duplicate) {
                    $addresses = UserAddress::where('user_id', $duplicate->user_id)
                        ->where('contact_name', $duplicate->contact_name)
                        ->where('contact_phone', $duplicate->contact_phone)
                        ->where('province', $duplicate->province)
                        ->where('city', $duplicate->city)
                        ->where('district', $duplicate->district)
                        ->where('address', $duplicate->address)
                        ->orderBy('created_at', 'desc')
                        ->get();
                        
                    // 保留最新的一条
                    $keepId = $addresses->first()->id;
                    foreach ($addresses as $address) {
                        if ($address->id != $keepId) {
                            $address->delete();
                        }
                    }
                }
                $this->info('✅ 重复地址清理完成');
            }
        } else {
            $this->info('没有发现重复地址');
        }
        
        // 3. 检查默认地址
        $this->info('检查默认地址设置...');
        $usersWithoutDefault = DB::table('users')
            ->leftJoin('user_addresses', function ($join) {
                $join->on('users.id', '=', 'user_addresses.user_id')
                    ->where('user_addresses.is_default', 1);
            })
            ->whereNull('user_addresses.id')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('user_addresses')
                    ->whereRaw('user_addresses.user_id = users.id');
            })
            ->select('users.id')
            ->get();
            
        if ($usersWithoutDefault->count() > 0) {
            $this->warn("发现 {$usersWithoutDefault->count()} 个用户有地址但没有设置默认地址");
            if ($this->confirm('是否为这些用户设置最新的地址为默认地址？')) {
                foreach ($usersWithoutDefault as $user) {
                    $latestAddress = UserAddress::where('user_id', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->first();
                        
                    if ($latestAddress) {
                        $latestAddress->is_default = true;
                        $latestAddress->save();
                    }
                }
                $this->info('✅ 默认地址设置完成');
            }
        } else {
            $this->info('默认地址设置正常');
        }
    }
    
    /**
     * 迁移指定用户的地址
     */
    private function migrateAddressesForUser($userId, $dryRun)
    {
        // 检查用户是否存在
        $user = User::find($userId);
        if (!$user) {
            $this->error("用户ID: {$userId} 不存在");
            return;
        }
        
        $this->info("查找用户 {$user->name} ({$user->openid}) 的旧地址数据...");
        
        // 通过openid查找老系统用户ID
        $oldUserId = null;
        if ($user->openid) {
            $oldUser = DB::connection('mysql_old')->table('zjhj_bd_user')
                ->where('username', $user->openid)
                ->first();
                
            if ($oldUser) {
                $oldUserId = $oldUser->id;
            }
        }
        
        // 如果找不到，尝试通过手机号查找
        if (!$oldUserId && $user->phone) {
            $oldUser = DB::connection('mysql_old')->table('zjhj_bd_user')
                ->where('mobile', $user->phone)
                ->first();
                
            if ($oldUser) {
                $oldUserId = $oldUser->id;
            }
        }
        
        if (!$oldUserId) {
            $this->warn("未找到用户 {$user->name} 在老系统中的对应记录");
            return;
        }
        
        // 查询老系统中的用户地址
        $addresses = DB::connection('mysql_old')->table('zjhj_bd_address')
            ->where('user_id', $oldUserId)
            ->where('is_delete', 0)
            ->get();
            
        if ($addresses->isEmpty()) {
            $this->warn("用户 {$user->name} 在老系统中没有地址记录");
            return;
        }
        
        $this->info("找到 " . $addresses->count() . " 条地址记录");
        
        if ($dryRun) {
            $this->previewAddressMigration($addresses);
        } else {
            foreach ($addresses as $address) {
                try {
                    $result = $this->migrateAddress($address, $user->id);
                    if ($result) {
                        // 获取地址数据
                        $name = is_object($address) ? $address->name : $address['name'] ?? '';
                        $mobile = is_object($address) ? $address->mobile : $address['mobile'] ?? '';
                        $province = is_object($address) ? $address->province : $address['province'] ?? '';
                        $city = is_object($address) ? $address->city : $address['city'] ?? '';
                        $district = is_object($address) ? $address->district : $address['district'] ?? '';
                        $detail = is_object($address) ? $address->detail : $address['detail'] ?? '';
                        
                        $this->info("✅ 地址迁移成功：{$name}, {$mobile}, {$province} {$city} {$district} {$detail}");
                    }
                } catch (\Exception $e) {
                    $this->error("迁移地址失败: " . $e->getMessage());
                }
            }
        }
    }
    
    /**
     * 加载用户映射关系
     */
    private function loadUserMapping()
    {
        $this->info('加载用户映射关系...');
        
        // 获取新系统中有openid的用户
        $newUsers = User::whereNotNull('openid')->get(['id', 'openid', 'phone']);
        
        foreach ($newUsers as $user) {
            if (!empty($user->openid)) {
                $this->userMapping['openid_' . $user->openid] = $user->id;
            }
            
            if (!empty($user->phone)) {
                $this->userMapping['phone_' . $user->phone] = $user->id;
            }
        }
        
        $this->info('✅ 已加载 ' . count($this->userMapping) . ' 个用户映射');
    }
    
    /**
     * 从老系统获取地址
     */
    private function getAddressesFromOldSystem($limit, $offset)
    {
        try {
            $this->info("从老系统获取地址数据...");

            // 获取地址数据
            $addresses = DB::connection('mysql_old')->table('zjhj_bd_address')
                ->where('is_delete', 0)
                ->orderBy('id')
                ->limit($limit)
                ->offset($offset)
                ->get();

            if ($addresses->isNotEmpty()) {
                $this->info("✅ 成功获取数据，记录数: " . $addresses->count());
                return $addresses;
            } else {
                $this->warn("没有找到地址数据");
                return [];
            }

        } catch (\Exception $e) {
            $this->error("获取地址数据失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 预览迁移数据
     */
    private function previewAddressMigration($addresses)
    {
        $this->info('=== 地址迁移预览 ===');
        
        $sampleCount = min(count($addresses), 5);
        
        // 处理前5条记录
        $counter = 0;
        foreach ($addresses as $index => $address) {
            if ($counter >= $sampleCount) break;
            
            // 根据类型处理数据
            $id = is_object($address) ? $address->id : $address['id'];
            $userId = is_object($address) ? $address->user_id : $address['user_id'];
            $name = is_object($address) ? $address->name : $address['name'];
            $mobile = is_object($address) ? $address->mobile : $address['mobile'];
            $province = is_object($address) ? $address->province : ($address['province'] ?? '');
            $city = is_object($address) ? $address->city : ($address['city'] ?? '');
            $district = is_object($address) ? $address->district : ($address['district'] ?? '');
            $detail = is_object($address) ? $address->detail : ($address['detail'] ?? '');
            $isDefault = is_object($address) ? $address->is_default : ($address['is_default'] ?? 0);
            
            // 查找对应的新用户ID
            $newUserId = $this->findNewUserId($userId);
            
            $this->info("地址 " . ($counter + 1) . ":");
            $this->line("  ID: {$id}");
            $this->line("  用户ID (老): {$userId}");
            $this->line("  用户ID (新): " . ($newUserId ? $newUserId : '未找到'));
            $this->line("  联系人: {$name}");
            $this->line("  联系电话: {$mobile}");
            $this->line("  地址: {$province} {$city} {$district} {$detail}");
            $this->line("  是否默认: " . ($isDefault ? '是' : '否'));
            $this->line('---');
            
            $counter++;
        }
        
        if (count($addresses) > 5) {
            $this->info('... 还有 ' . (count($addresses) - 5) . ' 个地址');
        }
    }
    
    /**
     * 批量迁移地址
     */
    private function migrateAddresses($addresses)
    {
        $this->info('开始迁移地址...');
        
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        
        foreach ($addresses as $address) {
            try {
                $result = $this->migrateAddress($address);
                
                if ($result) {
                    $successCount++;
                } else {
                    $skippedCount++;
                }
            } catch (\Exception $e) {
                $this->error("迁移地址 ID {$address->id} 失败: " . $e->getMessage());
                $errorCount++;
            }
        }
        
        $this->info("地址迁移结果:");
        $this->info("成功: {$successCount}");
        $this->info("跳过: {$skippedCount}");
        $this->info("失败: {$errorCount}");
    }
    
    /**
     * 迁移单个地址
     */
    private function migrateAddress($address, $forcedUserId = null)
    {
        // 获取地址数据（处理对象或数组）
        $id = is_object($address) ? $address->id : $address['id'];
        $oldUserId = is_object($address) ? $address->user_id : $address['user_id'];
        $name = is_object($address) ? $address->name : $address['name'] ?? '';
        $mobile = is_object($address) ? $address->mobile : $address['mobile'] ?? '';
        $province = is_object($address) ? $address->province : $address['province'] ?? null;
        $city = is_object($address) ? $address->city : $address['city'] ?? null;
        $district = is_object($address) ? $address->district : $address['district'] ?? null;
        $detail = is_object($address) ? $address->detail : $address['detail'] ?? '';
        $isDefault = is_object($address) ? $address->is_default : $address['is_default'] ?? 0;
        $latitude = is_object($address) ? $address->latitude : $address['latitude'] ?? null;
        $longitude = is_object($address) ? $address->longitude : $address['longitude'] ?? null;
        $location = is_object($address) ? ($address->location ?? '') : ($address['location'] ?? '');
        $type = is_object($address) ? ($address->type ?? null) : ($address['type'] ?? null);
        $createdAt = is_object($address) ? ($address->created_at ?? null) : ($address['created_at'] ?? null);
        
        // 获取新系统中的用户ID
        $newUserId = $forcedUserId ?: $this->findNewUserId($oldUserId);
        
        if (!$newUserId) {
            return false;
        }
        
        // 检查地址是否已存在
        $existingAddress = UserAddress::where('user_id', $newUserId)
            ->where(function($query) use ($name, $mobile, $province, $city, $district, $detail) {
                // 匹配相同的联系信息和地址
                $query->where('contact_name', $name)
                    ->where('contact_phone', $mobile)
                    ->where('province', $province)
                    ->where('city', $city)
                    ->where('district', $district)
                    ->where('address', $detail);
            })
            ->first();
            
        if ($existingAddress) {
            // 如果已存在相同地址，更新为最新的数据
            if ($isDefault) {
                $existingAddress->is_default = true;
                $existingAddress->save();
                
                // 更新其他地址为非默认
                UserAddress::where('user_id', $newUserId)
                    ->where('id', '!=', $existingAddress->id)
                    ->update(['is_default' => false]);
            }
            return true;
        }
        
        // 构建备注信息
        $notes = [];
        if (!empty($location)) {
            $notes[] = "位置信息: {$location}";
        }
        if ($type !== null) {
            $addressType = $type == 0 ? '快递' : '同城';
            $notes[] = "地址类型: {$addressType}";
        }
        
        // 处理经纬度值
        $latitude = !empty($latitude) ? $latitude : null;
        $longitude = !empty($longitude) ? $longitude : null;
        
        // 确保详细地址不为空
        $finalDetail = !empty($detail) ? $detail : 
                (!empty($province) ? $province . ' ' : '') .
                (!empty($city) ? $city . ' ' : '') .
                (!empty($district) ? $district : '');
        
        // 映射地址字段
        $addressData = [
            'user_id' => $newUserId,
            'contact_name' => $name ?: '',
            'contact_phone' => $mobile ?: '',
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'address' => $finalDetail,
            'postal_code' => null,
            'notes' => !empty($notes) ? implode(', ', $notes) : null,
            'is_default' => $isDefault ?: 0,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'created_at' => !empty($createdAt) && $createdAt != '0000-00-00 00:00:00' ? 
                Carbon::parse($createdAt) : now(),
            'updated_at' => now(),
        ];
        
        // 创建新地址
        $newAddress = UserAddress::create($addressData);
        
        // 如果是默认地址，更新其他地址为非默认
        if ($isDefault) {
            UserAddress::where('user_id', $newUserId)
                ->where('id', '!=', $newAddress->id)
                ->update(['is_default' => false]);
        }
        
        return true;
    }
    
    /**
     * 在新系统中查找对应的用户ID
     */
    private function findNewUserId($oldUserId)
    {
        // 查找老系统中的用户
        $oldUser = DB::connection('mysql_old')->table('zjhj_bd_user')
            ->where('id', $oldUserId)
            ->first();
            
        if (!$oldUser) {
            return null;
        }
        
        // 通过openid查找
        if (!empty($oldUser->username) && isset($this->userMapping['openid_' . $oldUser->username])) {
            return $this->userMapping['openid_' . $oldUser->username];
        }
        
        // 通过手机号查找
        if (!empty($oldUser->mobile) && isset($this->userMapping['phone_' . $oldUser->mobile])) {
            return $this->userMapping['phone_' . $oldUser->mobile];
        }
        
        // 如果缓存中没有，尝试直接从数据库查找
        if (!empty($oldUser->username)) {
            $user = User::where('openid', $oldUser->username)->first();
            if ($user) {
                $this->userMapping['openid_' . $oldUser->username] = $user->id;
                return $user->id;
            }
        }
        
        if (!empty($oldUser->mobile)) {
            $user = User::where('phone', $oldUser->mobile)->first();
            if ($user) {
                $this->userMapping['phone_' . $oldUser->mobile] = $user->id;
                return $user->id;
            }
        }
        
        return null;
    }
}
