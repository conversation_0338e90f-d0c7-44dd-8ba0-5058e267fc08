<?php

namespace App\Billing\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BillAdjustmentResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'adjustment_no' => $this->adjustment_no,
            'adjustment_type' => $this->adjustment_type,
            'adjustment_type_text' => $this->adjustment_type_text,
            'amount_before' => $this->amount_before,
            'amount_after' => $this->amount_after,
            'adjustment_amount' => $this->adjustment_amount,
            'reason' => $this->reason,
            'adjustment_details' => $this->adjustment_details,
            'affected_items' => $this->affected_items,
            'status' => $this->status,
            'status_text' => $this->status_text,

            // 🔥 新增：前端期望的字段映射
            'description' => $this->adjustment_details, // 前端期望 description，后端是 adjustment_details
            'applied_at' => $this->adjusted_at?->format('Y-m-d H:i:s'), // 前端期望 applied_at，后端是 adjusted_at
            'operator_id' => $this->operator_id, // 前端期望直接的 operator_id

            // 时间信息
            'adjusted_at' => $this->adjusted_at?->format('Y-m-d H:i:s'),
            'approved_at' => $this->approved_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),

            // 操作员信息
            'adjuster' => $this->whenLoaded('adjuster', function () {
                return [
                    'id' => $this->adjuster->id,
                    'name' => $this->adjuster->name,
                ];
            }),

            // 审批人信息
            'approver' => $this->whenLoaded('approver', function () {
                return [
                    'id' => $this->approver->id,
                    'name' => $this->approver->name,
                ];
            }),

            // 业务逻辑状态
            'is_increasing' => $this->isIncreasing(),
            'is_decreasing' => $this->isDecreasing(),
            'can_be_approved' => $this->canBeApproved(),
            'can_be_applied' => $this->canBeApplied(),
        ];
    }
} 