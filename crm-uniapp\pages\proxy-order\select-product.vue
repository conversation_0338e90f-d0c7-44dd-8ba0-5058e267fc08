<template>
	<view class="select-product-container">
		<!-- 搜索栏 -->
		<view class="search-header">
			<view class="search-box">
				<input
					class="search-input"
					type="text"
					placeholder="搜索商品名称"
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="onSearchConfirm"
				/>
				<button class="clear-btn" @tap="clearSearch" v-if="searchKeyword">×</button>
			</view>
		</view>

		<!-- 🌍 客户和区域信息 -->
		<view class="client-region-bar" v-if="selectedClient">
			<view class="client-info">
				<text class="client-name">👤 {{ selectedClient.name }}</text>
				<text class="region-status" v-if="clientRegionId">
					🌍 区域ID: {{ clientRegionId }}
				</text>
				<text class="region-status no-region" v-else>
					⚠️ 无区域信息
				</text>
			</view>
			<button class="manual-region-btn" @tap="showManualRegionDialog" v-if="!clientRegionId">
				手动设置
			</button>
		</view>
		
		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 左侧分类树 -->
			<view class="category-sidebar">
				<scroll-view class="category-scroll" scroll-y="true">
					<!-- 全部分类 -->
					<view
						class="category-item level-0"
						:class="{ active: selectedCategoryId === null }"
						@tap="selectCategory(null)"
					>
						<text class="category-name">全部</text>
					</view>

					<!-- 一级分类 -->
					<view v-for="category1 in categoryTree" :key="category1.id" class="category-group">
						<view
							class="category-item level-1"
							:class="{
								active: selectedCategoryId === category1.id,
								expanded: category1.expanded
							}"
							@tap="toggleCategory(category1)"
						>
							<text class="category-name">{{ category1.name }}</text>
							<text class="expand-icon" v-if="category1.children && category1.children.length > 0">
								{{ category1.expanded ? '−' : '+' }}
							</text>
						</view>

						<!-- 二级分类 -->
						<view v-if="category1.expanded && category1.children" class="sub-categories">
							<view v-for="category2 in category1.children" :key="category2.id" class="category-group">
								<view
									class="category-item level-2"
									:class="{
										active: selectedCategoryId === category2.id,
										expanded: category2.expanded
									}"
									@tap="toggleCategory(category2)"
								>
									<text class="category-name">{{ category2.name }}</text>
									<text class="expand-icon" v-if="category2.children && category2.children.length > 0">
										{{ category2.expanded ? '−' : '+' }}
									</text>
								</view>

								<!-- 三级分类 -->
								<view v-if="category2.expanded && category2.children" class="sub-categories">
									<view
										v-for="category3 in category2.children"
										:key="category3.id"
										class="category-item level-3"
										:class="{ active: selectedCategoryId === category3.id }"
										@tap="selectCategory(category3.id)"
									>
										<text class="category-name">{{ category3.name }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 右侧商品列表 -->
			<scroll-view
				class="product-scroll"
				scroll-y="true"
				@scrolltolower="loadMore"
				refresher-enabled="true"
				@refresherrefresh="onRefresh"
				:refresher-triggered="refreshing"
			>
			<!-- 已选商品统计 -->
			<view class="selected-summary" v-if="selectedProducts.length > 0">
				<view class="summary-info">
					<text class="summary-text">已选 {{ selectedProducts.length }} 种商品</text>
					<text class="summary-amount">¥{{ getTotalAmount() }}</text>
				</view>
				<button class="cart-btn" @tap="toggleCartView">{{ showCart ? '隐藏' : '查看' }}购物车</button>
			</view>
			
			<!-- 购物车视图 -->
			<view class="cart-section" v-if="showCart && selectedProducts.length > 0">
				<view class="cart-header">
					<text class="cart-title">购物车</text>
					<button class="clear-cart-btn" @tap="clearCart">清空</button>
				</view>
				<view class="cart-list">
					<view class="cart-item" v-for="(product, index) in selectedProducts" :key="product.id">
						<image class="cart-product-image" :src="getProductImageUrl(product)" mode="aspectFill" @error="(e) => handleImageError(e, product)"></image>

						<!-- 购物车商品详情 -->
						<view class="cart-product-details">
							<!-- 商品名称 - 单独一行 -->
							<view class="cart-name-row">
								<text class="cart-product-name">{{ product.name }}</text>
								<button class="remove-btn" @tap="removeFromCart(index)">
									<text class="remove-icon">🗑️</text>
								</button>
							</view>

							<!-- 价格信息行 -->
							<view class="cart-price-row">
								<view class="cart-price-info">
									<text class="cart-product-price" v-if="product.region_price_info && product.region_price_info.final_price && product.region_price_info.final_price !== product.price">
										¥{{ formatPrice(product.region_price_info.final_price) }}/{{ getSaleUnitDisplay(product) }}
									</text>
									<text class="cart-product-price" v-else>
										¥{{ formatPrice(product.price) }}/{{ getSaleUnitDisplay(product) }}
									</text>
									<!-- 价格类型标签 -->
									<view class="cart-price-badges" v-if="product.region_price_info && product.region_price_info.final_price && product.region_price_info.final_price !== product.price">
										<text class="cart-price-badge member-badge" v-if="product.region_price_info.has_member_discount">会员价</text>
										<text class="cart-price-badge region-badge" v-if="product.region_price_info.has_region_price">区域价</text>
									</view>
								</view>
							</view>

							<!-- 数量控制器 - 右下角单独一行 -->
							<view class="cart-quantity-row">
								<view class="cart-quantity-control">
									<button class="cart-quantity-btn decrease" @tap="decreaseCartQuantity(index)">−</button>
									<input
										class="cart-quantity-input"
										type="number"
										:value="product.quantity"
										@input="onCartQuantityInput($event, index)"
										@blur="onCartQuantityBlur($event, index)"
									/>
									<button class="cart-quantity-btn increase" @tap="increaseCartQuantity(index)">+</button>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 商品列表 -->
			<view class="product-list" v-if="productList.length > 0">
				<view
					class="product-card"
					v-for="product in productList"
					:key="product.id"
				>
					<!-- 商品信息 -->
					<view class="product-content" :class="{ 'out-of-stock': isOutOfStock(product) }">
						<image
							:src="getProductImageUrl(product)"
							mode="aspectFill"
							class="product-image"
							@error="(e) => handleImageError(e, product)"
						/>

						<!-- 商品详情区域 -->
						<view class="product-details">
							<!-- 商品名称 - 单独一行 -->
							<view class="product-name-row">
								<text class="product-name">{{ product.name }}</text>
							</view>

							<!-- 价格和库存信息行 -->
							<view class="product-info-row">
								<!-- 🌍 价格显示 - 支持会员优惠 -->
								<view class="product-price-section">
									<view class="product-price-row" v-if="product.region_price_info && product.region_price_info.final_price && product.region_price_info.final_price !== product.price">
										<!-- 有优惠价格（区域价或会员价） -->
										<view class="price-main">
											<text class="current-price" :class="getPriceClass(product.region_price_info)">¥{{ formatPrice(product.region_price_info.final_price) }}</text>
											<text class="price-unit">/{{ getSaleUnitDisplay(product) }}</text>
											<!-- 价格标签 -->
											<view class="price-badges">
												<text class="price-badge member-badge" v-if="product.region_price_info.has_member_discount">会员价</text>
												<text class="price-badge region-badge" v-if="product.region_price_info.has_region_price">区域价</text>
											</view>
										</view>
										<view class="price-extra">
											<text class="original-price">原价¥{{ formatPrice(product.price) }}</text>
											<text class="discount-badge">{{ getDiscountText(product.region_price_info) }}</text>
											<!-- 会员折扣详情 -->
											<text class="member-discount-detail" v-if="product.region_price_info.member_discount_info">
												会员优惠¥{{ formatPrice(product.region_price_info.member_discount_info.discount_amount) }}
											</text>
										</view>
									</view>
									<view class="product-price-row" v-else>
										<!-- 普通价格 -->
										<view class="price-main">
											<text class="current-price">¥{{ formatPrice(product.price) }}</text>
											<text class="price-unit">/{{ getSaleUnitDisplay(product) }}</text>
										</view>
									</view>
								</view>

								<!-- 库存信息 -->
								<view class="stock-info">
									<text class="stock-text" v-if="!isOutOfStock(product)">
										库存: {{ getStockText(product) }}
									</text>
									<text class="out-of-stock-text" v-else>缺货</text>
								</view>
							</view>

							<!-- 加购按钮/数量选择器 - 右下角单独一行 -->
							<view class="quantity-row">
								<!-- 当商品未加购时显示加购按钮 -->
								<button
									class="add-to-cart-btn"
									v-if="getProductQuantity(product.id) === 0"
									@tap="addToCart(product)"
									:disabled="isOutOfStock(product)"
									:class="{ 'disabled': isOutOfStock(product) }"
								>
									<text class="add-btn-text">{{ isOutOfStock(product) ? '缺货' : '加购' }}</text>
								</button>

								<!-- 当商品已加购时显示数量选择器 -->
								<view class="quantity-selector" v-else>
									<button
										class="quantity-btn decrease"
										@tap="decreaseQuantity(product)"
									>−</button>
									<input
										class="quantity-input"
										type="number"
										:value="getProductQuantity(product.id)"
										@input="onQuantityInput($event, product)"
										@blur="onQuantityBlur($event, product)"
									/>
									<button
										class="quantity-btn increase"
										@tap="increaseQuantity(product)"
										:disabled="isOutOfStock(product)"
										:class="{ 'disabled': isOutOfStock(product) }"
									>+</button>
								</view>
							</view>
						</view>

						<!-- 缺货遮罩 -->
						<view class="out-of-stock-mask" v-if="isOutOfStock(product)">
							<text class="mask-text">缺货</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-section" v-if="listLoading && productList.length === 0">
				<view class="loading-content">
					<view class="loading-spinner"></view>
					<text class="loading-text">正在加载商品列表...</text>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-section" v-if="!listLoading && productList.length === 0">
				<text class="empty-text">{{ searchKeyword ? '未找到相关商品' : '暂无商品数据' }}</text>
			</view>
			
			<!-- 分页加载 -->
			<view class="pagination-section" v-if="hasMore && !listLoading && productList.length > 0">
				<button class="load-more-btn" @tap="loadMore">加载更多</button>
			</view>
		</scroll-view>
	</view>

	<!-- 底部操作栏 -->
		<view class="bottom-actions" v-if="selectedProducts.length > 0">
			<view class="action-info">
				<text class="total-text">共 {{ getTotalQuantity() }} 件</text>
				<text class="total-amount">¥{{ getTotalAmount() }}</text>
			</view>
			<button class="confirm-btn" @tap="confirmSelection">
				<text class="confirm-text">确认选择</text>
			</button>
		</view>

		<!-- 🔧 手动区域设置对话框 -->
		<view class="region-dialog-overlay" v-if="showRegionDialog" @tap="hideRegionDialog">
			<view class="region-dialog" @tap.stop>
				<view class="dialog-header">
					<text class="dialog-title">选择区域</text>
					<button class="dialog-close" @tap="hideRegionDialog">×</button>
				</view>
				<view class="dialog-content">
					<text class="dialog-desc">为客户手动设置区域以启用区域价格功能</text>
					<view class="region-list">
						<view
							class="region-item"
							v-for="region in availableRegions"
							:key="region.id"
							@tap="selectManualRegion(region)"
						>
							<text class="region-name">{{ region.name }}</text>
							<text class="region-id">ID: {{ region.id }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import productApi from '../../api/product.js'
import request from '../../utils/request.js'

export default {
	data() {
		return {
			searchKeyword: '',
			productList: [],
			selectedProducts: [], // 已选择的商品列表
			showCart: false, // 是否显示购物车
			listLoading: false,
			hasMore: true,
			currentPage: 1,
			pageSize: 20,
			searchTimer: null,
			refreshing: false,
			// 分类相关
			categoryTree: [], // 分类树结构
			selectedCategoryId: null,
			categoryLoading: false,
			// 🌍 客户信息（用于区域价格计算）
			selectedClient: null,
			clientRegionId: null,

			// 🔧 手动区域设置
			manualRegionId: '',
			showRegionDialog: false,
			availableRegions: [
				{ id: 1, name: '成都市' },
				{ id: 2, name: '重庆市' },
				{ id: 87, name: '四川省' },
				{ id: 88, name: '云南省' },
				{ id: 89, name: '贵州省' }
			]
		}
	},
	
	onLoad() {
		console.log('🌍 商品选择页面加载开始')
		// 🌍 获取客户信息（从代客下单页面传递）
		this.getSelectedClientInfo()
		this.loadCategories()
		this.loadProducts()
	},

	onShow() {
		console.log('🌍 商品选择页面显示')
		// 重新检查客户信息
		this.getSelectedClientInfo()
	},
	
	onPullDownRefresh() {
		this.refreshProducts()
	},
	
	onReachBottom() {
		if (this.hasMore && !this.listLoading) {
			this.loadMore()
		}
	},
	
	methods: {
		// 🌍 获取选中的客户信息
		getSelectedClientInfo() {
			try {
				// 从代客下单页面获取客户信息
				const pages = getCurrentPages()
				const proxyOrderPage = pages.find(page => page.route.includes('proxy-order'))

				if (proxyOrderPage && proxyOrderPage.data && proxyOrderPage.data.selectedClient) {
					this.selectedClient = proxyOrderPage.data.selectedClient
					this.clientRegionId = this.selectedClient.region_id

					// 🔧 如果没有region_id，尝试基于地区信息自动匹配
					if (!this.clientRegionId) {
						this.clientRegionId = this.autoMatchRegionByLocation(this.selectedClient)
						if (this.clientRegionId) {
							this.selectedClient.region_id = this.clientRegionId
							console.log('🔧 自动匹配区域成功:', this.clientRegionId)
						}
					}

					console.log('🌍 获取到客户信息:', {
						client_id: this.selectedClient.id,
						client_name: this.selectedClient.name,
						region_id: this.clientRegionId,
						auto_matched: !proxyOrderPage.data.selectedClient.region_id && this.clientRegionId
					})
				} else {
					console.log('⚠️ 未找到客户信息，将使用基础价格')
				}
			} catch (error) {
				console.error('❌ 获取客户信息失败:', error)
			}
		},

		// 🌍 获取商品的区域价格信息
		async getProductRegionPrice(product) {
			if (!this.selectedClient || !this.clientRegionId) {
				return null
			}

			try {
				// 调用价格计算API
				const response = await productApi.calculatePrice({
					product_id: product.id,
					client_id: this.selectedClient.id,
					region_id: this.clientRegionId,
					quantity: 1
				})

				if (response && response.data) {
					console.log(`🌍 商品 ${product.name} 价格计算结果:`, response.data)
					return response.data
				}
			} catch (error) {
				console.error('❌ 获取区域价格失败:', error)
			}

			return null
		},

		// 🌍 批量获取商品区域价格信息
		async batchGetProductRegionPrices(products) {
			if (!this.selectedClient || !this.clientRegionId || !products.length) {
				return {}
			}

			console.log('🌍 开始批量获取区域价格，客户区域ID:', this.clientRegionId)
			console.log('🔧 使用CRM专用价格API')

			try {
				const response = await productApi.batchCalculatePrice({
					product_ids: products.map(p => p.id),
					client_id: this.selectedClient.id,
					region_id: this.clientRegionId
				})

				if (response && response.data && response.data.items) {
					// 转换响应格式为 product_id -> pricing 的映射
					const priceMap = {}
					response.data.items.forEach(item => {
						if (item.pricing) {
							priceMap[item.product_id] = item.pricing
						}
					})

					console.log('🌍 批量价格计算结果:', priceMap)
					return priceMap
				}
			} catch (error) {
				console.error('❌ 批量获取区域价格失败:', error)
			}

			return {}
		},

		// 🌍 获取折扣文本
		getDiscountText(regionPriceInfo) {
			if (!regionPriceInfo || regionPriceInfo.final_price >= regionPriceInfo.base_price) {
				return ''
			}

			const discount = regionPriceInfo.base_price - regionPriceInfo.final_price

			// 如果有total_discount字段，优先使用
			const actualDiscount = regionPriceInfo.total_discount || discount

			return `省¥${actualDiscount.toFixed(2)}`
		},

		// 🌍 获取价格CSS类
		getPriceClass(priceInfo) {
			if (!priceInfo) return ''

			const classes = []
			if (priceInfo.has_member_discount) {
				classes.push('member-price')
			}
			if (priceInfo.has_region_price) {
				classes.push('region-price')
			}

			return classes.join(' ')
		},

		// 🌍 优化折扣文本显示
		getDiscountText(priceInfo) {
			if (!priceInfo || priceInfo.final_price >= (priceInfo.base_price || priceInfo.original_price)) {
				return ''
			}

			const basePrice = priceInfo.base_price || priceInfo.original_price || 0
			const totalDiscount = basePrice - priceInfo.final_price

			if (totalDiscount <= 0) return ''

			// 如果有会员折扣信息，显示详细信息
			if (priceInfo.member_discount_info && priceInfo.member_discount_info.discount_amount > 0) {
				const memberDiscount = priceInfo.member_discount_info.discount_amount
				const regionDiscount = totalDiscount - memberDiscount

				if (regionDiscount > 0) {
					return `省¥${totalDiscount.toFixed(2)}(含会员优惠¥${memberDiscount.toFixed(2)})`
				} else {
					return `会员优惠¥${memberDiscount.toFixed(2)}`
				}
			}

			return `省¥${totalDiscount.toFixed(2)}`
		},

		// 🔧 显示手动区域设置对话框
		showManualRegionDialog() {
			console.log('🔧 显示手动区域设置对话框')
			this.showRegionDialog = true
		},

		// 🔧 隐藏手动区域设置对话框
		hideRegionDialog() {
			console.log('🔧 隐藏手动区域设置对话框')
			this.showRegionDialog = false
		},

		// 🔧 选择手动区域
		async selectManualRegion(region) {
			console.log('🔧 手动选择区域:', region)

			try {
				// 设置区域ID
				this.clientRegionId = region.id
				this.manualRegionId = region.id

				// 更新客户信息
				if (this.selectedClient) {
					this.selectedClient.region_id = region.id
					this.selectedClient.region_name = region.name
				}

				// 隐藏对话框
				this.hideRegionDialog()

				// 显示成功提示
				uni.showToast({
					title: `已设置区域: ${region.name}`,
					icon: 'success',
					duration: 2000
				})

				// 重新加载商品价格
				if (this.productList.length > 0) {
					console.log('🔧 重新获取区域价格...')
					await this.refreshRegionPrices()
				}

			} catch (error) {
				console.error('🔧 设置区域失败:', error)
				uni.showToast({
					title: '设置区域失败',
					icon: 'error'
				})
			}
		},

		// 🔧 刷新区域价格
		async refreshRegionPrices() {
			if (!this.selectedClient || !this.clientRegionId || this.productList.length === 0) {
				console.log('🔧 刷新区域价格条件不满足')
				return
			}

			try {
				console.log('🔧 开始刷新区域价格...')
				const regionPrices = await this.batchGetProductRegionPrices(this.productList)

				// 更新商品价格信息
				this.productList = this.productList.map(product => {
					if (regionPrices[product.id]) {
						product.region_price_info = regionPrices[product.id]
						console.log(`🔧 更新商品 ${product.name} 区域价格:`, regionPrices[product.id])
					}
					return product
				})

				// 强制更新视图
				this.$forceUpdate()

				console.log('🔧 区域价格刷新完成')

			} catch (error) {
				console.error('🔧 刷新区域价格失败:', error)
			}
		},

		// 🔧 获取销售单位显示文本
		getSaleUnitDisplay(product) {
			// 必须使用商品的销售单位名称
			if (product.sale_unit && product.sale_unit.name) {
				return product.sale_unit.name
			}
			// 如果没有销售单位，返回空或提示
			return product.sale_unit ? (product.sale_unit.name || '') : ''
		},

		// 🔧 基于地区信息自动匹配区域ID
		autoMatchRegionByLocation(client) {
			if (!client) return null

			// 地区到区域ID的映射表
			const locationToRegionMap = {
				// 四川省
				'成都': 1,
				'成都市': 1,
				'锦江区': 1,
				'青羊区': 1,
				'金牛区': 1,
				'武侯区': 1,
				'成华区': 1,
				'龙泉驿区': 1,
				'青白江区': 1,
				'新都区': 1,
				'温江区': 1,
				'双流区': 1,
				'郫都区': 1,
				'四川': 87,
				'四川省': 87,

				// 重庆市
				'重庆': 2,
				'重庆市': 2,
				'渝中区': 2,
				'江北区': 2,
				'南岸区': 2,
				'九龙坡区': 2,
				'沙坪坝区': 2,
				'大渡口区': 2,
				'渝北区': 2,
				'巴南区': 2,

				// 云南省
				'云南': 88,
				'云南省': 88,
				'昆明': 88,
				'昆明市': 88,

				// 贵州省
				'贵州': 89,
				'贵州省': 89,
				'贵阳': 89,
				'贵阳市': 89
			}

			// 检查各个地区字段
			const locationFields = [
				client.district,
				client.city,
				client.province,
				client.merchant_name
			]

			for (const field of locationFields) {
				if (field) {
					// 直接匹配
					if (locationToRegionMap[field]) {
						console.log(`🔧 基于 ${field} 匹配到区域ID: ${locationToRegionMap[field]}`)
						return locationToRegionMap[field]
					}

					// 模糊匹配
					for (const [location, regionId] of Object.entries(locationToRegionMap)) {
						if (field.includes(location) || location.includes(field)) {
							console.log(`🔧 基于 ${field} 模糊匹配到区域ID: ${regionId}`)
							return regionId
						}
					}
				}
			}

			console.log('🔧 无法自动匹配区域，客户地区信息:', {
				district: client.district,
				city: client.city,
				province: client.province,
				merchant_name: client.merchant_name
			})

			return null
		},



		// 加载分类树
		async loadCategories() {
			if (this.categoryLoading) return

			this.categoryLoading = true
			try {
				// 尝试多个分类API端点
				let response = null

				// 首先尝试公共分类树API
				try {
					console.log('🌲 尝试公共分类树API...')
					response = await request.get('/public/categories/tree')
					console.log('🌲 公共分类树API响应:', response)
				} catch (treeError) {
					console.log('🌲 公共分类树API失败，尝试普通分类API...')
					// 如果树API失败，尝试普通分类API
					response = await request.get('/public/categories')
					console.log('🌲 普通分类API响应:', response)
				}

				if (response && (response.code === 200 || response.code === 0)) {
					const categories = response.data || []
					console.log('🌲 原始分类数据:', categories)
					console.log('🌲 分类数据长度:', categories.length)

					if (categories.length > 0) {
						// 检查第一个分类的结构
						if (categories[0]) {
							console.log('🌲 第一个分类结构:', {
								id: categories[0].id,
								name: categories[0].name,
								hasChildren: !!categories[0].children,
								hasChildrenData: !!categories[0].children_data,
								childrenCount: (categories[0].children || []).length,
								childrenDataCount: (categories[0].children_data || []).length
							})
						}

						this.categoryTree = this.buildCategoryTree(categories)
						console.log('🌲 分类树构建成功:', this.categoryTree)
						console.log('🌲 构建后第一个分类:', this.categoryTree[0])
					} else {
						console.log('🌲 分类数据为空，使用模拟数据')
						this.categoryTree = this.getMockCategories()
					}
				} else {
					console.error('🌲 分类加载失败:', response)
					this.categoryTree = this.getMockCategories()
				}
			} catch (error) {
				console.error('🌲 分类加载异常:', error)
				this.categoryTree = this.getMockCategories()
			} finally {
				this.categoryLoading = false
			}
		},

		// 获取模拟分类数据（临时使用）
		getMockCategories() {
			return [
				{
					id: 1,
					name: '蔬菜类',
					expanded: false,
					children: [
						{
							id: 11,
							name: '叶菜类',
							expanded: false,
							children: [
								{ id: 111, name: '白菜', expanded: false, children: [] },
								{ id: 112, name: '菠菜', expanded: false, children: [] },
								{ id: 113, name: '生菜', expanded: false, children: [] },
								{ id: 114, name: '小白菜', expanded: false, children: [] }
							]
						},
						{
							id: 12,
							name: '根茎类',
							expanded: false,
							children: [
								{ id: 121, name: '萝卜', expanded: false, children: [] },
								{ id: 122, name: '胡萝卜', expanded: false, children: [] },
								{ id: 123, name: '土豆', expanded: false, children: [] },
								{ id: 124, name: '红薯', expanded: false, children: [] }
							]
						},
						{
							id: 13,
							name: '茄果类',
							expanded: false,
							children: [
								{ id: 131, name: '番茄', expanded: false, children: [] },
								{ id: 132, name: '茄子', expanded: false, children: [] },
								{ id: 133, name: '青椒', expanded: false, children: [] }
							]
						}
					]
				},
				{
					id: 2,
					name: '水果类',
					expanded: false,
					children: [
						{
							id: 21,
							name: '苹果类',
							expanded: false,
							children: [
								{ id: 211, name: '红富士', expanded: false, children: [] },
								{ id: 212, name: '青苹果', expanded: false, children: [] },
								{ id: 213, name: '黄元帅', expanded: false, children: [] }
							]
						},
						{
							id: 22,
							name: '柑橘类',
							expanded: false,
							children: [
								{ id: 221, name: '橙子', expanded: false, children: [] },
								{ id: 222, name: '柚子', expanded: false, children: [] },
								{ id: 223, name: '柠檬', expanded: false, children: [] }
							]
						},
						{
							id: 23,
							name: '热带水果',
							expanded: false,
							children: [
								{ id: 231, name: '香蕉', expanded: false, children: [] },
								{ id: 232, name: '芒果', expanded: false, children: [] },
								{ id: 233, name: '菠萝', expanded: false, children: [] }
							]
						}
					]
				},
				{
					id: 3,
					name: '肉类',
					expanded: false,
					children: [
						{
							id: 31,
							name: '猪肉类',
							expanded: false,
							children: [
								{ id: 311, name: '五花肉', expanded: false, children: [] },
								{ id: 312, name: '瘦肉', expanded: false, children: [] },
								{ id: 313, name: '排骨', expanded: false, children: [] }
							]
						},
						{
							id: 32,
							name: '牛肉类',
							expanded: false,
							children: [
								{ id: 321, name: '牛腩', expanded: false, children: [] },
								{ id: 322, name: '牛腱', expanded: false, children: [] }
							]
						},
						{
							id: 33,
							name: '禽肉类',
							expanded: false,
							children: [
								{ id: 331, name: '鸡肉', expanded: false, children: [] },
								{ id: 332, name: '鸭肉', expanded: false, children: [] }
							]
						}
					]
				},
				{
					id: 4,
					name: '海鲜类',
					expanded: false,
					children: [
						{
							id: 41,
							name: '鱼类',
							expanded: false,
							children: [
								{ id: 411, name: '草鱼', expanded: false, children: [] },
								{ id: 412, name: '鲫鱼', expanded: false, children: [] }
							]
						},
						{
							id: 42,
							name: '虾蟹类',
							expanded: false,
							children: [
								{ id: 421, name: '基围虾', expanded: false, children: [] },
								{ id: 422, name: '螃蟹', expanded: false, children: [] }
							]
						}
					]
				}
			]
		},

		// 构建分类树结构
		buildCategoryTree(categories) {
			return categories.map(category => {
				// 适配后端数据结构：children_data 或 children
				const childrenData = category.children_data || category.children || []

				return {
					...category,
					expanded: false, // 默认收起
					children: childrenData.length > 0 ? this.buildCategoryTree(childrenData) : []
				}
			})
		},

		// 切换分类展开/收起
		toggleCategory(category) {
			// 如果有子分类，切换展开状态
			if (category.children && category.children.length > 0) {
				category.expanded = !category.expanded
			} else {
				// 如果没有子分类，直接选择该分类
				this.selectCategory(category.id)
			}
		},

		// 选择分类
		selectCategory(categoryId) {
			this.selectedCategoryId = categoryId
			console.log('选择分类:', categoryId)

			// 重新加载商品
			this.currentPage = 1
			this.productList = []
			this.hasMore = true
			this.loadProducts()
		},

		// 格式化价格
		formatPrice(price) {
			return parseFloat(price || 0).toFixed(2)
		},
		
		// 获取总数量
		getTotalQuantity() {
			return this.selectedProducts.reduce((total, product) => total + product.quantity, 0)
		},
		
		// 🌍 获取总金额（使用区域价格）
		getTotalAmount() {
			const total = this.selectedProducts.reduce((sum, product) => {
				// 只有当区域价格存在且与原价不同时才使用区域价格
				let price = product.price
				if (product.region_price_info &&
					product.region_price_info.final_price &&
					product.region_price_info.final_price !== product.price) {
					price = product.region_price_info.final_price
				}
				return sum + (price * product.quantity)
			}, 0)
			return this.formatPrice(total)
		},
		
		// 检查商品是否已选择
		isProductSelected(productId) {
			return this.selectedProducts.some(p => p.id === productId)
		},
		
		// 获取商品数量
		getProductQuantity(productId) {
			const product = this.selectedProducts.find(p => p.id === productId)
			return product ? product.quantity : 0
		},

		// 检查商品是否缺货
		isOutOfStock(product) {
			// 优先使用后端返回的缺货标识
			if (product.out_of_stock !== undefined) {
				return product.out_of_stock
			}

			// 检查是否可以购买
			if (product.can_purchase !== undefined) {
				return !product.can_purchase
			}

			// 检查库存状态
			if (product.stock_status === 'out_of_stock') {
				return true
			}

			// 如果商品不跟踪库存，则永远不缺货
			if (product.track_inventory === false) {
				return false
			}

			// 如果库存策略是无限库存，则永远不缺货
			if (product.inventory_policy === 'unlimited') {
				return false
			}

			// 如果库存策略是允许负库存，则永远不缺货
			if (product.inventory_policy === 'allow_negative') {
				return false
			}

			// 获取当前库存
			const currentStock = this.getCurrentStock(product)

			// 如果库存小于等于0，则缺货
			return currentStock <= 0
		},

		// 获取当前可用库存
		getCurrentStock(product) {
			// 基础库存 - 使用后端返回的库存字段
			let stock = product.current_stock || product.total_stock || product.stock || product.inventory || 0

			// 减去已选择的数量
			const selectedQuantity = this.getProductQuantity(product.id)

			return stock - selectedQuantity
		},

		// 获取库存显示文本
		getStockText(product) {
			// 根据后端返回的库存状态显示
			if (product.stock_status === 'unlimited' || product.stock_status === 'untracked') {
				return '充足'
			}

			// 如果不跟踪库存，显示充足
			if (product.track_inventory === false) {
				return '充足'
			}

			// 如果是无限库存策略，显示充足
			if (product.inventory_policy === 'unlimited') {
				return '充足'
			}

			// 如果允许负库存，显示充足
			if (product.inventory_policy === 'allow_negative') {
				return '充足'
			}

			const currentStock = this.getCurrentStock(product)

			if (currentStock > 100) {
				return '充足'
			} else if (currentStock > 10) {
				return currentStock + '件'
			} else if (currentStock > 0) {
				return '仅剩' + currentStock + '件'
			} else {
				return '0件'
			}
		},
		
		// 切换购物车视图
		toggleCartView() {
			this.showCart = !this.showCart
		},
		
		// 添加到购物车
		addToCart(product) {
			const existingProduct = this.selectedProducts.find(p => p.id === product.id)
			if (existingProduct) {
				this.increaseQuantity(product)
			} else {
				// 检查库存
				if (this.isOutOfStock(product)) {
					uni.showToast({
						title: '商品缺货，无法添加',
						icon: 'none',
						duration: 2000
					})
					return
				}

				// 🔧 统一使用销售单位ID，确保与后端一致
				const saleUnitId = product.sale_unit?.id || null

				this.selectedProducts.push({
					...product,
					quantity: 1,
					unit_id: saleUnitId, // 统一使用销售单位ID
					sale_unit: product.sale_unit // 保留销售单位完整信息
				})

				// 添加成功反馈
				uni.showToast({
					title: '已加入购物车',
					icon: 'success',
					duration: 1500
				})

				console.log('商品已添加到购物车:', product.name)
			}
		},
		
		// 从购物车移除
		removeFromCart(index) {
			this.selectedProducts.splice(index, 1)
			
			uni.showToast({
				title: '已移除',
				icon: 'success',
				duration: 1000
			})
		},
		
		// 清空购物车
		clearCart() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空购物车吗？',
				success: (res) => {
					if (res.confirm) {
						this.selectedProducts = []
						this.showCart = false
						uni.showToast({
							title: '已清空购物车',
							icon: 'success',
							duration: 1000
						})
					}
				}
			})
		},
		
		// 增加商品数量
		increaseQuantity(product) {
			// 检查参数是索引还是产品对象
			let cartProduct, originalProduct

			if (typeof product === 'number') {
				// 如果是索引（购物车中的操作）
				cartProduct = this.selectedProducts[product]
				// 从商品列表中找到原始商品信息
				originalProduct = this.productList.find(p => p.id === cartProduct.id)
			} else {
				// 如果是产品对象（商品列表中的操作）
				originalProduct = product
				cartProduct = this.selectedProducts.find(p => p.id === product.id)

				// 如果购物车中不存在，则添加
				if (!cartProduct) {
					// 在添加前检查库存
					if (this.isOutOfStock(originalProduct)) {
						uni.showToast({
							title: '商品缺货，无法添加',
							icon: 'none',
							duration: 2000
						})
						return
					}
					this.addToCart(product)
					return
				}
			}

			// 检查库存限制（如果不允许负库存）
			if (originalProduct && !this.canIncreaseQuantity(originalProduct, cartProduct)) {
				const stockText = this.getStockText(originalProduct)
				uni.showToast({
					title: `库存不足，当前${stockText}`,
					icon: 'none',
					duration: 2000
				})
				return
			}

			cartProduct.quantity++
		},

		// 检查是否可以增加数量
		canIncreaseQuantity(originalProduct, cartProduct) {
			// 优先使用后端的can_purchase判断
			if (originalProduct.can_purchase === false) {
				return false
			}

			// 如果不跟踪库存，则可以无限增加
			if (originalProduct.track_inventory === false) {
				return true
			}

			// 如果是无限库存策略，则可以无限增加
			if (originalProduct.inventory_policy === 'unlimited') {
				return true
			}

			// 如果允许负库存，则可以无限增加
			if (originalProduct.inventory_policy === 'allow_negative') {
				return true
			}

			// 检查增加后是否超过库存
			const currentStock = originalProduct.current_stock || originalProduct.total_stock || originalProduct.stock || originalProduct.inventory || 0
			const currentQuantity = cartProduct ? cartProduct.quantity : 0

			return currentQuantity < currentStock
		},
		
		// 减少商品数量
		decreaseQuantity(product) {
			const cartProduct = this.selectedProducts.find(p => p.id === product.id)
			if (cartProduct && cartProduct.quantity > 1) {
				cartProduct.quantity--
			} else if (cartProduct) {
				this.removeFromCart(this.selectedProducts.indexOf(cartProduct))
			}
		},

		// 购物车中减少数量
		decreaseCartQuantity(index) {
			if (this.selectedProducts[index].quantity > 1) {
				this.selectedProducts[index].quantity--
			} else {
				this.removeFromCart(index)
			}
		},

		// 购物车中增加数量
		increaseCartQuantity(index) {
			const cartProduct = this.selectedProducts[index]
			const originalProduct = this.productList.find(p => p.id === cartProduct.id)

			if (originalProduct && this.canIncreaseQuantity(originalProduct, cartProduct)) {
				cartProduct.quantity++
			} else {
				const stockText = originalProduct ? this.getStockText(originalProduct) : '库存不足'
				uni.showToast({
					title: `库存不足，当前${stockText}`,
					icon: 'none',
					duration: 2000
				})
			}
		},

		// 处理数量输入
		onQuantityInput(event, product) {
			const value = parseInt(event.detail.value) || 0
			if (value < 0) return

			const cartProduct = this.selectedProducts.find(p => p.id === product.id)
			if (value === 0) {
				// 如果输入0，从购物车移除
				if (cartProduct) {
					this.removeFromCart(this.selectedProducts.indexOf(cartProduct))
				}
			} else {
				// 检查库存限制
				if (this.canSetQuantity(product, value)) {
					if (cartProduct) {
						cartProduct.quantity = value
					} else {
						// 添加到购物车
						this.addToCart(product)
						const newCartProduct = this.selectedProducts.find(p => p.id === product.id)
						if (newCartProduct) {
							newCartProduct.quantity = value
						}
					}
				} else {
					// 恢复原值
					event.target.value = cartProduct ? cartProduct.quantity : 0
					const stockText = this.getStockText(product)
					uni.showToast({
						title: `库存不足，当前${stockText}`,
						icon: 'none',
						duration: 2000
					})
				}
			}
		},

		// 处理数量输入失焦
		onQuantityBlur(event, product) {
			const value = parseInt(event.detail.value) || 0
			const cartProduct = this.selectedProducts.find(p => p.id === product.id)

			if (value <= 0 && cartProduct) {
				this.removeFromCart(this.selectedProducts.indexOf(cartProduct))
			}
		},

		// 购物车数量输入处理
		onCartQuantityInput(event, index) {
			const value = parseInt(event.detail.value) || 0
			if (value < 0) return

			const cartProduct = this.selectedProducts[index]
			const originalProduct = this.productList.find(p => p.id === cartProduct.id)

			if (value === 0) {
				this.removeFromCart(index)
			} else if (originalProduct && this.canSetQuantity(originalProduct, value)) {
				cartProduct.quantity = value
			} else {
				// 恢复原值
				event.target.value = cartProduct.quantity
				const stockText = originalProduct ? this.getStockText(originalProduct) : '库存不足'
				uni.showToast({
					title: `库存不足，当前${stockText}`,
					icon: 'none',
					duration: 2000
				})
			}
		},

		// 购物车数量输入失焦
		onCartQuantityBlur(event, index) {
			const value = parseInt(event.detail.value) || 0
			if (value <= 0) {
				this.removeFromCart(index)
			}
		},

		// 检查是否可以设置指定数量
		canSetQuantity(product, quantity) {
			// 优先使用后端的can_purchase判断
			if (product.can_purchase === false) {
				return false
			}

			// 如果不跟踪库存，则可以设置任意数量
			if (product.track_inventory === false) {
				return true
			}

			// 如果是无限库存策略，则可以设置任意数量
			if (product.inventory_policy === 'unlimited') {
				return true
			}

			// 如果允许负库存，则可以设置任意数量
			if (product.inventory_policy === 'allow_negative') {
				return true
			}

			// 检查是否超过库存
			const currentStock = product.current_stock || product.total_stock || product.stock || product.inventory || 0
			return quantity <= currentStock
		},
		
		// 🌍 确认选择（包含区域价格信息）
		confirmSelection() {
			if (this.selectedProducts.length === 0) {
				uni.showToast({
					title: '请选择商品',
					icon: 'none'
				})
				return
			}

			// 🌍 处理选中商品的价格信息
			const processedProducts = this.selectedProducts.map(product => {
				const processedProduct = { ...product }

				// 如果有区域价格信息，使用区域价格作为最终价格
				if (product.region_price_info && product.region_price_info.final_price) {
					processedProduct.price = product.region_price_info.final_price
					processedProduct.original_price = product.price // 保存原价
					processedProduct.price_type = product.region_price_info.price_type || 'region'
					processedProduct.discount_info = product.region_price_info.discount_info || []
					processedProduct.total_discount = product.region_price_info.total_discount || 0

					console.log(`🌍 商品 ${product.name} 使用区域价格: ¥${processedProduct.price} (原价: ¥${product.price})`)
				} else {
					processedProduct.price_type = 'base'
					processedProduct.total_discount = 0
					console.log(`📦 商品 ${product.name} 使用基础价格: ¥${processedProduct.price}`)
				}

				return processedProduct
			})

			// 通过页面栈传递数据
			const pages = getCurrentPages()
			const prevPage = pages[pages.length - 2]

			if (prevPage) {
				prevPage.data = prevPage.data || {}
				prevPage.data.selectedProducts = processedProducts
			}

			console.log('🌍 传递给代客下单页面的商品数据:', processedProducts)

			// 返回上一页
			uni.navigateBack()
		},
		
		// 清空搜索
		clearSearch() {
			this.searchKeyword = ''
			this.refreshProducts()
		},
		
		// 加载商品列表
		async loadProducts(isRefresh = false) {
			if (this.listLoading) return
			
			this.listLoading = true
			let response = null;
			
			try {
				// 统一使用productApi的getProductList方法处理搜索和普通列表
				const params = {
					page: isRefresh ? 1 : this.currentPage,
					per_page: this.pageSize
				};

				// 如果有搜索关键词，添加keyword参数
				if (this.searchKeyword.trim()) {
					params.keyword = this.searchKeyword.trim();
				}

				// 如果选择了分类，添加分类参数
				if (this.selectedCategoryId) {
					params.category_id = this.selectedCategoryId;
				}
				
				console.log('商品列表请求参数:', params);
				
				// 使用统一的API方法
				response = await productApi.getProductList(params);
				
				// 根据返回的数据格式进行适配
				let newProducts = [];
				
				// 处理搜索或列表API返回的数据
				if (response && response.data) {
					console.log('API返回原始数据:', response.data)
					
					// 适配不同的返回结构
					// 1. 标准结构: { code: 200, message: '搜索成功', data: { list: [...], total: 100 } }
					// 2. 简化结构: { data: [...], total: 100 }
					// 3. 直接返回数组: [...]
					
					let responseData = response.data
					
					// 检查是否有标准的状态码结构
					if (responseData.code && responseData.code === 200 && responseData.data) {
						responseData = responseData.data
					}
					
					// 提取商品数据列表
					if (Array.isArray(responseData)) {
						// 直接是数组
						newProducts = responseData
					} else if (responseData.list && Array.isArray(responseData.list)) {
						// { list: [...] } 结构
						newProducts = responseData.list
					} else if (responseData.data && Array.isArray(responseData.data)) {
						// { data: [...] } 结构
						newProducts = responseData.data
					} else if (responseData.items && Array.isArray(responseData.items)) {
						// { items: [...] } 结构
						newProducts = responseData.items
					}
					
					console.log('提取的商品数据:', newProducts)

					// 🌍 处理商品库存信息
					newProducts = newProducts.map(product => {
						// 记录原始库存信息用于调试
						console.log(`商品 ${product.name} 库存信息:`, {
							// 后端返回的字段
							current_stock: product.current_stock,
							total_stock: product.total_stock,
							stock_status: product.stock_status,
							can_purchase: product.can_purchase,
							out_of_stock: product.out_of_stock,
							inventory_policy: product.inventory_policy,
							track_inventory: product.track_inventory,
							min_stock_threshold: product.min_stock_threshold
						})

						return product
					})

					// 🌍 批量获取区域价格信息
					console.log('🌍 检查区域价格获取条件:', {
						hasClient: !!this.selectedClient,
						hasRegionId: !!this.clientRegionId,
						productCount: newProducts.length,
						clientInfo: this.selectedClient
					})

					if (this.selectedClient && this.clientRegionId && newProducts.length > 0) {
						try {
							console.log('🌍 开始批量获取区域价格...')
							const regionPrices = await this.batchGetProductRegionPrices(newProducts)

							console.log('🌍 批量价格获取结果:', regionPrices)

							// 将区域价格信息附加到商品上
							newProducts = newProducts.map(product => {
								if (regionPrices[product.id]) {
									product.region_price_info = regionPrices[product.id]
									console.log(`🌍 商品 ${product.name} 设置区域价格:`, {
										基础价格: product.price,
										区域价格: regionPrices[product.id].final_price,
										价格类型: regionPrices[product.id].price_type
									})
								} else {
									console.log(`📦 商品 ${product.name} 无区域价格，使用基础价格: ¥${product.price}`)
								}
								return product
							})

							console.log('🌍 价格处理完成，商品列表:', newProducts.map(p => ({
								name: p.name,
								basePrice: p.price,
								hasRegionPrice: !!p.region_price_info,
								finalPrice: p.region_price_info ? p.region_price_info.final_price : p.price
							})))

						} catch (error) {
							console.error('❌ 批量获取区域价格失败:', error)
						}
					} else {
						console.log('⚠️ 跳过区域价格获取，原因:', {
							noClient: !this.selectedClient,
							noRegionId: !this.clientRegionId,
							noProducts: newProducts.length === 0
						})
					}

					if (isRefresh) {
						this.productList = newProducts
						this.currentPage = 1
					} else {
						this.productList = [...this.productList, ...newProducts]
					}
					
					// 更新分页信息
					// 尝试获取总数
					let total = 0
					
					// 兼容多种总数字段
					if (responseData.total !== undefined) {
						total = responseData.total
					} else if (responseData.count !== undefined) {
						total = responseData.count
					} else if (responseData.pagination && responseData.pagination.total !== undefined) {
						total = responseData.pagination.total
					}
					
					console.log('分页信息:', { 
						total, 
						currentPage: this.currentPage, 
						newProductsCount: newProducts.length 
					})
					
					// 获取每页大小
					const limit = responseData.per_page || responseData.pageSize || this.pageSize
					
					if (total > 0) {
						// 如果响应中包含总数，使用总数判断是否有更多数据
						this.hasMore = (this.currentPage - 1) * limit + newProducts.length < total
					} else {
						// 否则根据返回的数据量判断
						this.hasMore = newProducts.length >= this.pageSize
					}
				} else {
					// 无响应数据
					newProducts = []
					if (isRefresh) {
						this.productList = []
					}
					this.hasMore = false
				}
				this.currentPage++
				
				console.log('加载商品数据成功:', newProducts.length)
				
			} catch (error) {
				console.error('加载商品列表失败，详细错误:', error)

				// 根据错误类型显示不同的提示
				let errorMessage = '加载失败'
				if (error.errMsg && error.errMsg.includes('timeout')) {
					errorMessage = '请求超时，请检查网络后重试'
				} else if (error.errMsg && error.errMsg.includes('fail')) {
					errorMessage = '网络连接失败，请检查网络'
				} else if (this.searchKeyword) {
					errorMessage = '搜索失败，请重试'
				} else if (this.selectedCategoryId) {
					errorMessage = '分类商品加载失败，请重试'
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.listLoading = false
				if (isRefresh) {
					this.refreshing = false
					uni.stopPullDownRefresh()
				}
			}
		},
		
		// 刷新商品列表
		refreshProducts() {
			this.currentPage = 1
			this.hasMore = true
			this.productList = []
			this.loadProducts(true)
		},
		
		// 加载更多
		loadMore() {
			this.loadProducts()
		},
		
		// 搜索输入处理
		onSearchInput(event) {
			this.searchKeyword = event.detail.value
			
			// 清除之前的定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			
			// 如果输入为空，立即恢复到普通商品列表
			if (!this.searchKeyword.trim()) {
				this.refreshProducts()
				return
			}
			
			// 设置新的定时器，延迟搜索以减少API调用
			this.searchTimer = setTimeout(() => {
				this.searchProducts()
			}, 500)
		},
		
		// 搜索商品
		searchProducts() {
			if (!this.searchKeyword.trim()) {
				return this.refreshProducts()
			}
			
			console.log('开始搜索:', this.searchKeyword)
			this.refreshProducts()
		},
		
		// 搜索确认处理
		onSearchConfirm(event) {
			this.searchProducts()
		},
		
		// 刷新处理
		onRefresh() {
			this.refreshing = true
			this.refreshProducts()
		},

		// 处理图片加载错误
		handleImageError(e, product) {
			console.log('图片加载失败:', e)
			// 设置默认图片
			if (e.target && product) {
				// 如果是商品列表中的图片
				if (product.image === e.target.src) {
					product.image = '/static/default-product.png'
				} else if (product.cover_url === e.target.src) {
					product.cover_url = '/static/default-product.png'
				}
			}
		},
		
		// 获取商品图片URL
		getProductImageUrl(product) {
			// 优先使用 images 数组中的第一张图片
			if (product.images && product.images.length > 0) {
				return product.images[0].url
			}
			// 兼容旧数据结构
			return product.cover_url || product.image || '/static/default-product.png'
		}
	}
}
</script>

<style scoped>
.select-product-container {
	background: #f8f9fa;
	height: 100vh;
	position: relative;
	overflow: hidden;
}

/* 搜索栏 */
.search-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
	z-index: 1001;
	height: 68rpx; /* 固定高度 */
	box-sizing: border-box;
}

.search-box {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 20rpx;
	height: 64rpx;
	margin: 0 16rpx;
	border: 1rpx solid #e9ecef;
}

.search-input {
	flex: 1;
	font-size: 30rpx;
	color: #262626;
}

.clear-btn {
	background: none;
	border: none;
	padding: 8rpx;
	font-size: 28rpx;
	color: #8c8c8c;
}

/* 🌍 客户区域信息栏 */
.client-region-bar {
	position: fixed;
	top: 68rpx; /* 搜索栏下方 */
	left: 0;
	right: 0;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	padding: 12rpx 20rpx;
	border-bottom: 1rpx solid #dee2e6;
	display: flex;
	justify-content: space-between;
	align-items: center;
	z-index: 1000;
	height: 60rpx;
	box-sizing: border-box;
}

.client-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2rpx;
}

.client-name {
	font-size: 24rpx;
	font-weight: 600;
	color: #495057;
	line-height: 1.2;
}

.region-status {
	font-size: 20rpx;
	color: #28a745;
	font-weight: 500;
	line-height: 1.2;
}

.region-status.no-region {
	color: #ffc107;
}

.manual-region-btn {
	background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
	color: #fff;
	border: none;
	border-radius: 16rpx;
	padding: 8rpx 16rpx;
	font-size: 20rpx;
	font-weight: 600;
	box-shadow: 0 2rpx 6rpx rgba(0, 123, 255, 0.3);
	flex-shrink: 0;
}

/* 主要内容区域 */
.main-content {
	display: flex;
	position: fixed;
	top: 128rpx; /* 搜索栏(68rpx) + 客户区域栏(60rpx) */
	left: 0;
	right: 0;
	bottom: 120rpx; /* 为底部操作栏留出空间 */
	background: #f8f9fa;
}

/* 左侧分类栏 */
.category-sidebar {
	width: 200rpx;
	background: #ffffff;
	border-right: 1rpx solid #f0f0f0;
	flex-shrink: 0; /* 防止被压缩 */
	height: 100%;
	overflow: hidden;
}

.category-scroll {
	height: 100%;
}

.category-group {
	border-bottom: 1rpx solid #f5f5f5;
}

.category-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 28rpx 16rpx;
	font-size: 28rpx;
	color: #595959;
	background: #ffffff;
	border-bottom: 1rpx solid #f8f9fa;
}

.category-item:active {
	background: #f8f9fa;
}

.category-item.active {
	background: #e6f7ff;
	color: #1890ff;
	font-weight: 500;
}

/* 分类层级样式 */
.category-item.level-0 {
	background: #f8f9fa;
	font-weight: 600;
	font-size: 30rpx;
}

.category-item.level-1 {
	padding-left: 16rpx;
	font-weight: 500;
	font-size: 28rpx;
}

.category-item.level-2 {
	padding-left: 32rpx;
	font-size: 26rpx;
}

.category-item.level-3 {
	padding-left: 48rpx;
	font-size: 24rpx;
	color: #8c8c8c;
}

.category-name {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.expand-icon {
	font-size: 20rpx;
	color: #bfbfbf;
	margin-left: 8rpx;
}

.sub-categories {
	background: #fafafa;
}

/* 右侧商品列表 */
.product-scroll {
	flex: 1;
	height: 100%;
	background: #f8f9fa;
}



/* 已选商品统计 */
.selected-summary {
	background: #ffffff;
	padding: 16rpx 20rpx;
	margin: 16rpx;
	border-radius: 8rpx;
	border: 1rpx solid #f0f0f0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.summary-info {
	flex: 1;
}

.summary-text {
	font-size: 32rpx;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.summary-amount {
	font-size: 36rpx;
	font-weight: 700;
	color: #007AFF;
}

.cart-btn {
	background: #f8f9fa;
	border: 2rpx solid #007AFF;
	color: #007AFF;
	border-radius: 12rpx;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 28rpx;
	font-weight: 500;
}

.cart-btn:active {
	background: #e6f7ff;
}

/* 购物车视图 */
.cart-section {
	background: #ffffff;
	margin: 0 20rpx 16rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.cart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.cart-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.clear-cart-btn {
	background: none;
	border: none;
	color: #dc3545;
	font-size: 28rpx;
	padding: 8rpx 16rpx;
}

.cart-list {
	padding: 0 24rpx 24rpx;
}

.cart-item {
	display: flex;
	align-items: flex-start;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f8f9fa;
	gap: 16rpx;
}

.cart-product-image {
	width: 100rpx;
	height: 100rpx;
	border-radius: 8rpx;
	background: #f5f5f5;
	flex-shrink: 0;
}

.cart-product-details {
	flex: 1;
	min-width: 0;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.cart-name-row {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	width: 100%;
}

.cart-price-row {
	width: 100%;
}

.cart-quantity-row {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-top: 20rpx;
}

.cart-product-info {
	flex: 1;
}

.cart-product-name {
	font-size: 32rpx;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.cart-price-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.cart-product-price {
	font-size: 28rpx;
	color: #666666;
}

/* 购物车价格标签容器 */
.cart-price-badges {
	display: flex;
	gap: 6rpx;
}

/* 购物车价格标签 */
.cart-price-badge {
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 4rpx;
	color: #ffffff;
	font-weight: 600;
}

.cart-price-badge.member-badge {
	background: #faad14;
}

.cart-price-badge.region-badge {
	background: #52c41a;
}

/* 兼容旧的购物车价格类型 */
.cart-price-type {
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 4rpx;
	background: #52c41a;
	color: #ffffff;
	font-weight: 600;
}

.cart-quantity-control {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 6rpx;
	border: 1rpx solid #e9ecef;
	overflow: hidden;
	margin-right: 20rpx;
}

.cart-quantity-btn {
	width: 44rpx;
	height: 44rpx;
	background: #ffffff;
	border: none;
	font-size: 24rpx;
	color: #262626;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
}

.cart-quantity-btn.decrease {
	border-right: 1rpx solid #e9ecef;
}

.cart-quantity-btn.increase {
	border-left: 1rpx solid #e9ecef;
}

.cart-quantity-btn:active {
	background: #e9ecef;
}

.cart-quantity-input {
	width: 56rpx;
	height: 44rpx;
	text-align: center;
	font-size: 26rpx;
	color: #262626;
	background: #ffffff;
	border: none;
	font-weight: 600;
}

.remove-btn {
	width: 48rpx;
	height: 48rpx;
	background: #dc3545;
	border: none;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.remove-icon {
	font-size: 20rpx;
	color: #ffffff;
}

/* 商品列表 */
.product-list {
	padding: 16rpx;
	padding-bottom: 32rpx; /* 为底部留出额外空间 */
}

.product-card {
	background: #ffffff;
	border-radius: 8rpx;
	margin-bottom: 8rpx;
	border: 1rpx solid #f0f0f0;
}

.product-content {
	display: flex;
	align-items: flex-start;
	padding: 16rpx 20rpx;
	position: relative;
	gap: 16rpx;
}

/* 缺货状态 */
.product-content.out-of-stock {
	opacity: 0.6;
}

.product-content.out-of-stock .product-image {
	filter: grayscale(50%);
}

.out-of-stock-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
}

.mask-text {
	background: rgba(255, 77, 79, 0.9);
	color: #ffffff;
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
	font-size: 20rpx;
	font-weight: 500;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	background: #f5f5f5;
	flex-shrink: 0;
}

.product-details {
	flex: 1;
	min-width: 0;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.product-name-row {
	width: 100%;
}

.product-info-row {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.quantity-row {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-top: 20rpx;
}

/* 加购按钮 */
.add-to-cart-btn {
	background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 28rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 80rpx;
	height: 48rpx;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
	margin-right: 20rpx;
}

.add-to-cart-btn:active {
	background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
	transform: translateY(1rpx);
}

.add-to-cart-btn.disabled {
	background: #f5f5f5 !important;
	color: #bfbfbf !important;
	box-shadow: none !important;
	transform: none !important;
}

.add-btn-text {
	font-size: 28rpx;
	font-weight: 600;
}

.product-info {
	flex: 1;
}

.product-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #262626;
	margin-bottom: 12rpx;
	line-height: 1.4;
}

.product-price-section {
	margin-bottom: 12rpx;
}

.product-price-row {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.price-main {
	display: flex;
	align-items: baseline;
	gap: 6rpx;
}

.current-price {
	font-size: 36rpx;
	font-weight: 700;
	color: #ff4d4f;
}

.current-price.region-price {
	color: #ff6b00;
}

.current-price.member-price {
	color: #faad14;
}

.current-price.member-price.region-price {
	color: #ff6b00;
}

.price-unit {
	font-size: 24rpx;
	color: #8c8c8c;
}

/* 价格标签容器 */
.price-badges {
	display: flex;
	gap: 6rpx;
	margin-left: 8rpx;
}

/* 价格标签基础样式 */
.price-badge {
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 4rpx;
	color: #ffffff;
	font-weight: 600;
}

/* 会员价标签 */
.price-badge.member-badge {
	background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
}

/* 区域价标签 */
.price-badge.region-badge {
	background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

/* 兼容旧的价格类型标签 */
.price-type-badge {
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 4rpx;
	background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
	color: #ffffff;
	font-weight: 600;
	margin-left: 8rpx;
}

.price-extra {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.original-price {
	font-size: 24rpx;
	color: #999999;
	text-decoration: line-through;
}

/* 会员折扣详情 */
.member-discount-detail {
	font-size: 22rpx;
	color: #faad14;
	font-weight: 600;
}

.discount-badge {
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	background: linear-gradient(135deg, #ff6b00 0%, #ff8c00 100%);
	color: #ffffff;
	font-weight: 600;
}

/* 🔧 手动区域设置对话框 */
.region-dialog-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}

.region-dialog {
	background: #fff;
	border-radius: 16rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.dialog-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #e9ecef;
	background: #f8f9fa;
}

.dialog-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.dialog-close {
	background: none;
	border: none;
	font-size: 32rpx;
	color: #999;
	padding: 0;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.dialog-content {
	padding: 24rpx;
}

.dialog-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

.region-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.region-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx;
	border-radius: 8rpx;
	margin-bottom: 8rpx;
	background: #f8f9fa;
	border: 1rpx solid #e9ecef;
	transition: all 0.2s;
}

.region-item:active {
	background: #e9ecef;
	transform: scale(0.98);
}

.region-name {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.region-id {
	font-size: 20rpx;
	color: #999;
}

.product-unit {
	font-size: 22rpx;
	color: #8c8c8c;
}

/* 库存信息 */
.stock-info {
	margin-top: 8rpx;
}

.stock-text {
	font-size: 26rpx;
	color: #52c41a;
	font-weight: 500;
}

.out-of-stock-text {
	font-size: 26rpx;
	color: #ff4d4f;
	font-weight: 600;
}

.product-stock {
	font-size: 24rpx;
	color: #999999;
}

.product-tags {
	display: flex;
	gap: 8rpx;
}

.tag {
	background: #f8f9fa;
	color: #666666;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.product-actions {
	margin-left: 24rpx;
}

.add-btn {
	background: #007AFF;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 24rpx;
	font-weight: 600;
}

.add-icon {
	font-size: 28rpx;
}

/* 数量选择器 */
.quantity-selector {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	border: 1rpx solid #e9ecef;
	overflow: hidden;
	margin-right: 20rpx;
}

.quantity-btn {
	width: 48rpx;
	height: 48rpx;
	background: #ffffff;
	border: none;
	font-size: 28rpx;
	color: #262626;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
}

.quantity-btn.decrease {
	border-right: 1rpx solid #e9ecef;
}

.quantity-btn.increase {
	border-left: 1rpx solid #e9ecef;
}

.quantity-btn:active {
	background: #e9ecef;
}

.quantity-btn:disabled,
.quantity-btn.disabled {
	background: #f5f5f5 !important;
	color: #bfbfbf !important;
	cursor: not-allowed;
}

.quantity-input {
	width: 60rpx;
	height: 48rpx;
	text-align: center;
	font-size: 28rpx;
	color: #262626;
	background: #ffffff;
	border: none;
	font-weight: 600;
}

.quantity-input:disabled {
	background: #f5f5f5;
	color: #bfbfbf;
}

/* 加载状态 */
.loading-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #f0f0f0;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 16rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

/* 空状态 */
.empty-section {
	text-align: center;
	padding: 60rpx 40rpx;
}

.empty-text {
	font-size: 26rpx;
	color: #8c8c8c;
	color: #666666;
	display: block;
	margin-bottom: 12rpx;
}

.empty-tip {
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 32rpx;
}

.empty-action {
	background: #007AFF;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

/* 分页加载 */
.pagination-section {
	text-align: center;
	padding: 40rpx 0;
}

.load-more-btn {
	background: #f8f9fa;
	color: #007AFF;
	border: 2rpx solid #007AFF;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 16rpx 20rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	z-index: 1000;
}

.action-info {
	flex: 1;
}

.total-text {
	font-size: 28rpx;
	color: #8c8c8c;
	display: block;
	margin-bottom: 4rpx;
}

.total-amount {
	font-size: 36rpx;
	font-weight: 700;
	color: #1890ff;
}

.confirm-btn {
	background: #1890ff;
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	padding: 18rpx 36rpx;
	font-size: 32rpx;
	font-weight: 600;
}

.confirm-btn:active {
	background: #096dd9;
}
</style> 