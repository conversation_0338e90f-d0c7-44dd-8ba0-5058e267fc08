<?php

namespace App\Billing\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * 🔥 新增：账单系统专用付款链接模型
 * 与订单系统的PaymentLink区分，避免冲突
 */
class BillingPaymentLink extends Model
{
    use HasFactory;

    protected $table = 'billing_payment_links';
    
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'link_no',
        'bill_id',
        'order_id', 
        'correction_id',
        'amount',
        'paid_amount',
        'payment_method',
        'payment_type',
        'status',
        'qr_code_url',
        'short_url',
        'expires_at',
        'paid_at',
        'transaction_id',
        'external_payment_no',
        'reminder_count',
        'last_reminder_at',
        'notes',
        'failure_reason',
        'created_by',
        'metadata'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'expires_at' => 'datetime',
        'paid_at' => 'datetime',
        'last_reminder_at' => 'datetime',
        'metadata' => 'json'
    ];

    // 状态常量
    const STATUS_ACTIVE = 'active';
    const STATUS_PROCESSING = 'processing';
    const STATUS_PAID = 'paid';
    const STATUS_EXPIRED = 'expired';
    const STATUS_CANCELLED = 'cancelled';

    // 支付类型常量
    const TYPE_CORRECTION_SUPPLEMENT = 'correction_supplement';
    const TYPE_FULL_PAYMENT = 'full_payment';
    const TYPE_PARTIAL_PAYMENT = 'partial_payment';
    const TYPE_BILL_PAYMENT = 'bill_payment';

    /**
     * 关联账单
     */
    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class);
    }

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(\App\Order\Models\Order::class);
    }

    /**
     * 关联订单更正
     */
    public function correction(): BelongsTo
    {
        return $this->belongsTo(\App\Order\Models\OrderCorrection::class, 'correction_id');
    }

    /**
     * 关联创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Employee::class, 'created_by');
    }

    /**
     * 生成付款链接ID
     */
    public static function generateLinkId(): string
    {
        return 'BPL' . date('YmdHis') . Str::random(8);
    }

    /**
     * 生成付款链接编号
     */
    public static function generateLinkNo(): string
    {
        return 'BPAY' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 是否已支付
     */
    public function isPaid(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * 是否可用
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE && !$this->isExpired();
    }

    /**
     * 检查是否可以支付
     */
    public function canPay(): bool
    {
        return $this->status === self::STATUS_ACTIVE && !$this->isExpired();
    }

    /**
     * 检查是否正在处理支付
     */
    public function isProcessing(): bool
    {
        return $this->status === self::STATUS_PROCESSING;
    }

    /**
     * 开始支付处理（防重复）
     */
    public function startPaymentProcessing(): bool
    {
        return $this->where('id', $this->id)
            ->where('status', self::STATUS_ACTIVE)
            ->update(['status' => self::STATUS_PROCESSING, 'updated_at' => now()]) > 0;
    }

    /**
     * 完成支付
     */
    public function completePayment(array $paymentData): void
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'paid_at' => now(),
            'paid_amount' => $paymentData['amount'] ?? $this->amount,
            'transaction_id' => $paymentData['transaction_id'] ?? null,
            'external_payment_no' => $paymentData['external_payment_no'] ?? null,
        ]);
    }

    /**
     * 支付失败，恢复状态
     */
    public function failPayment(string $reason = null): void
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'failure_reason' => $reason,
            'updated_at' => now(),
        ]);
    }

    /**
     * 取消付款链接
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'failure_reason' => $reason,
            'updated_at' => now(),
        ]);
    }

    /**
     * 标记为过期
     */
    public function markExpired(): void
    {
        $this->update([
            'status' => self::STATUS_EXPIRED,
            'updated_at' => now(),
        ]);
    }

    /**
     * 获取完整的付款链接URL
     */
    public function getFullUrlAttribute(): string
    {
        return config('app.url') . '/billing/payment/' . $this->id;
    }

    /**
     * 获取付款类型中文名称
     */
    public function getPaymentTypeNameAttribute(): string
    {
        $types = [
            self::TYPE_CORRECTION_SUPPLEMENT => '订单更正补款',
            self::TYPE_FULL_PAYMENT => '全额付款',
            self::TYPE_PARTIAL_PAYMENT => '部分付款',
            self::TYPE_BILL_PAYMENT => '账单付款',
        ];

        return $types[$this->payment_type] ?? $this->payment_type;
    }

    /**
     * 获取状态中文名称
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            self::STATUS_ACTIVE => '有效',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_PAID => '已支付',
            self::STATUS_EXPIRED => '已过期',
            self::STATUS_CANCELLED => '已取消',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 检查支付处理是否超时
     */
    public function isPaymentTimeout(): bool
    {
        if ($this->status !== self::STATUS_PROCESSING) {
            return false;
        }

        // 支付处理超过10分钟视为超时
        return $this->updated_at->addMinutes(10)->isPast();
    }

    /**
     * 根据交易号查找付款链接
     */
    public static function findByTradeNo(string $tradeNo): ?self
    {
        return self::where('external_payment_no', $tradeNo)
            ->orWhere('transaction_id', $tradeNo)
            ->first();
    }

    /**
     * 根据链接编号查找付款链接
     */
    public static function findByLinkNo(string $linkNo): ?self
    {
        return self::where('link_no', $linkNo)->first();
    }
} 