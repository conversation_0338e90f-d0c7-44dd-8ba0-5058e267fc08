<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class TestOldDatabaseConnection extends Command
{
    protected $signature = 'test:old-db-connection';
    protected $description = '测试老数据库连接';

    public function handle()
    {
        try {
            // 测试连接
            $this->info('正在测试老数据库连接...');
            
            $connection = DB::connection('mysql_old');
            $version = $connection->selectOne('SELECT VERSION() as version');
            
            $this->info('✅ 连接成功！');
            $this->info('数据库版本: ' . $version->version);
            
            // 显示数据库信息
            $database = $connection->getDatabaseName();
            $this->info('数据库名: ' . $database);
            
            // 获取表列表
            $tables = $connection->select("SHOW TABLES");
            $this->info('表数量: ' . count($tables));
            
            if (count($tables) > 0) {
                $this->info('前10个表:');
                $tableNames = array_slice(array_map(function($table) use ($database) {
                    return $table->{"Tables_in_{$database}"};
                }, $tables), 0, 10);
                
                foreach ($tableNames as $tableName) {
                    $this->line('  - ' . $tableName);
                }
            }
            
        } catch (Exception $e) {
            $this->error('❌ 连接失败: ' . $e->getMessage());
            $this->error('请检查.env文件中的老数据库配置');
            return 1;
        }
        
        return 0;
    }
} 