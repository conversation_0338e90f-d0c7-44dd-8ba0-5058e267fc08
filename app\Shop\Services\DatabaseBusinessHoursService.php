<?php

namespace App\Shop\Services;

use App\Shop\Models\BusinessHoursConfig;
use App\Shop\Models\WeeklyBusinessHours;
use App\Shop\Models\SpecialBusinessHours;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 数据库版营业时间服务类
 * 
 * 基于数据库实现的营业时间管理，支持动态配置
 */
class DatabaseBusinessHoursService
{
    /**
     * 缓存键前缀
     */
    const CACHE_PREFIX = 'business_hours:';
    
    /**
     * 缓存时间（秒）
     */
    const CACHE_TTL = 300; // 5分钟

    /**
     * 检查当前是否在营业时间内
     * 🔥 优化：精确时间控制
     */
    public function isOpen(Carbon $time = null): bool
    {
        $time = $time ?: $this->getCurrentTime();

        // 检查是否启用营业时间限制
        if (!$this->isEnabled()) {
            return true;
        }

        // 检查维护模式
        if ($this->isMaintenanceMode()) {
            return false;
        }

        // 获取当前日期的营业时间
        $businessHours = $this->getBusinessHoursForDate($time);

        // 如果当天不营业
        if (!$businessHours['enabled']) {
            return false;
        }

        // 🔥 精确时间检查：精确到秒
        $startTime = $time->copy()->setTimeFromTimeString($businessHours['start'])->setSecond(0)->setMicrosecond(0);
        $endTime = $time->copy()->setTimeFromTimeString($businessHours['end'])->setSecond(59)->setMicrosecond(999999);

        // 🔥 记录关键时间点的日志
        if ($this->isNearBusinessBoundary($time, $startTime, $endTime)) {
            Log::info('营业时间边界检查', [
                'current_time' => $time->toISOString(),
                'start_time' => $startTime->toISOString(),
                'end_time' => $endTime->toISOString(),
                'is_open' => $time->between($startTime, $endTime)
            ]);
        }

        return $time->between($startTime, $endTime);
    }

    /**
     * 获取营业状态信息
     */
    public function getBusinessStatus(Carbon $time = null): array
    {
        $time = $time ?: $this->getCurrentTime();
        $isOpen = $this->isOpen($time);
        
        $status = [
            'is_open' => $isOpen,
            'current_time' => $time->format('Y-m-d H:i:s'),
            'timezone' => $this->getTimezone(),
            'maintenance_mode' => $this->isMaintenanceMode(),
        ];
        
        if ($this->isMaintenanceMode()) {
            $status['message'] = $this->getMaintenanceMessage();
            return $status;
        }
        
        // 获取今日营业时间
        $todayHours = $this->getBusinessHoursForDate($time);
        $status['today_hours'] = $todayHours;
        
        if ($isOpen) {
            // 营业中，检查是否即将关闭
            $closingTime = $time->copy()->setTimeFromTimeString($todayHours['end']);
            $minutesToClose = $time->diffInMinutes($closingTime, false);
            $warningMinutes = $this->getClosingWarningMinutes();

            if ($minutesToClose <= $warningMinutes && $minutesToClose > 0) {
                $status['closing_soon'] = true;
                $status['minutes_to_close'] = $minutesToClose;
                $status['message'] = "商城即将结束营业（{$minutesToClose}分钟后），请尽快下单";
            } else {
                $status['closing_soon'] = false;
                $status['minutes_to_close'] = 0;
                $status['message'] = '营业中';
            }
            $status['next_open_time'] = null;
        } else {
            // 未营业，计算下次营业时间
            $nextOpenTime = $this->getNextOpenTime($time);
            $status['next_open_time'] = $nextOpenTime ? $nextOpenTime->format('Y-m-d H:i:s') : null;
            $status['closing_soon'] = false;
            $status['minutes_to_close'] = 0;
            $status['message'] = $this->getClosedMessage($todayHours);
        }
        
        return $status;
    }

    /**
     * 获取指定日期的营业时间
     */
    public function getBusinessHoursForDate(Carbon $date): array
    {
        $cacheKey = self::CACHE_PREFIX . 'date:' . $date->format('Y-m-d');
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($date) {
            // 检查特殊日期设置
            $special = SpecialBusinessHours::getByDate($date);
            if ($special) {
                return [
                    'enabled' => $special->enabled,
                    'start' => $special->start_time ? $special->start_time->format('H:i') : '00:00',
                    'end' => $special->end_time ? $special->end_time->format('H:i') : '23:59',
                    'reason' => $special->reason,
                    'type' => 'special'
                ];
            }
            
            // 获取星期几的设置
            $dayOfWeek = strtolower($date->format('l'));
            $weekly = WeeklyBusinessHours::getByDayOfWeek($dayOfWeek);
            
            return [
                'enabled' => $weekly->enabled,
                'start' => $weekly->start_time->format('H:i'),
                'end' => $weekly->end_time->format('H:i'),
                'type' => 'weekly'
            ];
        });
    }

    /**
     * 获取下次营业时间
     */
    public function getNextOpenTime(Carbon $fromTime): ?Carbon
    {
        $searchTime = $fromTime->copy();
        
        // 最多查找7天
        for ($i = 0; $i < 7; $i++) {
            $businessHours = $this->getBusinessHoursForDate($searchTime);
            
            if ($businessHours['enabled']) {
                $openTime = $searchTime->copy()->setTimeFromTimeString($businessHours['start']);
                
                // 如果是今天，检查是否还未到营业时间
                if ($i === 0 && $fromTime->lt($openTime)) {
                    return $openTime;
                }
                
                // 如果是明天或之后的日期
                if ($i > 0) {
                    return $openTime;
                }
            }
            
            // 检查下一天
            $searchTime->addDay()->startOfDay();
        }
        
        return null;
    }

    /**
     * 获取配置信息
     */
    protected function getConfig()
    {
        $cacheKey = self::CACHE_PREFIX . 'config';
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return BusinessHoursConfig::getConfig();
        });
    }

    /**
     * 检查是否启用营业时间限制
     */
    public function isEnabled(): bool
    {
        return $this->getConfig()->enabled;
    }

    /**
     * 检查是否处于维护模式
     */
    public function isMaintenanceMode(): bool
    {
        return $this->getConfig()->maintenance_mode;
    }

    /**
     * 获取时区设置
     */
    public function getTimezone(): string
    {
        return $this->getConfig()->timezone;
    }

    /**
     * 获取关闭提醒分钟数
     */
    public function getClosingWarningMinutes(): int
    {
        return $this->getConfig()->closing_warning_minutes;
    }

    /**
     * 获取维护模式消息
     */
    public function getMaintenanceMessage(): string
    {
        return $this->getConfig()->maintenance_message ?: '系统维护中，暂停下单服务';
    }

    /**
     * 获取当前时间
     */
    public function getCurrentTime(): Carbon
    {
        return Carbon::now($this->getTimezone());
    }

    /**
     * 获取关闭状态消息
     */
    protected function getClosedMessage(array $businessHours): string
    {
        if (!$businessHours['enabled']) {
            return isset($businessHours['reason']) ? $businessHours['reason'] : '今日不营业';
        }
        
        $hoursText = $businessHours['start'] . '-' . $businessHours['end'];
        return "商城暂未营业，营业时间：{$hoursText}";
    }

    /**
     * 验证下单时间
     */
    public function validateOrderTime(Carbon $time = null): array
    {
        $time = $time ?: $this->getCurrentTime();
        
        if (!$this->isEnabled()) {
            return [
                'valid' => true,
                'message' => '营业时间限制已关闭',
                'next_open_time' => null,
            ];
        }
        
        if ($this->isMaintenanceMode()) {
            return [
                'valid' => false,
                'message' => $this->getMaintenanceMessage(),
                'next_open_time' => null,
            ];
        }
        
        if ($this->isOpen($time)) {
            return [
                'valid' => true,
                'message' => '当前可以下单',
                'next_open_time' => null,
            ];
        }
        
        // 未营业时间
        $todayHours = $this->getBusinessHoursForDate($time);
        $nextOpenTime = $this->getNextOpenTime($time);
        
        return [
            'valid' => false,
            'message' => $this->getClosedMessage($todayHours),
            'next_open_time' => $nextOpenTime ? $nextOpenTime->format('Y-m-d H:i:s') : null,
        ];
    }

    /**
     * 清除缓存
     */
    public function clearCache(): void
    {
        $keys = [
            self::CACHE_PREFIX . 'config',
            self::CACHE_PREFIX . 'weekly:*',
            self::CACHE_PREFIX . 'date:*',
        ];
        
        foreach ($keys as $pattern) {
            if (str_contains($pattern, '*')) {
                // 清除匹配模式的缓存
                $prefix = str_replace('*', '', $pattern);
                Cache::flush(); // 简化处理，实际可以更精确
            } else {
                Cache::forget($pattern);
            }
        }
        
        Log::info('营业时间缓存已清除');
    }

    /**
     * 更新配置并清除缓存
     */
    public function updateConfig(array $data): void
    {
        BusinessHoursConfig::updateConfig($data);
        $this->clearCache();
        
        Log::info('营业时间配置已更新', $data);
    }

    /**
     * 更新每周营业时间并清除缓存
     */
    public function updateWeeklyHours(array $weeklyData): void
    {
        WeeklyBusinessHours::updateWeeklyHours($weeklyData);
        $this->clearCache();
        
        Log::info('每周营业时间已更新', $weeklyData);
    }

    /**
     * 添加特殊日期并清除缓存
     */
    public function addSpecialDate($date, $enabled, $startTime = null, $endTime = null, $reason = null): void
    {
        SpecialBusinessHours::addSpecialDate($date, $enabled, $startTime, $endTime, $reason);
        $this->clearCache();
        
        Log::info('特殊日期营业时间已添加', [
            'date' => $date,
            'enabled' => $enabled,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'reason' => $reason
        ]);
    }

    /**
     * 🔥 新增：检查是否接近营业时间边界
     * @param Carbon $currentTime 当前时间
     * @param Carbon $startTime 营业开始时间
     * @param Carbon $endTime 营业结束时间
     * @return bool
     */
    protected function isNearBusinessBoundary(Carbon $currentTime, Carbon $startTime, Carbon $endTime): bool
    {
        $boundaryMinutes = 5; // 边界时间：5分钟

        // 检查是否在营业开始前后5分钟内
        $timeToStart = $currentTime->diffInMinutes($startTime, false);
        if (abs($timeToStart) <= $boundaryMinutes) {
            return true;
        }

        // 检查是否在营业结束前后5分钟内
        $timeToEnd = $currentTime->diffInMinutes($endTime, false);
        if (abs($timeToEnd) <= $boundaryMinutes) {
            return true;
        }

        return false;
    }

    /**
     * 🔥 新增：获取精确的营业状态（无缓存）
     */
    public function getBusinessStatusRealtime(Carbon $time = null): array
    {
        $time = $time ?: $this->getCurrentTime();

        // 直接计算，不使用缓存
        $isOpen = $this->isOpen($time);

        $status = [
            'is_open' => $isOpen,
            'current_time' => $time->format('Y-m-d H:i:s'),
            'timestamp' => $time->timestamp,
            'timezone' => $this->getTimezone(),
            'maintenance_mode' => $this->isMaintenanceMode(),
            'realtime' => true, // 标记为实时数据
        ];

        if ($this->isMaintenanceMode()) {
            $status['message'] = $this->getMaintenanceMessage();
            return $status;
        }

        // 获取今日营业时间
        $todayHours = $this->getBusinessHoursForDate($time);
        $status['today_hours'] = $todayHours;

        if ($isOpen) {
            // 营业中，检查是否即将关闭
            $closingTime = $time->copy()->setTimeFromTimeString($todayHours['end']);
            $minutesToClose = $time->diffInMinutes($closingTime, false);
            $warningMinutes = $this->getClosingWarningMinutes();

            if ($minutesToClose <= $warningMinutes && $minutesToClose > 0) {
                $status['closing_soon'] = true;
                $status['minutes_to_close'] = $minutesToClose;
                $status['seconds_to_close'] = $time->diffInSeconds($closingTime, false);
                $status['message'] = "商城即将结束营业（{$minutesToClose}分钟后），请尽快下单";
            } else {
                $status['closing_soon'] = false;
                $status['minutes_to_close'] = 0;
                $status['seconds_to_close'] = 0;
                $status['message'] = '营业中';
            }
            $status['next_open_time'] = null;
        } else {
            // 未营业，计算下次营业时间
            $nextOpenTime = $this->getNextOpenTime($time);
            $status['next_open_time'] = $nextOpenTime ? $nextOpenTime->format('Y-m-d H:i:s') : null;
            $status['closing_soon'] = false;
            $status['minutes_to_close'] = 0;
            $status['seconds_to_close'] = 0;
            $status['message'] = $this->getClosedMessage($todayHours);
        }

        return $status;
    }
}
