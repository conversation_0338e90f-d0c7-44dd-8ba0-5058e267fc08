<template>
	<view class="proxy-order-container">
		<!-- 客户信息卡片 -->
		<view class="order-card client-card">
			<view class="card-header">
				<text class="card-title">客户信息</text>
				<view class="header-actions">
					<button class="change-btn" v-if="selectedClient" @tap="selectClient">更换</button>
				</view>
			</view>
			<view class="client-selector" @tap="selectClient">
				<view class="client-info" v-if="selectedClient">
					<view class="client-details">
						<text class="client-name">{{ selectedClient.name }}</text>
						<text class="client-phone">{{ selectedClient.phone }}</text>
						<text class="client-merchant" v-if="selectedClient.merchant_name">{{ selectedClient.merchant_name }}</text>
					</view>
				</view>
				<view class="client-placeholder" v-else>
					<text class="placeholder-text">点击选择客户</text>
					<text class="arrow-icon">></text>
				</view>
			</view>
		</view>
		
		<!-- 收货地址卡片 -->
		<view class="order-card address-card" v-if="selectedClient">
			<view class="card-header">
				<text class="card-title">收货地址</text>
				<view class="header-actions">
					<button class="add-btn" @tap="addAddress">新增</button>
				</view>
			</view>
			
			<!-- 地址加载状态 -->
			<view class="loading-addresses" v-if="loadingAddresses">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载地址...</text>
			</view>
			
			<!-- 地址列表 -->
			<view class="address-list" v-else-if="clientAddresses.length > 0">
				<view
					class="address-item"
					:class="{ selected: selectedAddress && selectedAddress.id === address.id }"
					v-for="address in clientAddresses"
					:key="address.id"
					@tap="selectAddress(address)"
				>
					<view class="address-info">
						<view class="address-main">
							<text class="contact-name">{{ address.contact_name }}</text>
							<text class="contact-phone">{{ address.contact_phone }}</text>
							<text class="default-badge" v-if="address.is_default">默认</text>
						</view>
						<text class="address-detail">{{ getFullAddress(address) }}</text>
					</view>
					<view class="address-radio">
						<view class="radio" :class="{ checked: selectedAddress && selectedAddress.id === address.id }">
							<text class="radio-icon" v-if="selectedAddress && selectedAddress.id === address.id">✓</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 无地址状态 -->
			<view class="empty-address" v-else>
				<text class="empty-text">该客户暂无收货地址</text>
				<view class="empty-actions">
					<button class="empty-btn" @tap="addAddress">添加地址</button>
				</view>
			</view>
		</view>
		
		<!-- 商品列表卡片 -->
		<view class="order-card products-card">
			<view class="card-header">
				<view class="header-left">
					<text class="card-title">订单商品</text>
					<text class="product-count" v-if="orderProducts.length > 0">({{ orderProducts.length }}件)</text>
				</view>
				<view class="header-actions">
					<button class="add-btn" @tap="addProduct">添加</button>
				</view>
			</view>

			<!-- 商品列表 -->
			<view class="product-list" v-if="orderProducts.length > 0">
				<view
					class="product-item"
					:class="{ 'removing': product.removing }"
					v-for="(product, index) in orderProducts"
					:key="index"
				>
					<image class="product-image" :src="product.image" mode="aspectFill"></image>
					<view class="product-info">
						<text class="product-name">{{product.name}}</text>

						<!-- 🌍 价格显示 - 支持会员优惠 -->
						<view class="product-meta">
							<view class="price-info" v-if="product.price_type && product.price_type !== 'base'">
								<!-- 有优惠价格 -->
								<text class="product-price discounted">¥{{formatPrice(product.price)}}</text>
								<text class="original-price" v-if="product.original_price">¥{{formatPrice(product.original_price)}}</text>
								<!-- 价格标签 -->
								<view class="price-badges">
									<text class="price-badge member-badge" v-if="product.has_member_discount">会员价</text>
									<text class="price-badge region-badge" v-if="product.has_region_price">区域价</text>
									<text class="price-badge" v-if="!product.has_member_discount && !product.has_region_price">{{ getPriceTypeBadge(product.price_type) }}</text>
								</view>
							</view>
							<view class="price-info" v-else>
								<!-- 普通价格 -->
								<text class="product-price">¥{{formatPrice(product.price)}}</text>
							</view>
							<text class="product-unit">/{{ getSaleUnitDisplay(product) }}</text>
						</view>

						<view class="product-total-info">
							<text class="product-total">小计: ¥{{ formatPrice(product.price * product.quantity) }}</text>
						</view>
					</view>
					<view class="product-actions">
						<view class="quantity-control">
							<button
								class="quantity-btn decrease-btn"
								:class="{ disabled: product.quantity <= 1 }"
								@tap="decreaseQuantity(index)"
								:disabled="product.quantity <= 1"
							>
								<text class="btn-icon">−</text>
							</button>
							<input
								class="quantity-input"
								type="number"
								:value="product.quantity"
								@input="updateQuantity(index, $event)"
								@blur="validateAndUpdateQuantity(index, $event)"
								@focus="onQuantityFocus(index)"
								:maxlength="3"
							/>
							<button
								class="quantity-btn increase-btn"
								:class="{ disabled: product.quantity >= 999 }"
								@tap="increaseQuantity(index)"
								:disabled="product.quantity >= 999"
							>
								<text class="btn-icon">+</text>
							</button>
						</view>
						<button class="remove-btn" @tap="confirmRemoveProduct(index)">
							<text class="remove-icon">🗑️</text>
						</button>
					</view>
				</view>
			</view>
			<view class="empty-products">
				<text class="empty-text">暂无商品，请添加</text>
			</view>
		</view>

		<!-- 订单信息卡片 -->
		<view class="order-card order-info-card" v-if="orderProducts.length > 0">
			<view class="card-header">
				<text class="card-title">📋 订单信息</text>
			</view>
			<view class="order-form">
				<view class="form-item">
					<text class="form-label">💳 支付方式</text>
					<picker mode="selector" :value="paymentMethodIndex" :range="paymentMethods" range-key="label" @change="onPaymentMethodChange">
						<view class="picker-value">
							<text class="picker-icon">{{ paymentMethods[paymentMethodIndex].icon }}</text>
							<text class="picker-text">{{ paymentMethods[paymentMethodIndex].label }}</text>
							<text class="arrow-icon">></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<text class="form-label">🚚 配送方式</text>
					<picker mode="selector" :value="deliveryMethodIndex" :range="deliveryMethods" range-key="label" @change="onDeliveryMethodChange">
						<view class="picker-value">
							<text class="picker-icon">{{ deliveryMethods[deliveryMethodIndex].icon }}</text>
							<text class="picker-text">{{ deliveryMethods[deliveryMethodIndex].label }}</text>
							<text class="delivery-fee" v-if="deliveryMethods[deliveryMethodIndex].fee > 0">
								+¥{{ deliveryMethods[deliveryMethodIndex].fee }}
							</text>
							<text class="arrow-icon">></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<text class="form-label">📝 备注信息</text>
					<textarea 
						class="form-textarea" 
						placeholder="请输入订单备注（选填）" 
						v-model="orderNotes" 
						maxlength="200"
						:show-count="true"
					></textarea>
				</view>
			</view>
		</view>
		
		<!-- 订单总计 -->
		<view class="order-summary" v-if="orderProducts.length > 0">
			<view class="summary-header">
				<text class="summary-title">💰 订单总计</text>
			</view>
			<view class="summary-content">
				<view class="summary-row">
					<text class="summary-label">商品总计</text>
					<text class="summary-value">{{ getTotalQuantity() }}件</text>
				</view>
				<view class="summary-row">
					<text class="summary-label">商品金额</text>
					<text class="summary-value">¥{{ formatPrice(orderTotal) }}</text>
				</view>
				<view class="summary-row">
					<text class="summary-label">配送费用</text>
					<text class="summary-value">{{ getDeliveryFee() }}</text>
				</view>
				<view class="summary-divider"></view>
				<view class="summary-row total-row">
					<text class="summary-label">订单总额</text>
					<text class="summary-value total-value">¥{{ formatPrice(getFinalTotal()) }}</text>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="footer">
			<view class="total-amount">
				<text>总计：</text>
				<text class="price">¥{{ formatPrice(getFinalTotal()) }}</text>
			</view>
			<view class="submit-btn" :class="{ disabled: !canSubmit }" @click="submitOrder">提交订单</view>
		</view>
		
		<!-- 浮动操作按钮 -->
		<view class="floating-actions" v-if="selectedClient">
			<button class="floating-btn" @tap="quickAddProduct">
				<text class="floating-icon">🛍️</text>
			</button>
		</view>
	</view>
</template>

<script>
import clientApi from '../../api/client.js'
import orderApi from '../../api/order.js'
import config from '../../utils/config.js'

export default {
	data() {
		return {
			selectedClient: null,
			clientAddresses: [],
			selectedAddress: null,
			orderProducts: [],
			orderNotes: '',
			submitting: false,
			loadingAddresses: false,
			
			// 支付方式选项
			paymentMethods: [
				{ value: 'cod', label: '货到付款', icon: '💰' },
				{ value: 'wechat', label: '微信支付', icon: '💚' },
				{ value: 'alipay', label: '支付宝', icon: '💙' },
				{ value: 'cash', label: '现金支付', icon: '💵' }
			],
			paymentMethodIndex: 0,
			
			// 配送方式选项
			deliveryMethods: [
				{ value: 'standard', label: '标准配送', fee: 0, icon: '🚚' },
				{ value: 'express', label: '快递配送', fee: 10, icon: '⚡' },
				{ value: 'same_day', label: '当日达', fee: 20, icon: '🚀' },
				{ value: 'self_pickup', label: '自行提货', fee: 0, icon: '🏪' }
			],
			deliveryMethodIndex: 0,
			orderAmount: '0.00' // 新增订单总金额
		}
	},
	
	computed: {
		// 订单总金额
		orderTotal() {
			return this.orderProducts.reduce((total, product) => {
				return total + (product.price * product.quantity)
			}, 0)
		},
		
		// 是否可以提交订单
		canSubmit() {
			return this.selectedClient && 
				   this.selectedAddress && 
				   this.orderProducts.length > 0 &&
				   this.orderProducts.every(p => p.quantity > 0)
		}
	},
	
	onLoad(options) {
		// 页面加载时的初始化
		console.log('代客下单页面参数:', options)

		// 不自动选择客户，用户必须手动选择
		// 移除自动选择逻辑，确保用户主动选择客户

		// 初始化支付方式
		this.paymentMethodIndex = 0
		this.deliveryMethodIndex = 0
	},
	
	onShow() {
		// 页面显示时检查是否有新选择的客户或商品
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		
		// 检查是否有新选择的客户
		if (currentPage.data && currentPage.data.selectedClient) {
			this.selectedClient = currentPage.data.selectedClient
			this.loadClientAddresses()
			
			// 清除临时数据
			delete currentPage.data.selectedClient
		}
		
		// 检查是否有新选择的商品
		if (currentPage.data && currentPage.data.selectedProducts) {
			const newProducts = currentPage.data.selectedProducts
			
			// 记录选中的商品信息，用于调试
			console.log('接收到选中的商品:', JSON.stringify(newProducts, null, 2))
			
			// 合并商品，如果已存在则增加数量
			newProducts.forEach(newProduct => {
				const existingIndex = this.orderProducts.findIndex(p => p.id === newProduct.id)
				if (existingIndex >= 0) {
					this.orderProducts[existingIndex].quantity += newProduct.quantity
				} else {
					this.orderProducts.push({
						...newProduct,
						quantity: newProduct.quantity || 1
					})
				}
			})
			
			// 清除临时数据
			delete currentPage.data.selectedProducts
			
			uni.showToast({
				title: `已添加 ${newProducts.length} 个商品`,
				icon: 'success',
				duration: 2000
			})
		}
		
		// 检查是否有新添加的地址
		if (currentPage.data && currentPage.data.newAddress) {
			const newAddress = currentPage.data.newAddress
			
			// 添加到地址列表
			this.clientAddresses.unshift(newAddress)
			
			// 自动选择新地址
			this.selectedAddress = newAddress
			
			// 清除临时数据
			delete currentPage.data.newAddress
			
			uni.showToast({
				title: '新地址已添加并选中',
				icon: 'success',
				duration: 2000
			})
		}
	},
	
	onPullDownRefresh() {
		// 下拉刷新
		this.refreshData()
	},
	
	onReachBottom() {
		// 上拉加载更多（如果需要的话）
		console.log('到达页面底部')
	},
	
	methods: {
		// 接收从选择客户页面传递过来的客户信息
		onClientSelected(client) {
			console.log('接收到选中的客户:', client)
			console.log('🔍 客户完整数据结构:', JSON.stringify(client, null, 2))
			console.log('🔍 客户区域ID:', client.region_id)
			console.log('🔍 客户区域ID类型:', typeof client.region_id)

			this.selectedClient = client

			// 加载该客户的地址
			this.loadClientAddresses()

			// 显示选择成功的提示
			uni.showToast({
				title: `已选择客户：${client.name || client.merchant_name}`,
				icon: 'success',
				duration: 2000
			})
		},
		
		// 获取商品图片URL
		getProductImageUrl(product) {
			// 优先使用cover_url
			if (product.cover_url) {
				return this.optimizeImageUrl(product.cover_url);
			}
			
			// 其次使用images数组中的主图或第一张图
			if (product.images && product.images.length > 0) {
				// 尝试获取主图
				const mainImage = product.images.find(img => img.is_main);
				if (mainImage) {
					return this.optimizeImageUrl(mainImage.url);
				}
				// 否则返回第一张图
				return this.optimizeImageUrl(product.images[0].url);
			}
			
			// 最后使用image字段
			if (product.image) {
				return this.optimizeImageUrl(product.image);
			}
			
			// 默认图片
			return '/static/default-product.png';
		},
		
		// 优化图片URL，添加尺寸参数
		optimizeImageUrl(url) {
			if (!url) return '/static/default-product.png';
			
			// 检查URL是否为相对路径
			if (url.startsWith('/')) {
				// 相对路径不处理
				return url;
			}
			
			try {
				// 如果是阿里云OSS图片，添加尺寸参数
				if (url.includes('aliyuncs.com')) {
					// 添加尺寸处理参数，限制宽度为60像素
					if (!url.includes('x-oss-process=')) {
						return `${url}?x-oss-process=image/resize,w_60`;
					}
				}
				
				// 如果是腾讯云COS图片
				if (url.includes('myqcloud.com')) {
					// 添加尺寸处理参数
					if (!url.includes('imageView2')) {
						return `${url}?imageView2/2/w/60`;
					}
				}
			} catch (error) {
				console.error('处理图片URL出错:', error);
			}
			
			// 返回原始URL
			return url;
		},
		
		// 获取商品描述
		getProductDescription(product) {
			return product.description || '新鲜直达';
		},
		
		// 获取客户头像文字
		getClientAvatar(client) {
			if (client.merchant_name) {
				return client.merchant_name.charAt(0)
			} else if (client.name) {
				return client.name.charAt(0)
			}
			return '客'
		},
		
		// 判断是否为新客户
		isNewClient(client) {
			// 这里可以根据注册时间或订单数量判断
			return client.orders_count === 0 || !client.orders_count
		},
		
		// 格式化价格
		formatPrice(price) {
			return parseFloat(price || 0).toFixed(2)
		},

		// 🌍 获取价格类型标识
		getPriceTypeBadge(priceType) {
			const typeMap = {
				'region': '区域优惠',
				'member': '会员价',
				'manual': '特价',
				'base': '原价'
			}
			return typeMap[priceType] || '优惠'
		},
		
		// 获取商品总数量
		getTotalQuantity() {
			return this.orderProducts.reduce((total, product) => {
				return total + product.quantity
			}, 0)
		},
		
		// 获取配送费用
		getDeliveryFee() {
			const method = this.deliveryMethods[this.deliveryMethodIndex]
			return method.fee > 0 ? `¥${method.fee}` : '免费'
		},
		
		// 获取最终总额
		getFinalTotal() {
			const deliveryFee = this.deliveryMethods[this.deliveryMethodIndex].fee || 0
			return this.orderTotal + deliveryFee
		},
		
		// 获取提交提示
		getSubmitTip() {
			if (!this.selectedClient) {
				return '请先选择客户'
			}
			if (!this.selectedAddress) {
				return '请选择收货地址'
			}
			if (this.orderProducts.length === 0) {
				return '请添加商品'
			}
			return '请完善订单信息'
		},
		
		// 处理图片加载错误
		handleImageError(e) {
			console.log('图片加载失败:', e)
			// 可以设置默认图片
		},

		// 🔧 获取销售单位显示文本
		getSaleUnitDisplay(product) {
			// 必须使用商品的销售单位名称
			if (product.sale_unit && product.sale_unit.name) {
				return product.sale_unit.name
			}
			// 如果没有销售单位，返回空或提示
			return product.sale_unit ? (product.sale_unit.name || '') : ''
		},
		
		// 验证商品数量
		validateQuantity(index) {
			const product = this.orderProducts[index]
			if (product.quantity < 1) {
				product.quantity = 1
			}
			if (product.quantity > 999) {
				product.quantity = 999
				uni.showToast({
					title: '数量不能超过999',
					icon: 'none'
				})
			}
		},
		
		// 快速添加商品
		quickAddProduct() {
			this.addProduct()
		},
		
		// 刷新数据
		async refreshData() {
			if (this.selectedClient) {
				await this.loadClientAddresses()
			}
			uni.stopPullDownRefresh()
		},
		
		// 选择客户
		selectClient() {
			uni.navigateTo({
				url: '/pages/proxy-order/select-client'
			})
		},
		
		// 选择地址
		selectAddress(address) {
			this.selectedAddress = address
			
			// 添加选择反馈
			uni.showToast({
				title: '已选择地址',
				icon: 'success',
				duration: 1000
			})
		},
		
		// 🌍 添加商品（传递客户信息）
		addProduct() {
			if (!this.selectedClient) {
				uni.showToast({
					title: '请先选择客户',
					icon: 'none'
				})
				return
			}

			// 🌍 通过页面栈传递客户信息给商品选择页面
			const pages = getCurrentPages()
			const currentPage = pages[pages.length - 1]
			currentPage.data = currentPage.data || {}
			currentPage.data.selectedClient = this.selectedClient

			console.log('🌍 传递客户信息给商品选择页面:', {
				client_id: this.selectedClient.id,
				client_name: this.selectedClient.name,
				region_id: this.selectedClient.region_id
			})

			uni.navigateTo({
				url: '/pages/proxy-order/select-product'
			})
		},
		
		// 🔥 优化：增加商品数量
		increaseQuantity(index) {
			const product = this.orderProducts[index]
			if (product.quantity >= 999) {
				uni.showToast({
					title: '数量已达上限',
					icon: 'none',
					duration: 1500
				})
				return
			}

			product.quantity++

			// 添加触觉反馈
			uni.vibrateShort({
				type: 'light'
			})

			console.log(`📈 商品数量增加: ${product.name} → ${product.quantity}`)
		},

		// 🔥 优化：减少商品数量
		decreaseQuantity(index) {
			const product = this.orderProducts[index]
			if (product.quantity <= 1) {
				uni.showToast({
					title: '数量不能少于1',
					icon: 'none',
					duration: 1500
				})
				return
			}

			product.quantity--

			// 添加触觉反馈
			uni.vibrateShort({
				type: 'light'
			})

			console.log(`📉 商品数量减少: ${product.name} → ${product.quantity}`)
		},

		// 🔥 优化：更新商品数量（输入时）
		updateQuantity(index, event) {
			const inputValue = event.detail.value
			const quantity = parseInt(inputValue) || 0

			// 实时更新，但不做严格验证（在blur时验证）
			if (quantity >= 1 && quantity <= 999) {
				this.orderProducts[index].quantity = quantity
			}
		},

		// 🔥 新增：数量输入验证和更新（失焦时）
		validateAndUpdateQuantity(index, event) {
			const inputValue = event.detail.value
			let quantity = parseInt(inputValue) || 1

			// 数量范围验证
			if (quantity < 1) {
				quantity = 1
				uni.showToast({
					title: '数量不能少于1',
					icon: 'none',
					duration: 1500
				})
			} else if (quantity > 999) {
				quantity = 999
				uni.showToast({
					title: '数量不能超过999',
					icon: 'none',
					duration: 1500
				})
			}

			this.orderProducts[index].quantity = quantity
			console.log(`✏️ 手动输入数量: ${this.orderProducts[index].name} → ${quantity}`)
		},

		// 🔥 新增：数量输入框获得焦点
		onQuantityFocus(index) {
			console.log(`🎯 数量输入框获得焦点: ${this.orderProducts[index].name}`)
		},

		// 🔥 优化：确认删除商品
		confirmRemoveProduct(index) {
			const product = this.orderProducts[index]

			uni.showModal({
				title: '确认删除商品',
				content: `确定要从订单中删除"${product.name}"吗？\n\n当前数量：${product.quantity}${this.getSaleUnitDisplay(product)}\n单价：¥${this.formatPrice(product.price)}`,
				confirmText: '删除',
				cancelText: '取消',
				confirmColor: '#ff4d4f',
				success: (res) => {
					if (res.confirm) {
						this.removeProduct(index)
					}
				}
			})
		},

		// 🔥 优化：移除商品
		removeProduct(index) {
			const product = this.orderProducts[index]

			// 添加删除动画效果的数据标记
			this.$set(product, 'removing', true)

			// 延迟删除，给用户视觉反馈
			setTimeout(() => {
				this.orderProducts.splice(index, 1)

				uni.showToast({
					title: '商品已删除',
					icon: 'success',
					duration: 2000
				})

				// 添加触觉反馈
				uni.vibrateShort({
					type: 'medium'
				})

				console.log(`🗑️ 商品已删除: ${product.name}`)

				// 如果没有商品了，显示提示
				if (this.orderProducts.length === 0) {
					uni.showToast({
						title: '订单商品已清空',
						icon: 'none',
						duration: 2000
					})
				}
			}, 300)
		},
		
		// 支付方式改变
		onPaymentMethodChange(event) {
			this.paymentMethodIndex = event.detail.value
		},
		
		// 配送方式改变
		onDeliveryMethodChange(event) {
			this.deliveryMethodIndex = event.detail.value
		},
		
		// 加载客户地址
		async loadClientAddresses() {
			if (!this.selectedClient) return

			console.log('🔍 开始加载客户地址, 客户ID:', this.selectedClient.id)
			this.loadingAddresses = true

			try {
				const response = await clientApi.getClientAddresses(this.selectedClient.id)
				console.log('📋 地址API响应:', response)

				this.clientAddresses = response.data || []
				console.log('📍 解析后的地址列表:', this.clientAddresses)
				console.log('📊 地址数量:', this.clientAddresses.length)

				// 自动选择默认地址
				const defaultAddress = this.clientAddresses.find(addr => addr.is_default)
				if (defaultAddress) {
					this.selectedAddress = defaultAddress
					console.log('✅ 自动选择默认地址:', defaultAddress)
				} else if (this.clientAddresses.length > 0) {
					this.selectedAddress = this.clientAddresses[0]
					console.log('✅ 自动选择第一个地址:', this.clientAddresses[0])
				} else {
					console.log('⚠️ 没有找到任何地址')
				}
			} catch (error) {
				console.error('❌ 加载客户地址失败:', error)
				console.error('❌ 错误详情:', JSON.stringify(error))
				uni.showToast({
					title: '加载地址失败',
					icon: 'none'
				})
			} finally {
				this.loadingAddresses = false
			}
		},
		
		// 提交订单
		async submitOrder() {
			if (this.submitting) return

			// 检查是否选择了客户
			if (!this.selectedClient) {
				uni.showToast({
					title: '请先选择客户',
					icon: 'none',
					duration: 2000
				})
				return
			}

			// 检查是否选择了地址
			if (!this.selectedAddress) {
				uni.showToast({
					title: '请选择收货地址',
					icon: 'none',
					duration: 2000
				})
				return
			}

			// 检查是否有商品
			if (this.orderProducts.length === 0) {
				uni.showToast({
					title: '请添加商品',
					icon: 'none',
					duration: 2000
				})
				return
			}

			if (!this.canSubmit) return
			
			// 最终确认
			const confirmResult = await new Promise((resolve) => {
				uni.showModal({
					title: '确认下单',
					content: `客户：${this.selectedClient.name}\n商品：${this.getTotalQuantity()}件\n总额：¥${this.formatPrice(this.getFinalTotal())}`,
					confirmColor: '#007AFF',
					success: (res) => resolve(res.confirm),
					fail: () => resolve(false)
				})
			})
			
			if (!confirmResult) return
			
			this.submitting = true
			
			try {
				// 🌍 准备订单数据（包含区域信息）
				const orderData = {
					client_id: this.selectedClient.id,
					user_address_id: this.selectedAddress.id,
					contact_name: this.selectedAddress.contact_name,
					contact_phone: this.selectedAddress.contact_phone,
					shipping_address: this.getFullAddress(this.selectedAddress),
					payment_method: this.paymentMethods[this.paymentMethodIndex].value,
					delivery_method: this.deliveryMethods[this.deliveryMethodIndex].value,
					delivery_fee: this.deliveryMethods[this.deliveryMethodIndex].fee || 0,
					notes: this.orderNotes,
					// 🌍 传递客户区域信息
					region_id: this.selectedClient.region_id,
					items: this.orderProducts.map(product => {
						console.log('🔧 提交商品单位信息:', {
							product_id: product.id,
							product_name: product.name,
							sale_unit: product.sale_unit,
							unit_id: product.sale_unit?.id
						})

						return {
							product_id: product.id,
							product_name: product.name || '未知商品',
							product_sku: product.sku || product.code || '',
							quantity: product.quantity,
							price: product.price,
							total: (product.price * product.quantity).toFixed(2),
							// 🔧 只传递销售单位ID，后端会通过关联获取单位名称
							unit_id: product.sale_unit?.id || null,
							// 🌍 传递价格类型信息
							price_type: product.price_type || 'base',
							original_price: product.original_price || product.price,
							discount_info: product.discount_info || []
						}
					})
				}
				
				console.log('提交订单数据:', orderData)
				const response = await orderApi.createProxyOrder(orderData)
				console.log('订单创建响应:', response)
				
				// 检查响应格式
				if (response && response.code === 200) {
					uni.showToast({
						title: '下单成功',
						icon: 'success',
						duration: 2000
					})

					// 获取订单ID
					const orderId = response.data.id
				
				// 跳转到订单详情页
				setTimeout(() => {
					uni.redirectTo({
						url: `/pages/orders/order-detail?id=${orderId}`
					})
				}, 2000)
				} else {
					// 处理业务逻辑错误
					const errorMessage = response?.message || '下单失败，请重试'
					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				}
				
			} catch (error) {
				console.error('提交订单失败:', error)
				
				// 更详细的错误处理
				let errorMessage = '网络错误，请检查网络连接'
				if (error.response) {
					errorMessage = error.response.data?.message || `服务器错误 (${error.response.status})`
				} else if (error.message) {
					errorMessage = error.message
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.submitting = false
			}
	},
	
		// 添加地址
		addAddress() {
			if (!this.selectedClient) {
				uni.showToast({
					title: '请先选择客户',
					icon: 'none'
				})
				return
			}
			
			const params = {
				clientId: this.selectedClient.id,
				clientName: encodeURIComponent(this.selectedClient.name),
				clientPhone: this.selectedClient.phone
			}
			
			const queryString = Object.keys(params).map(key => `${key}=${params[key]}`).join('&')
			
			uni.navigateTo({
				url: `/pages/proxy-order/add-address?${queryString}`
			})
		},
		
		// 获取完整地址
		getFullAddress(address) {
			if (!address) return ''

			let fullAddress = ''

			// 组合省市区
			if (address.province || address.city || address.district) {
				fullAddress = `${address.province || ''}${address.city || ''}${address.district || ''}`
			}

			// 添加详细地址 - 支持多种字段名
			if (address.address) {
				fullAddress += address.address
			} else if (address.detail_address) {
				fullAddress += address.detail_address
			}

			// 如果有full_address字段，优先使用
			if (address.full_address) {
				fullAddress = address.full_address
			}

			return fullAddress || '地址信息不完整'
		},
		
		// 更新选中的商品
		updateSelectedProducts(products) {
			if (products && products.length > 0) {
				// 合并已有商品和新选择的商品
				products.forEach(newProduct => {
					const existingProduct = this.orderProducts.find(item => item.id === newProduct.id);
					if (existingProduct) {
						// 如果商品已存在，增加数量
						existingProduct.quantity += newProduct.quantity;
					} else {
						// 否则添加新商品
						this.orderProducts.push(newProduct);
					}
				});
				
				// 更新总价
				this.calculateTotalAmount();
			}
		},
		
		// 计算订单总金额
		calculateTotalAmount() {
			let total = 0;
			this.orderProducts.forEach(product => {
				total += product.price * product.quantity;
			});
			this.orderAmount = total.toFixed(2);
		}
	}
}
</script>

<style scoped>
.proxy-order-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 通用卡片样式 */
.order-card {
	margin: 16rpx;
	background: #ffffff;
	border-radius: 8rpx;
	border: 1rpx solid #f0f0f0;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.header-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.card-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #262626;
}

.change-btn, .add-btn {
	padding: 8rpx 16rpx;
	border-radius: 6rpx;
	font-size: 22rpx;
	font-weight: 500;
	border: 1rpx solid #e9ecef;
	background: #ffffff;
	color: #595959;
}

.change-btn:active, .add-btn:active {
	background: #f8f9fa;
}

.add-btn {
	background: #1890ff;
	color: #ffffff;
	border-color: #1890ff;
}

.add-btn:active {
	background: #096dd9;
}

.btn-icon {
	font-size: 20rpx;
	font-weight: 600;
}

.btn-text {
	font-size: 22rpx;
}

.card-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.required {
	color: #dc3545;
	font-size: 24rpx;
	font-weight: 600;
}

.client-selector {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.client-selector:active {
	background: #f8f9fa;
}

.client-details {
	flex: 1;
}

.client-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #262626;
	margin-bottom: 4rpx;
	display: block;
}

.client-phone {
	font-size: 24rpx;
	color: #8c8c8c;
	margin-bottom: 4rpx;
	display: block;
}

.client-merchant {
	font-size: 24rpx;
	color: #8c8c8c;
	display: block;
}

.client-details {
	flex: 1;
}

.client-main {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 8rpx;
}

.client-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.client-tags {
	display: flex;
	gap: 8rpx;
}

.tag {
	font-size: 18rpx;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
	font-weight: 500;
}

.vip-tag {
	background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
	color: #ffffff;
}

.new-tag {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	color: #ffffff;
}

.client-phone,
.client-merchant {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

/* 占位符样式 */
.client-placeholder {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
}

.placeholder-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.placeholder-icon {
	font-size: 60rpx;
	opacity: 0.3;
	margin-bottom: 12rpx;
}

.placeholder-text {
	font-size: 28rpx;
	color: #666666;
	font-weight: 500;
	margin-bottom: 4rpx;
}

.placeholder-tip {
	font-size: 24rpx;
	color: #999999;
}

/* 收货地址卡片 */
.order-card.address-card {
	margin-bottom: 12rpx;
}

/* 地址列表 */
.loading-addresses {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 0;
}

.loading-spinner {
	width: 32rpx;
	height: 32rpx;
	border: 3rpx solid rgba(0, 122, 255, 0.3);
	border-radius: 50%;
	border-top-color: #007AFF;
	animation: spin 1s linear infinite;
	margin-bottom: 12rpx;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.loading-text {
	font-size: 24rpx;
	color: #666666;
}

.address-list {
	padding: 0 24rpx 20rpx;
}

.address-item {
	display: flex;
	align-items: center;
	padding: 16rpx 20rpx;
	margin-bottom: 8rpx;
	border-radius: 8rpx;
	background: #f8f9fa;
	border: 1rpx solid #e9ecef;
}

.address-item.selected {
	background: #e6f7ff;
	border-color: #1890ff;
}

.address-info {
	flex: 1;
}

.address-main {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 8rpx;
}

.contact-name {
	font-size: 26rpx;
	font-weight: 600;
	color: #262626;
}

.contact-phone {
	font-size: 24rpx;
	color: #8c8c8c;
}

.default-badge {
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	border-radius: 4rpx;
	background: #52c41a;
	color: #ffffff;
}

.address-detail {
	font-size: 24rpx;
	color: #595959;
	line-height: 1.4;
}

.address-badges {
	display: flex;
	gap: 8rpx;
}

.badge {
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 600;
}

.default-badge {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	color: #ffffff;
}

.note-badge {
	background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
	color: #ffffff;
}

.address-detail {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
}

.address-actions {
	margin-left: 24rpx;
}

.address-radio {
	margin-left: 16rpx;
}

.radio {
	width: 32rpx;
	height: 32rpx;
	border: 1rpx solid #d9d9d9;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.address-item.selected .radio {
	border-color: #1890ff;
	background: #1890ff;
}

.radio-icon {
	font-size: 20rpx;
	color: #ffffff;
	font-weight: 600;
}

/* 商品列表卡片 */
.order-card.products-card {
	margin-bottom: 12rpx;
}

.product-list {
	padding: 0 24rpx 20rpx;
}

.product-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
	transition: all 0.3s ease;
	transform: translateX(0);
	opacity: 1;
}

.product-item:last-child {
	border-bottom: none;
}

/* 🔥 删除动画效果 */
.product-item.removing {
	transform: translateX(-100%);
	opacity: 0;
	height: 0;
	padding: 0;
	margin: 0;
	border: none;
	overflow: hidden;
}

.product-image {
	width: 60rpx;
	height: 60rpx;
	border-radius: 6rpx;
	margin-right: 16rpx;
	flex-shrink: 0;
	background: #f5f5f5;
}

.product-info {
	flex: 1;
}

.product-name {
	font-size: 26rpx;
	color: #262626;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.product-meta {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.price-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
	flex: 1;
}

.product-price {
	font-size: 24rpx;
	color: #ff4d4f;
	font-weight: 600;
}

/* 🌍 区域价格样式 */
.product-price.discounted {
	color: #ff6b00;
	font-size: 26rpx;
	font-weight: 700;
}

.original-price {
	font-size: 20rpx;
	color: #999999;
	text-decoration: line-through;
}

/* 价格标签容器 */
.price-badges {
	display: flex;
	gap: 6rpx;
}

/* 价格标签基础样式 */
.price-badge {
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 4rpx;
	color: #ffffff;
	font-weight: 600;
	background: linear-gradient(135deg, #ff6b00 0%, #ff8c00 100%);
}

/* 会员价标签 */
.price-badge.member-badge {
	background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
}

/* 区域价标签 */
.price-badge.region-badge {
	background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

/* 兼容旧的价格类型标签 */
.price-type-badge {
	font-size: 18rpx;
	padding: 2rpx 6rpx;
	border-radius: 4rpx;
	background: linear-gradient(135deg, #ff6b00 0%, #ff8c00 100%);
	color: #ffffff;
	font-weight: 600;
}

.product-unit {
	font-size: 22rpx;
	color: #8c8c8c;
}

.product-quantity {
	font-size: 22rpx;
	color: #595959;
}

.empty-products {
	padding: 20rpx 24rpx;
	text-align: center;
}

.product-spec {
	font-size: 20rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.product-desc {
	font-size: 20rpx;
	color: #666666;
	margin-bottom: 4rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.product-price-info {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.product-price {
	font-size: 24rpx;
	color: #ff6b00;
	font-weight: 600;
}

.product-unit {
	font-size: 20rpx;
	color: #999;
	margin-left: 6rpx;
}

.product-quantity {
	font-size: 20rpx;
	color: #666;
	margin-left: 6rpx;
}

.product-actions {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 16rpx;
}

.quantity-control {
	display: flex;
	align-items: center;
	border: 2rpx solid #e9ecef;
	border-radius: 16rpx;
	overflow: hidden;
	background: #ffffff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.quantity-btn {
	width: 64rpx;
	height: 64rpx;
	background: #f8f9fa;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
	position: relative;
}

.quantity-btn .btn-icon {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	line-height: 1;
}

.quantity-btn:active {
	background: #e9ecef;
	transform: scale(0.95);
}

.quantity-btn.disabled {
	background: #f5f5f5;
	cursor: not-allowed;
}

.quantity-btn.disabled .btn-icon {
	color: #cccccc;
}

/* 减少按钮样式 */
.decrease-btn {
	background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
	border-radius: 16rpx 0 0 16rpx;
}

.decrease-btn .btn-icon {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 300;
}

.decrease-btn:active {
	background: linear-gradient(135deg, #ff5252 0%, #f44336 100%);
}

.decrease-btn.disabled {
	background: #f5f5f5;
}

/* 增加按钮样式 */
.increase-btn {
	background: linear-gradient(135deg, #4caf50 0%, #43a047 100%);
	border-radius: 0 16rpx 16rpx 0;
}

.increase-btn .btn-icon {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 400;
}

.increase-btn:active {
	background: linear-gradient(135deg, #43a047 0%, #388e3c 100%);
}

.increase-btn.disabled {
	background: #f5f5f5;
}

.quantity-input {
	width: 88rpx;
	height: 64rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 600;
	border: none;
	background: #ffffff;
	color: #333333;
	outline: none;
	border-left: 1rpx solid #e9ecef;
	border-right: 1rpx solid #e9ecef;
}

.quantity-input:focus {
	background: #f0f8ff;
	color: #1890ff;
}

.product-total-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.product-total {
	font-size: 28rpx;
	font-weight: 700;
	color: #007AFF;
}

.remove-btn {
	width: 64rpx;
	height: 64rpx;
	background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
	border: none;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
	transition: all 0.2s ease;
	position: relative;
	overflow: hidden;
}

.remove-btn:active {
	background: linear-gradient(135deg, #ff3742 0%, #ff1744 100%);
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}

.remove-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s ease;
}

.remove-btn:active::before {
	left: 100%;
}

.remove-icon {
	font-size: 20rpx;
	color: #ffffff;
	z-index: 1;
}

/* 订单信息卡片 */
.order-card.order-info-card {
	margin-bottom: 12rpx;
}

.order-form {
	padding: 20rpx 24rpx;
}

.form-item {
	margin-bottom: 20rpx;
}

.form-label {
	font-size: 26rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 12rpx;
	display: block;
}

.picker-value {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.picker-value:active {
	background: rgba(0, 122, 255, 0.05);
	border-color: #007AFF;
}

.picker-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.picker-text {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
}

.delivery-fee {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 600;
	margin-right: 16rpx;
}

.form-textarea {
	width: 100%;
	min-height: 160rpx;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 2rpx solid transparent;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.5;
	transition: all 0.3s ease;
}

.form-textarea:focus {
	background: rgba(0, 122, 255, 0.05);
	border-color: #007AFF;
}

/* 订单总计 */
.order-summary {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.summary-header {
	padding: 32rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.summary-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.summary-content {
	padding: 32rpx;
}

.summary-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.summary-label {
	font-size: 28rpx;
	color: #666666;
}

.summary-value {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
}

.summary-divider {
	height: 2rpx;
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	margin: 24rpx 0;
}

.total-row {
	margin-bottom: 0;
	padding-top: 16rpx;
}

.total-row .summary-label {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.total-value {
	font-size: 40rpx;
	font-weight: 700;
	color: #007AFF;
}

/* 提交按钮 */
.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	padding: 32rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(20rpx);
}

.submit-info {
	text-align: center;
	margin-bottom: 20rpx;
}

.submit-tip {
	font-size: 28rpx;
	color: #999999;
}

.submit-btn {
	width: 200rpx;
	height: 64rpx;
	background: #1890ff;
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	font-size: 26rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn:active {
	background: #096dd9;
}

.submit-btn.disabled {
	background: #d9d9d9;
	color: #8c8c8c;
}

.submit-content {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.submit-text {
	font-size: 32rpx;
	font-weight: 700;
}

.submit-amount {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

/* 空地址状态 */
.empty-address {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
}

.empty-text {
	font-size: 26rpx;
	color: #8c8c8c;
}

.empty-actions {
	display: flex;
	justify-content: flex-end;
}

.empty-btn {
	padding: 8rpx 16rpx;
	border-radius: 6rpx;
	font-size: 22rpx;
	font-weight: 500;
	background: #1890ff;
	color: #ffffff;
	border: none;
}

.empty-btn:active {
	background: #096dd9;
}

/* 浮动操作按钮 */
.floating-actions {
	position: fixed;
	bottom: 140rpx;
	right: 32rpx;
	z-index: 999;
}

.floating-btn {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	color: #ffffff;
	border: none;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.4);
	transition: all 0.3s ease;
}

.floating-btn:active {
	transform: scale(0.95);
}

.floating-icon {
	font-size: 48rpx;
}

/* 通用样式 */
.arrow-icon {
	font-size: 32rpx;
	color: #cccccc;
	margin-left: 16rpx;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
	.stats-overview {
		padding: 24rpx 16rpx 16rpx;
	}
	
	.stats-card {
		padding: 16rpx 12rpx;
		margin: 0 4rpx;
	}
	
	.stats-number {
		font-size: 32rpx;
	}
	
	.stats-label {
		font-size: 22rpx;
	}
	
	.progress-indicator {
		padding: 24rpx 16rpx;
	}
	
	.step-text {
		font-size: 20rpx;
	}
	
	.order-card {
		margin: 16rpx;
		padding: 24rpx;
	}
	
	.client-avatar {
		width: 80rpx;
		height: 80rpx;
		margin-right: 24rpx;
	}
	
	.avatar-text {
		font-size: 32rpx;
	}
}

	/* 底部操作栏 */
	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 80rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 24rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 100;
	}

	.total-amount {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.total-amount .price {
		color: #ff6b00;
		font-size: 32rpx;
		font-weight: 600;
	}

	.submit-btn {
		width: 200rpx;
		height: 64rpx;
		background-color: #ff6b00;
		color: #fff;
		border-radius: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
	}
</style> 