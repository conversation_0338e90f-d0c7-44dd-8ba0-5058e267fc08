<?php

namespace App\Order\Models;

use App\Crm\Models\UserAddress;
use App\Employee\Models\Employee;
use App\Models\User;
use App\Delivery\Models\Delivery;
use App\Region\Models\Region;
use App\Order\Models\OrderPromotionRecord;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'user_address_id',
        'created_by_id',
        'order_no',
        'total',
        'subtotal',
        'discount',
        'promotion_discount',
        'promotion_details',
        'payment_discount',
        'payment_discount_info',
        'original_total',
        'pricing_info',
        'status',
        'source',
        'shipping_address',
        'contact_name',
        'contact_phone',
        'payment_method',
        'payment_no',
        'notes',
        'delivery_method',
        'delivery_date',
        'delivery_time',
        'delivery_time_custom',
        'delivery_notes',
        'delivery_arranged_at',
        'delivery_arranged_by',
        'is_cod',
        'cod_status',
        'cod_payment_method',
        'region_id',
        'inventory_policies_used',
        'has_negative_stock_items',
        'negative_stock_approved',
        'approved_by_id',
        'approved_at',
        'cancelled_by',
        'stock_warnings',
        // 🔥 库存处理状态字段
        'inventory_method',
        'inventory_processed',
        'inventory_restored',
        // 货到付款结算相关字段
        'actual_payment_amount',
        'amount_difference',
        'settlement_status',
        'settlement_method',
        'settlement_completed_at',
        'settlement_operator_id',
        // 订单合并相关字段
        'is_merged',
        'is_merged_from',
        'merged_to_order_id',
        'order_merge_id',
        'pre_merge_data',
        'merge_discount_details',
        'merge_savings',
        // 确认收货相关字段
        'confirmed_at',
        'confirmed_by',
        // 🔥 新增：支付回调相关字段
        'payment_confirmed_at',
        'transaction_id',
        'payment_success_time',
        'payment_callback_data',
        // 🔥 重要：支付时间字段
        'paid_at',
        'shipped_at',
        'delivered_at',
        'cancelled_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'discount' => 'decimal:2',
        'promotion_discount' => 'decimal:2',
        'promotion_details' => 'array',
        'payment_discount' => 'decimal:2',
        'original_total' => 'decimal:2',
        'pricing_info' => 'array',
        'payment_discount_info' => 'array',
        'is_cod' => 'boolean',
        'is_corrected' => 'boolean',
        'original_payment_amount' => 'decimal:2',
        'final_payment_amount' => 'decimal:2',
        'delivery_date' => 'date',
        'paid_at' => 'datetime',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'confirmed_at' => 'datetime',
        // 🔥 新增：支付回调字段类型转换
        'payment_confirmed_at' => 'datetime',
        'payment_success_time' => 'datetime',
        'payment_callback_data' => 'array',
        'delivery_arranged_at' => 'datetime',
        'cod_paid_at' => 'datetime',
        'payment_link_expires_at' => 'datetime',
        'inventory_policies_used' => 'array',
        'has_negative_stock_items' => 'boolean',
        'negative_stock_approved' => 'boolean',
        'approved_at' => 'datetime',
        'stock_warnings' => 'array',
        // 🔥 库存处理状态字段类型转换
        'inventory_processed' => 'boolean',
        'inventory_restored' => 'boolean',
        // 货到付款结算相关字段
        'actual_payment_amount' => 'decimal:2',
        'amount_difference' => 'decimal:2',
        'settlement_completed_at' => 'datetime',
        // 订单合并相关字段
        'is_merged' => 'boolean',
        'is_merged_from' => 'boolean',
        'pre_merge_data' => 'array',
        'merge_discount_details' => 'array',
        'merge_savings' => 'decimal:2',
    ];

    /**
     * 生成唯一订单号
     * 
     * @return string
     */
    public static function generateOrderNo()
    {
        return date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }
    
    /**
     * 获取订单状态文本
     * 
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            'pending' => '待付款',
            'paid' => '已付款',
            'shipped' => '已发货',
            'delivered' => '已送达',
            'cancelled' => '已取消',
        ];
        
        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 获取订单状态名称（用于H5页面）
     * 
     * @return string
     */
    public function getStatusNameAttribute()
    {
        return $this->getStatusTextAttribute();
    }

    /**
     * 获取支付方式名称
     * 
     * @return string
     */
    public function getPaymentMethodNameAttribute()
    {
        $methodMap = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            'bank_transfer' => '银行转账',
            'cod' => '货到付款',
        ];
        
        return $methodMap[$this->payment_method] ?? '未知支付方式';
    }

    /**
     * 获取货到付款实际收款方式名称
     * 
     * @return string|null
     */
    public function getCodPaymentMethodNameAttribute()
    {
        if ($this->payment_method !== 'cod' || !$this->cod_payment_method) {
            return null;
        }
        
        $methodMap = [
            'cash' => '现金收款',
            'wechat' => '微信收款',
            'alipay' => '支付宝收款',
        ];
        
        return $methodMap[$this->cod_payment_method] ?? null;
    }

    /**
     * 获取完整的支付方式显示名称（包含货到付款的实际收款方式）
     * 
     * @return string
     */
    public function getFullPaymentMethodNameAttribute()
    {
        if ($this->payment_method === 'cod' && $this->cod_payment_method) {
            return $this->payment_method_name . '（' . $this->cod_payment_method_name . '）';
        }
        
        return $this->payment_method_name;
    }

    /**
     * 获取订单总金额
     * 
     * @return float
     */
    public function getTotalAmountAttribute()
    {
        return $this->total;
    }

    /**
     * 获取优惠金额
     * 
     * @return float
     */
    public function getDiscountAmountAttribute()
    {
        return $this->discount ?? 0;
    }

    /**
     * 获取收货地址
     * 
     * @return string
     */
    public function getDeliveryAddressAttribute()
    {
        return $this->shipping_address;
    }

    /**
     * Get the user that owns the order.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the employee who created this order (for proxy orders).
     */
    public function createdBy()
    {
        return $this->belongsTo(\App\Employee\Models\Employee::class, 'created_by_id');
    }

    /**
     * Get the delivery associated with the order.
     */
    public function delivery()
    {
        return $this->hasOne(Delivery::class);
    }
    
    /**
     * Get the items for the order.
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }
    
    /**
     * 获取订单关联的用户地址
     */
    public function userAddress()
    {
        return $this->belongsTo(UserAddress::class);
    }
    
    /**
     * 获取订单关联的区域
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }
    
    /**
     * 获取订单的更正记录
     */
    public function corrections()
    {
        return $this->hasMany(OrderCorrection::class);
    }

    /**
     * 获取订单的满减记录
     */
    public function promotionRecords()
    {
        return $this->hasMany(OrderPromotionRecord::class);
    }

    /**
     * 获取订单的付款链接（已迁移到账单系统）
     * @deprecated 使用 bills()->with('billingPaymentLinks') 替代
     */
    public function paymentLinks()
    {
        // 🔥 付款链接功能已迁移到账单系统
        // 请使用 $order->bills()->with('billingPaymentLinks') 获取付款链接
        return $this->hasMany(\App\Billing\Models\BillingPaymentLink::class, 'order_id');
    }
    
    /**
     * 获取订单关联的账单（一对多）
     */
    public function bills()
    {
        return $this->hasMany(\App\Billing\Models\Bill::class);
    }

    /**
     * 获取订单的主要账单（一对一）
     */
    public function bill()
    {
        return $this->hasOne(\App\Billing\Models\Bill::class)->latest();
    }

    /**
     * 获取订单的微信支付记录（一对多）
     */
    public function wechatPayments()
    {
        return $this->hasMany(\App\WechatPayment\Models\WechatServicePayment::class);
    }

    /**
     * 获取订单的微信退款记录（一对多）
     */
    public function wechatRefunds()
    {
        return $this->hasMany(\App\WechatPayment\Models\WechatServiceRefund::class);
    }
    
    /**
     * 获取订单关联的出库单据
     */
    public function outboundDocuments()
    {
        return $this->hasMany(\App\Inventory\Models\OutboundDocument::class);
    }
    
    /**
     * 获取订单的主要出库单据（销售出库）
     */
    public function outboundDocument()
    {
        return $this->hasOne(\App\Inventory\Models\OutboundDocument::class)
                    ->where('document_type', 'sales');
    }
    
    /**
     * 获取订单的更正状态
     * 
     * @return string none|pending|confirmed|cancelled
     */
    public function getCorrectionStatusAttribute()
    {
        // 如果没有更正记录，返回none
        if (!$this->corrections || $this->corrections->count() === 0) {
            return 'none';
        }
        
        // 检查是否有待处理的更正
        $hasPending = $this->corrections->where('status', 'pending')->count() > 0;
        if ($hasPending) {
            return 'pending';
        }
        
        // 检查是否有已确认的更正
        $hasConfirmed = $this->corrections->where('status', 'confirmed')->count() > 0;
        if ($hasConfirmed) {
            return 'confirmed';
        }
        
        // 如果只有已取消的更正，返回none
        return 'none';
    }
    
    /**
     * 获取是否已更正的布尔值
     * 
     * @return bool
     */
    public function getIsCorrectedAttribute()
    {
        return $this->correction_status === 'confirmed';
    }
    
    /**
     * 获取更正状态文本
     * 
     * @return string
     */
    public function getCorrectionStatusTextAttribute()
    {
        $statusMap = [
            'none' => '未更正',
            'pending' => '更正中',
            'confirmed' => '已更正',
            'cancelled' => '未更正'
        ];
        
        return $statusMap[$this->correction_status] ?? '未知状态';
    }
    
    /**
     * 获取更正记录数量
     * 
     * @return int
     */
    public function getCorrectionsCountAttribute()
    {
        return $this->corrections ? $this->corrections->count() : 0;
    }
    
    /**
     * 获取待处理更正记录数量
     * 
     * @return int
     */
    public function getPendingCorrectionsCountAttribute()
    {
        return $this->corrections ? $this->corrections->where('status', 'pending')->count() : 0;
    }
    
    /**
     * 获取已确认更正记录数量
     * 
     * @return int
     */
    public function getConfirmedCorrectionsCountAttribute()
    {
        return $this->corrections ? $this->corrections->where('status', 'confirmed')->count() : 0;
    }
    
    /**
     * 填充地址信息
     * 在设置了user_address_id后自动填充地址字段
     */
    public function fillAddressInfo()
    {
        if ($this->user_address_id && $this->userAddress) {
            $this->shipping_address = $this->userAddress->getFullAddressAttribute();
            $this->contact_name = $this->userAddress->contact_name;
            $this->contact_phone = $this->userAddress->contact_phone;
            return true;
        }
        return false;
    }
    
    /**
     * 检查订单是否可以支付
     */
    public function canBePaid()
    {
        return $this->status === 'pending';
    }
    
    /**
     * 检查订单是否可以发货
     */
    public function canBeShipped()
    {
        return $this->status === 'paid';
    }
    
    /**
     * 检查订单是否可以确认送达
     */
    public function canBeDelivered()
    {
        return $this->status === 'shipped';
    }
    
    /**
     * 检查订单是否可以取消
     */
    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'paid']);
    }

    /**
     * 检查订单是否为代客下单
     */
    public function isProxyOrder()
    {
        return $this->source === 'proxy' && $this->created_by_id !== null;
    }

    /**
     * 检查订单是否为货到付款
     */
    public function isCashOnDelivery()
    {
        return $this->is_cod === true || $this->payment_method === 'cod';
    }
    
    /**
     * 获取货到付款状态文本
     */
    public function getCodStatusTextAttribute()
    {
        if (!$this->isCashOnDelivery()) {
            return '非货到付款';
        }
        
        $statusMap = [
            'unpaid' => '等待收款',
            'paid' => '已收款',
            'paid_cash' => '已收款（现金）',
            'paid_online' => '已收款（线上）',
            'pending_online_payment' => '等待线上付款',
            'link_generated' => '付款链接已生成',
            'partial_paid' => '部分收款',
            'overpaid' => '超额收款',
            'failed' => '收款失败'
        ];
        
        return $statusMap[$this->cod_status] ?? '未知状态';
    }

    /**
     * 获取配送方式文本
     */
    public function getDeliveryMethodTextAttribute()
    {
        $methodMap = [
            'express' => '快递配送',
            'self_pickup' => '自行提货',
            'same_day' => '当日达',
            'scheduled' => '定时配送'
        ];
        
        return $methodMap[$this->delivery_method] ?? '标准配送';
    }

    /**
     * 获取配送时间段文本
     */
    public function getDeliveryTimeTextAttribute()
    {
        if ($this->delivery_time === 'custom' && $this->delivery_time_custom) {
            return $this->delivery_time_custom;
        }
        
        $timeMap = [
            'morning' => '上午(09:00-12:00)',
            'afternoon' => '下午(12:00-18:00)',
            'evening' => '晚上(18:00-21:00)'
        ];
        
        return $timeMap[$this->delivery_time] ?? '全天';
    }

    /**
     * 获取配送安排人员
     */
    public function deliveryArrangedBy()
    {
        return $this->belongsTo(Employee::class, 'delivery_arranged_by');
    }

    /**
     * 检查订单是否已安排配送
     */
    public function isDeliveryArranged()
    {
        return $this->delivery_arranged_at !== null;
    }

    /**
     * 安排配送
     */
    public function arrangeDelivery($employeeId)
    {
        $this->delivery_arranged_at = now();
        $this->delivery_arranged_by = $employeeId;
        return $this->save();
    }

    /**
     * 获取订单的打印记录
     */
    public function printRecords()
    {
        return $this->morphMany(\App\Printing\Models\PrintRecord::class, 'printable');
    }
    
    /**
     * 检查订单是否已打印过
     */
    public function isPrinted(string $printType = null): bool
    {
        return \App\Printing\Models\PrintRecord::isPrinted($this, $printType);
    }
    
    /**
     * 获取订单打印历史
     */
    public function getPrintHistory(string $printType = null): \Illuminate\Database\Eloquent\Collection
    {
        return \App\Printing\Models\PrintRecord::getPrintHistory($this, $printType);
    }
    
    /**
     * 获取打印统计信息
     */
    public function getPrintStats(string $printType = null): array
    {
        $query = $this->printRecords();
        
        if ($printType) {
            $query->where('print_type', $printType);
        }
        
        $records = $query->get();
        
        return [
            'total_prints' => $records->count(),
            'successful_prints' => $records->where('status', 'completed')->count(),
            'failed_prints' => $records->where('status', 'failed')->count(),
            'last_printed_at' => $records->where('status', 'completed')->max('created_at'),
        ];
    }

    /**
     * 获取订单折扣信息
     * 
     * @return array
     */
    public function getDiscountInfo(): array
    {
        $discountInfo = [
            'has_discount' => false,
            'original_total' => round((float)($this->original_total ?? $this->total), 2),
            'final_total' => round((float)$this->total, 2),
            'discount_amount' => round((float)($this->discount ?? 0), 2),
            'payment_discount_amount' => round((float)($this->payment_discount ?? 0), 2),
            'total_discount_amount' => round((float)(($this->discount ?? 0) + ($this->payment_discount ?? 0)), 2),
            'discount_details' => [],
            'discount_labels' => []
        ];

        // 检查是否有折扣
        if ($this->discount > 0 || $this->payment_discount > 0 || ($this->original_total && $this->original_total > $this->total)) {
            $discountInfo['has_discount'] = true;
            $discountInfo['discount_amount'] = round((float)($this->original_total ?
                ($this->original_total - $this->total) : ($this->discount + $this->payment_discount)), 2);
        }

        // 解析价格计算详情
        if ($this->pricing_info && is_array($this->pricing_info)) {
            $discountInfo['discount_details'] = $this->pricing_info;
            
            // 生成折扣标签
            foreach ($this->pricing_info as $detail) {
                switch ($detail['type'] ?? '') {
                    case 'region_price':
                        $discountInfo['discount_labels'][] = [
                            'type' => 'region',
                            'text' => '区域价',
                            'color' => 'blue',
                            'amount' => $detail['discount_amount'] ?? 0
                        ];
                        break;
                    case 'product_member_discount':
                    case 'category_member_discount':
                    case 'global_member_discount':
                        $discountInfo['discount_labels'][] = [
                            'type' => 'member',
                            'text' => '会员优惠',
                            'color' => 'gold',
                            'amount' => $detail['discount_amount'] ?? 0
                        ];
                        break;
                    case 'category_region_discount':
                        $discountInfo['discount_labels'][] = [
                            'type' => 'category_region',
                            'text' => '分类优惠',
                            'color' => 'green',
                            'amount' => $detail['discount_amount'] ?? 0
                        ];
                        break;
                }
            }
        }

        // 添加支付优惠标签
        if ($this->payment_discount > 0) {
            $discountInfo['discount_labels'][] = [
                'type' => 'payment',
                'text' => '支付优惠',
                'color' => 'orange',
                'amount' => $this->payment_discount
            ];
        }

        return $discountInfo;
    }

    /**
     * 获取支付优惠信息
     * 
     * @return array
     */
    public function getPaymentDiscountInfo(): array
    {
        return [
            'has_payment_discount' => $this->payment_discount > 0,
            'payment_discount_amount' => $this->payment_discount ?? 0,
            'payment_method' => $this->payment_method,
            'payment_discount_details' => $this->payment_discount_info ?? [],
        ];
    }

    /**
     * 获取折扣百分比
     * 
     * @return float
     */
    public function getDiscountPercentage(): float
    {
        $originalTotal = $this->original_total ?? $this->total;
        if ($originalTotal <= 0) {
            return 0;
        }
        
        $discountAmount = $this->discount ?? ($originalTotal - $this->total);
        return round(($discountAmount / $originalTotal) * 100, 1);
    }

    /**
     * 检查订单是否有某种折扣类型
     * 
     * @param string $type 折扣类型
     * @return bool
     */
    public function hasDiscountType(string $type): bool
    {
        $discountInfo = $this->getDiscountInfo();
        
        foreach ($discountInfo['discounts'] as $discount) {
            if ($discount['type'] === $type) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 记录订单使用的库存策略
     * 
     * @param array $policies 库存策略数组 [product_id => policy_info]
     * @return void
     */
    public function recordInventoryPolicies(array $policies)
    {
        $this->inventory_policies_used = $policies;
        
        // 检查是否有负库存商品
        $hasNegativeStock = false;
        $warnings = [];
        
        foreach ($policies as $productId => $policyInfo) {
            if (isset($policyInfo['warning']) && $policyInfo['warning']) {
                $warnings[] = [
                    'product_id' => $productId,
                    'product_name' => $policyInfo['product_name'] ?? '',
                    'warning' => $policyInfo['warning']
                ];
                
                // 检查是否是负库存警告
                if (strpos($policyInfo['warning'], '负数') !== false) {
                    $hasNegativeStock = true;
                }
            }
        }
        
        $this->has_negative_stock_items = $hasNegativeStock;
        $this->stock_warnings = $warnings;
        $this->save();
    }

    /**
     * 批准负库存
     * 
     * @param int $approvedById 批准人ID
     * @return bool
     */
    public function approveNegativeStock($approvedById)
    {
        if (!$this->has_negative_stock_items) {
            return false;
        }
        
        $this->negative_stock_approved = true;
        $this->approved_by_id = $approvedById;
        $this->approved_at = now();
        
        return $this->save();
    }

    /**
     * 获取库存策略摘要
     * 
     * @return array
     */
    public function getInventoryPolicySummary()
    {
        $summary = [
            'total_items' => 0,
            'policies_used' => [],
            'has_warnings' => false,
            'warning_count' => 0,
            'has_negative_stock' => $this->has_negative_stock_items,
            'negative_stock_approved' => $this->negative_stock_approved,
            'approved_by' => null,
            'approved_at' => $this->approved_at
        ];
        
        if ($this->inventory_policies_used) {
            $summary['total_items'] = count($this->inventory_policies_used);
            
            // 统计使用的策略类型
            $policyCount = [];
            foreach ($this->inventory_policies_used as $productId => $policyInfo) {
                $policy = $policyInfo['policy'] ?? 'unknown';
                $policyCount[$policy] = ($policyCount[$policy] ?? 0) + 1;
            }
            $summary['policies_used'] = $policyCount;
        }
        
        if ($this->stock_warnings) {
            $summary['has_warnings'] = true;
            $summary['warning_count'] = count($this->stock_warnings);
        }
        
        // 获取批准人信息
        if ($this->approved_by_id) {
            $summary['approved_by'] = $this->approvedBy;
        }
        
        return $summary;
    }

    /**
     * 获取批准人关联
     */
    public function approvedBy()
    {
        return $this->belongsTo(\App\Employee\Models\Employee::class, 'approved_by_id');
    }

    /**
     * 检查订单是否需要负库存审批
     * 
     * @return bool
     */
    public function needsNegativeStockApproval()
    {
        return $this->has_negative_stock_items && !$this->negative_stock_approved;
    }

    /**
     * 获取库存警告的文本描述
     * 
     * @return array
     */
    public function getStockWarningTexts()
    {
        if (!$this->stock_warnings) {
            return [];
        }
        
        $texts = [];
        foreach ($this->stock_warnings as $warning) {
            $texts[] = "商品 {$warning['product_name']}: {$warning['warning']}";
        }
        
        return $texts;
    }

    /**
     * 获取库存策略使用情况的显示文本
     * 
     * @return string
     */
    public function getInventoryPolicyDisplayText()
    {
        $summary = $this->getInventoryPolicySummary();
        
        if (empty($summary['policies_used'])) {
            return '未记录库存策略';
        }
        
        $texts = [];
        foreach ($summary['policies_used'] as $policy => $count) {
            $policyName = match($policy) {
                'strict' => '严格库存',
                'allow_negative' => '允许负库存',
                'unlimited' => '无限库存',
                default => $policy
            };
            $texts[] = "{$policyName}({$count}件)";
        }
        
        $result = implode(', ', $texts);
        
        if ($summary['has_warnings']) {
            $result .= " - 有{$summary['warning_count']}个警告";
        }
        
        return $result;
    }

    /**
     * 关联订单合并记录
     */
    public function orderMerge()
    {
        return $this->belongsTo(OrderMerge::class, 'order_merge_id');
    }

    /**
     * 关联合并到的订单（如果是被合并的原订单）
     */
    public function mergedToOrder()
    {
        return $this->belongsTo(Order::class, 'merged_to_order_id');
    }

    /**
     * 检查是否为合并订单
     */
    public function isMergedOrder(): bool
    {
        return $this->is_merged === true;
    }

    /**
     * 检查是否为被合并的原订单
     */
    public function isMergedFromOrder(): bool
    {
        return $this->is_merged_from === true;
    }

    /**
     * 获取合并相关信息
     */
    public function getMergeInfo(): array
    {
        if ($this->isMergedOrder()) {
            return [
                'type' => 'merged_order',
                'savings' => $this->merge_savings,
                'original_order_count' => $this->orderMerge ? $this->orderMerge->getOriginalOrderCount() : 0,
                'merge_strategy' => $this->orderMerge ? $this->orderMerge->getStrategyText() : '',
            ];
        }

        if ($this->isMergedFromOrder()) {
            return [
                'type' => 'merged_from_order',
                'merged_to_order_id' => $this->merged_to_order_id,
                'can_revert' => $this->orderMerge && !$this->orderMerge->isReverted(),
            ];
        }

        return [
            'type' => 'normal_order',
        ];
    }

    /**
     * 获取合并优惠详情
     */
    public function getMergeDiscountDetails(): array
    {
        if (!$this->merge_discount_details) {
            return [];
        }

        return $this->merge_discount_details;
    }
} 