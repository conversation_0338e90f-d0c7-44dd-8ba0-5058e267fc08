<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Services\OrderService;
use App\Employee\Models\Employee;
use App\Models\User;

class TestOrderPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:order-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试订单权限控制';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始测试订单权限控制...');

        // 测试1: 检查CRM专员权限
        $this->testCrmAgentPermissions();

        // 测试2: 检查管理员权限
        $this->testAdminPermissions();

        // 测试3: 对比权限差异
        $this->comparePermissions();

        $this->info('测试完成！');
    }

    /**
     * 测试CRM专员权限
     */
    private function testCrmAgentPermissions()
    {
        $this->info('=== 测试CRM专员权限 ===');

        try {
            // 查找CRM专员
            $crmAgent = Employee::where('role', 'crm_agent')->first();

            if (!$crmAgent) {
                $this->error('未找到CRM专员用户');
                return;
            }

            $this->info("测试用户: {$crmAgent->name} (ID: {$crmAgent->id}, 角色: {$crmAgent->role})");

            // 调用订单服务
            $orderService = app(OrderService::class);

            // 不添加crm_agent_id过滤条件，看看是否返回所有订单
            $allOrders = $orderService->getOrders([], $crmAgent, 20);

            // 添加crm_agent_id过滤条件，看看是否正确过滤
            $filteredOrders = $orderService->getOrders(['crm_agent_id' => $crmAgent->id], $crmAgent, 20);

            $this->info("不加过滤条件的订单数: " . $allOrders->total());
            $this->info("加过滤条件的订单数: " . $filteredOrders->total());

            if ($allOrders->total() > $filteredOrders->total()) {
                $this->warn("🚨 权限控制问题：CRM专员能看到不属于自己的订单！");
            } else {
                $this->info("✅ 权限控制正常");
            }

            // 验证过滤后的订单是否都属于该CRM专员
            $this->validateOrders($filteredOrders, $crmAgent->id);

        } catch (\Exception $e) {
            $this->error("测试失败: " . $e->getMessage());
        }

        $this->line('');
    }

    /**
     * 测试管理员权限
     */
    private function testAdminPermissions()
    {
        $this->info('=== 测试管理员权限 ===');

        try {
            // 查找管理员
            $admin = Employee::where('role', 'admin')->first();

            if (!$admin) {
                $this->error('未找到管理员用户');
                return;
            }

            $this->info("测试用户: {$admin->name} (ID: {$admin->id}, 角色: {$admin->role})");

            // 调用订单服务
            $orderService = app(OrderService::class);
            $orders = $orderService->getOrders([], $admin, 20);

            $this->info("管理员可访问的订单数: " . $orders->total());
            $this->info("当前页订单数: " . $orders->count());

        } catch (\Exception $e) {
            $this->error("测试失败: " . $e->getMessage());
        }

        $this->line('');
    }

    /**
     * 对比不同角色的权限
     */
    private function comparePermissions()
    {
        $this->info('=== 权限对比 ===');

        try {
            $orderService = app(OrderService::class);

            // 获取不同角色的用户
            $admin = Employee::where('role', 'admin')->first();
            $crmAgent = Employee::where('role', 'crm_agent')->first();

            if ($admin && $crmAgent) {
                $adminOrders = $orderService->getOrders([], $admin, 20);
                $crmOrders = $orderService->getOrders(['crm_agent_id' => $crmAgent->id], $crmAgent, 20);

                $this->table(
                    ['角色', '用户名', '可访问订单数'],
                    [
                        ['管理员', $admin->name, $adminOrders->total()],
                        ['CRM专员', $crmAgent->name, $crmOrders->total()]
                    ]
                );

                if ($adminOrders->total() > $crmOrders->total()) {
                    $this->info("✅ 权限控制正常：管理员能看到更多订单");
                } else {
                    $this->warn("🚨 可能的权限问题：CRM专员和管理员看到相同数量的订单");
                }
            }

        } catch (\Exception $e) {
            $this->error("对比测试失败: " . $e->getMessage());
        }
    }

    /**
     * 验证订单是否属于指定CRM专员
     */
    private function validateOrders($orders, $crmAgentId)
    {
        $validCount = 0;
        $invalidCount = 0;

        foreach ($orders as $order) {
            if ($order->user && $order->user->crm_agent_id == $crmAgentId) {
                $validCount++;
            } else {
                $invalidCount++;
            }
        }

        $this->info("有效订单: {$validCount}, 无效订单: {$invalidCount}");

        if ($invalidCount > 0) {
            $this->warn("🚨 发现 {$invalidCount} 个不属于该CRM专员的订单");
        }
    }
}
