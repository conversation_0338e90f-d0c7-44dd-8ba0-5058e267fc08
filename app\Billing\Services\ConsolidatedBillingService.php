<?php

namespace App\Billing\Services;

use App\Billing\Models\Bill;
use App\Billing\Models\BillItem;
use App\Billing\Models\BillAdjustment;
use App\Billing\Models\PaymentRecord;
use App\Models\User;
use App\Order\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class ConsolidatedBillingService
{
    /**
     * 为用户的多个订单创建累计账单
     */
    public function createConsolidatedBill(User $user, array $orderIds, array $options = []): Bill
    {
        $orders = Order::whereIn('id', $orderIds)
            ->where('user_id', $user->id)
            ->where('status', 'pending') // 只能累计待付款的订单
            ->get();

        if ($orders->isEmpty()) {
            throw new Exception('没有找到可累计的订单');
        }

        return DB::transaction(function () use ($user, $orders, $options) {
            // 创建累计账单 - 使用final_payment_amount字段（各种优惠后的最终金额）
            $totalAmount = $orders->sum(function ($order) {
                return $order->final_payment_amount ?? $order->total;
            });
            
            $consolidatedBill = Bill::create([
                'bill_no' => $this->generateConsolidatedBillNo(),
                'bill_type' => Bill::TYPE_CONSOLIDATED, // 🔥 修复：使用正确的累计账单类型
                'user_id' => $user->id,
                'order_id' => null, // 累计账单不关联单个订单
                'original_amount' => $totalAmount,
                'adjustment_amount' => $options['adjustment_amount'] ?? 0,
                'final_amount' => $totalAmount + ($options['adjustment_amount'] ?? 0),
                'pending_amount' => $totalAmount + ($options['adjustment_amount'] ?? 0),
                'due_date' => $options['due_date'] ?? now()->addDays(7),
                'status' => Bill::STATUS_PENDING,
                'payment_status' => Bill::PAYMENT_STATUS_UNPAID,
                'created_by' => $options['created_by'] ?? null,
                'notes' => $options['notes'] ?? "累计账单包含 {$orders->count()} 个订单",
                'metadata' => array_merge($options['metadata'] ?? [], [
                    'is_consolidated' => true,
                    'order_count' => $orders->count(),
                    'order_ids' => $orders->pluck('id')->toArray(),
                    'consolidated_date' => now()->toDateString(),
                ]),
            ]);

            // 为每个订单创建子账单，关联到累计账单
            foreach ($orders as $order) {
                $orderFinalAmount = $order->final_payment_amount ?? $order->total;
                
                $childBill = Bill::create([
                    'bill_no' => Bill::generateBillNo(),
                    'bill_type' => Bill::TYPE_ORDER,
                    'user_id' => $user->id,
                    'order_id' => $order->id,
                    'parent_bill_id' => $consolidatedBill->id,
                    'original_amount' => $orderFinalAmount,
                    'final_amount' => $orderFinalAmount,
                    'pending_amount' => 0, // 子账单不单独付款
                    'status' => Bill::STATUS_PENDING,
                    'payment_status' => Bill::PAYMENT_STATUS_UNPAID,
                    'notes' => "订单 {$order->order_no} 的子账单",
                    'metadata' => [
                        'is_child_bill' => true,
                        'parent_consolidated_bill_id' => $consolidatedBill->id,
                    ],
                ]);

                // 创建子账单明细
                foreach ($order->items as $orderItem) {
                    // 处理单位字段：如果是对象则提取display_name，否则使用原值
                    $unitValue = '件'; // 默认值
                    if ($orderItem->unit) {
                        if (is_string($orderItem->unit)) {
                            $unitValue = $orderItem->unit;
                        } elseif (is_array($orderItem->unit) && isset($orderItem->unit['display_name'])) {
                            $unitValue = $orderItem->unit['display_name'];
                        } elseif (is_object($orderItem->unit) && isset($orderItem->unit->display_name)) {
                            $unitValue = $orderItem->unit->display_name;
                        }
                    }
                    
                    BillItem::create([
                        'bill_id' => $childBill->id,
                        'product_id' => $orderItem->product_id,
                        'item_name' => $orderItem->product_name,
                        'item_type' => BillItem::TYPE_PRODUCT,
                        'item_description' => $orderItem->product_specification ?? '',
                        'quantity' => $orderItem->quantity,
                        'unit' => $unitValue,
                        'unit_price' => $orderItem->price,
                        'total_price' => $orderItem->total_price,
                        'final_amount' => $orderItem->total_price,
                        'order_item_id' => $orderItem->id,
                    ]);
                }

                // 同时在累计账单中也创建明细（用于显示）
                foreach ($order->items as $orderItem) {
                    // 处理单位字段：如果是对象则提取display_name，否则使用原值
                    $unitValue = '件'; // 默认值
                    if ($orderItem->unit) {
                        if (is_string($orderItem->unit)) {
                            $unitValue = $orderItem->unit;
                        } elseif (is_array($orderItem->unit) && isset($orderItem->unit['display_name'])) {
                            $unitValue = $orderItem->unit['display_name'];
                        } elseif (is_object($orderItem->unit) && isset($orderItem->unit->display_name)) {
                            $unitValue = $orderItem->unit->display_name;
                        }
                    }
                    
                    BillItem::create([
                        'bill_id' => $consolidatedBill->id,
                        'product_id' => $orderItem->product_id,
                        'item_name' => $orderItem->product_name,
                        'item_type' => BillItem::TYPE_PRODUCT,
                        'item_description' => "订单{$order->order_no}: " . ($orderItem->product_specification ?? ''),
                        'quantity' => $orderItem->quantity,
                        'unit' => $unitValue,
                        'unit_price' => $orderItem->price,
                        'total_price' => $orderItem->total_price,
                        'final_amount' => $orderItem->total_price,
                        'order_item_id' => $orderItem->id,
                    ]);
                }
            }

            Log::info('累计账单创建成功', [
                'consolidated_bill_id' => $consolidatedBill->id,
                'order_count' => $orders->count(),
                'total_amount' => $totalAmount,
                'order_ids' => $orders->pluck('id')->toArray()
            ]);

            return $consolidatedBill;
        });
    }

    /**
     * 自动为用户创建周期性累计账单
     */
    public function createPeriodicConsolidatedBill(User $user, string $period = 'monthly'): ?Bill
    {
        $dateRange = $this->getDateRangeForPeriod($period);
        
        // 查找该周期内的待付款订单
        $orders = Order::where('user_id', $user->id)
            ->where('status', 'pending')
            ->whereBetween('created_at', $dateRange)
            ->whereDoesntHave('bills') // 排除已有账单的订单
            ->get();

        if ($orders->isEmpty()) {
            return null;
        }

        $options = [
            'notes' => "自动生成的{$this->getPeriodText($period)}累计账单",
            'due_date' => $this->getDueDateForPeriod($period),
            'metadata' => [
                'auto_generated' => true,
                'period_type' => $period,
                'period_start' => $dateRange[0]->toDateString(),
                'period_end' => $dateRange[1]->toDateString(),
            ]
        ];

        return $this->createConsolidatedBill($user, $orders->pluck('id')->toArray(), $options);
    }

    /**
     * 检查用户是否有可累计的订单
     */
    public function hasConsolidatableOrders(User $user, int $minOrderCount = 2): bool
    {
        $orderCount = Order::where('user_id', $user->id)
            ->where('status', 'pending')
            ->whereDoesntHave('bills')
            ->count();

        return $orderCount >= $minOrderCount;
    }

    /**
     * 获取用户可累计的订单列表
     */
    public function getConsolidatableOrders(User $user)
    {
        return Order::where('user_id', $user->id)
            ->where('status', 'pending')
            ->whereDoesntHave('bills')
            ->with('items.product')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 计算累计账单的统计信息
     */
    public function getConsolidationStatistics(User $user): array
    {
        $consolidatableOrders = $this->getConsolidatableOrders($user);
        
        // 使用final_payment_amount字段计算总金额（各种优惠后的最终金额）
        $totalAmount = $consolidatableOrders->sum(function ($order) {
            return $order->final_payment_amount ?? $order->total;
        });
        
        return [
            'order_count' => $consolidatableOrders->count(),
            'total_amount' => $totalAmount,
            'earliest_order_date' => $consolidatableOrders->min('created_at'),
            'latest_order_date' => $consolidatableOrders->max('created_at'),
        ];
    }

    /**
     * 生成累计账单号
     */
    protected function generateConsolidatedBillNo(): string
    {
        return 'CB' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 获取周期的日期范围
     */
    protected function getDateRangeForPeriod(string $period): array
    {
        $now = Carbon::now();
        
        return match($period) {
            'weekly' => [
                $now->copy()->startOfWeek(),
                $now->copy()->endOfWeek()
            ],
            'monthly' => [
                $now->copy()->startOfMonth(),
                $now->copy()->endOfMonth()
            ],
            'quarterly' => [
                $now->copy()->startOfQuarter(),
                $now->copy()->endOfQuarter()
            ],
            default => [
                $now->copy()->startOfMonth(),
                $now->copy()->endOfMonth()
            ]
        };
    }

    /**
     * 获取周期文本
     */
    protected function getPeriodText(string $period): string
    {
        return match($period) {
            'weekly' => '本周',
            'monthly' => '本月',
            'quarterly' => '本季度',
            default => '本月'
        };
    }

    /**
     * 获取周期的到期日期
     */
    protected function getDueDateForPeriod(string $period): Carbon
    {
        $now = Carbon::now();
        
        return match($period) {
            'weekly' => $now->copy()->addWeek(),
            'monthly' => $now->copy()->addMonth(),
            'quarterly' => $now->copy()->addMonths(3),
            default => $now->copy()->addMonth()
        };
    }

    /**
     * 🔥 重新设计：从已有账单创建累计账单
     * 将多个用户的待付款账单合并成一个累计账单
     */
    public function createConsolidatedBillFromExistingBills(User $user, array $billIds, array $options = []): Bill
    {
        // 验证账单
        $bills = Bill::whereIn('id', $billIds)
            ->where('user_id', $user->id)
            ->where('bill_type', Bill::TYPE_ORDER)
            ->whereIn('status', [Bill::STATUS_PENDING, Bill::STATUS_PARTIAL_PAID])
            ->where('order_id', '!=', null) // 只处理有关联订单的账单
            ->with(['order', 'items', 'paymentRecords'])
            ->get();

        if ($bills->isEmpty()) {
            throw new Exception('没有找到可合并的账单');
        }

        if ($bills->count() < 2) {
            throw new Exception('至少需要2个账单才能创建累计账单');
        }

        return DB::transaction(function () use ($user, $bills, $options) {
            // 计算累计金额
            $totalOriginalAmount = $bills->sum('original_amount');
            $totalAdjustmentAmount = $bills->sum('adjustment_amount');
            $totalFinalAmount = $bills->sum('final_amount');
            $totalPaidAmount = $bills->sum('paid_amount');
            $totalPendingAmount = $bills->sum('pending_amount');
            
            // 创建累计账单
            $consolidatedBill = Bill::create([
                'bill_no' => $this->generateConsolidatedBillNo(),
                'bill_type' => Bill::TYPE_CONSOLIDATED, // 🔥 修复：使用正确的累计账单类型
                'user_id' => $user->id,
                'order_id' => null, // 累计账单不关联单个订单
                'original_amount' => $totalOriginalAmount,
                'adjustment_amount' => $totalAdjustmentAmount + ($options['adjustment_amount'] ?? 0),
                'final_amount' => $totalFinalAmount + ($options['adjustment_amount'] ?? 0),
                'paid_amount' => $totalPaidAmount,
                'pending_amount' => $totalPendingAmount + ($options['adjustment_amount'] ?? 0),
                'due_date' => $options['due_date'] ?? now()->addDays(7),
                'status' => $totalPaidAmount > 0 ? Bill::STATUS_PARTIAL_PAID : Bill::STATUS_PENDING,
                'payment_status' => $this->determinePaymentStatus($totalFinalAmount, $totalPaidAmount),
                'created_by' => $options['created_by'] ?? null,
                'notes' => $options['notes'] ?? "累计账单包含 {$bills->count()} 个账单",
                'metadata' => array_merge([
                    'is_consolidated' => true,
                    'bill_count' => $bills->count(),
                    'original_bill_ids' => $bills->pluck('id')->toArray(),
                    'order_ids' => $bills->pluck('order_id')->filter()->toArray(),
                    'consolidated_date' => now()->toDateString(),
                    'consolidation_type' => 'manual', // 手动创建
                ], $options['metadata'] ?? []),
            ]);

            // 将原账单设置为子账单
            foreach ($bills as $bill) {
                $bill->update([
                    'parent_bill_id' => $consolidatedBill->id,
                    'status' => Bill::STATUS_CONSOLIDATED, // 新增状态：已合并
                    'metadata' => array_merge($bill->metadata ?? [], [
                        'is_child_bill' => true,
                        'consolidated_to' => $consolidatedBill->id,
                        'consolidated_at' => now()->toISOString(),
                    ]),
                ]);

                // 复制账单项到累计账单
                foreach ($bill->items as $item) {
                    BillItem::create([
                        'bill_id' => $consolidatedBill->id,
                        'product_id' => $item->product_id,
                        'item_name' => $item->item_name,
                        'item_type' => $item->item_type,
                        'item_description' => "原账单{$bill->bill_no}: " . $item->item_description,
                        'quantity' => $item->quantity,
                        'unit' => $item->unit,
                        'unit_price' => $item->unit_price,
                        'total_price' => $item->total_price,
                        'final_amount' => $item->final_amount,
                        'order_item_id' => $item->order_item_id,
                        'original_bill_item_id' => $item->id, // 记录原始账单项ID
                    ]);
                }

                // 复制支付记录到累计账单
                foreach ($bill->paymentRecords as $payment) {
                    $newPayment = PaymentRecord::create([
                        'bill_id' => $consolidatedBill->id,
                        'payment_no' => $payment->payment_no . '-C', // 添加累计标识
                        'payment_method' => $payment->payment_method,
                        'payment_amount' => $payment->payment_amount,
                        'payment_type' => $payment->payment_type,
                        'balance_used' => $payment->balance_used,
                        'transaction_id' => $payment->transaction_id,
                        'external_payment_no' => $payment->external_payment_no,
                        'status' => $payment->status,
                        'payment_time' => $payment->payment_time,
                        'confirmed_at' => $payment->confirmed_at,
                        'received_by' => $payment->received_by,
                        'notes' => "从账单{$bill->bill_no}转移: " . $payment->notes,
                        'payment_details' => array_merge($payment->payment_details ?? [], [
                            'consolidated_from' => $bill->id,
                            'original_payment_id' => $payment->id,
                        ]),
                    ]);

                    // 标记原支付记录为已转移
                    $payment->update([
                        'status' => PaymentRecord::STATUS_TRANSFERRED,
                        'notes' => $payment->notes . " [已转移到累计账单{$consolidatedBill->bill_no}]",
                        'payment_details' => array_merge($payment->payment_details ?? [], [
                            'transferred_to' => $consolidatedBill->id,
                            'transferred_payment_id' => $newPayment->id,
                            'transferred_at' => now()->toISOString(),
                        ]),
                    ]);
                }
            }

            // 如果有额外调整金额，创建调整记录
            if (isset($options['adjustment_amount']) && $options['adjustment_amount'] != 0) {
                BillAdjustment::create([
                    'bill_id' => $consolidatedBill->id,
                    'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                    'adjustment_type' => BillAdjustment::TYPE_MANUAL_ADJUST,
                    'adjustment_amount' => $options['adjustment_amount'],
                    'reason' => '累计账单创建时调整',
                    'description' => $options['adjustment_reason'] ?? '累计账单手动调整',
                    'operator_id' => $options['created_by'],
                    'status' => BillAdjustment::STATUS_APPROVED,
                    'applied_at' => now(),
                ]);
            }

            Log::info('累计账单创建成功', [
                'consolidated_bill_id' => $consolidatedBill->id,
                'bill_count' => $bills->count(),
                'total_amount' => $totalFinalAmount,
                'original_bill_ids' => $bills->pluck('id')->toArray()
            ]);

            return $consolidatedBill;
        });
    }

    /**
     * 🔥 新增：获取用户可合并的账单列表
     */
    public function getConsolidatableBills(User $user): Collection
    {
        return Bill::where('user_id', $user->id)
            ->where('bill_type', Bill::TYPE_ORDER)
            ->whereIn('status', [Bill::STATUS_PENDING, Bill::STATUS_PARTIAL_PAID])
            ->whereNull('parent_bill_id') // 排除已经是子账单的
            ->where('order_id', '!=', null) // 只处理有关联订单的账单
            ->with(['order', 'items'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 🔥 新增：检查用户是否有可合并的账单
     */
    public function hasConsolidatableBills(User $user, int $minBillCount = 2): bool
    {
        $consolidatableBills = $this->getConsolidatableBills($user);
        return $consolidatableBills->count() >= $minBillCount;
    }

    /**
     * 🔥 新增：获取累计账单统计
     */
    public function getBillConsolidationStatistics(User $user): array
    {
        $consolidatableBills = $this->getConsolidatableBills($user);
        
        return [
            'bill_count' => $consolidatableBills->count(),
            'total_amount' => $consolidatableBills->sum('final_amount'),
            'pending_amount' => $consolidatableBills->sum('pending_amount'),
            'earliest_bill_date' => $consolidatableBills->min('created_at'),
            'latest_bill_date' => $consolidatableBills->max('created_at'),
            'can_consolidate' => $consolidatableBills->count() >= 2,
        ];
    }

    /**
     * 🔥 修复：确定支付状态
     */
    private function determinePaymentStatus(float $finalAmount, float $paidAmount): string
    {
        if ($paidAmount <= 0) {
            return Bill::PAYMENT_STATUS_UNPAID;
        } elseif ($paidAmount >= $finalAmount) {
            return $paidAmount > $finalAmount ? Bill::PAYMENT_STATUS_OVERPAID : Bill::PAYMENT_STATUS_PAID;
        } else {
            return Bill::PAYMENT_STATUS_PARTIAL;
        }
    }
} 