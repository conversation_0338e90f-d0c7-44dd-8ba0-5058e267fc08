<?php

use App\Admin\Http\Controllers\EmployeeController;
use App\Admin\Http\Controllers\AuthController as EmployeeAuthController;
use App\Admin\Http\Controllers\ShopConfigController;
use App\Admin\Http\Controllers\AddressController;
use App\Admin\Http\Controllers\PointsRuleController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin API 路由
|--------------------------------------------------------------------------
|
| Admin模块的API路由定义
|
*/

// 员工认证路由 - 不需要认证的端点
Route::prefix('api/admin/auth')->group(function () {
    Route::post('/login', [EmployeeAuthController::class, 'login']);
});

// 需要认证的员工路由
Route::middleware('auth:sanctum')->prefix('api/admin')->group(function () {
    // 员工认证路由 - 需要认证的端点
    Route::prefix('auth')->group(function () {
        Route::get('/me', [EmployeeAuthController::class, 'me']);
        Route::post('/logout', [EmployeeAuthController::class, 'logout']);
    });
    
    // 员工管理路由
    Route::prefix('employees')->middleware(['employee.role:admin,manager'])->name('api.employees.')->group(function () {
        Route::get('/', [EmployeeController::class, 'index'])->name('index');
        Route::post('/', [EmployeeController::class, 'store'])->name('store');
        Route::get('/{employee}', [EmployeeController::class, 'show'])->name('show');
        Route::put('/{employee}', [EmployeeController::class, 'update'])->name('update');
        Route::delete('/{employee}', [EmployeeController::class, 'destroy'])->name('destroy');
    });
    
    // 用户地址管理路由
    Route::prefix('users/{userId}/addresses')->middleware(['employee.role:admin,manager,crm_agent'])->group(function () {
        Route::get('/', [AddressController::class, 'index']);
        Route::post('/', [AddressController::class, 'store']);
        Route::get('/{addressId}', [AddressController::class, 'show']);
        Route::put('/{addressId}', [AddressController::class, 'update']);
        Route::delete('/{addressId}', [AddressController::class, 'destroy']);
        Route::put('/{addressId}/default', [AddressController::class, 'setDefault']);
    });
    
    // 系统配置相关路由
    Route::prefix('shop-configs')->middleware(['employee.role:admin,manager'])->group(function () {
        Route::get('/', [ShopConfigController::class, 'index']);
        Route::get('/groups', [ShopConfigController::class, 'groups']);
        Route::get('/{key}', [ShopConfigController::class, 'show']);
        Route::post('/', [ShopConfigController::class, 'store']);
        Route::put('/{id}', [ShopConfigController::class, 'update']);
        Route::delete('/{id}', [ShopConfigController::class, 'destroy']);
        Route::post('/batch', [ShopConfigController::class, 'batchSave']);
        
        // 微信小程序配置
        Route::get('/wechat/mini-program', [ShopConfigController::class, 'getWechatMiniProgramConfig']);
        Route::post('/wechat/mini-program', [ShopConfigController::class, 'saveWechatMiniProgramConfig']);
    });
    
    // 积分规则管理路由
    Route::prefix('points-rules')->middleware(['employee.role:admin,manager'])->group(function () {
        Route::get('/', [PointsRuleController::class, 'index']);
        Route::post('/', [PointsRuleController::class, 'store']);
        Route::get('/rule-types', [PointsRuleController::class, 'ruleTypeOptions']);
        Route::post('/create-defaults', [PointsRuleController::class, 'createDefaults']);
        Route::post('/batch-status', [PointsRuleController::class, 'batchUpdateStatus']);
        Route::get('/{id}', [PointsRuleController::class, 'show']);
        Route::put('/{id}', [PointsRuleController::class, 'update']);
        Route::delete('/{id}', [PointsRuleController::class, 'destroy']);
    });

    // 🔥 新增：配送单配置管理路由
    Route::prefix('delivery-config')->middleware(['employee.role:admin,manager'])->group(function () {
        Route::get('/', [\App\Admin\Http\Controllers\DeliveryConfigController::class, 'getConfig']);
        Route::put('/', [\App\Admin\Http\Controllers\DeliveryConfigController::class, 'updateConfig']);
        Route::get('/details', [\App\Admin\Http\Controllers\DeliveryConfigController::class, 'getConfigDetails']);
        Route::post('/reset', [\App\Admin\Http\Controllers\DeliveryConfigController::class, 'resetConfig']);
    });


});