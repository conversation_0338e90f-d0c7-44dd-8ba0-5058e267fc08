<?php

namespace App\Api\Models;

class ApiResponse
{
    /**
     * 创建成功响应
     *
     * @param mixed $data 返回数据
     * @param string $message 成功消息
     * @param int $code 状态码
     * @return array
     */
    public static function success($data = null, string $message = 'Success', int $code = 200): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 创建错误响应
     *
     * @param string $message 错误消息
     * @param int $code 状态码
     * @param mixed $data 额外数据
     * @return array
     */
    public static function error(string $message = 'Error', int $code = 400, $data = null): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }
} 