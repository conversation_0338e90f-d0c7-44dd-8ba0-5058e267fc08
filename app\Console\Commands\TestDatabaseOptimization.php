<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\Billing\Models\Bill;
use App\Payment\Models\PaymentRecord;

class TestDatabaseOptimization extends Command
{
    protected $signature = 'db:test-optimization {--detailed : 显示详细信息}';
    protected $description = '测试数据库优化效果';

    public function handle()
    {
        $this->info('🧪 开始测试数据库优化效果...');
        $this->newLine();

        // 1. 测试外键约束
        $this->testForeignKeyConstraints();
        
        // 2. 测试索引效果
        $this->testIndexPerformance();
        
        // 3. 测试数据完整性约束
        $this->testDataIntegrityConstraints();
        
        // 4. 测试软删除功能
        $this->testSoftDeleteFeature();
        
        // 5. 测试核心业务功能
        $this->testCoreFunctionality();
        
        // 6. 生成优化报告
        $this->generateOptimizationReport();
        
        $this->newLine();
        $this->info('🎉 数据库优化测试完成！');
    }

    private function testForeignKeyConstraints()
    {
        $this->info('🔒 测试外键约束安全性...');
        
        $constraints = DB::select("
            SELECT 
                TABLE_NAME,
                CONSTRAINT_NAME,
                DELETE_RULE,
                UPDATE_RULE
            FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE()
            AND TABLE_NAME IN ('orders', 'bills', 'order_corrections', 'payment_records')
        ");
        
        $safeCount = 0;
        $dangerousCount = 0;
        
        foreach ($constraints as $constraint) {
            if ($constraint->DELETE_RULE === 'RESTRICT') {
                $safeCount++;
                if ($this->option('detailed')) {
                    $this->line("  ✅ {$constraint->TABLE_NAME}.{$constraint->CONSTRAINT_NAME}: RESTRICT (安全)");
                }
            } elseif ($constraint->DELETE_RULE === 'CASCADE') {
                $dangerousCount++;
                $this->error("  ❌ {$constraint->TABLE_NAME}.{$constraint->CONSTRAINT_NAME}: CASCADE (危险)");
            }
        }
        
        $this->info("  安全约束: {$safeCount}, 危险约束: {$dangerousCount}");
        $this->newLine();
    }

    private function testIndexPerformance()
    {
        $this->info('🚀 测试索引性能...');
        
        $testQueries = [
            'orders表区域+状态查询' => "SELECT COUNT(*) FROM orders WHERE region_id = 1 AND status = 'paid'",
            'orders表支付方式+状态查询' => "SELECT COUNT(*) FROM orders WHERE payment_method = 'wechat' AND status = 'delivered'",
            'bills表用户+支付状态查询' => "SELECT COUNT(*) FROM bills WHERE user_id = 1 AND payment_status = 'unpaid'",
            'order_corrections表状态查询' => "SELECT COUNT(*) FROM order_corrections WHERE status = 'pending'",
            'payment_records表支付类型查询' => "SELECT COUNT(*) FROM payment_records WHERE payment_type = 'supplement'"
        ];
        
        foreach ($testQueries as $description => $query) {
            $startTime = microtime(true);
            $result = DB::select($query);
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            $status = $executionTime < 10 ? '✅' : ($executionTime < 50 ? '⚠️' : '❌');
            $this->line("  {$status} {$description}: {$executionTime}ms");
        }
        $this->newLine();
    }

    private function testDataIntegrityConstraints()
    {
        $this->info('🛡️ 测试数据完整性约束...');
        
        $constraints = DB::select("
            SELECT 
                TABLE_NAME,
                CONSTRAINT_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE()
            AND CONSTRAINT_TYPE = 'CHECK'
            AND TABLE_NAME IN ('bills', 'order_corrections', 'payment_records')
        ");
        
        $this->info("  已添加 " . count($constraints) . " 个数据完整性约束");
        
        if ($this->option('detailed')) {
            foreach ($constraints as $constraint) {
                $this->line("  ✅ {$constraint->TABLE_NAME}.{$constraint->CONSTRAINT_NAME}");
            }
        }
        $this->newLine();
    }

    private function testSoftDeleteFeature()
    {
        $this->info('🗑️ 测试软删除功能...');
        
        if (Schema::hasColumn('orders', 'deleted_at')) {
            $this->line("  ✅ orders表已添加deleted_at字段");
            
            // 测试软删除查询
            $totalOrders = Order::count();
            $activeOrders = Order::whereNull('deleted_at')->count();
            $this->line("  📊 总订单: {$totalOrders}, 活跃订单: {$activeOrders}");
        } else {
            $this->error("  ❌ orders表缺少deleted_at字段");
        }
        $this->newLine();
    }

    private function testCoreFunctionality()
    {
        $this->info('🔧 测试核心业务功能...');
        
        try {
            // 测试订单查询
            $orderCount = Order::count();
            $this->line("  ✅ 订单查询正常 (共{$orderCount}条记录)");
            
            // 测试订单更正查询
            $correctionCount = OrderCorrection::count();
            $this->line("  ✅ 订单更正查询正常 (共{$correctionCount}条记录)");
            
            // 测试账单查询
            $billCount = Bill::count();
            $this->line("  ✅ 账单查询正常 (共{$billCount}条记录)");
            
            // 测试支付记录查询
            $paymentCount = PaymentRecord::count();
            $this->line("  ✅ 支付记录查询正常 (共{$paymentCount}条记录)");
            
        } catch (\Exception $e) {
            $this->error("  ❌ 核心功能测试失败: " . $e->getMessage());
        }
        $this->newLine();
    }

    private function generateOptimizationReport()
    {
        $this->info('📊 生成优化报告...');
        
        // 统计新增索引
        $newIndexes = DB::select("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND INDEX_NAME LIKE 'idx_%'
            AND TABLE_NAME IN ('orders', 'bills', 'order_corrections', 'payment_records')
        ");
        
        // 统计约束
        $constraints = DB::select("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE() 
            AND CONSTRAINT_TYPE = 'CHECK'
            AND TABLE_NAME IN ('bills', 'order_corrections', 'payment_records')
        ");
        
        // 统计安全外键
        $safeKeys = DB::select("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE() 
            AND DELETE_RULE = 'RESTRICT'
            AND TABLE_NAME IN ('orders', 'bills', 'order_corrections', 'payment_records')
        ");
        
        $this->table(
            ['优化项目', '数量', '状态'],
            [
                ['新增性能索引', $newIndexes[0]->count ?? 0, '✅ 完成'],
                ['数据完整性约束', $constraints[0]->count ?? 0, '✅ 完成'],
                ['安全外键约束', $safeKeys[0]->count ?? 0, '✅ 完成'],
                ['软删除支持', Schema::hasColumn('orders', 'deleted_at') ? 1 : 0, '✅ 完成'],
            ]
        );
    }
}
