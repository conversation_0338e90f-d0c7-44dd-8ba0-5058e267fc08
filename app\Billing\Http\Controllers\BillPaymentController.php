<?php

namespace App\Billing\Http\Controllers;

use App\Billing\Models\Bill;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class BillPaymentController extends Controller
{
    /**
     * 显示账单支付H5页面
     */
    public function show($billId, Request $request)
    {
        try {
            $bill = Bill::with(['user'])->findOrFail($billId);
            
            // 验证签名
            $this->validateSignature($request, $bill);
            
            // 检查账单状态
            if (!in_array($bill->status, ['pending', 'partial_paid'])) {
                return view('billing.payment.error', [
                    'message' => '账单状态不允许支付'
                ]);
            }
            
            // 检查是否过期
            $expires = $request->input('expires');
            if ($expires && now()->timestamp > $expires) {
                return view('billing.payment.error', [
                    'message' => '支付链接已过期'
                ]);
            }
            
            // 获取账单详情
            $billData = [
                'id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'amount' => $bill->pending_amount,
                'user_name' => $bill->user->merchant_name ?? $bill->user->name,
                'created_at' => $bill->created_at->format('Y-m-d H:i'),
                'is_consolidated' => $bill->order_id === null,
            ];
            
            // 如果是累计账单，获取包含的订单信息
            $includedOrders = [];
            if ($bill->order_id === null) {
                $includedOrders = Bill::where('parent_bill_id', $bill->id)
                    ->with(['order'])
                    ->get()
                    ->map(function($childBill) {
                        return [
                            'order_no' => $childBill->order->order_no ?? '未知订单',
                            'amount' => $childBill->final_amount,
                        ];
                    });
            }
            
            return view('billing.payment.h5', [
                'bill' => $billData,
                'includedOrders' => $includedOrders
            ]);
            
        } catch (\Exception $e) {
            Log::error('显示账单支付页面失败', [
                'bill_id' => $billId,
                'error' => $e->getMessage()
            ]);
            
            return view('billing.payment.error', [
                'message' => '页面加载失败，请重试'
            ]);
        }
    }

    /**
     * 🔥 新增：支付返回页面（H5支付完成后）
     */
    public function paymentReturn(Request $request)
    {
        $billId = $request->input('bill_id');

        if (!$billId) {
            return view('billing.payment.error', [
                'message' => '缺少账单参数'
            ]);
        }

        try {
            $bill = Bill::findOrFail($billId);

            // 检查支付状态（这里可能需要查询微信支付状态）
            // 暂时显示处理中页面，让用户等待支付结果
            return view('billing.payment.processing', [
                'bill' => [
                    'id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'amount' => $bill->pending_amount,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('支付返回页面错误', [
                'bill_id' => $billId,
                'error' => $e->getMessage()
            ]);

            return view('billing.payment.error', [
                'message' => '页面加载失败'
            ]);
        }
    }

    /**
     * 🔥 新增：支付成功页面
     */
    public function paymentSuccess(Request $request)
    {
        $billId = $request->input('bill_id');

        if (!$billId) {
            return view('billing.payment.error', [
                'message' => '缺少账单参数'
            ]);
        }

        try {
            $bill = Bill::findOrFail($billId);

            return view('billing.payment.success', [
                'bill' => [
                    'id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'amount' => $bill->final_amount,
                    'status' => $bill->status,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('支付成功页面错误', [
                'bill_id' => $billId,
                'error' => $e->getMessage()
            ]);

            return view('billing.payment.error', [
                'message' => '页面加载失败'
            ]);
        }
    }

    /**
     * 🔥 新增：获取支付状态API
     */
    public function getPaymentStatus(Request $request): JsonResponse
    {
        try {
            $billId = $request->input('bill_id');

            if (!$billId) {
                return response()->json([
                    'success' => false,
                    'message' => '缺少账单ID参数'
                ], 400);
            }

            $bill = Bill::find($billId);
            if (!$bill) {
                return response()->json([
                    'success' => false,
                    'message' => '账单不存在'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'status' => $bill->status,
                    'final_amount' => $bill->final_amount,
                    'paid_amount' => $bill->paid_amount,
                    'pending_amount' => $bill->pending_amount,
                    'updated_at' => $bill->updated_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('查询支付状态失败', [
                'bill_id' => $request->input('bill_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '查询失败'
            ], 500);
        }
    }

    /**
     * 验证签名
     */
    protected function validateSignature(Request $request, Bill $bill): void
    {
        $signature = $request->input('signature');

        if (!$signature) {
            abort(403, '缺少签名参数');
        }

        $data = [
            'bill_id' => $bill->id,
            'bill_no' => $bill->bill_no,
            'amount' => $bill->pending_amount,
            'user_id' => $bill->user_id,
        ];

        $expectedSignature = hash_hmac('sha256', json_encode($data), config('app.key'));

        if (!hash_equals($expectedSignature, $signature)) {
            abort(403, '签名验证失败');
        }
    }
}
