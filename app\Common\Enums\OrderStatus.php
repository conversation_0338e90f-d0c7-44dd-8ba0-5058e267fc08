<?php

namespace App\Common\Enums;

/**
 * 生鲜配送订单状态统一管理
 * 专门针对生鲜配送业务特点设计
 */
class OrderStatus
{
    // 🥬 生鲜配送订单主状态 - 按业务流程设计
    const PENDING = 'pending';           // 待确认（新订单，等待商家确认）
    const CONFIRMED = 'confirmed';       // 已确认（商家确认，开始备货）
    const PREPARING = 'preparing';       // 备货中（采购、分拣、打包）
    const READY = 'ready';               // 待配送（备货完成，等待配送）
    const DISPATCHED = 'dispatched';     // 配送中（配送员已取货，在路上）
    const DELIVERED = 'delivered';       // 已送达（配送员确认送达）
    const CORRECTING = 'correcting';     // 更正中（需要根据实际情况更正订单）
    const COMPLETED = 'completed';       // 已完成（客户确认收货，流程结束）
    const CANCELLED = 'cancelled';       // 已取消
    const REFUNDED = 'refunded';         // 已退款

    // 🥬 付款状态 - 独立于业务流程，适配生鲜配送
    const PAYMENT_UNPAID = 'unpaid';     // 未付款
    const PAYMENT_PARTIAL = 'partial';   // 部分付款（更正后产生差额）
    const PAYMENT_PAID = 'paid';         // 已付款
    const PAYMENT_OVERPAID = 'overpaid'; // 超额付款（需要退款）
    const PAYMENT_REFUNDED = 'refunded'; // 已退款

    // 🥬 配送状态 - 生鲜配送专用
    const DELIVERY_PENDING = 'pending';     // 待配送
    const DELIVERY_ASSIGNED = 'assigned';   // 已分配配送员
    const DELIVERY_PICKED = 'picked';       // 配送员已取货
    const DELIVERY_IN_TRANSIT = 'in_transit'; // 配送中
    const DELIVERY_ARRIVED = 'arrived';     // 已到达
    const DELIVERY_COMPLETED = 'completed'; // 配送完成
    const DELIVERY_FAILED = 'failed';       // 配送失败（客户不在等）

    /**
     * 生鲜配送订单主状态映射
     */
    public static function getStatusMap(): array
    {
        return [
            self::PENDING => '待确认',
            self::CONFIRMED => '已确认',
            self::PREPARING => '备货中',
            self::READY => '待配送',
            self::DISPATCHED => '配送中',
            self::DELIVERED => '已送达',
            self::CORRECTING => '更正中',
            self::COMPLETED => '已完成',
            self::CANCELLED => '已取消',
            self::REFUNDED => '已退款',
        ];
    }

    /**
     * 配送状态映射
     */
    public static function getDeliveryStatusMap(): array
    {
        return [
            self::DELIVERY_PENDING => '待配送',
            self::DELIVERY_ASSIGNED => '已分配',
            self::DELIVERY_PICKED => '已取货',
            self::DELIVERY_IN_TRANSIT => '配送中',
            self::DELIVERY_ARRIVED => '已到达',
            self::DELIVERY_COMPLETED => '配送完成',
            self::DELIVERY_FAILED => '配送失败',
        ];
    }

    /**
     * 付款状态映射
     */
    public static function getPaymentStatusMap(): array
    {
        return [
            self::PAYMENT_UNPAID => '未付款',
            self::PAYMENT_PARTIAL => '部分付款',
            self::PAYMENT_PAID => '已付款',
            self::PAYMENT_OVERPAID => '超额付款',
            self::PAYMENT_REFUNDED => '已退款',
        ];
    }

    /**
     * 获取状态文本
     */
    public static function getStatusText(string $status): string
    {
        return self::getStatusMap()[$status] ?? '未知状态';
    }

    /**
     * 获取付款状态文本
     */
    public static function getPaymentStatusText(string $paymentStatus): string
    {
        return self::getPaymentStatusMap()[$paymentStatus] ?? '未知状态';
    }

    /**
     * 生鲜配送状态流转规则 - 严格按照业务流程
     */
    public static function getStatusFlow(): array
    {
        return [
            // 🥬 新订单 → 确认或取消
            self::PENDING => [self::CONFIRMED, self::CANCELLED],

            // 🥬 已确认 → 开始备货或取消
            self::CONFIRMED => [self::PREPARING, self::CANCELLED],

            // 🥬 备货中 → 备货完成或取消
            self::PREPARING => [self::READY, self::CANCELLED],

            // 🥬 待配送 → 开始配送或取消
            self::READY => [self::DISPATCHED, self::CANCELLED],

            // 🥬 配送中 → 送达或配送失败
            self::DISPATCHED => [self::DELIVERED, self::CANCELLED],

            // 🥬 已送达 → 需要更正或直接完成
            self::DELIVERED => [self::CORRECTING, self::COMPLETED, self::REFUNDED],

            // 🥬 更正中 → 更正完成
            self::CORRECTING => [self::COMPLETED, self::REFUNDED],

            // 🥬 终态
            self::COMPLETED => [self::REFUNDED], // 完成后还可能退款
            self::CANCELLED => [], // 终态
            self::REFUNDED => [],  // 终态
        ];
    }

    /**
     * 配送状态流转规则
     */
    public static function getDeliveryStatusFlow(): array
    {
        return [
            self::DELIVERY_PENDING => [self::DELIVERY_ASSIGNED],
            self::DELIVERY_ASSIGNED => [self::DELIVERY_PICKED, self::DELIVERY_FAILED],
            self::DELIVERY_PICKED => [self::DELIVERY_IN_TRANSIT],
            self::DELIVERY_IN_TRANSIT => [self::DELIVERY_ARRIVED, self::DELIVERY_FAILED],
            self::DELIVERY_ARRIVED => [self::DELIVERY_COMPLETED, self::DELIVERY_FAILED],
            self::DELIVERY_COMPLETED => [], // 终态
            self::DELIVERY_FAILED => [self::DELIVERY_ASSIGNED], // 可以重新分配
        ];
    }

    /**
     * 检查状态转换是否合法
     */
    public static function canTransitionTo(string $fromStatus, string $toStatus): bool
    {
        $allowedTransitions = self::getStatusFlow()[$fromStatus] ?? [];
        return in_array($toStatus, $allowedTransitions);
    }

    /**
     * 根据支付方式和付款状态获取建议的订单状态（生鲜配送专用）
     */
    public static function getSuggestedOrderStatus(string $paymentMethod, string $paymentStatus, string $currentStatus): string
    {
        // 🥬 货到付款订单：状态完全由业务流程控制，不受付款状态影响
        if ($paymentMethod === 'cod') {
            return $currentStatus; // 保持当前状态，由配送流程驱动
        }

        // 🥬 在线支付订单：根据付款状态调整，但要考虑生鲜配送特点
        switch ($paymentStatus) {
            case self::PAYMENT_PAID:
            case self::PAYMENT_OVERPAID:
                // 已付款：如果是待确认状态，可以转为已确认开始备货
                if ($currentStatus === self::PENDING) {
                    return self::CONFIRMED;
                }
                break;

            case self::PAYMENT_UNPAID:
            case self::PAYMENT_PARTIAL:
                // 未付款或部分付款：如果已确认但还未开始备货，可以回到待确认
                if ($currentStatus === self::CONFIRMED) {
                    return self::PENDING;
                }
                // 如果已经在备货或配送中，不能回滚（生鲜特点：不能停止流程）
                break;
        }

        return $currentStatus; // 默认保持当前状态
    }

    /**
     * 检查是否需要订单更正（生鲜配送特有）
     */
    public static function needsCorrection(string $currentStatus): bool
    {
        // 只有已送达的订单才需要更正
        return $currentStatus === self::DELIVERED;
    }

    /**
     * 获取下一个可能的状态（生鲜配送流程）
     */
    public static function getNextPossibleStatuses(string $currentStatus): array
    {
        return self::getStatusFlow()[$currentStatus] ?? [];
    }

    /**
     * 获取用户端显示状态（生鲜配送专用）
     */
    public static function getUserDisplayStatus(string $status, string $paymentStatus, string $paymentMethod, ?string $deliveryStatus = null): array
    {
        $displayStatus = '';
        $displayClass = '';
        $canPay = false;
        $canCancel = false;
        $canConfirm = false;
        $estimatedTime = '';

        // 🥬 根据生鲜配送业务状态组合显示
        switch ($status) {
            case self::PENDING:
                if ($paymentMethod === 'cod') {
                    $displayStatus = '待确认';
                    $displayClass = 'warning';
                    $estimatedTime = '商家将在30分钟内确认';
                } else {
                    $displayStatus = $paymentStatus === self::PAYMENT_PAID ? '待确认' : '待付款';
                    $displayClass = 'warning';
                    $canPay = $paymentStatus !== self::PAYMENT_PAID;
                    $estimatedTime = $canPay ? '请尽快完成付款' : '商家将在30分钟内确认';
                }
                $canCancel = true;
                break;

            case self::CONFIRMED:
                $displayStatus = '已确认';
                $displayClass = 'info';
                $estimatedTime = '商家正在为您准备新鲜商品';
                $canCancel = true; // 备货前还可以取消
                break;

            case self::PREPARING:
                $displayStatus = '备货中';
                $displayClass = 'info';
                $estimatedTime = '预计30分钟内完成备货';
                break;

            case self::READY:
                $displayStatus = '待配送';
                $displayClass = 'primary';
                $estimatedTime = '配送员即将取货';
                break;

            case self::DISPATCHED:
                $displayStatus = '配送中';
                $displayClass = 'primary';
                $estimatedTime = self::getDeliveryEstimatedTime($deliveryStatus);
                break;

            case self::DELIVERED:
                $displayStatus = '已送达';
                $displayClass = 'success';
                $estimatedTime = '请确认商品数量和质量';
                $canConfirm = true;
                break;

            case self::CORRECTING:
                $displayStatus = '更正中';
                $displayClass = 'warning';
                $estimatedTime = '正在根据实际情况调整订单';
                break;

            case self::COMPLETED:
                $displayStatus = '已完成';
                $displayClass = 'success';
                $estimatedTime = '感谢您的购买，欢迎再次光临';
                break;

            case self::CANCELLED:
                $displayStatus = '已取消';
                $displayClass = 'danger';
                break;

            case self::REFUNDED:
                $displayStatus = '已退款';
                $displayClass = 'secondary';
                break;

            default:
                $displayStatus = '未知状态';
                $displayClass = 'secondary';
        }

        return [
            'status' => $displayStatus,
            'class' => $displayClass,
            'can_pay' => $canPay,
            'can_cancel' => $canCancel,
            'can_confirm' => $canConfirm,
            'estimated_time' => $estimatedTime,
            'is_in_delivery' => in_array($status, [self::READY, self::DISPATCHED]),
            'needs_attention' => in_array($status, [self::PENDING, self::DELIVERED, self::CORRECTING]),
        ];
    }

    /**
     * 根据配送状态获取预计时间
     */
    private static function getDeliveryEstimatedTime(?string $deliveryStatus): string
    {
        return match($deliveryStatus) {
            self::DELIVERY_PICKED => '配送员已取货，预计30分钟内送达',
            self::DELIVERY_IN_TRANSIT => '配送员正在路上，预计15分钟内送达',
            self::DELIVERY_ARRIVED => '配送员已到达，请准备收货',
            default => '配送员正在配送中'
        };
    }

    /**
     * 获取管理端显示状态（详细版）
     */
    public static function getAdminDisplayStatus(string $status, string $paymentStatus, string $paymentMethod): array
    {
        $statusText = self::getStatusText($status);
        $paymentText = self::getPaymentStatusText($paymentStatus);
        
        return [
            'status' => $statusText,
            'payment_status' => $paymentText,
            'combined' => $statusText . ($paymentMethod !== 'cod' ? " / {$paymentText}" : ''),
            'payment_method' => $paymentMethod === 'cod' ? '货到付款' : '在线支付',
        ];
    }
}
