<?php

use App\Billing\Http\Controllers\BillController;
use App\Billing\Http\Controllers\ConsolidatedBillController;
use App\Billing\Http\Controllers\Api\SettlementController;
use App\Billing\Http\Controllers\Api\PaymentRecordController;
use App\Billing\Http\Controllers\BillPaymentController;
use Illuminate\Support\Facades\Route;

// 🔥 新增：支付状态查询API（无需认证）
Route::get('/bill/payment-status', [BillPaymentController::class, 'getPaymentStatus']);

// 移除重复的路由定义，只保留billing前缀下的路由

    /*
    |--------------------------------------------------------------------------
    | Billing API Routes
    |--------------------------------------------------------------------------
    */

    Route::prefix('billing')->middleware(['auth:sanctum'])->group(function () {

        // 账单管理 - 使用不同的路由名称避免冲突
        Route::apiResource('bills', BillController::class)->names([
            'index' => 'billing.bills.index',
            'store' => 'billing.bills.store',
            'show' => 'billing.bills.show',
            'update' => 'billing.bills.update',
            'destroy' => 'billing.bills.destroy'
        ]);
        Route::post('bills/{bill}/pay', [BillController::class, 'pay']);
        Route::post('bills/{bill}/payment', [BillController::class, 'pay']); // 🔥 新增：兼容前端调用的payment路径
        Route::post('bills/{bill}/cancel', [BillController::class, 'cancel']);
        Route::post('bills/{bill}/adjust', [BillController::class, 'adjust']);
        Route::post('bills/{bill}/refund', [BillController::class, 'refund']); // 🔥 新增：退款路由
        Route::post('bills/batch-refund', [BillController::class, 'batchRefund']); // 🔥 新增：批量退款路由
        Route::get('bills/{bill}/items', [BillController::class, 'items']);
        Route::get('bills/statistics/summary', [BillController::class, 'statistics']);
        Route::get('bills/consolidated', [BillController::class, 'consolidated']); // 🔥 新增：累计账单列表

        // 账单报告接口
        Route::get('reports/bills', [BillController::class, 'reports']);
        Route::get('reports/payments', [PaymentRecordController::class, 'reports']);
        
        // 合并账单统计接口
        Route::get('bills/consolidated/statistics', [BillController::class, 'consolidatedStats']);
        
        // 累计账单管理
        Route::apiResource('consolidated-bills', ConsolidatedBillController::class);
        Route::get('consolidated-bills/statistics/summary', [ConsolidatedBillController::class, 'getStatistics']);
        Route::get('consolidated-bills/check-consolidatable', [ConsolidatedBillController::class, 'checkConsolidatable']);
        Route::post('consolidated-bills/create-periodic', [ConsolidatedBillController::class, 'createPeriodicBill']);
        Route::get('consolidated-bills/{consolidatedBill}/orders', [ConsolidatedBillController::class, 'getIncludedOrders']);
        Route::post('consolidated-bills/{consolidatedBill}/split', [ConsolidatedBillController::class, 'splitBill']);
        Route::get('consolidated-bills/{consolidatedBill}/history', [ConsolidatedBillController::class, 'getOperationHistory']);
        

        
        // 支付记录管理 - 特殊路由必须在apiResource之前定义
        Route::get('payment-records/statistics', [PaymentRecordController::class, 'statistics']);
        Route::get('payment-records/export', [PaymentRecordController::class, 'export']);
        Route::apiResource('payment-records', PaymentRecordController::class);
        Route::post('payment-records/{record}/retry', [PaymentRecordController::class, 'retry']);
        Route::post('payment-records/{record}/cancel', [PaymentRecordController::class, 'cancel']);
        
        // 结算管理路由已移至下方统一定义，避免重复
        Route::post('settlements/{settlement}/publish', [SettlementController::class, 'publish']);
        Route::post('settlements/{settlement}/archive', [SettlementController::class, 'archive']);
        Route::get('settlements/{settlement}/export', [SettlementController::class, 'export']);
    });

    // 添加一些非billing前缀的路由（用于兼容前端API调用）
    Route::get('bills/statistics/summary', [BillController::class, 'statistics']);
    Route::post('bills/{bill}/adjust', [BillController::class, 'adjust']);
    Route::get('consolidated-bills/statistics/summary', [BillController::class, 'consolidatedStats']);
    Route::get('consolidated-bills/consolidatable-orders', [ConsolidatedBillController::class, 'getConsolidatableOrders'])->withoutMiddleware(['auth:sanctum']);
    Route::get('consolidated-bills/check-consolidatable', [ConsolidatedBillController::class, 'checkConsolidatable']);
    Route::post('consolidated-bills/create-periodic', [ConsolidatedBillController::class, 'createPeriodicBill']);
    
    // 手动定义非前缀的consolidated-bills路由，而不是使用apiResource
    Route::get('consolidated-bills', [ConsolidatedBillController::class, 'index']);
    Route::post('consolidated-bills', [ConsolidatedBillController::class, 'store']);
    Route::get('consolidated-bills/{consolidatedBill}', [ConsolidatedBillController::class, 'show']);
    Route::put('consolidated-bills/{consolidatedBill}', [ConsolidatedBillController::class, 'update']);
    Route::delete('consolidated-bills/{consolidatedBill}', [ConsolidatedBillController::class, 'destroy']);
    


    // 支付记录兼容路由（移到这里避免重复）
    Route::get('payment-records/statistics', [PaymentRecordController::class, 'statistics']);
    Route::get('payment-records', [PaymentRecordController::class, 'index']);

    /*
    |--------------------------------------------------------------------------
    | Settlements API Routes
    |--------------------------------------------------------------------------
    */

    // 结算管理 - 统一路由定义
    Route::middleware(['auth:sanctum'])->group(function () {

        // ==================== 结算管理 ====================
        Route::apiResource('settlements', SettlementController::class)->names([
            'index' => 'billing.settlements.index',
            'store' => 'billing.settlements.store',
            'show' => 'billing.settlements.show',
            'update' => 'billing.settlements.update',
            'destroy' => 'billing.settlements.destroy'
        ]);
        
        // 结算操作
        Route::prefix('settlements')->group(function () {
            // 状态操作
            Route::post('{settlement}/calculate', [SettlementController::class, 'calculate']);
            Route::post('{settlement}/recalculate', [SettlementController::class, 'recalculate']);
            Route::post('{settlement}/verify', [SettlementController::class, 'verify']);
            Route::post('{settlement}/publish', [SettlementController::class, 'publish']);
            Route::post('{settlement}/archive', [SettlementController::class, 'archive']);
            
            // 数据获取
            Route::get('summary', [SettlementController::class, 'summary']);
            Route::get('analytics', [SettlementController::class, 'analytics']);
            Route::get('{settlement}/details', [SettlementController::class, 'details']);
            Route::get('{settlement}/logs', [SettlementController::class, 'logs']);
            
            // 导出功能
            Route::get('{settlement}/export', [SettlementController::class, 'export']);
            Route::post('batch-export', [SettlementController::class, 'batchExport']);
            
            // 快速创建
            Route::prefix('quick-create')->group(function () {
                Route::post('monthly-platform', [SettlementController::class, 'createMonthlyPlatform']);
                Route::post('quarterly-module', [SettlementController::class, 'createQuarterlyModule']);
                Route::post('yearly-total', [SettlementController::class, 'createYearlyTotal']);
            });
            
            // 数据维护
            Route::post('{settlement}/validate', [SettlementController::class, 'validateSettlement']);
            Route::post('{settlement}/repair', [SettlementController::class, 'repair']);
        });
    });

// 🔥 重要：统一支付回调路由（不需要认证）- 替代各模块的支付回调处理
use App\Billing\Http\Controllers\Api\PaymentCallbackController;

// 主要微信支付回调路由 - 由账单系统统一处理
Route::any('billing/callbacks/wechat', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);
Route::any('billing/callbacks/wechat/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);

// 其他支付方式回调
Route::any('billing/callbacks/alipay', [PaymentCallbackController::class, 'alipayCallback'])->withoutMiddleware(['auth:sanctum']);
Route::post('billing/callbacks/payment-link', [PaymentCallbackController::class, 'paymentLinkCallback'])->withoutMiddleware(['auth:sanctum']);

// 手动确认收款（需要认证）
Route::post('billing/callbacks/mark-payment-success', [PaymentCallbackController::class, 'markPaymentSuccess']);

// 🔥 兼容性路由：保持旧的微信支付回调路由，但转发到新的统一处理器
Route::post('payment-records/wechat/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);

// 🔥 全局兼容性路由：处理可能的历史回调地址
Route::any('api/wechat/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);
Route::any('api/payments/wechat/callback', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);
Route::any('wechat/payment/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);

