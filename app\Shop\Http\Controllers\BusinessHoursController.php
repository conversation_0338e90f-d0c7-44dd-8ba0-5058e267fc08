<?php

namespace App\Shop\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Shop\Services\DatabaseBusinessHoursService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 营业时间控制器
 * 
 * 提供营业时间相关的API接口：
 * - 获取营业状态
 * - 获取营业时间配置
 * - 验证下单时间
 */
class BusinessHoursController extends Controller
{
    /**
     * 营业时间服务
     */
    protected $businessHoursService;

    /**
     * 构造函数
     */
    public function __construct(DatabaseBusinessHoursService $businessHoursService)
    {
        $this->businessHoursService = $businessHoursService;
    }

    /**
     * 获取当前营业状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBusinessStatus(Request $request)
    {
        try {
            Log::info('获取营业状态请求', [
                'user_id' => $request->user()?->id,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            $status = $this->businessHoursService->getBusinessStatus();
            
            Log::info('营业状态获取成功', [
                'status' => $status,
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::success($status, '获取营业状态成功'));
        } catch (\Exception $e) {
            Log::error('获取营业状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::error('获取营业状态失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取营业时间配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBusinessHours(Request $request)
    {
        try {
            Log::info('获取营业时间配置请求', [
                'user_id' => $request->user()?->id
            ]);

            $currentTime = $this->businessHoursService->getCurrentTime();
            
            // 获取今天和明天的营业时间
            $today = $currentTime->copy();
            $tomorrow = $currentTime->copy()->addDay();
            
            $config = [
                'enabled' => $this->businessHoursService->isEnabled(),
                'timezone' => $this->businessHoursService->getTimezone(),
                'maintenance_mode' => $this->businessHoursService->isMaintenanceMode(),
                'current_time' => $currentTime->format('Y-m-d H:i:s'),
                'today' => [
                    'date' => $today->format('Y-m-d'),
                    'day_name' => $today->format('l'),
                    'hours' => $this->businessHoursService->getBusinessHoursForDate($today)
                ],
                'tomorrow' => [
                    'date' => $tomorrow->format('Y-m-d'),
                    'day_name' => $tomorrow->format('l'),
                    'hours' => $this->businessHoursService->getBusinessHoursForDate($tomorrow)
                ],
                'closing_warning_minutes' => $this->businessHoursService->getClosingWarningMinutes()
            ];

            return response()->json(ApiResponse::success($config, '获取营业时间配置成功'));
        } catch (\Exception $e) {
            Log::error('获取营业时间配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::error('获取营业时间配置失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 验证下单时间
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateOrderTime(Request $request)
    {
        try {
            // 可选参数：指定验证时间（用于测试）
            $timeString = $request->input('time');
            $time = $timeString ? Carbon::parse($timeString, $this->businessHoursService->getTimezone()) : null;

            Log::info('验证下单时间请求', [
                'user_id' => $request->user()?->id,
                'specified_time' => $timeString,
                'parsed_time' => $time?->toISOString()
            ]);

            $validation = $this->businessHoursService->validateOrderTime($time);
            
            Log::info('下单时间验证完成', [
                'validation' => $validation,
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::success($validation, '下单时间验证完成'));
        } catch (\Exception $e) {
            Log::error('验证下单时间失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id,
                'input_time' => $request->input('time')
            ]);

            return response()->json(ApiResponse::error('验证下单时间失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取下次营业时间
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNextOpenTime(Request $request)
    {
        try {
            $currentTime = $this->businessHoursService->getCurrentTime();
            $nextOpenTime = $this->businessHoursService->getNextOpenTime($currentTime);

            $result = [
                'current_time' => $currentTime->format('Y-m-d H:i:s'),
                'next_open_time' => $nextOpenTime ? $nextOpenTime->format('Y-m-d H:i:s') : null,
                'next_open_date' => $nextOpenTime ? $nextOpenTime->format('Y-m-d') : null,
                'next_open_day_name' => $nextOpenTime ? $nextOpenTime->format('l') : null,
                'hours_until_open' => $nextOpenTime ? $currentTime->diffInHours($nextOpenTime) : null,
                'minutes_until_open' => $nextOpenTime ? $currentTime->diffInMinutes($nextOpenTime) : null
            ];

            return response()->json(ApiResponse::success($result, '获取下次营业时间成功'));
        } catch (\Exception $e) {
            Log::error('获取下次营业时间失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::error('获取下次营业时间失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取营业时间统计信息（管理员接口）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBusinessStats(Request $request)
    {
        try {
            // 这里可以添加权限检查
            // $this->authorize('view-business-stats');

            $currentTime = $this->businessHoursService->getCurrentTime();
            $startOfWeek = $currentTime->copy()->startOfWeek();
            
            $weeklyStats = [];
            for ($i = 0; $i < 7; $i++) {
                $date = $startOfWeek->copy()->addDays($i);
                $hours = $this->businessHoursService->getBusinessHoursForDate($date);
                
                $weeklyStats[] = [
                    'date' => $date->format('Y-m-d'),
                    'day_name' => $date->format('l'),
                    'enabled' => $hours['enabled'],
                    'start' => $hours['start'],
                    'end' => $hours['end'],
                    'duration_hours' => $hours['enabled'] ? 
                        Carbon::parse($hours['end'])->diffInHours(Carbon::parse($hours['start'])) : 0
                ];
            }

            $stats = [
                'current_week' => $weeklyStats,
                'total_business_hours_this_week' => array_sum(array_column($weeklyStats, 'duration_hours')),
                'business_days_this_week' => count(array_filter($weeklyStats, fn($day) => $day['enabled'])),
                'current_status' => $this->businessHoursService->getBusinessStatus(),
                'config' => [
                    'enabled' => $this->businessHoursService->isEnabled(),
                    'maintenance_mode' => $this->businessHoursService->isMaintenanceMode(),
                    'timezone' => $this->businessHoursService->getTimezone()
                ]
            ];

            return response()->json(ApiResponse::success($stats, '获取营业时间统计成功'));
        } catch (\Exception $e) {
            Log::error('获取营业时间统计失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::error('获取营业时间统计失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 🔥 新增：获取实时营业状态（无缓存）
     * 用于精确时间控制，在营业时间边界时使用
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRealtimeStatus(Request $request)
    {
        try {
            Log::info('获取实时营业状态请求', [
                'user_id' => $request->user()?->id,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            $status = $this->businessHoursService->getBusinessStatusRealtime();

            Log::info('实时营业状态获取成功', [
                'status' => $status,
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::success($status, '获取实时营业状态成功'));
        } catch (\Exception $e) {
            Log::error('获取实时营业状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json(ApiResponse::error('获取实时营业状态失败: ' . $e->getMessage(), 500), 500);
        }
    }
}
