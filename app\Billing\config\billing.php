<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 账单配置
    |--------------------------------------------------------------------------
    */

    // 默认账单到期天数
    'default_due_days' => 7,

    // 支持的支付方式
    'payment_methods' => [
        'wechat' => '微信支付',
        'alipay' => '支付宝',
        'cash' => '现金',
        'bank_transfer' => '银行转账',
        'cod' => '货到付款',
        'balance' => '余额支付',
        'mixed' => '混合支付',
        'other' => '其他方式',
    ],

    // 账单类型
    'bill_types' => [
        'order' => '订单账单',
        'adjustment' => '调整账单',
        'refund' => '退款账单',
        'supplement' => '补差账单',
    ],

    // 账单状态
    'bill_statuses' => [
        'draft' => '草稿',
        'pending' => '待付款',
        'partial_paid' => '部分付款',
        'paid' => '已付款',
        'overpaid' => '超额付款',
        'cancelled' => '已取消',
        'refunded' => '已退款',
        'consolidated' => '已合并',
    ],

    // 付款状态
    'payment_statuses' => [
        'unpaid' => '未付款',
        'partial' => '部分付款',
        'paid' => '已付款',
        'overpaid' => '超额付款',
        'refunded' => '已退款',
    ],

    // 自动处理设置
    'auto_processing' => [
        'auto_confirm_online_payment' => true, // 自动确认线上支付
        'auto_update_bill_status' => true, // 自动更新账单状态
        'auto_create_balance_transaction' => true, // 自动创建余额变动记录
    ],

    // 通知设置
    'notifications' => [
        'overdue_reminder_days' => [1, 3, 7], // 逾期提醒天数
        'payment_success_notify' => true, // 支付成功通知
        'bill_created_notify' => true, // 账单创建通知
    ],

    // 报表设置
    'reports' => [
        'daily_summary' => true, // 每日汇总
        'monthly_summary' => true, // 月度汇总
        'overdue_report' => true, // 逾期报告
    ],

    // 🔥 新增：二维码支付配置
    'qr_code_payment' => [
        'enabled' => true,           // 是否启用二维码支付
        'size' => 60,               // 二维码尺寸（像素）
        'expire_hours' => 24,       // 支付链接有效期（小时）
        'min_amount' => 1,          // 最小支付金额
        'max_amount' => 50000,      // 最大支付金额
    ],
];