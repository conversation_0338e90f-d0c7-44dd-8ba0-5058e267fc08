<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PerformanceMonitor extends Command
{
    protected $signature = 'monitor:performance {--alert : 检查告警阈值}';
    protected $description = '性能监控和告警';

    private $thresholds = [
        'query_time_warning' => 10, // 10ms
        'query_time_critical' => 50, // 50ms
        'connection_count_warning' => 100,
        'connection_count_critical' => 200,
    ];

    public function handle()
    {
        $this->info('📊 开始性能监控...');
        $this->newLine();

        // 1. 数据库连接监控
        $this->monitorDatabaseConnections();
        
        // 2. 查询性能监控
        $this->monitorQueryPerformance();
        
        // 3. 索引使用监控
        $this->monitorIndexUsage();
        
        // 4. 缓存性能监控
        $this->monitorCachePerformance();
        
        // 5. 业务指标监控
        $this->monitorBusinessMetrics();
        
        if ($this->option('alert')) {
            $this->checkAlerts();
        }
        
        $this->newLine();
        $this->info('✅ 性能监控完成！');
    }

    private function monitorDatabaseConnections()
    {
        $this->info('🔗 监控数据库连接...');
        
        try {
            $connections = DB::select('SHOW STATUS LIKE "Threads_connected"');
            $maxConnections = DB::select('SHOW VARIABLES LIKE "max_connections"');
            
            $currentConnections = $connections[0]->Value ?? 0;
            $maxConnectionsValue = $maxConnections[0]->Value ?? 0;
            $connectionUsage = round(($currentConnections / $maxConnectionsValue) * 100, 2);
            
            $status = $currentConnections < $this->thresholds['connection_count_warning'] ? '✅' : 
                     ($currentConnections < $this->thresholds['connection_count_critical'] ? '⚠️' : '❌');
            
            $this->line("  {$status} 当前连接数: {$currentConnections}/{$maxConnectionsValue} ({$connectionUsage}%)");
            
            // 记录到缓存用于告警
            Cache::put('db_connections', $currentConnections, 300);
            
        } catch (\Exception $e) {
            $this->line("  ❌ 连接监控失败: " . $e->getMessage());
        }
    }

    private function monitorQueryPerformance()
    {
        $this->info('⚡ 监控查询性能...');
        
        $testQueries = [
            'orders状态查询' => "SELECT COUNT(*) FROM orders WHERE status = 'delivered'",
            'bills用户查询' => "SELECT COUNT(*) FROM bills WHERE user_id = 1",
            'corrections状态查询' => "SELECT COUNT(*) FROM order_corrections WHERE status = 'pending'",
            'payments类型查询' => "SELECT COUNT(*) FROM payment_records WHERE payment_type = 'supplement'",
        ];
        
        $performanceData = [];
        
        foreach ($testQueries as $description => $query) {
            $startTime = microtime(true);
            try {
                DB::select($query);
                $endTime = microtime(true);
                $executionTime = round(($endTime - $startTime) * 1000, 2);
                
                $status = $executionTime < $this->thresholds['query_time_warning'] ? '✅' : 
                         ($executionTime < $this->thresholds['query_time_critical'] ? '⚠️' : '❌');
                
                $this->line("  {$status} {$description}: {$executionTime}ms");
                
                $performanceData[$description] = $executionTime;
                
                // 记录慢查询
                if ($executionTime > $this->thresholds['query_time_warning']) {
                    Log::warning('慢查询检测', [
                        'query' => $description,
                        'execution_time' => $executionTime,
                        'sql' => $query
                    ]);
                }
                
            } catch (\Exception $e) {
                $this->line("  ❌ {$description}: 查询失败");
                $performanceData[$description] = -1;
            }
        }
        
        // 缓存性能数据
        Cache::put('query_performance', $performanceData, 300);
    }

    private function monitorIndexUsage()
    {
        $this->info('📈 监控索引使用情况...');
        
        try {
            $indexes = DB::select("
                SELECT 
                    TABLE_NAME,
                    INDEX_NAME,
                    CARDINALITY,
                    CASE 
                        WHEN CARDINALITY = 0 THEN '❌ 未使用'
                        WHEN CARDINALITY < 10 THEN '⚠️ 低效'
                        ELSE '✅ 正常'
                    END as status
                FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME IN ('orders', 'bills', 'order_corrections', 'payment_records')
                AND INDEX_NAME LIKE 'idx_%'
                ORDER BY TABLE_NAME, CARDINALITY DESC
            ");
            
            $indexData = [];
            foreach ($indexes as $index) {
                $this->line("  {$index->status} {$index->TABLE_NAME}.{$index->INDEX_NAME} (基数: {$index->CARDINALITY})");
                $indexData[] = [
                    'table' => $index->TABLE_NAME,
                    'index' => $index->INDEX_NAME,
                    'cardinality' => $index->CARDINALITY
                ];
            }
            
            Cache::put('index_usage', $indexData, 300);
            
        } catch (\Exception $e) {
            $this->line("  ❌ 索引监控失败: " . $e->getMessage());
        }
    }

    private function monitorCachePerformance()
    {
        $this->info('🗄️ 监控缓存性能...');
        
        try {
            // 测试缓存读写性能
            $cacheKey = 'performance_test_' . time();
            $testData = ['test' => 'data', 'timestamp' => time()];
            
            // 写入测试
            $startTime = microtime(true);
            Cache::put($cacheKey, $testData, 60);
            $writeTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // 读取测试
            $startTime = microtime(true);
            $cachedData = Cache::get($cacheKey);
            $readTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // 清理测试数据
            Cache::forget($cacheKey);
            
            $writeStatus = $writeTime < 5 ? '✅' : '⚠️';
            $readStatus = $readTime < 1 ? '✅' : '⚠️';
            
            $this->line("  {$writeStatus} 缓存写入: {$writeTime}ms");
            $this->line("  {$readStatus} 缓存读取: {$readTime}ms");
            
            // 检查缓存命中率（如果使用Redis）
            $this->checkCacheHitRate();
            
        } catch (\Exception $e) {
            $this->line("  ❌ 缓存监控失败: " . $e->getMessage());
        }
    }

    private function checkCacheHitRate()
    {
        try {
            // 这里可以添加Redis命中率检查
            // $info = Redis::info();
            // $hitRate = $info['keyspace_hits'] / ($info['keyspace_hits'] + $info['keyspace_misses']) * 100;
            
            $this->line("  ℹ️ 缓存命中率监控需要Redis配置");
            
        } catch (\Exception $e) {
            // 忽略Redis连接错误
        }
    }

    private function monitorBusinessMetrics()
    {
        $this->info('📊 监控业务指标...');
        
        try {
            // 订单相关指标
            $orderMetrics = [
                'total_orders' => DB::table('orders')->count(),
                'delivered_orders' => DB::table('orders')->where('status', 'delivered')->count(),
                'pending_orders' => DB::table('orders')->where('status', 'pending')->count(),
                'today_orders' => DB::table('orders')->whereDate('created_at', today())->count(),
            ];
            
            // 更正相关指标
            $correctionMetrics = [
                'total_corrections' => DB::table('order_corrections')->count(),
                'pending_corrections' => DB::table('order_corrections')->where('status', 'pending')->count(),
                'confirmed_corrections' => DB::table('order_corrections')->where('status', 'confirmed')->count(),
            ];
            
            // 账单相关指标
            $billMetrics = [
                'total_bills' => DB::table('bills')->count(),
                'unpaid_bills' => DB::table('bills')->where('payment_status', 'unpaid')->count(),
                'paid_bills' => DB::table('bills')->where('payment_status', 'paid')->count(),
            ];
            
            $this->line("  📦 订单: 总计{$orderMetrics['total_orders']}, 已送达{$orderMetrics['delivered_orders']}, 待处理{$orderMetrics['pending_orders']}");
            $this->line("  🔄 更正: 总计{$correctionMetrics['total_corrections']}, 待确认{$correctionMetrics['pending_corrections']}, 已确认{$correctionMetrics['confirmed_corrections']}");
            $this->line("  💰 账单: 总计{$billMetrics['total_bills']}, 未付{$billMetrics['unpaid_bills']}, 已付{$billMetrics['paid_bills']}");
            
            // 缓存业务指标
            Cache::put('business_metrics', array_merge($orderMetrics, $correctionMetrics, $billMetrics), 300);
            
        } catch (\Exception $e) {
            $this->line("  ❌ 业务指标监控失败: " . $e->getMessage());
        }
    }

    private function checkAlerts()
    {
        $this->info('🚨 检查告警条件...');
        
        $alerts = [];
        
        // 检查数据库连接告警
        $connections = Cache::get('db_connections', 0);
        if ($connections > $this->thresholds['connection_count_critical']) {
            $alerts[] = "数据库连接数过高: {$connections}";
        }
        
        // 检查查询性能告警
        $performance = Cache::get('query_performance', []);
        foreach ($performance as $query => $time) {
            if ($time > $this->thresholds['query_time_critical']) {
                $alerts[] = "慢查询告警: {$query} ({$time}ms)";
            }
        }
        
        // 检查索引告警
        $indexes = Cache::get('index_usage', []);
        foreach ($indexes as $index) {
            if ($index['cardinality'] == 0) {
                $alerts[] = "索引未使用: {$index['table']}.{$index['index']}";
            }
        }
        
        if (empty($alerts)) {
            $this->line("  ✅ 无告警条件触发");
        } else {
            foreach ($alerts as $alert) {
                $this->line("  🚨 {$alert}");
                Log::warning('性能告警', ['alert' => $alert]);
            }
        }
    }
}
