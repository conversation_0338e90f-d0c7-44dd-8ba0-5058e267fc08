<?php

namespace App\Billing\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BillResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'bill_no' => $this->bill_no,
            'bill_type' => $this->bill_type,
            'bill_type_text' => $this->bill_type_text,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'payment_status' => $this->payment_status,
            'payment_status_text' => $this->payment_status_text,
            'original_payment_method' => $this->original_payment_method, // 🔥 新增：原始付款方式
            
            // 金额信息（嵌套格式）
            'amounts' => [
                'original_amount' => $this->original_amount,
                'adjustment_amount' => $this->adjustment_amount,
                'final_amount' => $this->final_amount,
                'paid_amount' => $this->paid_amount,
                'pending_amount' => $this->pending_amount,
                'balance_amount' => $this->balance_amount,
                'online_amount' => $this->online_amount,
                'cash_amount' => $this->cash_amount,
            ],

            // 🔥 新增：扁平化金额字段（前端直接访问）
            'original_amount' => $this->original_amount,
            'adjustment_amount' => $this->adjustment_amount,
            'final_amount' => $this->final_amount,
            'paid_amount' => $this->paid_amount,
            'pending_amount' => $this->pending_amount,
            
            // 时间信息
            'dates' => [
                'due_date' => $this->due_date?->format('Y-m-d H:i:s'),
                'paid_at' => $this->paid_at?->format('Y-m-d H:i:s'),
                'cancelled_at' => $this->cancelled_at?->format('Y-m-d H:i:s'),
                'created_at' => $this->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            ],
            
            // 用户信息
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'phone' => $this->user->phone,
                    'balance' => $this->user->balance ?? 0,
                ];
            }),
            
            // 订单信息
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->id,
                    'order_no' => $this->order->order_no,
                    'status' => $this->order->status,
                ];
            }),
            

            
            // 账单明细
            'items' => BillItemResource::collection($this->whenLoaded('items')),
            
            // 支付记录
            'payment_records' => PaymentRecordResource::collection($this->whenLoaded('paymentRecords')),
            
            // 调整记录
            'adjustments' => BillAdjustmentResource::collection($this->whenLoaded('adjustments')),
            
            // 余额变动记录
            'balance_transactions' => BalanceTransactionResource::collection($this->whenLoaded('balanceTransactions')),
            
            'notes' => $this->notes,
            'metadata' => $this->metadata,
            
            // 业务逻辑状态
            'can_be_paid' => $this->canBePaid(),
            'can_be_cancelled' => $this->canBeCancelled(),
            'can_be_adjusted' => $this->canBeAdjusted(),
            'supports_balance_payment' => $this->supportsBalancePayment(),
            'max_balance_payment_amount' => $this->getMaxBalancePaymentAmount(),
        ];
    }
} 