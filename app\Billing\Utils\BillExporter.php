<?php

namespace App\Billing\Utils;

use App\Billing\Models\Bill;
use Illuminate\Support\Collection;

class BillExporter
{
    /**
     * 导出账单为CSV格式
     */
    public static function exportToCsv(Collection $bills): string
    {
        $csvData = [];
        
        // CSV头部
        $csvData[] = [
            '账单号',
            '账单类型',
            '用户姓名',
            '用户手机',
            '原始金额',
            '调整金额',
            '最终金额',
            '已付金额',
            '待付金额',
            '账单状态',
            '付款状态',
            '到期日期',
            '创建时间',
            '备注'
        ];

        // 账单数据
        foreach ($bills as $bill) {
            $csvData[] = [
                $bill->bill_no,
                $bill->bill_type_text,
                $bill->user->name ?? '',
                $bill->user->phone ?? '',
                $bill->original_amount,
                $bill->adjustment_amount,
                $bill->final_amount,
                $bill->paid_amount,
                $bill->pending_amount,
                $bill->status_text,
                $bill->payment_status_text,
                $bill->due_date?->format('Y-m-d H:i:s') ?? '',
                $bill->created_at->format('Y-m-d H:i:s'),
                $bill->notes ?? ''
            ];
        }

        return self::arrayToCsv($csvData);
    }

    /**
     * 导出支付记录为CSV格式
     */
    public static function exportPaymentRecordsToCsv(Collection $paymentRecords): string
    {
        $csvData = [];
        
        // CSV头部
        $csvData[] = [
            '支付流水号',
            '账单号',
            '用户姓名',
            '支付方式',
            '支付金额',
            '支付类型',
            '使用余额',
            '使用积分',
            '积分价值',
            '支付状态',
            '支付时间',
            '确认时间',
            '收款人',
            '备注'
        ];

        // 支付记录数据
        foreach ($paymentRecords as $record) {
            $csvData[] = [
                $record->payment_no,
                $record->bill->bill_no ?? '',
                $record->bill->user->name ?? '',
                $record->payment_method_text,
                $record->payment_amount,
                $record->payment_type_text,
                $record->balance_used,
                $record->points_used,
                $record->points_value,
                $record->status_text,
                $record->payment_time?->format('Y-m-d H:i:s') ?? '',
                $record->confirmed_at?->format('Y-m-d H:i:s') ?? '',
                $record->receiver->name ?? '',
                $record->notes ?? ''
            ];
        }

        return self::arrayToCsv($csvData);
    }

    /**
     * 导出余额变动记录为CSV格式
     */
    public static function exportBalanceTransactionsToCsv(Collection $transactions): string
    {
        $csvData = [];
        
        // CSV头部
        $csvData[] = [
            '交易流水号',
            '用户姓名',
            '用户手机',
            '交易金额',
            '交易前余额',
            '交易后余额',
            '交易类型',
            '交易来源',
            '描述',
            '交易状态',
            '交易时间',
            '操作员'
        ];

        // 交易记录数据
        foreach ($transactions as $transaction) {
            $csvData[] = [
                $transaction->transaction_no,
                $transaction->user->name ?? '',
                $transaction->user->phone ?? '',
                $transaction->amount,
                $transaction->balance_before,
                $transaction->balance_after,
                $transaction->transaction_type_text,
                $transaction->source,
                $transaction->description,
                $transaction->status_text,
                $transaction->transaction_time?->format('Y-m-d H:i:s') ?? '',
                $transaction->operator->name ?? ''
            ];
        }

        return self::arrayToCsv($csvData);
    }

    /**
     * 生成账单统计报告
     */
    public static function generateBillReport(array $filters = []): array
    {
        $query = Bill::with(['user', 'items', 'paymentRecords']);
        
        // 应用过滤条件
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        if (isset($filters['bill_type'])) {
            $query->where('bill_type', $filters['bill_type']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        $bills = $query->get();
        
        return [
            'summary' => BillCalculator::calculateBillSummary($bills->toArray()),
            'bills' => $bills,
            'export_time' => now()->format('Y-m-d H:i:s'),
            'filters_applied' => $filters
        ];
    }

    /**
     * 将数组转换为CSV字符串
     */
    private static function arrayToCsv(array $data): string
    {
        $output = fopen('php://temp', 'r+');
        
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }
} 