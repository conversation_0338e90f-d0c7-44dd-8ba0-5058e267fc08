<?php

use App\Region\Http\Controllers\Api\RegionController;
use App\Region\Http\Controllers\Api\RegionPriceController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'regions', 'middleware' => ['api']], function () {
    // 区域相关API
    Route::get('/', [RegionController::class, 'index'])->name('regions.index');
    Route::get('/all', [RegionController::class, 'all'])->name('regions.all');
    Route::get('/tree', [RegionController::class, 'tree'])->name('regions.tree');
    Route::post('/', [RegionController::class, 'store'])->name('regions.store');
    Route::get('/{id}', [RegionController::class, 'show'])->where('id', '[0-9]+')->name('regions.show');
    Route::put('/{id}', [RegionController::class, 'update'])->where('id', '[0-9]+')->name('regions.update');
    Route::delete('/{id}', [RegionController::class, 'destroy'])->where('id', '[0-9]+')->name('regions.destroy');
    Route::get('/{id}/ancestors', [RegionController::class, 'ancestors'])->where('id', '[0-9]+')->name('regions.ancestors');
    Route::get('/{id}/descendants', [RegionController::class, 'descendants'])->where('id', '[0-9]+')->name('regions.descendants');
    Route::get('/{id}/breadcrumb', [RegionController::class, 'breadcrumb'])->where('id', '[0-9]+')->name('regions.breadcrumb');
    Route::get('/{id}/children', [RegionController::class, 'children'])->where('id', '[0-9]+')->name('regions.children');
    
    // 地理位置相关API
    Route::get('/nearest', [RegionController::class, 'nearest'])->name('regions.nearest');
    Route::get('/within-radius', [RegionController::class, 'withinRadius'])->name('regions.within_radius');
    Route::get('/{id}/check-point', [RegionController::class, 'checkPoint'])->where('id', '[0-9]+')->name('regions.check_point');
    
    // 区域价格相关API
    Route::get('/prices', [RegionPriceController::class, 'getProductRegionPrice'])->name('regions.prices.get');
    Route::get('/{regionId}/products/{productId}/full-price', [RegionPriceController::class, 'getFullPriceInfo'])->where(['regionId' => '[0-9]+', 'productId' => '[0-9]+'])->name('regions.products.full-price');
    Route::get('/products/{productId}/prices', [RegionPriceController::class, 'getProductAllRegionPrices'])->where('productId', '[0-9]+')->name('regions.products.prices.all');
    Route::post('/products/{productId}/prices', [RegionPriceController::class, 'batchSetProductRegionPrices'])->where('productId', '[0-9]+')->name('regions.products.prices.batch');
    Route::delete('/products/{productId}/prices', [RegionPriceController::class, 'deleteProductRegionPrices'])->where('productId', '[0-9]+')->name('regions.products.prices.delete');
    Route::post('/prices/copy', [RegionPriceController::class, 'copyRegionPrices'])->name('regions.prices.copy');
    Route::post('/{regionId}/prices/adjust', [RegionPriceController::class, 'adjustRegionPrices'])->where('regionId', '[0-9]+')->name('regions.prices.adjust');
    
    // 批量操作API
    Route::post('/{regionId}/products/batch-add', [RegionPriceController::class, 'batchAddProducts'])->where('regionId', '[0-9]+')->name('regions.products.batch.add');
    Route::post('/{regionId}/prices/batch-set', [RegionPriceController::class, 'batchSetPrices'])->where('regionId', '[0-9]+')->name('regions.prices.batch.set');
    Route::post('/{regionId}/stock/batch-update', [RegionPriceController::class, 'batchUpdateStock'])->where('regionId', '[0-9]+')->name('regions.stock.batch.update');
    Route::post('/{regionId}/status/batch-update', [RegionPriceController::class, 'batchUpdateStatus'])->where('regionId', '[0-9]+')->name('regions.status.batch.update');
    Route::post('/{regionId}/validity/batch-set', [RegionPriceController::class, 'batchSetValidityPeriod'])->where('regionId', '[0-9]+')->name('regions.validity.batch.set');
    Route::get('/{regionId}/products/available', [RegionPriceController::class, 'getAvailableProducts'])->where('regionId', '[0-9]+')->name('regions.products.available');
    
    // 新增的区域价格管理API
    Route::post('/products/{productId}', [RegionPriceController::class, 'setProductRegionPrice'])->where('productId', '[0-9]+')->name('regions.products.price.set');
    Route::delete('/products/{productId}/region/{regionId}', [RegionPriceController::class, 'deleteProductRegionPrice'])
        ->where(['productId' => '[0-9]+', 'regionId' => '[0-9]+'])->name('regions.products.price.delete');
    Route::put('/{regionId}/products/{productId}', [RegionPriceController::class, 'updateProductRegionPrice'])
        ->where(['regionId' => '[0-9]+', 'productId' => '[0-9]+'])->name('regions.products.price.update');
    Route::post('/prices/preview', [RegionPriceController::class, 'previewAdjustPrices'])->name('regions.prices.preview');
    Route::post('/{regionId}/adjust', [RegionPriceController::class, 'adjustPrices'])->where('regionId', '[0-9]+')->name('regions.prices.adjust.new');
    Route::post('/{targetRegionId}/copy-from/{sourceRegionId}', [RegionPriceController::class, 'copyPricesFromRegion'])
        ->where(['targetRegionId' => '[0-9]+', 'sourceRegionId' => '[0-9]+'])->name('regions.prices.copy.from');
    
    // 获取区域内的商品及价格
    Route::get('/{regionId}/products', [RegionPriceController::class, 'getRegionProducts'])->where('regionId', '[0-9]+')->name('regions.products');
    
    // 获取商品完整价格信息（包括会员价格）
    Route::get('/{regionId}/products/{productId}/full-price', [RegionPriceController::class, 'getProductFullPriceInfo'])
        ->where(['regionId' => '[0-9]+', 'productId' => '[0-9]+'])->name('regions.products.full_price');
    
    // 预览会员价格
    Route::get('/{regionId}/products/{productId}/member-prices', [RegionPriceController::class, 'previewMemberPrices'])
        ->where(['regionId' => '[0-9]+', 'productId' => '[0-9]+'])->name('regions.products.member_prices');
    
    // 获取区域价格统计信息
    Route::get('/{regionId}/price-stats', [RegionPriceController::class, 'getRegionPriceStats'])
        ->where('regionId', '[0-9]+')->name('regions.price_stats');
    
    // 分类区域价格管理API
    Route::get('/category-prices', [RegionPriceController::class, 'getAllCategoryPrices'])
        ->name('regions.category_prices.all');
    Route::get('/{regionId}/category-prices', [RegionPriceController::class, 'getCategoryPrices'])
        ->where('regionId', '[0-9]+')->name('regions.category_prices.index');
    Route::post('/{regionId}/category-prices', [RegionPriceController::class, 'setCategoryPrice'])
        ->where('regionId', '[0-9]+')->name('regions.category_prices.store');
    Route::put('/{regionId}/category-prices/{id}', [RegionPriceController::class, 'updateCategoryPrice'])
        ->where(['regionId' => '[0-9]+', 'id' => '[0-9]+'])->name('regions.category_prices.update');
    Route::delete('/{regionId}/category-prices/{id}', [RegionPriceController::class, 'deleteCategoryPrice'])
        ->where(['regionId' => '[0-9]+', 'id' => '[0-9]+'])->name('regions.category_prices.destroy');
}); 