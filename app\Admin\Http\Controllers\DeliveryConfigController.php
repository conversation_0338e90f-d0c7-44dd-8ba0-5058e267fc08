<?php

namespace App\Admin\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\ShopConfig;
use App\Http\Controllers\Controller;
// use App\Employee\Api\ApiResponse; // 不再使用，改为直接返回数组格式
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * 配送单配置管理控制器
 */
class DeliveryConfigController extends Controller
{
    /**
     * 获取配送单配置
     */
    public function getConfig(Request $request): JsonResponse
    {
        try {
            $configs = ShopConfig::where('group', 'delivery')
                ->orderBy('sort_order')
                ->get()
                ->keyBy('key');

            $formattedConfig = [
                'show_bill_info' => (bool) ($configs['delivery.show_bill_info']->value ?? true),
                'show_bill_payment_qr' => (bool) ($configs['delivery.show_bill_payment_qr']->value ?? true),
                'show_bill_amount' => (bool) ($configs['delivery.show_bill_amount']->value ?? true),
                'bill_qr_min_amount' => (float) ($configs['delivery.bill_qr_min_amount']->value ?? 1),
                'bill_qr_max_amount' => (float) ($configs['delivery.bill_qr_max_amount']->value ?? 50000),
                'show_consolidated_bills_only' => (bool) ($configs['delivery.show_consolidated_bills_only']->value ?? false),
            ];

            return response()->json([
                'success' => true,
                'message' => '获取配送单配置成功',
                'data' => $formattedConfig
            ]);

        } catch (\Exception $e) {
            Log::error('获取配送单配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取配置失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新配送单配置
     */
    public function updateConfig(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'show_bill_info' => 'required|boolean',
            'show_bill_payment_qr' => 'required|boolean',
            'show_bill_amount' => 'required|boolean',
            'bill_qr_min_amount' => 'required|numeric|min:0.01|max:10000',
            'bill_qr_max_amount' => 'required|numeric|min:1|max:100000',
            'show_consolidated_bills_only' => 'required|boolean',
        ], [
            'show_bill_info.required' => '请选择是否显示账单信息',
            'show_bill_info.boolean' => '账单信息显示设置格式错误',
            'show_bill_payment_qr.required' => '请选择是否显示付款码',
            'show_bill_payment_qr.boolean' => '付款码显示设置格式错误',
            'show_bill_amount.required' => '请选择是否显示账单金额',
            'show_bill_amount.boolean' => '账单金额显示设置格式错误',
            'bill_qr_min_amount.required' => '请设置最小金额',
            'bill_qr_min_amount.numeric' => '最小金额必须是数字',
            'bill_qr_min_amount.min' => '最小金额不能小于0.01元',
            'bill_qr_min_amount.max' => '最小金额不能大于10000元',
            'bill_qr_max_amount.required' => '请设置最大金额',
            'bill_qr_max_amount.numeric' => '最大金额必须是数字',
            'bill_qr_max_amount.min' => '最大金额不能小于1元',
            'bill_qr_max_amount.max' => '最大金额不能大于100000元',
            'show_consolidated_bills_only.required' => '请选择账单显示类型',
            'show_consolidated_bills_only.boolean' => '账单显示类型格式错误',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()), 422);
        }

        // 验证金额逻辑
        if ($request->bill_qr_min_amount >= $request->bill_qr_max_amount) {
            return response()->json(ApiResponse::error('最小金额必须小于最大金额', 422), 422);
        }

        try {
            $configs = [
                'delivery.show_bill_info' => $request->show_bill_info ? '1' : '0',
                'delivery.show_bill_payment_qr' => $request->show_bill_payment_qr ? '1' : '0',
                'delivery.show_bill_amount' => $request->show_bill_amount ? '1' : '0',
                'delivery.bill_qr_min_amount' => (string) $request->bill_qr_min_amount,
                'delivery.bill_qr_max_amount' => (string) $request->bill_qr_max_amount,
                'delivery.show_consolidated_bills_only' => $request->show_consolidated_bills_only ? '1' : '0',
            ];

            foreach ($configs as $key => $value) {
                ShopConfig::updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }

            Log::info('配送单配置已更新', [
                'operator_id' => $request->user()?->id,
                'configs' => $configs
            ]);

            return response()->json([
                'success' => true,
                'message' => '配送单配置更新成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('更新配送单配置失败', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '更新配置失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取配送单配置详情（包含选项说明）
     */
    public function getConfigDetails(Request $request): JsonResponse
    {
        try {
            $configs = ShopConfig::where('group', 'delivery')
                ->orderBy('sort_order')
                ->get();

            $configDetails = $configs->map(function ($config) {
                return [
                    'key' => $config->key,
                    'title' => $config->title,
                    'description' => $config->description,
                    'type' => $config->type,
                    'value' => $config->value,
                    'options' => $config->options,
                    'sort_order' => $config->sort_order,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => '获取配置详情成功',
                'data' => $configDetails
            ]);

        } catch (\Exception $e) {
            Log::error('获取配送单配置详情失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取配置详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 重置配送单配置为默认值
     */
    public function resetConfig(Request $request): JsonResponse
    {
        try {
            $defaultConfigs = [
                'delivery.show_bill_info' => '1',
                'delivery.show_bill_payment_qr' => '1',
                'delivery.show_bill_amount' => '1',
                'delivery.bill_qr_min_amount' => '1',
                'delivery.bill_qr_max_amount' => '50000',
                'delivery.show_consolidated_bills_only' => '0',
            ];

            foreach ($defaultConfigs as $key => $value) {
                ShopConfig::updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }

            Log::info('配送单配置已重置为默认值', [
                'operator_id' => $request->user()?->id,
                'configs' => $defaultConfigs
            ]);

            return response()->json([
                'success' => true,
                'message' => '配送单配置已重置为默认值',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('重置配送单配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '重置配置失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
