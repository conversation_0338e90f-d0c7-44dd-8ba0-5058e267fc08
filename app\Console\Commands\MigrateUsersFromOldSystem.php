<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\UserAddress;
use Carbon\Carbon;

class MigrateUsersFromOldSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:users-from-old 
                            {--dry-run : 只显示将要迁移的数据，不实际执行}
                            {--limit=100 : 限制迁移的用户数量}
                            {--offset=0 : 跳过的记录数}
                            {--batch-size=100 : 批量处理的数量}
                            {--all : 迁移所有用户数据}
                            {--validate : 仅验证迁移结果，不执行迁移操作}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '从老系统迁移用户数据到新系统';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始用户数据迁移...');
        
        $dryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');
        $offset = (int) $this->option('offset');
        $batchSize = (int) $this->option('batch-size');
        $all = $this->option('all');
        $validate = $this->option('validate');
        
        if ($validate) {
            $this->validateMigrationResults();
            return;
        }
        
        if ($dryRun) {
            $this->warn('这是预览模式，不会实际修改数据');
        }
        
        try {
            // 测试老数据库连接
            $this->info('测试老数据库连接...');
            $oldConnection = DB::connection('mysql_old');
            $oldConnection->getPdo();
            $this->info('✅ 老数据库连接成功');
            
            // 获取老系统的表结构
            $this->info('分析老系统表结构...');
            $this->analyzeOldSystemTables();
            
            if ($all) {
                // 批量迁移所有用户
                $this->batchMigrateAllUsers($dryRun, $batchSize);
                
                // 如果成功完成批量迁移，自动验证结果
                if (!$dryRun) {
                    $this->validateMigrationResults();
                }
            } else {
                // 获取老系统用户数据
                $this->info("获取老系统用户数据 (limit: {$limit}, offset: {$offset})...");
                $oldUsers = $this->getOldSystemUsers($limit, $offset);
                
                if (empty($oldUsers)) {
                    $this->warn('没有找到需要迁移的用户数据');
                    return;
                }
                
                $this->info('找到 ' . count($oldUsers) . ' 个用户需要迁移');
                
                if ($dryRun) {
                    $this->previewMigration($oldUsers);
                } else {
                    $this->performMigration($oldUsers);
                }
            }
            
        } catch (\Exception $e) {
            $this->error('迁移失败: ' . $e->getMessage());
            $this->error('错误详情: ' . $e->getTraceAsString());
        }
    }
    
    /**
     * 批量迁移所有用户
     */
    private function batchMigrateAllUsers($dryRun, $batchSize)
    {
        $this->info('准备批量迁移所有用户数据...');
        
        // 获取总用户数量
        $totalCount = DB::connection('mysql_old')->table('zjhj_bd_user_info')
            ->where('is_delete', 0)
            ->whereNotNull('merchant')
            ->where('merchant', '!=', '')
            ->count();
        
        if ($totalCount === 0) {
            $this->warn('没有找到需要迁移的用户数据');
            return;
        }
        
        $this->info("总共找到 {$totalCount} 个用户需要迁移");
        
        if ($dryRun) {
            $this->warn('预览模式下不执行批量迁移，请使用 --limit 选项预览部分数据');
            return;
        }
        
        if (!$this->confirm("确定要迁移所有 {$totalCount} 个用户吗？")) {
            $this->info('已取消迁移操作');
            return;
        }
        
        $offset = 0;
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        $bar = $this->output->createProgressBar($totalCount);
        $bar->start();
        
        // 创建错误日志文件
        $errorLogFile = storage_path('logs/migration_errors_'.date('Y-m-d_H-i-s').'.log');
        $errorLog = fopen($errorLogFile, 'w');
        fwrite($errorLog, "用户迁移错误日志 - ".date('Y-m-d H:i:s')."\n");
        fwrite($errorLog, "--------------------------------------\n\n");
        
        // 创建成功日志文件
        $successLogFile = storage_path('logs/migration_success_'.date('Y-m-d_H-i-s').'.log');
        $successLog = fopen($successLogFile, 'w');
        fwrite($successLog, "用户迁移成功日志 - ".date('Y-m-d H:i:s')."\n");
        fwrite($successLog, "--------------------------------------\n\n");
        
        while ($offset < $totalCount) {
            $users = $this->getOldSystemUsers($batchSize, $offset);
            
            if (empty($users)) {
                break;
            }
            
            foreach ($users as $user) {
                DB::beginTransaction();
                
                try {
                    $newUser = $this->migrateUser($user);
                    DB::commit();
                    $successCount++;
                    
                    // 记录成功日志
                    $logEntry = "ID: {$user->user_main_id}, 名称: {$user->user_nickname}, 商户: {$user->merchant}\n";
                    fwrite($successLog, $logEntry);
                    
                } catch (\Exception $e) {
                    DB::rollback();
                    $errorCount++;
                    
                    // 记录错误日志
                    $errorMessage = "迁移用户 {$user->user_main_id} ({$user->user_nickname}) 失败: " . $e->getMessage() . "\n";
                    fwrite($errorLog, $errorMessage);
                    
                    // 如果错误太多，提示并确认是否继续
                    if ($errorCount > 50 && $errorCount % 50 === 0) {
                        $bar->clear();
                        $this->newLine();
                        $this->error("已发生 {$errorCount} 个错误。详情请查看错误日志: {$errorLogFile}");
                        
                        if (!$this->confirm('是否继续迁移？')) {
                            $this->warn('用户手动终止迁移');
                            break 2; // 跳出外层循环
                        }
                        
                        $bar->display();
                    }
                }
                
                $bar->advance();
            }
            
            $offset += count($users);
            
            // 每处理1000条记录输出一次状态
            if ($offset % 1000 === 0 || $offset === $totalCount) {
                $bar->clear();
                $this->newLine();
                $this->info("已处理: {$offset}/{$totalCount}, 成功: {$successCount}, 失败: {$errorCount}, 跳过: {$skippedCount}");
                $bar->display();
            }
        }
        
        $bar->finish();
        $this->newLine(2);
        
        // 关闭日志文件
        fclose($errorLog);
        fclose($successLog);
        
        $this->info("批量迁移完成！成功: {$successCount}, 失败: {$errorCount}, 跳过: {$skippedCount}");
        $this->info("错误日志: {$errorLogFile}");
        $this->info("成功日志: {$successLogFile}");
        
        // 显示部分错误样本
        if ($errorCount > 0) {
            $this->warn("前5个错误示例:");
            $errorSamples = array_slice(file($errorLogFile), 3, 5);
            foreach ($errorSamples as $errorSample) {
                $this->line("  - " . trim($errorSample));
            }
            
            if ($errorCount > 5) {
                $this->line("  ... 更多错误请查看日志文件");
            }
        }
    }
    
    /**
     * 分析老系统表结构
     */
    private function analyzeOldSystemTables()
    {
        try {
            $tables = DB::connection('mysql_old')->select('SHOW TABLES');
            $this->info('老系统数据库表数量: ' . count($tables));
            
            $userTables = [];
            foreach ($tables as $table) {
                $tableName = array_values((array)$table)[0];
                if (strpos(strtolower($tableName), 'user') !== false) {
                    $userTables[] = $tableName;
                }
            }
            
            $this->info('用户相关表: ' . implode(', ', $userTables));
            
            // 检查主要用户表的结构
            if (in_array('users', $userTables)) {
                $this->analyzeUserTableStructure('users');
            } elseif (in_array('user', $userTables)) {
                $this->analyzeUserTableStructure('user');
            } else {
                $this->warn('未找到标准的用户表，请手动指定表名');
            }
            
        } catch (\Exception $e) {
            $this->error('分析表结构失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 分析用户表结构
     */
    private function analyzeUserTableStructure($tableName)
    {
        try {
            $columns = DB::connection('mysql_old')->select("DESCRIBE {$tableName}");
            $this->info("表 {$tableName} 的字段结构:");
            
            foreach ($columns as $column) {
                $this->line("  - {$column->Field} ({$column->Type}) " . 
                          ($column->Null === 'YES' ? 'NULL' : 'NOT NULL') . 
                          ($column->Key ? " [{$column->Key}]" : ''));
            }
            
        } catch (\Exception $e) {
            $this->error("分析表 {$tableName} 结构失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取老系统用户数据
     */
    private function getOldSystemUsers($limit, $offset)
    {
        try {
            $this->info("从老系统获取用户数据...");

            // 获取用户基本信息和用户详情的关联数据
            $query = "
                SELECT
                    u.*,
                    ui.*,
                    u.id AS user_main_id,
                    ui.id AS user_info_id,
                    u.nickname AS user_nickname,
                    u.mobile AS user_mobile
                FROM zjhj_bd_user u
                LEFT JOIN zjhj_bd_user_info ui ON u.id = ui.user_id
                WHERE ui.is_delete = 0
                AND ui.merchant IS NOT NULL
                AND ui.merchant != ''
                ORDER BY u.id
                LIMIT {$limit} OFFSET {$offset}
            ";

            $users = DB::connection('mysql_old')->select($query);

            if (!empty($users)) {
                $this->info("✅ 成功获取数据，记录数: " . count($users));
                return $users;
            } else {
                $this->warn("没有找到用户数据");
                return [];
            }

        } catch (\Exception $e) {
            $this->error("获取用户数据失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 预览迁移数据
     */
    private function previewMigration($oldUsers)
    {
        $this->info('=== 迁移预览 ===');
        
        foreach (array_slice($oldUsers, 0, 5) as $index => $oldUser) {
            $this->info("用户 " . ($index + 1) . ":");
            foreach ((array)$oldUser as $key => $value) {
                $this->line("  {$key}: {$value}");
            }
            $this->line('---');
        }
        
        if (count($oldUsers) > 5) {
            $this->info('... 还有 ' . (count($oldUsers) - 5) . ' 个用户');
        }
    }
    
    /**
     * 执行迁移
     */
    private function performMigration($oldUsers)
    {
        $this->info('开始执行迁移...');
        
        $successCount = 0;
        $errorCount = 0;
        
        DB::beginTransaction();
        
        try {
            foreach ($oldUsers as $oldUser) {
                try {
                    $this->migrateUser($oldUser);
                    $successCount++;
                } catch (\Exception $e) {
                    $this->error("迁移用户失败: " . $e->getMessage());
                    $errorCount++;
                }
            }
            
            if ($errorCount === 0) {
                DB::commit();
                $this->info("✅ 迁移完成！成功: {$successCount}, 失败: {$errorCount}");
            } else {
                $this->warn("迁移完成但有错误。成功: {$successCount}, 失败: {$errorCount}");
                if ($this->confirm('是否提交更改？')) {
                    DB::commit();
                    $this->info('✅ 更改已提交');
                } else {
                    DB::rollback();
                    $this->warn('❌ 更改已回滚');
                }
            }
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error('迁移过程中发生严重错误，已回滚: ' . $e->getMessage());
        }
    }
    
    /**
     * 迁移单个用户
     */
    private function migrateUser($oldUser)
    {
        // 使用固定密码，而不是随机生成
        $password = '123456';
        
        // 处理日期格式
        $createdAt = null;
        if (!empty($oldUser->created_at) && $oldUser->created_at !== '0000-00-00 00:00:00') {
            $createdAt = Carbon::parse($oldUser->created_at);
        } elseif (!empty($oldUser->addtime) && $oldUser->addtime > 0) {
            $createdAt = Carbon::createFromTimestamp($oldUser->addtime);
        } else {
            $createdAt = now();
        }

        // 获取推介人ID与CRM专员ID的映射关系
        $crmAgentId = $this->mapParentIdToEmployeeId($oldUser->parent_id ?? 0);
        
        // 从老系统获取用户关联的配送员信息
        $defaultEmployeeDelivererId = $this->getUserDelivererId($oldUser->user_main_id);
        
        // 如果没有找到关联的配送员，则根据区域分配
        if (!$defaultEmployeeDelivererId) {
            $defaultEmployeeDelivererId = $this->getDelivererIdByDistrict($oldUser->district ?? '');
        }
        
        // 基于老系统用户表结构的字段映射
        $userData = [
            'name' => $this->getValidName($oldUser),
            'password' => bcrypt($password),
            'phone' => !empty($oldUser->user_mobile) ? $oldUser->user_mobile : ($oldUser->contact_way ?? null),
            'avatar' => $oldUser->avatar ?? null,
            'nickname' => $oldUser->user_nickname ?? null,
            'gender' => $oldUser->gender ?? 0,
            'openid' => $oldUser->username ?? null,  // 使用username作为openid
            // unionid字段不需要迁移，按照用户要求移除
            'province' => $oldUser->province ?? null, 
            'city' => $oldUser->city ?? null,
            'country' => $oldUser->country ?? null,
            'district' => $oldUser->district ?? null,
            'merchant_name' => $oldUser->merchant ?? null,
            'balance' => $oldUser->balance ?? 0.00,
            'member_points' => $oldUser->integral ?? 0,
            'total_spend' => $oldUser->total_balance ?? 0.00,
            'largest_order' => 0.00,
            'joined_at' => $createdAt,
            'crm_agent_id' => $crmAgentId, // 设置CRM专员ID
            'default_employee_deliverer_id' => $defaultEmployeeDelivererId, // 设置默认配送员ID
            'custom_data' => $this->buildCustomData($oldUser),
            'created_at' => $createdAt,
            'updated_at' => now(),
        ];

        // 检查用户是否已存在（根据openid或phone）
        $existingUser = null;
        if (!empty($userData['openid'])) {
            $existingUser = User::where('openid', $userData['openid'])->first();
        }
        
        // 如果没有找到，再尝试用手机号查找
        if (!$existingUser && !empty($userData['phone'])) {
            $existingUser = User::where('phone', $userData['phone'])->first();
        }

        if ($existingUser) {
            try {
                // 更新现有用户的部分字段，包括CRM专员和配送员字段
                $existingUser->update([
                    'balance' => $existingUser->balance + $userData['balance'],
                    'member_points' => $existingUser->member_points + $userData['member_points'],
                    'nickname' => !empty($userData['nickname']) ? $userData['nickname'] : $existingUser->nickname,
                    'avatar' => !empty($userData['avatar']) ? $userData['avatar'] : $existingUser->avatar,
                    'merchant_name' => !empty($userData['merchant_name']) ? $userData['merchant_name'] : $existingUser->merchant_name,
                    'crm_agent_id' => $crmAgentId, // 添加CRM专员ID
                    'default_employee_deliverer_id' => $defaultEmployeeDelivererId, // 添加配送员ID
                    'custom_data' => array_merge(
                        is_array($existingUser->custom_data) ? $existingUser->custom_data : [], 
                        json_decode($userData['custom_data'], true)
                    )
                ]);
                
                $this->warn("用户已存在，已更新信息: " . $existingUser->name . " (ID: {$existingUser->id})");

                // 显示更新后的CRM专员和配送员ID
                $this->line("  - CRM专员ID: " . ($crmAgentId ?? '无') . ", 配送员ID: " . ($defaultEmployeeDelivererId ?? '无'));
            } catch (\Exception $e) {
                $this->error("更新现有用户失败: " . $e->getMessage());
            }
            
            return $existingUser;
        }

        try {
            // 创建新用户
            $newUser = User::create($userData);
            $this->info("✅ 成功迁移用户: " . $newUser->name . " (ID: {$newUser->id}, phone: {$newUser->phone})");

            // 显示CRM专员和配送员ID
            $this->line("  - CRM专员ID: " . ($crmAgentId ?? '无') . ", 配送员ID: " . ($defaultEmployeeDelivererId ?? '无'));

            // 如果有用户地址信息，也进行迁移
            $this->migrateUserAddresses($oldUser, $newUser);
            
            return $newUser;
        } catch (\Exception $e) {
            // 处理唯一性冲突
            if (str_contains($e->getMessage(), 'Duplicate entry') && str_contains($e->getMessage(), 'phone')) {
                $this->error("用户手机号存在冲突: {$userData['phone']}");
                
                // 尝试生成唯一手机号（仅用于满足唯一性约束）
                $uniquePhone = $userData['phone'] . '_' . substr(uniqid(), -4);
                $userData['phone'] = $uniquePhone;
                $userData['custom_data'] = json_encode(array_merge(
                    json_decode($userData['custom_data'], true),
                    ['original_phone' => $oldUser->mobile ?? $oldUser->contact_way]
                ));
                
                // 重新尝试创建
                try {
                    $newUser = User::create($userData);
                    $this->warn("✅ 用户创建成功（手机号已修改）: " . $newUser->name);
                    
                    // 显示CRM专员和配送员ID
                    $this->line("  - CRM专员ID: " . ($crmAgentId ?? '无') . ", 配送员ID: " . ($defaultEmployeeDelivererId ?? '无'));
                    
                    return $newUser;
                } catch (\Exception $e2) {
                    $this->error("再次尝试创建用户失败: " . $e2->getMessage());
                    throw $e2;
                }
            }
            
            throw $e;
        }
    }

    /**
     * 迁移用户地址
     */
    private function migrateUserAddresses($oldUser, $newUser)
    {
        try {
            // 查询老系统中的用户地址
            $addresses = DB::connection('mysql_old')->select("
                SELECT * FROM zjhj_bd_address
                WHERE user_id = ? AND is_delete = 0
            ", [$oldUser->user_main_id]);
            
            if (empty($addresses)) {
                return;
            }
            
            foreach ($addresses as $address) {
                // 构建备注信息
                $notes = [];
                if (!empty($address->location)) {
                    $notes[] = "位置信息: {$address->location}";
                }
                if (isset($address->type) && $address->type !== null) {
                    $type = $address->type == 0 ? '快递' : '同城';
                    $notes[] = "地址类型: {$type}";
                }
                
                // 处理经纬度值，确保空值被设置为null而不是空字符串
                $latitude = !empty($address->latitude) ? $address->latitude : null;
                $longitude = !empty($address->longitude) ? $address->longitude : null;
                
                // 映射地址字段
                $addressData = [
                    'user_id' => $newUser->id,
                    'contact_name' => $address->name ?? $newUser->name,
                    'contact_phone' => $address->mobile ?? $newUser->phone,
                    'province' => $address->province ?? null,
                    'city' => $address->city ?? null,
                    'district' => $address->district ?? null,
                    'address' => $address->detail ?? '',
                    'postal_code' => null,
                    'notes' => !empty($notes) ? implode(', ', $notes) : null,
                    'is_default' => $address->is_default ?? 0,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'created_at' => !empty($address->created_at) && $address->created_at != '0000-00-00 00:00:00' ? 
                        Carbon::parse($address->created_at) : now(),
                    'updated_at' => now(),
                ];
                
                // 检查地址模型是否存在，如果不存在则跳过
                if (!class_exists(UserAddress::class)) {
                    $this->warn("用户地址模型不存在，跳过地址迁移");
                    return;
                }
                
                UserAddress::create($addressData);
            }
            
            $this->info("  - 成功迁移 " . count($addresses) . " 个地址");
            
        } catch (\Exception $e) {
            $this->warn("  - 迁移地址失败: " . $e->getMessage());
        }
    }

    /**
     * 获取有效的用户名
     */
    private function getValidName($oldUser)
    {
        // 按优先级使用各种可能的名称字段
        $possibleNames = [
            $oldUser->user_nickname,
            $oldUser->nickname,
            $oldUser->remark_name,
            $oldUser->merchant,
            $oldUser->contact_way,
            $oldUser->user_mobile,
            $oldUser->mobile,
            $oldUser->username,
        ];
        
        // 过滤空值并取第一个有效值
        foreach ($possibleNames as $name) {
            if (!empty($name)) {
                return $name;
            }
        }
        
        // 如果都没有，使用默认名称
        return '用户' . $oldUser->user_main_id;
    }

    /**
     * 构建自定义数据
     */
    private function buildCustomData($oldUser)
    {
        $customData = [
            'migration_source' => 'old_system',
            'old_user_id' => $oldUser->user_main_id,
            'old_info_id' => $oldUser->user_info_id,
            'old_username' => $oldUser->username ?? null,  // 保存原始username (openid)
            // 不需要保存platform_user_id，按照用户要求移除
            'total_integral' => $oldUser->total_integral ?? 0,
            'total_balance' => $oldUser->total_balance ?? 0,
            'invitationcode' => $oldUser->invitationcode ?? null,
            'location' => $oldUser->location ?? null,
            'parent_id' => $oldUser->parent_id ?? 0,  // 保存原始parent_id以供参考
            'platform' => $oldUser->platform ?? 'wxapp',
            'remark' => $oldUser->remark ?? null,
            'total_order_num' => $oldUser->total_order_num ?? 0,
            'no_order_day' => $oldUser->no_order_day ?? 0,
            'last_order_at' => $oldUser->last_order_at ?? null,
            'last_check_at' => $oldUser->last_check_at ?? null,
            'migrated_at' => now()->toISOString(),
        ];
        
        // 尝试获取老系统中用户的更多自定义属性
        try {
            // 检查是否存在扩展属性表
            $extAttributes = DB::connection('mysql_old')->select("
                SHOW TABLES LIKE 'zjhj_bd_user_ext_attribute'
            ");
            
            if (!empty($extAttributes)) {
                // 获取用户的扩展属性
                $extData = DB::connection('mysql_old')->select("
                    SELECT attribute_key, attribute_value 
                    FROM zjhj_bd_user_ext_attribute
                    WHERE user_id = ?
                ", [$oldUser->user_main_id]);
                
                if (!empty($extData)) {
                    foreach ($extData as $attr) {
                        $customData['ext_' . $attr->attribute_key] = $attr->attribute_value;
                    }
                }
            }
        } catch (\Exception $e) {
            // 忽略扩展属性错误
        }
        
        return json_encode($customData);
    }

    /**
     * 将老系统推介人ID映射到新系统员工ID
     */
    private function mapParentIdToEmployeeId($parentId)
    {
        // 老系统推介人ID与新系统员工ID的映射关系
        $mapping = [
            10436 => 18,
            5825 => 17,
            3765 => 16,
            37 => 15,
        ];
        
        // 如果存在映射关系，则返回对应的员工ID，否则返回null
        return $mapping[$parentId] ?? null;
    }

    /**
     * 从老系统获取用户关联的配送员信息
     */
    private function getUserDelivererId($oldUserId)
    {
        try {
            // 1. 查找用户关联的线路ID
            $relation = DB::connection('mysql_old')->table('zjhj_bd_deliver_relation')
                ->where('user_id', $oldUserId)
                ->first();
                
            if (!$relation) {
                return null;
            }
            
            $wayId = $relation->way_id;
            
            // 2. 通过线路ID查找对应的配送员ID
            $way = DB::connection('mysql_old')->table('zjhj_bd_deliver_way')
                ->where('id', $wayId)
                ->where('is_delete', 0)
                ->first();
                
            if (!$way) {
                return null;
            }
            
            $deliverUserId = $way->deliver_user_id;
            
            // 3. 获取配送员详细信息
            $deliverUser = DB::connection('mysql_old')->table('zjhj_bd_deliver_user')
                ->where('id', $deliverUserId)
                ->where('is_delete', 0)
                ->first();
                
            if (!$deliverUser) {
                return null;
            }
            
            // 获取配送员名称
            $delivererName = $deliverUser->name ?? '';
            if (empty($delivererName)) {
                return null;
            }
            
            // 4. 根据配送员名称查找新系统中的员工ID
            $delivererId = $this->getEmployeeIdByName($delivererName);
            
            // 记录映射信息用于调试
            if ($delivererId) {
                $this->info("用户 {$oldUserId} 映射到配送员ID {$delivererId}，通过线路ID {$wayId} 和配送员 {$delivererName}");
            } else {
                $this->warn("无法找到配送员 {$delivererName} 对应的新系统员工ID");
            }
            
            return $delivererId;
        } catch (\Exception $e) {
            $this->warn("查询用户 {$oldUserId} 的配送员关系时出错: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 根据区域分配默认配送员
     * 根据用户所在区域分配配送员，如果没有匹配的区域，则随机分配一个配送员
     */
    private function getDelivererIdByDistrict($district)
    {
        // 区域与配送员ID的映射关系
        $districtMapping = [
            '锦江区' => $this->getEmployeeIdByPhone('13730670672'), // 周太军
            '青羊区' => $this->getEmployeeIdByPhone('18202857659'), // 张正林
            '金牛区' => $this->getEmployeeIdByPhone('13648064232'), // 方先红
            '武侯区' => $this->getEmployeeIdByPhone('13880045187'), // 张金
            '成华区' => $this->getEmployeeIdByPhone('13880522953'), // 李焦
            '龙泉驿区' => $this->getEmployeeIdByPhone('13882147779'), // 熊启江
            '新都区' => $this->getEmployeeIdByPhone('18188458175'), // 熊江洪
            '温江区' => $this->getEmployeeIdByPhone('18583958997'), // 王水晶
            '双流区' => $this->getEmployeeIdByPhone('13628012269'), // 王银全
            '郫都区' => $this->getEmployeeIdByPhone('18980542028'), // 石亚涛
            '新津区' => $this->getEmployeeIdByPhone('13558650480'), // 黄建
            '邛崃市' => $this->getEmployeeIdByPhone('18080041788'), // 龙水红
        ];
        
        // 如果存在映射关系，则返回对应的配送员ID
        if (!empty($district) && isset($districtMapping[$district])) {
            return $districtMapping[$district];
        }
        
        // 如果没有匹配的区域，则随机分配一个配送员
        $allDeliverers = array_values($districtMapping);
        $validDeliverers = array_filter($allDeliverers); // 过滤掉null值
        
        if (count($validDeliverers) > 0) {
            return $validDeliverers[array_rand($validDeliverers)];
        }
        
        return null;
    }
    
    /**
     * 根据配送员名称获取员工ID
     */
    private function getEmployeeIdByName($name)
    {
        static $nameToIdMap = null;
        
        // 初始化缓存
        if ($nameToIdMap === null) {
            $nameToIdMap = [];
            
            // 获取所有配送员
            $deliverers = DB::table('employees')
                ->where('role', 'delivery')
                ->get(['id', 'name', 'username']);
                
            foreach ($deliverers as $deliverer) {
                // 使用员工的名字和用户名作为映射键
                $nameToIdMap[$deliverer->name] = $deliverer->id;
                $nameToIdMap[$deliverer->username] = $deliverer->id;
            }
        }
        
        return $nameToIdMap[$name] ?? null;
    }

    /**
     * 根据手机号获取员工ID
     */
    private function getEmployeeIdByPhone($phone)
    {
        static $phoneToIdMap = null;
        
        // 初始化缓存
        if ($phoneToIdMap === null) {
            $phoneToIdMap = [];
            
            // 获取所有配送员
            $deliverers = DB::table('employees')
                ->where('role', 'delivery')
                ->get(['id', 'phone']);
                
            foreach ($deliverers as $deliverer) {
                $phoneToIdMap[$deliverer->phone] = $deliverer->id;
            }
        }
        
        return $phoneToIdMap[$phone] ?? null;
    }

    /**
     * 验证迁移结果
     */
    private function validateMigrationResults()
    {
        $this->info('开始验证迁移结果...');
        
        try {
            // 验证老系统有效用户数量（有商户名称的非删除用户）
            $oldSystemUserCount = DB::connection('mysql_old')->table('zjhj_bd_user_info')
                ->where('is_delete', 0)
                ->whereNotNull('merchant')
                ->where('merchant', '!=', '')
                ->count();
            
            // 验证新系统具有老系统ID标记的用户数量
            $newSystemUserCount = User::whereNotNull('custom_data')
                ->where(function($query) {
                    $query->whereRaw('JSON_EXTRACT(custom_data, "$.migration_source") = "old_system"')
                        ->orWhereRaw('JSON_EXTRACT(custom_data, "$.old_user_id") IS NOT NULL');
                })
                ->count();
            
            $this->info("老系统有效用户数量: {$oldSystemUserCount}");
            $this->info("新系统中与老系统关联的用户数量: {$newSystemUserCount}");
            
            $diffCount = $oldSystemUserCount - $newSystemUserCount;
            
            if ($diffCount > 0) {
                $this->warn("未完全迁移，有 {$diffCount} 个用户未成功迁移");
                
                if ($this->confirm('是否生成未迁移用户报告？')) {
                    $this->generateUnmigratedUsersReport();
                }
            } elseif ($diffCount < 0) {
                $this->warn("迁移用户数量异常，新系统的迁移用户数量超过老系统");
            } else {
                $this->info("✅ 验证成功！所有有效用户都已成功迁移");
            }
            
            // 验证关键字段
            $this->validateKeyFields();
            
        } catch (\Exception $e) {
            $this->error('验证失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 验证关键字段完整性
     */
    private function validateKeyFields()
    {
        $this->info('验证关键字段完整性...');
        
        // 检查空值情况
        $emptyFields = [];
        
        // 检查缺少商户名称的用户
        $missingMerchantCount = User::whereNull('merchant_name')
            ->whereRaw('JSON_EXTRACT(custom_data, "$.migration_source") = "old_system"')
            ->count();
        
        if ($missingMerchantCount > 0) {
            $emptyFields[] = "商户名称 (merchant_name): {$missingMerchantCount}个";
        }
        
        // 检查缺少openid的用户
        $missingOpenidCount = User::whereNull('openid')
            ->whereRaw('JSON_EXTRACT(custom_data, "$.migration_source") = "old_system"')
            ->count();
        
        if ($missingOpenidCount > 0) {
            $emptyFields[] = "微信ID (openid): {$missingOpenidCount}个";
        }
        
        // 检查缺少手机号的用户
        $missingPhoneCount = User::whereNull('phone')
            ->whereRaw('JSON_EXTRACT(custom_data, "$.migration_source") = "old_system"')
            ->count();
        
        if ($missingPhoneCount > 0) {
            $emptyFields[] = "手机号 (phone): {$missingPhoneCount}个";
        }
        
        if (empty($emptyFields)) {
            $this->info("✅ 所有关键字段验证通过");
        } else {
            $this->warn("⚠️ 以下字段存在空值情况:");
            foreach ($emptyFields as $field) {
                $this->line("  - {$field}");
            }
        }
    }
    
    /**
     * 生成未迁移用户报告
     */
    private function generateUnmigratedUsersReport()
    {
        $this->info('生成未迁移用户报告...');
        
        // 获取所有已迁移用户的老系统ID
        $migratedUserIds = User::whereRaw('JSON_EXTRACT(custom_data, "$.old_user_id") IS NOT NULL')
            ->get()
            ->pluck('custom_data')
            ->map(function($data) {
                $decoded = is_array($data) ? $data : json_decode($data, true);
                return $decoded['old_user_id'] ?? null;
            })
            ->filter()
            ->toArray();
        
        // 获取老系统中未迁移的用户
        $unmigratedUsers = DB::connection('mysql_old')->table('zjhj_bd_user')
            ->select('id', 'nickname', 'mobile', 'created_at')
            ->join('zjhj_bd_user_info', 'zjhj_bd_user.id', '=', 'zjhj_bd_user_info.user_id')
            ->where('zjhj_bd_user_info.is_delete', 0)
            ->whereNotNull('zjhj_bd_user_info.merchant')
            ->where('zjhj_bd_user_info.merchant', '!=', '')
            ->whereNotIn('zjhj_bd_user.id', $migratedUserIds)
            ->orderBy('zjhj_bd_user.id')
            ->limit(100)  // 最多显示100个
            ->get();
        
        if ($unmigratedUsers->isEmpty()) {
            $this->info("未找到未迁移的用户");
            return;
        }
        
        // 创建未迁移用户报告文件
        $reportFile = storage_path('logs/unmigrated_users_'.date('Y-m-d_H-i-s').'.csv');
        $file = fopen($reportFile, 'w');
        
        // 写入CSV头
        fputcsv($file, ['用户ID', '昵称', '手机号', '创建时间']);
        
        foreach ($unmigratedUsers as $user) {
            fputcsv($file, [
                $user->id,
                $user->nickname,
                $user->mobile,
                $user->created_at
            ]);
        }
        
        fclose($file);
        
        $this->info("未迁移用户报告已生成: {$reportFile}");
    }
}
