<?php

use App\Cart\Http\Controllers\CartController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 购物车模块 API 路由
|--------------------------------------------------------------------------
|
| 这里定义购物车模块的所有API路由
|
*/


Route::prefix('api')->middleware('auth:sanctum')->group(function () {
    // 获取购物车
    Route::get('/cart', [CartController::class, 'index']);
    
    // 获取购物车商品数量
    Route::get('/cart/count', [CartController::class, 'count']);
    
    // 添加商品到购物车
    Route::post('/cart', [CartController::class, 'store']);
    
    // 更新购物车商品数量
    Route::put('/cart/items/{id}', [CartController::class, 'updateItem']);
    
    // 删除购物车商品
    Route::delete('/cart/items/{id}', [CartController::class, 'removeItem']);
    
    // 切换商品选中状态
    Route::put('/cart/items/{id}/toggle-select', [CartController::class, 'toggleSelectItem']);
    
    // 全选/取消全选
    Route::put('/cart/select-all', [CartController::class, 'toggleSelectAll']);
    
    // 清空购物车
    Route::delete('/cart', [CartController::class, 'clear']);
    
    // 合并本地购物车
    Route::post('/cart/merge', [CartController::class, 'merge']);
}); 