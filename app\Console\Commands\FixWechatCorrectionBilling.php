<?php

namespace App\Console\Commands;

use App\Billing\Models\Bill;
use App\Billing\Models\PaymentRecord;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\WechatPayment\Models\WechatServicePayment;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixWechatCorrectionBilling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:fix-wechat-correction 
                            {--dry-run : 只显示需要修复的数据，不执行实际修复}
                            {--limit=10 : 限制处理的账单数量}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复微信支付订单更正后的账单逻辑';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 开始修复微信支付订单更正后的账单逻辑...');
        $this->newLine();
        
        $isDryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');
        
        if ($isDryRun) {
            $this->warn('⚠️  DRY RUN 模式：只显示需要修复的数据，不执行实际修复');
            $this->newLine();
        }
        
        // 1. 查找所有微信支付订单的账单
        $wechatBills = $this->findWechatOrderBills($limit);
        $this->info("📋 找到 {$wechatBills->count()} 个微信支付订单的账单");
        $this->newLine();
        
        if ($wechatBills->isEmpty()) {
            $this->info('✅ 没有需要修复的账单');
            return 0;
        }
        
        $fixedCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        
        $progressBar = $this->output->createProgressBar($wechatBills->count());
        $progressBar->start();
        
        foreach ($wechatBills as $bill) {
            try {
                $result = $this->processBill($bill, $isDryRun);
                
                if ($result === 'fixed') {
                    $fixedCount++;
                } elseif ($result === 'skipped') {
                    $skippedCount++;
                }
                
            } catch (\Exception $e) {
                $errorCount++;
                $this->newLine();
                $this->error("❌ 处理账单 {$bill->bill_no} 失败: " . $e->getMessage());
                Log::error('修复微信支付账单失败', [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'error' => $e->getMessage()
                ]);
            }
            
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->newLine(2);
        
        // 显示结果统计
        $this->info('🎉 处理完成！');
        $this->table(['状态', '数量'], [
            ['需要修复', $fixedCount],
            ['无需修复', $skippedCount],
            ['处理失败', $errorCount],
            ['总计', $wechatBills->count()]
        ]);
        
        if (!$isDryRun && $fixedCount > 0) {
            $this->info("✅ 成功修复 {$fixedCount} 个账单");
        } elseif ($isDryRun && $fixedCount > 0) {
            $this->warn("⚠️  发现 {$fixedCount} 个账单需要修复，使用 --no-dry-run 执行实际修复");
        }
        
        return 0;
    }
    
    /**
     * 查找所有微信支付订单的账单
     */
    private function findWechatOrderBills(int $limit)
    {
        return Bill::with(['order', 'paymentRecords'])
            ->whereHas('order', function($query) {
                $query->where('payment_method', 'wechat');
            })
            ->where('status', '!=', 'cancelled')
            ->limit($limit)
            ->get();
    }
    
    /**
     * 处理单个账单
     */
    private function processBill(Bill $bill, bool $isDryRun): string
    {
        $order = $bill->order;
        
        // 检查是否有订单更正记录
        $corrections = OrderCorrection::where('order_id', $order->id)
            ->where('status', 'confirmed')
            ->get();
            
        if ($corrections->isEmpty()) {
            return 'skipped'; // 没有更正记录，无需修复
        }
        
        // 获取最新的更正记录
        $latestCorrection = $corrections->sortByDesc('created_at')->first();
        
        // 获取微信支付记录
        $wechatPayment = WechatServicePayment::where('order_id', $order->id)
            ->where('trade_state', 'SUCCESS')
            ->first();
            
        if (!$wechatPayment) {
            throw new \Exception("未找到订单 {$order->order_no} 的微信支付记录");
        }
        
        // 计算正确的金额
        $originalPaidAmount = $wechatPayment->total_fee; // 原始支付金额
        $correctedAmount = $latestCorrection->corrected_total; // 更正后应付金额
        $differenceAmount = $latestCorrection->difference_amount; // 差额
        
        // 检查是否需要修复
        $needsFix = $this->checkIfNeedsFix($bill, $originalPaidAmount, $correctedAmount, $differenceAmount);
        
        if (!$needsFix) {
            return 'skipped';
        }
        
        // 显示修复信息
        $this->displayFixInfo($bill, $order, $latestCorrection, $originalPaidAmount, $correctedAmount, $differenceAmount);
        
        if (!$isDryRun) {
            // 执行实际修复
            $this->executeFixBill($bill, $latestCorrection, $originalPaidAmount, $correctedAmount, $differenceAmount);
        }
        
        return 'fixed';
    }
    
    /**
     * 检查账单是否需要修复
     */
    private function checkIfNeedsFix(Bill $bill, float $originalPaidAmount, float $correctedAmount, float $differenceAmount): bool
    {
        // 检查是否已有正确的支付记录
        $hasWechatPayment = PaymentRecord::where('bill_id', $bill->id)
            ->where('payment_type', 'payment')
            ->where('payment_method', 'wechat')
            ->exists();
            
        // 检查账单金额是否正确
        $correctBillAmount = abs($bill->final_amount - $correctedAmount) < 0.01;
        
        // 检查支付状态是否正确
        $correctPaymentStatus = false;
        if ($differenceAmount < 0) {
            // 退款场景：应该是已付款状态
            $correctPaymentStatus = ($bill->payment_status === 'paid' && $bill->pending_amount == 0);
        } elseif ($differenceAmount > 0) {
            // 补款场景：应该是部分付款状态
            $correctPaymentStatus = ($bill->payment_status === 'partial' && abs($bill->pending_amount - $differenceAmount) < 0.01);
        } else {
            // 无差异场景：应该是已付款状态
            $correctPaymentStatus = ($bill->payment_status === 'paid' && $bill->pending_amount == 0);
        }
        
        return !$hasWechatPayment || !$correctBillAmount || !$correctPaymentStatus;
    }
    
    /**
     * 显示修复信息
     */
    private function displayFixInfo(Bill $bill, Order $order, OrderCorrection $correction, float $originalPaid, float $corrected, float $difference)
    {
        $this->newLine();
        $this->info("🔧 账单: {$bill->bill_no} (订单: {$order->order_no})");
        $this->line("   原始支付: ¥{$originalPaid}");
        $this->line("   更正后应付: ¥{$corrected}");
        $this->line("   差额: ¥{$difference}");
        
        if ($difference < 0) {
            $refundAmount = abs($difference);
            $this->line("   场景: 退款 ¥{$refundAmount}");
            $this->line("   修复后: 账单¥{$corrected}, 已付¥{$corrected}, 待付¥0, 状态:已付款");
        } elseif ($difference > 0) {
            $this->line("   场景: 补款 ¥{$difference}");
            $this->line("   修复后: 账单¥{$corrected}, 已付¥{$originalPaid}, 待付¥{$difference}, 状态:部分付款");
        } else {
            $this->line("   场景: 无差异");
            $this->line("   修复后: 账单¥{$corrected}, 已付¥{$corrected}, 待付¥0, 状态:已付款");
        }
    }
    
    /**
     * 执行账单修复
     */
    private function executeFixBill(Bill $bill, OrderCorrection $correction, float $originalPaidAmount, float $correctedAmount, float $differenceAmount)
    {
        DB::transaction(function() use ($bill, $correction, $originalPaidAmount, $correctedAmount, $differenceAmount) {
            
            // 1. 创建原始支付记录（如果不存在）
            $existingPayment = PaymentRecord::where('bill_id', $bill->id)
                ->where('payment_type', 'payment')
                ->where('payment_method', 'wechat')
                ->first();
                
            if (!$existingPayment) {
                PaymentRecord::create([
                    'bill_id' => $bill->id,
                    'payment_no' => 'PAY' . date('YmdHis') . mt_rand(1000, 9999),
                    'payment_method' => 'wechat',
                    'payment_amount' => $originalPaidAmount,
                    'payment_type' => 'payment',
                    'status' => 'success',
                    'payment_time' => $bill->created_at ?? now(),
                    'confirmed_at' => $bill->created_at ?? now(),
                    'notes' => '微信支付原始付款记录（系统修复创建）',
                    'metadata' => [
                        'correction_id' => $correction->id,
                        'system_fix' => true,
                        'original_wechat_payment' => true
                    ]
                ]);
            }
            
            if ($differenceAmount < 0) {
                // 退款场景
                $refundAmount = abs($differenceAmount);
                
                // 创建退款记录（如果不存在）
                $existingRefund = PaymentRecord::where('bill_id', $bill->id)
                    ->where('payment_type', 'refund')
                    ->where('payment_method', 'wechat')
                    ->first();
                    
                if (!$existingRefund) {
                    PaymentRecord::create([
                        'bill_id' => $bill->id,
                        'payment_no' => 'REF' . date('YmdHis') . mt_rand(1000, 9999),
                        'payment_method' => 'wechat',
                        'payment_amount' => $refundAmount,
                        'payment_type' => 'refund',
                        'status' => 'success',
                        'payment_time' => $correction->updated_at,
                        'confirmed_at' => $correction->updated_at,
                        'notes' => '订单更正退款记录（系统修复创建）',
                        'metadata' => [
                            'correction_id' => $correction->id,
                            'system_fix' => true,
                            'refund_reason' => '订单更正金额减少'
                        ]
                    ]);
                }
                
                // 更新账单状态
                $bill->update([
                    'original_amount' => $correctedAmount,
                    'final_amount' => $correctedAmount,
                    'paid_amount' => $correctedAmount,
                    'pending_amount' => 0,
                    'status' => Bill::STATUS_PAID,
                    'payment_status' => Bill::PAYMENT_STATUS_PAID,
                    'paid_at' => now(),
                    'notes' => ($bill->notes ?? '') . "\n[系统修复] 微信支付订单更正退款场景修复"
                ]);
                
            } elseif ($differenceAmount > 0) {
                // 补款场景
                $supplementAmount = $differenceAmount;
                
                // 创建补款记录（如果不存在）
                $existingSupplement = PaymentRecord::where('bill_id', $bill->id)
                    ->where('payment_type', 'supplement')
                    ->first();
                    
                if (!$existingSupplement) {
                    PaymentRecord::create([
                        'bill_id' => $bill->id,
                        'payment_no' => 'SUP' . date('YmdHis') . mt_rand(1000, 9999),
                        'payment_method' => 'pending',
                        'payment_amount' => $supplementAmount,
                        'payment_type' => 'supplement',
                        'status' => 'pending',
                        'payment_time' => now(),
                        'notes' => '订单更正补款记录（系统修复创建）',
                        'metadata' => [
                            'correction_id' => $correction->id,
                            'system_fix' => true,
                            'supplement_reason' => '订单更正金额增加'
                        ]
                    ]);
                }
                
                // 更新账单状态
                $bill->update([
                    'original_amount' => $correctedAmount,
                    'final_amount' => $correctedAmount,
                    'paid_amount' => $originalPaidAmount,
                    'pending_amount' => $supplementAmount,
                    'status' => Bill::STATUS_PARTIAL_PAID,
                    'payment_status' => Bill::PAYMENT_STATUS_PARTIAL,
                    'notes' => ($bill->notes ?? '') . "\n[系统修复] 微信支付订单更正补款场景修复"
                ]);
                
            } else {
                // 无差异场景
                $bill->update([
                    'original_amount' => $correctedAmount,
                    'final_amount' => $correctedAmount,
                    'paid_amount' => $correctedAmount,
                    'pending_amount' => 0,
                    'status' => Bill::STATUS_PAID,
                    'payment_status' => Bill::PAYMENT_STATUS_PAID,
                    'paid_at' => now(),
                    'notes' => ($bill->notes ?? '') . "\n[系统修复] 微信支付订单更正无差异场景修复"
                ]);
            }
            
            Log::info('微信支付账单修复完成', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'correction_id' => $correction->id,
                'difference_amount' => $differenceAmount,
                'final_amount' => $correctedAmount,
                'paid_amount' => $bill->paid_amount,
                'pending_amount' => $bill->pending_amount,
                'payment_status' => $bill->payment_status
            ]);
        });
    }
}