<?php

namespace App\Common\Services;

use App\Common\Enums\OrderStatus;
use App\Common\Enums\BillStatus;
use App\Common\Enums\OrderCorrectionStatus;
use App\Order\Models\Order;
use App\Billing\Models\Bill;
use App\Order\Models\OrderCorrection;
use Illuminate\Support\Facades\Log;

/**
 * 状态统一管理服务
 * 负责协调订单、账单、更正之间的状态同步
 */
class StatusUnificationService
{
    /**
     * 同步订单和账单状态
     */
    public function syncOrderBillStatus(Order $order, Bill $bill): void
    {
        Log::info('开始同步订单和账单状态', [
            'order_id' => $order->id,
            'order_status' => $order->status,
            'order_payment_method' => $order->payment_method,
            'bill_id' => $bill->id,
            'bill_status' => $bill->status,
            'bill_payment_status' => $bill->payment_status,
        ]);

        // 🔥 获取建议的订单状态
        $suggestedStatus = OrderStatus::getSuggestedOrderStatus(
            $order->payment_method,
            $bill->payment_status,
            $order->status
        );

        // 🔥 如果建议状态与当前状态不同，且转换合法，则更新
        if ($suggestedStatus !== $order->status && 
            OrderStatus::canTransitionTo($order->status, $suggestedStatus)) {
            
            $this->updateOrderStatus($order, $suggestedStatus, $bill);
        }

        Log::info('订单和账单状态同步完成', [
            'order_id' => $order->id,
            'final_order_status' => $order->status,
            'suggested_status' => $suggestedStatus,
            'status_changed' => $suggestedStatus !== $order->getOriginal('status'),
        ]);
    }

    /**
     * 更新订单状态
     */
    protected function updateOrderStatus(Order $order, string $newStatus, ?Bill $bill = null): void
    {
        $oldStatus = $order->status;
        $updateData = ['status' => $newStatus];

        // 🔥 根据新状态设置相关时间戳
        switch ($newStatus) {
            case OrderStatus::CONFIRMED:
                if ($order->payment_method !== 'cod') {
                    $updateData['paid_at'] = now();
                }
                break;
            case OrderStatus::SHIPPED:
                $updateData['shipped_at'] = now();
                break;
            case OrderStatus::DELIVERED:
                $updateData['delivered_at'] = now();
                break;
            case OrderStatus::COMPLETED:
                $updateData['completed_at'] = now();
                break;
            case OrderStatus::CANCELLED:
                $updateData['cancelled_at'] = now();
                break;
        }

        $order->update($updateData);

        Log::info('订单状态已更新', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'bill_id' => $bill?->id,
            'trigger' => $bill ? 'bill_sync' : 'manual',
        ]);
    }

    /**
     * 处理订单更正后的状态同步
     */
    public function handleOrderCorrectionStatusSync(OrderCorrection $correction): void
    {
        $order = $correction->order;
        
        Log::info('处理订单更正状态同步', [
            'correction_id' => $correction->id,
            'correction_status' => $correction->status,
            'order_id' => $order->id,
            'order_status' => $order->status,
        ]);

        // 🔥 根据更正状态更新订单的更正状态
        $correctionStatus = $this->calculateOrderCorrectionStatus($order);
        
        if ($correctionStatus !== $order->correction_status) {
            $order->update(['correction_status' => $correctionStatus]);
            
            Log::info('订单更正状态已更新', [
                'order_id' => $order->id,
                'old_correction_status' => $order->getOriginal('correction_status'),
                'new_correction_status' => $correctionStatus,
            ]);
        }
    }

    /**
     * 计算订单的更正状态
     */
    protected function calculateOrderCorrectionStatus(Order $order): string
    {
        $corrections = $order->corrections;
        
        if ($corrections->isEmpty()) {
            return 'none';
        }

        $hasPending = $corrections->where('status', OrderCorrectionStatus::PENDING)->count() > 0;
        $hasConfirmed = $corrections->where('status', OrderCorrectionStatus::CONFIRMED)->count() > 0;

        if ($hasPending) {
            return 'pending';
        } elseif ($hasConfirmed) {
            return 'confirmed';
        } else {
            return 'cancelled';
        }
    }

    /**
     * 获取订单的统一显示状态（用户端）
     */
    public function getOrderUserDisplayStatus(Order $order): array
    {
        // 🔥 获取最新的账单信息
        $bill = $order->bill;
        $paymentStatus = $bill ? $bill->payment_status : OrderStatus::PAYMENT_UNPAID;

        return OrderStatus::getUserDisplayStatus(
            $order->status,
            $paymentStatus,
            $order->payment_method
        );
    }

    /**
     * 获取订单的统一显示状态（管理端）
     */
    public function getOrderAdminDisplayStatus(Order $order): array
    {
        $bill = $order->bill;
        $paymentStatus = $bill ? $bill->payment_status : OrderStatus::PAYMENT_UNPAID;

        $baseStatus = OrderStatus::getAdminDisplayStatus(
            $order->status,
            $paymentStatus,
            $order->payment_method
        );

        // 🔥 添加更正状态信息
        $correctionInfo = [];
        if ($order->correction_status && $order->correction_status !== 'none') {
            $correctionInfo = [
                'correction_status' => $order->correction_status_text,
                'pending_corrections_count' => $order->pending_corrections_count,
                'has_corrections' => true,
            ];
        } else {
            $correctionInfo = [
                'correction_status' => '未更正',
                'pending_corrections_count' => 0,
                'has_corrections' => false,
            ];
        }

        return array_merge($baseStatus, $correctionInfo);
    }

    /**
     * 获取账单的统一显示状态（用户端）
     */
    public function getBillUserDisplayStatus(Bill $bill): array
    {
        return BillStatus::getUserDisplayInfo(
            $bill->status,
            $bill->payment_status,
            $bill->final_amount,
            $bill->paid_amount
        );
    }

    /**
     * 获取账单的统一显示状态（管理端）
     */
    public function getBillAdminDisplayStatus(Bill $bill): array
    {
        return BillStatus::getAdminDisplayInfo(
            $bill->status,
            $bill->payment_status,
            $bill->final_amount,
            $bill->paid_amount
        );
    }

    /**
     * 获取订单更正的统一显示状态（用户端）
     */
    public function getCorrectionUserDisplayStatus(OrderCorrection $correction): array
    {
        return OrderCorrectionStatus::getUserDisplayInfo(
            $correction->status,
            $correction->correction_type,
            $correction->amount_difference
        );
    }

    /**
     * 获取订单更正的统一显示状态（管理端）
     */
    public function getCorrectionAdminDisplayStatus(OrderCorrection $correction): array
    {
        return OrderCorrectionStatus::getAdminDisplayInfo(
            $correction->status,
            $correction->correction_type,
            $correction->amount_difference,
            $correction->settlement_status,
            $correction->settlement_method
        );
    }

    /**
     * 批量同步订单状态
     */
    public function batchSyncOrderStatus(array $orderIds): array
    {
        $results = [];
        
        foreach ($orderIds as $orderId) {
            try {
                $order = Order::with(['bill'])->find($orderId);
                if (!$order) {
                    $results[$orderId] = ['success' => false, 'error' => '订单不存在'];
                    continue;
                }

                if ($order->bill) {
                    $this->syncOrderBillStatus($order, $order->bill);
                }

                $results[$orderId] = ['success' => true, 'status' => $order->status];
            } catch (\Exception $e) {
                $results[$orderId] = ['success' => false, 'error' => $e->getMessage()];
                Log::error('批量同步订单状态失败', [
                    'order_id' => $orderId,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }
}
