<?php

namespace App\Billing\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Billing\Models\Bill;

class CreateBillRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'bill_type' => 'required|string|in:order,adjustment,refund,supplement',
            'user_id' => 'required|integer|exists:users,id',
            'order_id' => 'nullable|integer|exists:orders,id',
            'original_amount' => 'required|numeric|min:0',
            'adjustment_amount' => 'nullable|numeric',
            'due_date' => 'nullable|date|after:today',
            'created_by' => 'nullable|integer|exists:employees,id',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
            
            // 账单明细
            'items' => 'nullable|array',
            'items.*.product_id' => 'nullable|integer|exists:products,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_type' => 'required|string|in:product,service,adjustment,fee,discount',
            'items.*.item_description' => 'nullable|string|max:500',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit' => 'nullable|string|max:50',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
        ];
    }

    public function messages(): array
    {
        return [
            'bill_type.required' => '账单类型不能为空',
            'bill_type.in' => '账单类型不正确',
            'user_id.required' => '用户ID不能为空',
            'user_id.exists' => '用户不存在',
            'order_id.exists' => '订单不存在',
            'original_amount.required' => '原始金额不能为空',
            'original_amount.numeric' => '原始金额必须为数字',
            'original_amount.min' => '原始金额不能为负数',
            'adjustment_amount.numeric' => '调整金额必须为数字',
            'due_date.date' => '到期日期格式不正确',
            'due_date.after' => '到期日期必须是未来日期',
            'created_by.exists' => '创建人不存在',
            'notes.max' => '备注不能超过1000个字符',
            
            // 账单明细验证消息
            'items.*.item_name.required' => '明细项目名称不能为空',
            'items.*.item_type.required' => '明细项目类型不能为空',
            'items.*.item_type.in' => '明细项目类型不正确',
            'items.*.quantity.required' => '数量不能为空',
            'items.*.quantity.min' => '数量必须大于0',
            'items.*.unit_price.required' => '单价不能为空',
            'items.*.unit_price.min' => '单价不能为负数',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // 如果账单类型是订单，必须提供订单ID
            if ($this->bill_type === Bill::TYPE_ORDER && !$this->order_id) {
                $validator->errors()->add('order_id', '订单账单必须提供订单ID');
            }
        });
    }
} 