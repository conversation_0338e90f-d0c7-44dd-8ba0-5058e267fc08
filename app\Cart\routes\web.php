<?php

use App\Cart\Http\Controllers\CartController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 购物车模块 Web 路由
|--------------------------------------------------------------------------
|
| 这里定义购物车模块的所有Web路由
|
*/

// 购物车管理Web路由 - 管理后台
Route::group(['prefix' => 'admin/cart', 'middleware' => ['web', 'auth']], function () {
    // 购物车列表
    Route::get('/', [CartController::class, 'adminIndex'])->name('admin.cart.index');
    
    // 查看特定用户的购物车
    Route::get('/user/{userId}', [CartController::class, 'adminUserCart'])->name('admin.cart.user');
    
    // 其他后台管理功能可以在这里添加
}); 