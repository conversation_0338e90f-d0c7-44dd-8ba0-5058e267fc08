<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Inventory\Services\CostPriceService;
use App\Product\Models\Product;
use Illuminate\Support\Facades\Log;

class RecalculateProductsCostPrice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:recalculate-cost-price {--product-id= : 指定商品ID，不指定则处理所有商品} {--force : 强制重新计算，包括没有库存的商品}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '重新计算商品的移动加权平均成本价并更新到商品表';

    /**
     * 成本价服务
     */
    private CostPriceService $costPriceService;

    /**
     * 创建命令实例
     */
    public function __construct(CostPriceService $costPriceService)
    {
        parent::__construct();
        $this->costPriceService = $costPriceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始重新计算商品成本价...');
        
        $productId = $this->option('product-id');
        $force = $this->option('force');
        
        try {
            if ($productId) {
                // 重新计算指定商品
                $this->recalculateSingleProduct($productId);
            } else {
                // 批量重新计算所有商品
                $this->recalculateAllProducts($force);
            }
            
            $this->info('成本价重新计算完成！');
            
        } catch (\Exception $e) {
            $this->error('重新计算失败: ' . $e->getMessage());
            Log::error('重新计算商品成本价失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 重新计算单个商品的成本价
     */
    private function recalculateSingleProduct($productId)
    {
        $product = Product::find($productId);
        
        if (!$product) {
            $this->error("商品 ID {$productId} 不存在");
            return;
        }
        
        $this->info("重新计算商品: {$product->name} (ID: {$productId})");
        
        $oldCostPrice = $product->cost_price;
        $newCostPrice = $this->costPriceService->recalculateProductCostPrice($productId);
        
        if ($newCostPrice !== null) {
            $this->line("  原成本价: ¥{$oldCostPrice} -> 新成本价: ¥{$newCostPrice}");
        } else {
            $this->line("  无法计算成本价（没有有效的入库记录）");
        }
    }
    
    /**
     * 批量重新计算所有商品的成本价
     */
    private function recalculateAllProducts($force)
    {
        $this->info('开始批量重新计算所有商品成本价...');
        
        $stats = $this->costPriceService->recalculateAllProductsCostPrice();
        
        $this->newLine();
        $this->info('重新计算完成统计:');
        $this->line("  总商品数: {$stats['total_products']}");
        $this->line("  成功更新: {$stats['updated_products']}");
        $this->line("  跳过商品: {$stats['skipped_products']}");
        $this->line("  失败商品: {$stats['failed_products']}");
        
        if (!empty($stats['errors'])) {
            $this->newLine();
            $this->warn('失败的商品:');
            foreach ($stats['errors'] as $error) {
                $this->line("  - {$error['product_name']} (ID: {$error['product_id']}): {$error['error']}");
            }
        }
    }
}
