<?php

namespace App\Cart;

use Illuminate\Support\ServiceProvider;
use App\Cart\Services\CartService;

class CartServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register()
    {
        // 绑定服务到容器
        $this->app->bind('cart.service', function ($app) {
            return new CartService();
        });
        
        // 直接绑定CartService类，以便在控制器等地方可以直接注入
        $this->app->bind(CartService::class, function ($app) {
            return new CartService();
        });
    }

    /**
     * 启动服务
     */
    public function boot()
    {
        // 加载API路由
        $this->loadRoutesFrom(__DIR__.'/routes/api.php');
        
        // 加载Web路由
        $this->loadRoutesFrom(__DIR__.'/routes/web.php');
    }
}