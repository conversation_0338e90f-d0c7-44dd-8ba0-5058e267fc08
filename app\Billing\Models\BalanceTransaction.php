<?php

namespace App\Billing\Models;

use App\Models\User;
use App\Employee\Models\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BalanceTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'transaction_no',
        'amount',
        'balance_before',
        'balance_after',
        'transaction_type',
        'source',
        'source_id',
        'description',
        'bill_id',
        'payment_record_id',
        'operator_id',
        'transaction_time',
        'status',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'transaction_time' => 'datetime',
    ];

    // 交易类型常量
    const TYPE_RECHARGE = 'recharge';
    const TYPE_PAYMENT = 'payment';
    const TYPE_REFUND = 'refund';
    const TYPE_ADJUSTMENT = 'adjustment';
    const TYPE_REWARD = 'reward';
    const TYPE_PENALTY = 'penalty';
    const TYPE_TRANSFER_IN = 'transfer_in';
    const TYPE_TRANSFER_OUT = 'transfer_out';
    const TYPE_CORRECTION = 'correction';

    // 交易状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * 生成唯一交易流水号
     */
    public static function generateTransactionNo(): string
    {
        return 'BT' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联账单
     */
    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class);
    }

    /**
     * 关联收款记录
     */
    public function paymentRecord(): BelongsTo
    {
        return $this->belongsTo(PaymentRecord::class);
    }

    /**
     * 关联操作员
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'operator_id');
    }

    /**
     * 获取交易类型文本
     */
    public function getTransactionTypeTextAttribute(): string
    {
        return match($this->transaction_type) {
            self::TYPE_RECHARGE => '充值',
            self::TYPE_PAYMENT => '支付消费',
            self::TYPE_REFUND => '退款',
            self::TYPE_ADJUSTMENT => '人工调整',
            self::TYPE_REWARD => '奖励',
            self::TYPE_PENALTY => '扣款',
            self::TYPE_TRANSFER_IN => '转入',
            self::TYPE_TRANSFER_OUT => '转出',
            self::TYPE_CORRECTION => '更正',
            default => '未知类型'
        };
    }

    /**
     * 获取交易状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => '待处理',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消',
            default => '未知状态'
        };
    }

    /**
     * 检查是否为增加余额的交易
     */
    public function isIncreasing(): bool
    {
        return $this->amount > 0;
    }

    /**
     * 检查是否为减少余额的交易
     */
    public function isDecreasing(): bool
    {
        return $this->amount < 0;
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按交易类型筛选
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('transaction_type', $type);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：成功的交易
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * 作用域：增加余额的交易
     */
    public function scopeIncreasing($query)
    {
        return $query->where('amount', '>', 0);
    }

    /**
     * 作用域：减少余额的交易
     */
    public function scopeDecreasing($query)
    {
        return $query->where('amount', '<', 0);
    }
} 