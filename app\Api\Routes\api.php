<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API 路由
|--------------------------------------------------------------------------
|
| 这里是应用的 API 路由文件，可以注册 API 路由
|
*/

// 注意：各模块路由已迁移到对应模块的routes/api.php文件中
// - 认证相关路由：app/shop/routes/api.php
// - 订单相关路由：app/Order/routes/api.php
// - 配送相关路由：app/Delivery/routes/api.php
// - CRM相关路由：app/Crm/routes/api.php
// - 商品相关路由：app/Product/routes/api.php
// - 库存相关路由：app/Inventory/routes/api.php
// - 仓库相关路由：app/Warehouse/routes/api.php
// - 购物车相关路由：app/Cart/routes/api.php
// - 单位相关路由：app/Unit/routes/api.php

// 此文件仅保留尚未迁移的路由

// 如需添加新路由，请在对应模块的routes/api.php文件中添加 