<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\Order;
use App\Inventory\Models\InventoryTransaction;
use App\Inventory\Jobs\CreateOrderInventoryTransactions;
use Illuminate\Support\Facades\Log;

class TestOrderInventoryTransactions extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'test:order-inventory-transactions {order_id?}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '测试订单库存交易记录创建';

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $orderId = $this->argument('order_id');
        
        if ($orderId) {
            // 测试单个订单
            $this->testSingleOrder($orderId);
        } else {
            // 测试最近10个订单
            $this->testRecentOrders();
        }
        
        return 0;
    }
    
    /**
     * 测试单个订单的库存交易记录
     *
     * @param int $orderId
     * @return void
     */
    protected function testSingleOrder($orderId)
    {
        $order = Order::with('items')->find($orderId);
        
        if (!$order) {
            $this->error("找不到订单ID: {$orderId}");
            return;
        }
        
        $this->info("===== 订单 #{$order->id} ({$order->order_no}) =====");
        $this->info("状态: {$order->status}");
        $this->info("商品数量: " . $order->items->count());
        
        // 查找库存交易记录
        $transactions = InventoryTransaction::where('reference_type', 'order')
            ->where('reference_id', $order->id)
            ->get();
            
        $this->info("库存交易记录数量: " . $transactions->count());
        
        if ($transactions->count() > 0) {
            $this->table(
                ['ID', '商品ID', '商品名称', '数量', '单位', '仓库', '创建时间'],
                $transactions->map(function ($transaction) {
                    return [
                        'id' => $transaction->id,
                        'product_id' => $transaction->product_id,
                        'product_name' => $transaction->product->name ?? '未知商品',
                        'quantity' => abs($transaction->quantity),
                        'unit_id' => $transaction->unit_id,
                        'warehouse_id' => $transaction->warehouse_id,
                        'created_at' => $transaction->created_at
                    ];
                })
            );
        } else {
            $this->warn("该订单没有库存交易记录");
            
            if ($this->confirm('是否创建库存交易记录?')) {
                $this->info("调度库存交易记录创建任务...");
                CreateOrderInventoryTransactions::dispatch($order->id);
                $this->info("任务已调度，请稍后检查结果");
            }
        }
    }
    
    /**
     * 测试最近10个订单的库存交易记录
     *
     * @return void
     */
    protected function testRecentOrders()
    {
        $orders = Order::orderBy('id', 'desc')->take(10)->get();
        
        $this->info("===== 最近 {$orders->count()} 个订单 =====");
        
        $rows = [];
        foreach ($orders as $order) {
            $transactionCount = InventoryTransaction::where('reference_type', 'order')
                ->where('reference_id', $order->id)
                ->count();
                
            $itemsCount = $order->items()->count();
            
            $rows[] = [
                'id' => $order->id,
                'order_no' => $order->order_no,
                'status' => $order->status,
                'items_count' => $itemsCount,
                'transaction_count' => $transactionCount,
                'created_at' => $order->created_at,
                'has_all_transactions' => ($transactionCount == $itemsCount) ? '是' : '否'
            ];
        }
        
        $this->table(
            ['ID', '订单号', '状态', '商品数量', '交易记录数', '创建时间', '交易记录完整'],
            $rows
        );
        
        // 询问是否修复缺失的交易记录
        if ($this->confirm('是否修复缺失的库存交易记录?')) {
            $fixCount = 0;
            foreach ($orders as $order) {
                $transactionCount = InventoryTransaction::where('reference_type', 'order')
                    ->where('reference_id', $order->id)
                    ->count();
                    
                $itemsCount = $order->items()->count();
                
                if ($transactionCount < $itemsCount) {
                    $this->info("调度订单 #{$order->id} 的库存交易记录创建任务");
                    CreateOrderInventoryTransactions::dispatch($order->id);
                    $fixCount++;
                }
            }
            
            $this->info("已调度 {$fixCount} 个订单的库存交易记录创建任务");
        }
    }
} 