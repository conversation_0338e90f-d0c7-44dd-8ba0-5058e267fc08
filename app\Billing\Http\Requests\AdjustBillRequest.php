<?php

namespace App\Billing\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AdjustBillRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'adjustment_amount' => 'required|numeric|not_in:0',
            'reason' => 'required|string|max:500',
            'description' => 'nullable|string|max:1000',
            'operator_id' => 'nullable|integer|exists:employees,id',
            'adjustment_type' => 'nullable|string|in:increase,decrease,discount,fee',
        ];
    }

    public function messages(): array
    {
        return [
            'adjustment_amount.required' => '调整金额不能为空',
            'adjustment_amount.numeric' => '调整金额必须为数字',
            'adjustment_amount.not_in' => '调整金额不能为0',
            'reason.required' => '调整原因不能为空',
            'reason.max' => '调整原因不能超过500个字符',
            'description.max' => '调整说明不能超过1000个字符',
            'operator_id.exists' => '操作员不存在',
            'adjustment_type.in' => '调整类型无效，只能是 increase、decrease、discount 或 fee',
        ];
    }
} 