<?php

namespace App\Auth\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Crm\Models\MembershipLevel;

class AuthController extends Controller
{
    /**
     * 用户注册
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string',
            'nickname' => 'required|string|max:50',
            'merchant_name' => 'required|string|max:100',
            'type' => 'nullable|string|in:register',
            'openid' => 'nullable|string', // 允许传入openid
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        // 验证短信验证码
        try {
            $smsService = app(\App\Shop\Services\Sms\AliyunSmsService::class);
            
            // 检查短信服务是否启用
            if (!$smsService->isEnabled()) {
                return response()->json(ApiResponse::error('短信验证服务未启用', 403), 403);
            }
            
            // 验证验证码
            $isValid = $smsService->verifyCode(
                $request->phone,
                $request->code,
                $request->input('type', 'register')
            );
            
            if (!$isValid) {
                return response()->json(ApiResponse::error('验证码无效或已过期', 400), 400);
            }
            
            // 检查用户是否已存在
            $existingUser = null;
            
            // 优先通过openid查找用户
            if ($request->has('openid') && !empty($request->openid)) {
                $existingUser = User::where('openid', $request->openid)->first();
                Log::info('通过openid查找用户', ['openid' => $request->openid, 'found' => $existingUser ? 'yes' : 'no']);
            }
            
            // 如果通过openid没找到，则通过手机号查找
            if (!$existingUser) {
                $existingUser = User::where('phone', $request->phone)->first();
                Log::info('通过phone查找用户', ['phone' => $request->phone, 'found' => $existingUser ? 'yes' : 'no']);
            }
            
            if ($existingUser) {
                // 用户已存在，更新信息
                Log::info('用户已存在，更新信息', ['user_id' => $existingUser->id]);
                
                $existingUser->nickname = $request->nickname;
                $existingUser->name = $request->nickname;
                $existingUser->merchant_name = $request->merchant_name;
                $existingUser->phone = $request->phone;
                $existingUser->save();
                
                // 创建新token
                $token = $existingUser->createToken('auth_token')->plainTextToken;
                
                return response()->json(ApiResponse::success([
                    'token' => $token,
                    'token_type' => 'Bearer',
                    'user' => $existingUser,
                ], '信息更新成功'));
            }
            
            // 用户不存在，创建新用户
            $user = User::create([
                'name' => $request->nickname,
                'nickname' => $request->nickname,
                'phone' => $request->phone,
                'merchant_name' => $request->merchant_name,
                'password' => Hash::make(Str::random(10)),
                'openid' => $request->openid, // 如果有openid，保存它
                'joined_at' => now(),
            ]);

            // 分配默认会员等级
            $defaultLevel = MembershipLevel::where('is_default', true)->first();
            if ($defaultLevel) {
                $user->membership_level_id = $defaultLevel->id;
                $user->level_upgraded_at = now();
                $user->save();
            }

            // 创建token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json(ApiResponse::success([
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $user,
            ], '注册成功'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('用户注册失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('注册失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 用户登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // 记录请求日志
        Log::info('用户登录API被调用', [
            'email' => $request->input('email'),
            'has_password' => $request->has('password'),
        ]);
        
        try {
            // 验证请求 - 支持邮箱或用户名登录
            $credentials = $request->validate([
                'email' => 'required|string',  // 允许用户名或邮箱
                'password' => 'required',
            ]);
            
            // 记录验证结果
            Log::info('登录验证数据', [
                'email' => $credentials['email']
            ]);
            
            // 支持用户名或邮箱登录
            $loginField = filter_var($credentials['email'], FILTER_VALIDATE_EMAIL) ? 'email' : 'name';
            $user = User::where($loginField, $credentials['email'])->first();
            
            // 如果找到用户并且密码正确
            if ($user && Hash::check($credentials['password'], $user->password)) {
                // 手动登录用户
                Auth::login($user);
                Log::info('用户登录成功', ['user_id' => $user->id, 'login_field' => $loginField]);
                
                // 创建token
                $token = $user->createToken('auth_token')->plainTextToken;
                Log::info('Token创建成功', ['token_length' => strlen($token)]);
                
                return response()->json([
                    'status' => 'success',
                    'message' => '登录成功',
                    'access_token' => $token,
                    'token_type' => 'Bearer',
                    'user' => $user
                ]);
            }
            
            // 登录失败
            Log::warning('登录失败，用户名或密码不正确', ['input' => $credentials['email']]);
            return response()->json([
                'status' => 'error',
                'message' => '用户名或密码不正确',
            ], 401);
        } catch (\Exception $e) {
            Log::error('登录处理过程中发生错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => '登录处理过程中发生错误: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户登出
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        Log::info('用户登出API被调用');
        
        try {
            $request->user()->tokens()->delete();
            Log::info('用户token已删除', ['user_id' => $request->user()->id]);
            
            return response()->json([
                'status' => 'success',
                'message' => '登出成功'
            ]);
        } catch (\Exception $e) {
            Log::error('登出处理过程中发生错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => '登出失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取当前用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        Log::info('获取用户信息API被调用', [
            'path' => $request->path(),
            'authenticated' => $request->user() ? 'yes' : 'no'
        ]);
        
        if ($request->user()) {
            $user = $request->user();
            Log::info('用户已认证', ['user_id' => $user->id]);
            
            // 获取默认配送员信息
            $defaultDeliverer = null;
            if ($user->default_deliverer_id) {
                $deliverer = \App\Models\User::find($user->default_deliverer_id);
                if ($deliverer) {
                    $defaultDeliverer = [
                        'id' => $deliverer->id,
                        'name' => $deliverer->name,
                        'phone' => $deliverer->phone,
                    ];
                }
            }
            
            return response()->json([
                'status' => 'success',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role ?: 'user',
                    'avatar' => $user->avatar,
                    'default_deliverer_id' => $user->default_deliverer_id,
                    'default_deliverer' => $defaultDeliverer,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at
                ]
            ]);
        } else {
            Log::warning('获取用户信息失败，用户未认证');
            return response()->json([
                'status' => 'error',
                'message' => '用户未认证'
            ], 401);
        }
    }
} 