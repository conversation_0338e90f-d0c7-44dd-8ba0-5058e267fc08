<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 添加 warehouse_pending 状态到 deliveries 表的 status 枚举字段
        DB::statement("ALTER TABLE `deliveries` MODIFY COLUMN `status` ENUM(
            'pending',
            'warehouse_pending',
            'in_progress',
            'completed'
        ) DEFAULT 'pending' COMMENT '配送状态：pending=待配送，warehouse_pending=等待仓库确认，in_progress=配送中，completed=已完成'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 移除 warehouse_pending 状态（注意：这会删除所有使用该状态的记录）
        DB::statement("ALTER TABLE `deliveries` MODIFY COLUMN `status` ENUM(
            'pending',
            'in_progress',
            'completed'
        ) DEFAULT 'pending' COMMENT '配送状态'");
    }
};
