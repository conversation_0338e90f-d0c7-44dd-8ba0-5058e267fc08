<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Migration\Services\ProductMigrationService;

class MigrateProductData extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migrate:products 
                            {--dry-run : 试运行模式，不实际修改数据}
                            {--categories : 迁移分类数据}
                            {--products : 迁移商品数据}
                            {--inventory : 迁移库存数据}
                            {--images : 迁移图片数据}
                            {--all : 迁移所有数据}
                            {--batch-size=100 : 批处理大小}
                            {--continue-on-error : 遇到错误时继续执行}';

    /**
     * The console command description.
     */
    protected $description = '从老系统迁移商品数据到新系统';

    private $migrationService;

    public function __construct(ProductMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 商品数据迁移工具');
        $this->info('=' . str_repeat('=', 50));

        // 处理命令选项
        $options = $this->processOptions();

        // 确认执行
        if (!$this->confirmExecution($options)) {
            $this->warn('❌ 迁移被取消');
            return Command::FAILURE;
        }

        try {
            // 设置动态配置
            $this->updateConfig($options);

            // 执行迁移
            $this->info('开始执行数据迁移...');
            $report = $this->migrationService->migrateAll($options);

            // 显示结果
            $this->displayResults($report);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ 迁移失败: ' . $e->getMessage());
            $this->line($e->getTraceAsString());
            return Command::FAILURE;
        }
    }

    /**
     * 处理命令选项
     */
    private function processOptions()
    {
        $options = [];

        // 检查是否指定了具体的迁移类型
        if ($this->option('all')) {
            $options = [
                'migrate_categories' => true,
                'migrate_products' => true,
                'migrate_inventory' => true,
                'migrate_images' => true,
            ];
        } else {
            $options = [
                'migrate_categories' => $this->option('categories'),
                'migrate_products' => $this->option('products'),
                'migrate_inventory' => $this->option('inventory'),
                'migrate_images' => $this->option('images'),
            ];

            // 如果没有指定任何选项，默认迁移商品和库存
            if (!array_filter($options)) {
                $options['migrate_products'] = true;
                $options['migrate_inventory'] = true;
                $this->info('💡 未指定迁移类型，默认迁移商品和库存数据');
            }
        }

        $options['dry_run'] = $this->option('dry-run');
        $options['batch_size'] = (int) $this->option('batch-size');
        $options['continue_on_error'] = $this->option('continue-on-error');

        return $options;
    }

    /**
     * 确认执行
     */
    private function confirmExecution($options)
    {
        $this->info('迁移配置:');
        
        if ($options['dry_run']) {
            $this->line('  模式: 🧪 试运行 (不会修改数据)');
        } else {
            $this->line('  模式: ⚡ 正式执行 (会修改数据库)');
        }

        $this->line('  批处理大小: ' . $options['batch_size']);
        $this->line('  错误处理: ' . ($options['continue_on_error'] ? '继续执行' : '遇到错误停止'));
        $this->line('');

        $this->info('迁移内容:');
        foreach (['categories' => '分类', 'products' => '商品', 'inventory' => '库存', 'images' => '图片'] as $key => $label) {
            $status = $options["migrate_{$key}"] ? '✅' : '❌';
            $this->line("  {$status} {$label}");
        }

        $this->line('');

        if ($options['dry_run']) {
            return true; // 试运行不需要确认
        }

        return $this->confirm('⚠️  确定要执行数据迁移吗？这将修改数据库内容！');
    }

    /**
     * 更新动态配置
     */
    private function updateConfig($options)
    {
        // 更新迁移设置
        config([
            'migration.migration_settings.dry_run' => $options['dry_run'],
            'migration.migration_settings.batch_size' => $options['batch_size'],
            'migration.migration_settings.continue_on_error' => $options['continue_on_error'],
        ]);
    }

    /**
     * 显示迁移结果
     */
    private function displayResults($report)
    {
        $this->info('');
        $this->info('🎉 迁移完成！');
        $this->info('=' . str_repeat('=', 50));

        $summary = $report['summary'];

        // 总体统计
        $this->line(sprintf(
            '⏱️  执行时间: %d 秒',
            $summary['execution_time']
        ));

        if ($report['dry_run']) {
            $this->warn('🧪 这是试运行结果，未实际修改数据');
        }

        $this->line('');

        // 各类型统计
        $types = [
            'products' => '📦 商品',
            'categories' => '📂 分类',
            'inventory' => '📊 库存',
            'images' => '🖼️  图片',
        ];

        foreach ($types as $type => $label) {
            $stats = $summary[$type];
            if ($stats['total'] > 0) {
                $this->line(sprintf(
                    '%s: 总数 %d, 成功 %d, 失败 %d',
                    $label,
                    $stats['total'],
                    $stats['success'],
                    $stats['failed']
                ));

                // 成功率
                $successRate = $stats['total'] > 0 ? round(($stats['success'] / $stats['total']) * 100, 1) : 0;
                if ($successRate >= 95) {
                    $this->info("  ✅ 成功率: {$successRate}%");
                } elseif ($successRate >= 80) {
                    $this->warn("  ⚠️  成功率: {$successRate}%");
                } else {
                    $this->error("  ❌ 成功率: {$successRate}%");
                }
            }
        }

        // 显示建议
        if (!empty($report['recommendations'])) {
            $this->line('');
            $this->info('💡 建议:');
            foreach ($report['recommendations'] as $recommendation) {
                $this->line("  • {$recommendation}");
            }
        }

        // 报告文件位置
        $this->line('');
        $this->info('📄 详细报告已保存到 storage/migration_reports/ 目录');
    }
} 