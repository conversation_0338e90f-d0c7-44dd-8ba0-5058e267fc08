<?php

namespace App\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Employee\Models\Employee;

/**
 * 后台访问控制中间件
 * 
 * 检查当前登录员工是否有权限访问特定的后台系统
 * 基于员工角色划分访问权限
 */
class CheckBackendAccess
{
    /**
     * 处理传入的请求，检查员工是否可以访问特定后台
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $backend 后台类型 (admin, crm, delivery, warehouse)
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, string $backend): Response
    {
        // 从sanctum获取当前登录员工
        $employee = $request->user('sanctum');
        
        // 检查是否已登录
        if (!$employee || !($employee instanceof Employee)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'code' => 401,
                    'message' => '未授权访问，请先以员工身份登录',
                    'data' => null
                ], 401);
            }
            return redirect()->route('employee.login');
        }
        
        // 检查员工是否有权访问指定后台
        if (!$employee->canAccessBackend($backend)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'code' => 403,
                    'message' => '无权访问' . $this->getBackendName($backend) . '系统',
                    'data' => null
                ], 403);
            }
            
            // 重定向到对应角色可访问的后台
            if ($employee->isAdmin() || $employee->isManager() || $employee->isStaff()) {
                return redirect()->route('admin.dashboard');
            } else if ($employee->isCrmAgent()) {
                return redirect()->route('crm.dashboard');
            } else if ($employee->isDeliveryPerson()) {
                return redirect()->route('delivery.dashboard');
            } else if ($employee->isWarehouseManager()) {
                return redirect()->route('warehouse.dashboard');
            } else {
                return redirect()->route('employee.login')->with('error', '无权访问' . $this->getBackendName($backend) . '系统');
            }
        }
        
        return $next($request);
    }
    
    /**
     * 获取后台名称
     * 
     * @param string $backend 后台代码
     * @return string 后台名称
     */
    private function getBackendName(string $backend): string
    {
        switch ($backend) {
            case 'admin':
                return '管理';
            case 'crm':
                return 'CRM管理';
            case 'delivery':
                return '配送管理';
            case 'warehouse':
                return '仓库管理';
            default:
                return '';
        }
    }
} 