<?php

namespace App\Billing\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Settlement;
use App\Models\SettlementDetail;
use App\Billing\Services\SettlementService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class SettlementController extends Controller
{
    protected $settlementService;

    public function __construct(SettlementService $settlementService)
    {
        $this->settlementService = $settlementService;
    }

    /**
     * 获取结算列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'settlement_type' => 'string',
                'business_module' => 'string', 
                'status' => 'string',
                'region_id' => 'integer',
                'date_from' => 'date',
                'date_to' => 'date',
                'search' => 'string'
            ]);

            $result = $this->settlementService->getSettlementList($params);

            return response()->json([
                'success' => true,
                'data' => $result['data'],
                'meta' => $result['meta']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取结算列表失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取结算详情
     */
    public function show($id): JsonResponse
    {
        try {
            $settlement = $this->settlementService->getSettlementDetail($id);
            
            return response()->json([
                'success' => true,
                'data' => $settlement
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取结算详情失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建结算
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'settlement_type' => 'required|string',
                'business_module' => 'required|string',
                'region_id' => 'integer',
                'period_start' => 'required|date',
                'period_end' => 'required|date',
                'period_label' => 'string',
                'user_segment' => 'array',
                'product_categories' => 'array',
                'employee_scope' => 'array',
                'notes' => 'string'
            ]);

            $settlement = $this->settlementService->createSettlement($data);

            return response()->json([
                'success' => true,
                'data' => $settlement
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新结算
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $data = $request->validate([
                'settlement_type' => 'string',
                'business_module' => 'string',
                'region_id' => 'integer',
                'period_start' => 'date',
                'period_end' => 'date',
                'period_label' => 'string',
                'user_segment' => 'array',
                'product_categories' => 'array',
                'employee_scope' => 'array',
                'notes' => 'string'
            ]);

            $settlement = $this->settlementService->updateSettlement($id, $data);

            return response()->json([
                'success' => true,
                'data' => $settlement
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '更新结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除结算
     */
    public function destroy($id): JsonResponse
    {
        try {
            $this->settlementService->deleteSettlement($id);

            return response()->json([
                'success' => true,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '删除结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取结算汇总信息
     */
    public function summary(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'settlement_type' => 'string',
                'business_module' => 'string',
                'region_id' => 'integer',
                'date_from' => 'date',
                'date_to' => 'date'
            ]);

            $summary = $this->settlementService->getSettlementSummary($params);

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取汇总信息失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取结算分析数据
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'settlement_type' => 'string',
                'business_module' => 'string',
                'region_id' => 'integer',
                'date_from' => 'date',
                'date_to' => 'date',
                'chart_type' => 'string'
            ]);

            $analytics = $this->settlementService->getSettlementAnalytics($params);

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取分析数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取结算趋势数据
     */
    public function trends(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'settlement_type' => 'string',
                'business_module' => 'string',
                'period' => 'string',
                'date_from' => 'date',
                'date_to' => 'date'
            ]);

            $trends = $this->settlementService->getSettlementTrends($params);

            return response()->json([
                'success' => true,
                'data' => $trends
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取趋势数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取业务模块分布数据
     */
    public function moduleDistribution(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'settlement_type' => 'string',
                'date_from' => 'date',
                'date_to' => 'date'
            ]);

            $distribution = $this->settlementService->getBusinessModuleDistribution($params);

            return response()->json([
                'success' => true,
                'data' => $distribution
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取模块分布数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取支付方式分析数据
     */
    public function paymentAnalysis(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'settlement_type' => 'string',
                'business_module' => 'string',
                'date_from' => 'date',
                'date_to' => 'date'
            ]);

            $analysis = $this->settlementService->getPaymentMethodAnalysis($params);

            return response()->json([
                'success' => true,
                'data' => $analysis
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取支付分析数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 计算结算数据
     */
    public function calculate($id): JsonResponse
    {
        try {
            $result = $this->settlementService->calculateSettlement($id);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '计算结算数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 审核结算
     */
    public function verify(Request $request, $id): JsonResponse
    {
        try {
            $data = $request->validate([
                'notes' => 'string'
            ]);

            $result = $this->settlementService->verifySettlement($id, $data);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '审核结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 发布结算
     */
    public function publish(Request $request, $id): JsonResponse
    {
        try {
            $data = $request->validate([
                'notes' => 'string'
            ]);

            $result = $this->settlementService->publishSettlement($id, $data);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '发布结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 归档结算
     */
    public function archive(Request $request, $id): JsonResponse
    {
        try {
            $data = $request->validate([
                'notes' => 'string'
            ]);

            $result = $this->settlementService->archiveSettlement($id, $data);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '归档结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 导出结算数据
     */
    public function export(Request $request, $id)
    {
        try {
            $format = $request->get('format', 'excel');
            
            return $this->settlementService->exportSettlement($id, $format);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 导出分析报告
     */
    public function exportAnalytics(Request $request)
    {
        try {
            $params = $request->validate([
                'settlement_type' => 'string',
                'business_module' => 'string',
                'date_from' => 'date',
                'date_to' => 'date',
                'format' => 'string'
            ]);

            return $this->settlementService->exportSettlementAnalytics($params);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出分析报告失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取结算日志
     */
    public function logs($id): JsonResponse
    {
        try {
            $logs = $this->settlementService->getSettlementLogs($id);

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取日志失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 快速创建月度平台结算
     */
    public function createMonthlyPlatform(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'year' => 'required|integer',
                'month' => 'required|integer|min:1|max:12'
            ]);

            $settlement = $this->settlementService->createMonthlyPlatformSettlement($data['year'], $data['month']);

            return response()->json([
                'success' => true,
                'data' => $settlement
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建月度结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 快速创建季度业务模块结算
     */
    public function createQuarterlyModule(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'year' => 'required|integer',
                'quarter' => 'required|integer|min:1|max:4',
                'business_module' => 'required|string'
            ]);

            $settlement = $this->settlementService->createQuarterlyModuleSettlement(
                $data['year'], 
                $data['quarter'], 
                $data['business_module']
            );

            return response()->json([
                'success' => true,
                'data' => $settlement
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建季度结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 快速创建年度汇总结算
     */
    public function createYearlyTotal(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'year' => 'required|integer'
            ]);

            $settlement = $this->settlementService->createYearlyTotalSettlement($data['year']);

            return response()->json([
                'success' => true,
                'data' => $settlement
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建年度结算失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取结算明细
     */
    public function details(Request $request, Settlement $settlement): JsonResponse
    {
        $query = SettlementDetail::where('settlement_id', $settlement->id);

        // 关联类型过滤
        if ($request->filled('relation_type')) {
            $query->where('relation_type', $request->input('relation_type'));
        }

        $details = $query->paginate($request->input('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $details->items(),
            'meta' => [
                'total' => $details->total(),
                'per_page' => $details->perPage(),
                'current_page' => $details->currentPage(),
                'last_page' => $details->lastPage(),
            ]
        ]);
    }

    /**
     * 校验结算数据
     */
    public function validateSettlement(Settlement $settlement): JsonResponse
    {
        try {
            $result = $this->settlementService->validateSettlement($settlement);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => '校验完成'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '校验失败：' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * 修复结算数据
     */
    public function repair(Request $request, Settlement $settlement): JsonResponse
    {
        $validated = $request->validate([
            'repair_type' => ['nullable', Rule::in(['recalculate', 'fix_associations', 'update_totals'])],
            'force' => ['nullable', 'boolean']
        ]);

        try {
            $result = $this->settlementService->repairSettlement(
                $settlement,
                $validated['repair_type'] ?? 'recalculate',
                $validated['force'] ?? false
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => '修复完成'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '修复失败：' . $e->getMessage()
            ], 422);
        }
    }
} 