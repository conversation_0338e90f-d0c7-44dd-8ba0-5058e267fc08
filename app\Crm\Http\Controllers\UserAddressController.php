<?php

namespace App\Crm\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Crm\Models\UserAddress;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class UserAddressController extends Controller
{
    /**
     * 获取用户所有地址
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $addresses = $user->addresses()->get();
        
        return response()->json(ApiResponse::success($addresses));
    }
    
    /**
     * 创建新地址
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // 添加调试日志
        Log::info('🔍 地址创建请求数据:', $request->all());
        
        $validator = Validator::make($request->all(), [
            'contact_name' => 'required|string|max:50',
            'contact_phone' => 'required|string|max:20',
            'province' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'district' => 'nullable|string|max:50',
            'address' => 'required|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
            'is_default' => 'boolean',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = $request->user();
        
        // 如果设置为默认地址，将其他地址设为非默认
        if ($request->is_default) {
            $user->addresses()->update(['is_default' => false]);
        }
        
        $address = $user->addresses()->create($request->all());
        
        // 添加调试日志
        Log::info('🔍 地址创建成功:', $address->toArray());
        
        return response()->json(ApiResponse::success($address, '地址添加成功'), 201);
    }
    
    /**
     * 获取单个地址详情
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        $address = $user->addresses()->findOrFail($id);
        
        return response()->json(ApiResponse::success($address));
    }
    
    /**
     * 更新地址
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'contact_name' => 'string|max:50',
            'contact_phone' => 'string|max:20',
            'province' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'district' => 'nullable|string|max:50',
            'address' => 'string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
            'is_default' => 'boolean',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = $request->user();
        $address = $user->addresses()->findOrFail($id);
        
        // 如果设置为默认地址，将其他地址设为非默认
        if ($request->has('is_default') && $request->is_default) {
            $user->addresses()->where('id', '!=', $id)->update(['is_default' => false]);
        }
        
        $address->update($request->all());
        
        return response()->json(ApiResponse::success($address, '地址更新成功'));
    }
    
    /**
     * 删除地址
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        $address = $user->addresses()->findOrFail($id);
        
        $address->delete();
        
        // 如果删除的是默认地址，设置最新的一个地址为默认
        if ($address->is_default) {
            $newDefault = $user->addresses()->first();
            if ($newDefault) {
                $newDefault->update(['is_default' => true]);
            }
        }
        
        return response()->json(ApiResponse::success(null, '地址删除成功'));
    }
    
    /**
     * 设置默认地址
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefault(Request $request, $id)
    {
        $user = $request->user();
        
        // 清除其他默认地址
        $user->addresses()->update(['is_default' => false]);
        
        // 设置新的默认地址
        $address = $user->addresses()->findOrFail($id);
        $address->update(['is_default' => true]);
        
        return response()->json(ApiResponse::success($address, '默认地址设置成功'));
    }

    /**
     * 获取指定用户的所有地址 (CRM专用)
     * 
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAddresses($userId)
    {
        $user = \App\Models\User::findOrFail($userId);
        $addresses = $user->addresses()->get();
        
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $addresses
        ]);
    }

    /**
     * 为指定用户创建地址 (CRM专用) 
     * 
     * @param Request $request
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeForUser(Request $request, $userId)
    {
        // 添加调试日志
        Log::info('🔍 CRM为用户创建地址请求数据:', [
            'userId' => $userId,
            'data' => $request->all()
        ]);
        
        $validator = Validator::make($request->all(), [
            'contact_name' => 'required|string|max:50',
            'contact_phone' => 'required|string|max:20',
            'province' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'district' => 'nullable|string|max:50',
            'region_id' => 'nullable|integer|exists:regions,id',
            'address' => 'required|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
            'is_default' => 'boolean',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = \App\Models\User::findOrFail($userId);
        
        // 准备创建数据，确保包含user_id
        $data = $request->all();
        $data['user_id'] = $userId;
        
        // 如果设置为默认地址，将该用户的其他地址设为非默认
        if ($request->get('is_default', false)) {
            $user->addresses()->update(['is_default' => false]);
        }
        
        $address = UserAddress::create($data);
        
        // 添加调试日志
        Log::info('🔍 CRM地址创建成功:', $address->toArray());
        
        return response()->json(ApiResponse::success($address, '地址添加成功'), 201);
    }
} 