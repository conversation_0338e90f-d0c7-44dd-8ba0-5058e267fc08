<?php

namespace App\Billing\Services;

use App\Billing\Models\Bill;
use App\Billing\Models\PaymentRecord;
use App\Billing\Models\BillingPaymentLink;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 🔥 新增：账单系统付款链接服务
 * 统一处理所有付款链接逻辑，替代订单模块的付款处理
 */
class PaymentLinkService
{
    /**
     * 为账单创建付款链接
     */
    public function createPaymentLinkForBill(Bill $bill, array $options = []): BillingPaymentLink
    {
        return DB::transaction(function () use ($bill, $options) {
            // 验证账单状态
            if (!$bill->canBePaid()) {
                throw new Exception('账单状态不允许创建付款链接');
            }

            $paymentAmount = $options['amount'] ?? $bill->pending_amount;
            $paymentMethod = $options['payment_method'] ?? 'wechat';
            $expiresAt = $options['expires_at'] ?? now()->addHours(24);

            // 🔥 创建真正的付款链接记录
            $paymentLink = BillingPaymentLink::create([
                'id' => BillingPaymentLink::generateLinkId(),
                'link_no' => BillingPaymentLink::generateLinkNo(),
                'bill_id' => $bill->id,
                'order_id' => $bill->order_id,
                'correction_id' => $options['correction_id'] ?? null,
                'payment_type' => $this->determinePaymentType($bill, $options),
                'payment_method' => $paymentMethod,
                'amount' => $paymentAmount,
                'status' => BillingPaymentLink::STATUS_ACTIVE,
                'expires_at' => $expiresAt,
                'created_by' => $options['created_by'] ?? null,
                'metadata' => [
                    'bill_no' => $bill->bill_no,
                    'payment_scenario' => $options['payment_scenario'] ?? 'bill_payment',
                    'created_source' => 'billing_system',
                    'business_context' => $options['business_context'] ?? []
                ]
            ]);

            // 根据支付方式处理附加逻辑
            switch ($paymentMethod) {
                case 'wechat':
                    $this->setupWechatPayment($paymentLink, $options);
                    break;
                case 'alipay':
                    $this->setupAlipayPayment($paymentLink, $options);
                    break;
                default:
                    $this->setupGeneralPayment($paymentLink, $options);
                    break;
            }

            Log::info('账单付款链接创建成功', [
                'bill_id' => $bill->id,
                'payment_link_id' => $paymentLink->id,
                'link_no' => $paymentLink->link_no,
                'amount' => $paymentAmount,
                'payment_method' => $paymentMethod
            ]);

            return $paymentLink;
        });
    }

    /**
     * 处理付款链接支付成功回调
     */
    public function handlePaymentSuccess(string $linkNo, array $paymentData): void
    {
        DB::transaction(function () use ($linkNo, $paymentData) {
            Log::info('账单系统处理付款链接支付成功', [
                'link_no' => $linkNo,
                'payment_data' => $paymentData
            ]);

            // 查找付款链接
            $paymentLink = $this->getPaymentLinkByNo($linkNo);
            if (!$paymentLink) {
                throw new Exception("付款链接不存在: {$linkNo}");
            }

            $bill = Bill::findOrFail($paymentLink->bill_id);

            // 创建支付记录
            Log::info('🔄 开始创建账单系统支付记录', [
                'table' => 'payment_records',
                'link_no' => $linkNo,
                'bill_id' => $bill->id,
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'payment_method' => $paymentData['payment_method'] ?? $paymentLink->payment_method,
                'amount' => $paymentData['amount'] ?? $paymentLink->amount,
                'has_transaction_id' => !empty($paymentData['transaction_id'])
            ]);

            $paymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                'correction_id' => $paymentLink->correction_id,
                'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $paymentData['payment_method'] ?? $paymentLink->payment_method,
                'original_payment_method' => $bill->order?->payment_method,
                'payment_amount' => $paymentData['amount'] ?? $paymentLink->amount,
                'payment_type' => $this->getPaymentTypeFromLink($paymentLink),
                'payment_scenario' => $this->getPaymentScenarioFromLink($paymentLink),
                'count_as_paid' => true,
                'status' => PaymentRecord::STATUS_SUCCESS,
                'payment_time' => now(),
                'confirmed_at' => now(),
                'received_by' => $paymentData['received_by'] ?? null,
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'external_payment_no' => $paymentData['external_payment_no'] ?? null,
                'notes' => "通过付款链接支付: {$linkNo}",
                'payment_details' => [
                    'payment_link_no' => $linkNo,
                    'payment_source' => 'payment_link',
                    'original_payment_data' => $paymentData
                ],
                'payment_context' => [
                    'link_metadata' => $paymentLink->metadata ?? [],
                    'payment_timestamp' => now()->toISOString()
                ]
            ]);

            // 更新账单状态
            $bill->updateAmounts();

            // 🔥 关键：通过账单系统同步订单状态
            $this->syncOrderStatusFromBill($bill);

            // 标记付款链接为已完成
            $this->markPaymentLinkCompleted($linkNo, $paymentRecord);

            // 🔥 详细验证：确保transaction_id被正确存储
            if (!empty($paymentData['transaction_id'])) {
                Log::info('✅ 账单系统transaction_id写入成功', [
                    'table' => 'payment_records',
                    'link_no' => $linkNo,
                    'payment_record_id' => $paymentRecord->id,
                    'transaction_id' => $paymentData['transaction_id'],
                    'payment_method' => $paymentRecord->payment_method,
                    'amount' => $paymentRecord->amount,
                    'business_type' => $paymentRecord->business_type,
                    'payment_type' => $paymentRecord->payment_type
                ]);
            } else {
                Log::warning('⚠️ 账单系统支付成功但缺少交易流水号', [
                    'table' => 'payment_records',
                    'link_no' => $linkNo,
                    'payment_record_id' => $paymentRecord->id,
                    'payment_data' => $paymentData,
                    'risk_level' => 'HIGH',
                    'impact' => '可能影响财务对账'
                ]);
            }

            Log::info('付款链接支付处理完成', [
                'link_no' => $linkNo,
                'bill_id' => $bill->id,
                'payment_record_id' => $paymentRecord->id,
                'bill_status' => $bill->fresh()->status,
                'order_status' => $bill->order?->fresh()->status,
                'transaction_id' => $paymentRecord->transaction_id
            ]);
        });
    }

    /**
     * 处理微信支付回调
     */
    public function handleWechatCallback(array $notifyData): array
    {
        return DB::transaction(function () use ($notifyData) {
            Log::info('账单系统处理微信支付回调', [
                'out_trade_no' => $notifyData['out_trade_no'] ?? '',
                'transaction_id' => $notifyData['transaction_id'] ?? ''
            ]);

            // 验证回调数据
            if (($notifyData['return_code'] ?? '') !== 'SUCCESS') {
                throw new Exception('微信回调通信失败: ' . ($notifyData['return_msg'] ?? ''));
            }

            if (($notifyData['result_code'] ?? '') !== 'SUCCESS') {
                throw new Exception('微信回调业务失败: ' . ($notifyData['err_code_des'] ?? ''));
            }

            // 验证必要字段
            if (empty($notifyData['out_trade_no'])) {
                throw new Exception('微信回调缺少商户订单号');
            }

            if (empty($notifyData['transaction_id'])) {
                Log::warning('微信回调缺少交易流水号', [
                    'out_trade_no' => $notifyData['out_trade_no'] ?? '',
                    'notify_data' => $notifyData
                ]);
                // 不抛出异常，继续处理，但记录警告
            }

            $outTradeNo = $notifyData['out_trade_no'];

            // 🔥 重要：优先查找账单系统的支付记录
            $paymentRecord = PaymentRecord::where('payment_no', $outTradeNo)->first();
            
            if ($paymentRecord) {
                // 账单系统支付记录，按账单逻辑处理
                return $this->handleBillingPaymentCallback($paymentRecord, $notifyData);
            }

            // 🔥 兼容性：查找付款链接
            $paymentLink = $this->getPaymentLinkByTradeNo($outTradeNo);
            if ($paymentLink) {
                // 付款链接支付，转换为账单支付记录
                $this->handlePaymentSuccess($paymentLink->link_no, [
                    'payment_method' => 'wechat',
                    'amount' => $notifyData['total_fee'] / 100, // 分转元
                    'transaction_id' => $notifyData['transaction_id'],
                    'external_payment_no' => $outTradeNo
                ]);
                return ['code' => 'SUCCESS', 'message' => '付款链接支付处理成功'];
            }

            // 🔥 兼容性：查找原始订单（历史订单）
            $order = Order::where('payment_no', $outTradeNo)->first();
            if ($order) {
                return $this->handleLegacyOrderPayment($order, $notifyData);
            }

            throw new Exception("未找到对应的支付记录: {$outTradeNo}");
        });
    }

    /**
     * 🔥 关键：通过账单状态同步订单状态
     */
    public function syncOrderStatusFromBill(Bill $bill): void
    {
        if (!$bill->order) {
            return;
        }

        $order = $bill->order;
        $newStatus = null;
        $updateData = [];

        switch ($bill->payment_status) {
            case Bill::PAYMENT_STATUS_PAID:
            case Bill::PAYMENT_STATUS_OVERPAID:
                // 🔥 优化：支持标准的订单状态流转 pending → paid → confirmed
                if (in_array($order->status, ['pending', 'pending_payment'])) {
                    // 🔥 修复：从账单的支付记录中获取交易号
                    $latestPaymentRecord = $bill->paymentRecords()
                        ->where('status', \App\Billing\Models\PaymentRecord::STATUS_SUCCESS)
                        ->whereNotNull('transaction_id')
                        ->latest()
                        ->first();

                    $transactionId = $latestPaymentRecord ? $latestPaymentRecord->transaction_id : null;

                    // 🔥 优化：先更新为paid状态，触发相关逻辑
                    $order->update([
                        'status' => 'paid',
                        'payment_status' => 'paid',
                        'paid_at' => now(),
                        'transaction_id' => $transactionId
                    ]);

                    Log::info('🎉 订单支付完成，状态更新为paid', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'bill_id' => $bill->id,
                        'transaction_id' => $transactionId,
                        'old_status' => 'pending',
                        'new_status' => 'paid'
                    ]);



                    // 🔥 优化：然后立即更新为confirmed状态（在线支付自动确认）
                    $newStatus = 'confirmed';
                    $updateData = [
                        'status' => 'confirmed',
                        'confirmed_at' => now(),
                        'payment_confirmed_at' => now()
                    ];
                }
                break;

            case Bill::PAYMENT_STATUS_PARTIAL:
                // 🔥 修复：支持 pending_payment 状态的订单
                if (in_array($order->status, ['pending', 'pending_payment'])) {
                    $newStatus = 'partial_paid';
                    $updateData = [
                        'status' => 'pending_payment', // 部分付款仍保持待付款状态
                        'payment_status' => 'partial'
                    ];
                }
                break;

            case Bill::PAYMENT_STATUS_REFUNDED:
                // 如果账单已退款，但订单已发货，需要特殊处理
                if (!in_array($order->status, ['shipped', 'delivered'])) {
                    $newStatus = 'refunded';
                    $updateData = [
                        'status' => 'cancelled',
                        'payment_status' => 'refunded',
                        'cancelled_at' => now()
                    ];
                }
                break;
        }

        if ($newStatus && !empty($updateData)) {
            $oldStatus = $order->status;
            $order->update($updateData);

            Log::info('🎉 订单状态已通过账单系统同步', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'bill_id' => $bill->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'bill_payment_status' => $bill->payment_status,
                'bill_total_amount' => $bill->total_amount,
                'bill_paid_amount' => $bill->paid_amount,
                'update_data' => $updateData
            ]);
        } else {
            Log::info('订单状态无需同步', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'current_status' => $order->status,
                'bill_payment_status' => $bill->payment_status,
                'reason' => $newStatus ? '更新数据为空' : '状态不匹配同步条件'
            ]);
        }
    }

    /**
     * 处理账单系统支付回调
     */
    private function handleBillingPaymentCallback(PaymentRecord $paymentRecord, array $notifyData): array
    {
        // 🔥 防重复处理：检查支付记录状态
        if ($paymentRecord->status === PaymentRecord::STATUS_SUCCESS) {
            Log::info('支付记录已处理，跳过重复回调', [
                'payment_record_id' => $paymentRecord->id,
                'payment_no' => $paymentRecord->payment_no,
                'status' => $paymentRecord->status,
                'confirmed_at' => $paymentRecord->confirmed_at
            ]);
            return ['code' => 'SUCCESS', 'message' => '支付记录已处理'];
        }

        // 更新支付记录
        $updateData = [
            'status' => PaymentRecord::STATUS_SUCCESS,
            'confirmed_at' => now(),
            'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                'wechat_callback' => $notifyData,
                'callback_time' => now()->toISOString()
            ])
        ];

        // 只有当transaction_id存在且不为空时才更新
        if (!empty($notifyData['transaction_id'])) {
            // 如果已经有交易流水号，不要覆盖（防止重复回调覆盖）
            if (empty($paymentRecord->transaction_id)) {
                $updateData['transaction_id'] = $notifyData['transaction_id'];
                Log::info('更新支付记录交易流水号', [
                    'payment_record_id' => $paymentRecord->id,
                    'transaction_id' => $notifyData['transaction_id']
                ]);
            } else {
                Log::info('交易流水号已存在，跳过更新', [
                    'payment_record_id' => $paymentRecord->id,
                    'existing_transaction_id' => $paymentRecord->transaction_id,
                    'callback_transaction_id' => $notifyData['transaction_id']
                ]);
            }
        } else {
            Log::warning('支付成功但缺少交易流水号', [
                'payment_record_id' => $paymentRecord->id,
                'out_trade_no' => $notifyData['out_trade_no'] ?? ''
            ]);
        }

        $paymentRecord->update($updateData);

        // 更新账单状态
        $bill = $paymentRecord->bill;
        if ($bill) {
            $bill->updateAmounts();
            $this->syncOrderStatusFromBill($bill);
        }

        return ['code' => 'SUCCESS', 'message' => '支付成功'];
    }

    /**
     * 补录支付成功订单的交易流水号
     * 用于处理支付成功但缺少transaction_id的情况
     */
    public function backfillTransactionId($paymentRecordId)
    {
        try {
            $paymentRecord = PaymentRecord::find($paymentRecordId);
            if (!$paymentRecord) {
                throw new \Exception("支付记录不存在: {$paymentRecordId}");
            }

            // 如果已经有交易流水号，不需要补录
            if (!empty($paymentRecord->transaction_id)) {
                return ['success' => true, 'message' => '交易流水号已存在'];
            }

            // 只处理支付成功的记录
            if ($paymentRecord->status !== PaymentRecord::STATUS_SUCCESS) {
                return ['success' => false, 'message' => '只能补录支付成功的记录'];
            }

            // 通过微信支付查询API获取交易流水号
            $wechatService = new \App\WechatPayment\Services\WechatServiceProviderPayment(
                $paymentRecord->getWechatProvider(),
                $paymentRecord->getWechatSubMerchant()
            );

            $queryResult = $wechatService->queryOrder($paymentRecord->payment_no);

            if ($queryResult && isset($queryResult['transaction_id'])) {
                // 更新交易流水号
                $paymentRecord->update([
                    'transaction_id' => $queryResult['transaction_id'],
                    'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                        'backfill_transaction_id' => [
                            'time' => now()->toISOString(),
                            'source' => 'wechat_query_api',
                            'transaction_id' => $queryResult['transaction_id']
                        ]
                    ])
                ]);

                Log::info('补录交易流水号成功', [
                    'payment_record_id' => $paymentRecordId,
                    'transaction_id' => $queryResult['transaction_id']
                ]);

                return ['success' => true, 'transaction_id' => $queryResult['transaction_id']];
            }

            return ['success' => false, 'message' => '无法从微信查询到交易流水号'];

        } catch (\Exception $e) {
            Log::error('补录交易流水号失败', [
                'payment_record_id' => $paymentRecordId,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 🔥 新增：批量检查并补录缺失的交易流水号
     */
    public function batchBackfillMissingTransactionIds(int $limit = 100): array
    {
        try {
            // 查找缺少transaction_id的成功支付记录
            $missingRecords = PaymentRecord::where('status', PaymentRecord::STATUS_SUCCESS)
                ->where('payment_method', 'wechat')
                ->where(function($query) {
                    $query->whereNull('transaction_id')
                          ->orWhere('transaction_id', '');
                })
                ->whereNotNull('payment_no')
                ->limit($limit)
                ->get();

            $results = [
                'total_checked' => $missingRecords->count(),
                'success_count' => 0,
                'failed_count' => 0,
                'details' => []
            ];

            foreach ($missingRecords as $record) {
                $result = $this->backfillTransactionId($record->id);

                if ($result['success']) {
                    $results['success_count']++;
                } else {
                    $results['failed_count']++;
                }

                $results['details'][] = [
                    'payment_record_id' => $record->id,
                    'payment_no' => $record->payment_no,
                    'result' => $result
                ];
            }

            Log::info('批量补录交易流水号完成', $results);
            return $results;

        } catch (\Exception $e) {
            Log::error('批量补录交易流水号失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 🔥 新增：验证支付记录的transaction_id完整性
     */
    public function validateTransactionIdIntegrity(): array
    {
        try {
            // 统计各种情况
            $stats = [
                'total_wechat_payments' => PaymentRecord::where('payment_method', 'wechat')
                    ->where('status', PaymentRecord::STATUS_SUCCESS)
                    ->count(),

                'with_transaction_id' => PaymentRecord::where('payment_method', 'wechat')
                    ->where('status', PaymentRecord::STATUS_SUCCESS)
                    ->whereNotNull('transaction_id')
                    ->where('transaction_id', '!=', '')
                    ->count(),

                'missing_transaction_id' => PaymentRecord::where('payment_method', 'wechat')
                    ->where('status', PaymentRecord::STATUS_SUCCESS)
                    ->where(function($query) {
                        $query->whereNull('transaction_id')
                              ->orWhere('transaction_id', '');
                    })
                    ->count(),
            ];

            $stats['completion_rate'] = $stats['total_wechat_payments'] > 0
                ? round(($stats['with_transaction_id'] / $stats['total_wechat_payments']) * 100, 2)
                : 100;

            // 获取最近缺失的记录示例
            $recentMissing = PaymentRecord::where('payment_method', 'wechat')
                ->where('status', PaymentRecord::STATUS_SUCCESS)
                ->where(function($query) {
                    $query->whereNull('transaction_id')
                          ->orWhere('transaction_id', '');
                })
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['id', 'payment_no', 'payment_amount', 'created_at']);

            return [
                'success' => true,
                'statistics' => $stats,
                'recent_missing_examples' => $recentMissing->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('验证交易流水号完整性失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 🔥 兼容性：处理历史订单支付
     */
    private function handleLegacyOrderPayment(Order $order, array $notifyData): array
    {
        Log::info('处理历史订单微信支付回调', [
            'order_id' => $order->id,
            'order_no' => $order->order_no
        ]);

        // 🔥 修复：在线支付订单不需要创建账单
        // 在线支付成功意味着已经收到款项，直接更新订单状态即可

        // 直接更新订单状态为已确认
        $order->update([
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'paid_at' => now(),
            'payment_confirmed_at' => now(),
            'confirmed_at' => now(),
            'payment_success_time' => now(),
            'payment_callback_data' => $notifyData
        ]);

        Log::info('🎉 在线支付订单回调处理完成', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'transaction_id' => $notifyData['transaction_id'] ?? null,
            'amount' => $notifyData['total_fee'] / 100,
            'final_status' => $order->fresh()->status,
            'note' => '在线支付无需创建账单'
        ]);

        return ['code' => 'SUCCESS', 'message' => '在线支付处理成功'];
    }

    // ... 其他辅助方法
    private function generateLinkNo(): string
    {
        return 'PL' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    private function determinePaymentType(Bill $bill, array $options): string
    {
        if (isset($options['correction_id'])) {
            return 'correction_supplement'; // 订单更正补款
        }
        
        if ($bill->pending_amount == $bill->final_amount) {
            return 'full_payment'; // 全额支付
        }
        
        return 'partial_payment'; // 部分支付
    }

    private function getPaymentTypeFromLink(BillingPaymentLink $paymentLink): string
    {
        return match($paymentLink->payment_type) {
            BillingPaymentLink::TYPE_CORRECTION_SUPPLEMENT => PaymentRecord::TYPE_SUPPLEMENT,
            BillingPaymentLink::TYPE_FULL_PAYMENT => PaymentRecord::TYPE_FULL,
            BillingPaymentLink::TYPE_PARTIAL_PAYMENT => PaymentRecord::TYPE_PARTIAL,
            default => PaymentRecord::TYPE_FULL
        };
    }

    private function getPaymentScenarioFromLink(BillingPaymentLink $paymentLink): string
    {
        return match($paymentLink->payment_type) {
            BillingPaymentLink::TYPE_CORRECTION_SUPPLEMENT => PaymentRecord::SCENARIO_CORRECTION_SUPPLEMENT ?? 'correction_supplement',
            default => PaymentRecord::SCENARIO_BILL_PAYMENT ?? 'bill_payment'
        };
    }

    /**
     * 🔥 修复：设置微信支付相关配置
     */
    private function setupWechatPayment(BillingPaymentLink $paymentLink, array $options): void
    {
        // 调用微信支付API创建预支付订单
        // 这里可以集成微信支付SDK
        // 暂时记录日志
        Log::info('设置微信支付配置', [
            'payment_link_id' => $paymentLink->id,
            'amount' => $paymentLink->amount
        ]);
    }

    /**
     * 🔥 修复：设置支付宝支付相关配置
     */
    private function setupAlipayPayment(BillingPaymentLink $paymentLink, array $options): void
    {
        // 调用支付宝API创建预支付订单
        Log::info('设置支付宝支付配置', [
            'payment_link_id' => $paymentLink->id,
            'amount' => $paymentLink->amount
        ]);
    }

    /**
     * 🔥 修复：设置通用支付相关配置
     */
    private function setupGeneralPayment(BillingPaymentLink $paymentLink, array $options): void
    {
        // 通用支付设置
        Log::info('设置通用支付配置', [
            'payment_link_id' => $paymentLink->id,
            'payment_method' => $paymentLink->payment_method
        ]);
    }

    /**
     * 🔥 修复：根据链接编号查找付款链接
     */
    private function getPaymentLinkByNo(string $linkNo): ?BillingPaymentLink
    {
        return BillingPaymentLink::findByLinkNo($linkNo);
    }

    /**
     * 🔥 修复：根据交易号查找付款链接
     */
    private function getPaymentLinkByTradeNo(string $tradeNo): ?BillingPaymentLink
    {
        return BillingPaymentLink::findByTradeNo($tradeNo);
    }

    /**
     * 🔥 修复：标记付款链接为已完成
     */
    private function markPaymentLinkCompleted(string $linkNo, PaymentRecord $paymentRecord): void
    {
        $paymentLink = $this->getPaymentLinkByNo($linkNo);
        if ($paymentLink) {
            $paymentLink->completePayment([
                'amount' => $paymentRecord->payment_amount,
                'transaction_id' => $paymentRecord->transaction_id,
                'external_payment_no' => $paymentRecord->external_payment_no
            ]);

            Log::info('付款链接标记为已完成', [
                'link_no' => $linkNo,
                'payment_record_id' => $paymentRecord->id,
                'payment_link_id' => $paymentLink->id
            ]);
        }
    }

    /**
     * 🔥 新增：取消付款链接（替代旧系统的cancelPaymentLink方法）
     */
    public function cancelPaymentLink(BillingPaymentLink $paymentLink, string $reason = null): void
    {
        $paymentLink->cancel($reason);
        
        Log::info('付款链接已取消', [
            'payment_link_id' => $paymentLink->id,
            'link_no' => $paymentLink->link_no,
            'bill_id' => $paymentLink->bill_id,
            'reason' => $reason
        ]);
    }

    /**
     * 🔥 新增：清理过期付款链接（替代旧系统的cleanupExpiredLinks方法）
     */
    public function cleanupExpiredLinks(): int
    {
        $expiredLinks = BillingPaymentLink::where('status', BillingPaymentLink::STATUS_ACTIVE)
            ->where('expires_at', '<', now())
            ->get();

        $count = 0;
        foreach ($expiredLinks as $link) {
            $link->markExpired();
            $count++;
        }

        Log::info('清理过期付款链接', [
            'cleaned_count' => $count
        ]);

        return $count;
    }

    /**
     * 为已付款订单创建库存事务记录
     *
     * @param \App\Order\Models\Order $order
     * @throws \Exception
     */
    protected function createInventoryTransactionsForPaidOrder($order): void
    {
        // 检查是否已经处理过库存
        if ($order->inventory_processed) {
            Log::info('订单库存已处理，跳过创建库存事务记录', [
                'order_id' => $order->id,
                'order_no' => $order->order_no
            ]);
            return;
        }

        DB::beginTransaction();
        try {
            $totalItemsCount = 0;

            foreach ($order->items as $item) {
                // 🔥 修复：正确获取商品的仓库ID
                $warehouseId = $this->getWarehouseIdForOrderItem($item);

                // 创建库存事务记录
                $transaction = \App\Inventory\Models\InventoryTransaction::create([
                    'product_id' => $item->product_id,
                    'unit_id' => $item->unit_id,
                    'warehouse_id' => $warehouseId,
                    'type' => 'out',
                    'quantity' => $item->quantity,
                    'reference_type' => 'order',
                    'reference_id' => $order->id,
                    'notes' => "微信支付订单出库：{$order->order_no}",
                    'created_by' => 1, // 系统用户
                    'processed_at' => now()
                ]);

                $totalItemsCount++;

                Log::info('创建库存事务记录', [
                    'transaction_id' => $transaction->id,
                    'order_id' => $order->id,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'type' => 'out'
                ]);
            }

            // 标记订单库存已处理
            $order->update([
                'inventory_processed' => true,
                'inventory_method' => 'immediate_on_payment'
            ]);

            DB::commit();

            Log::info('微信支付订单库存事务记录创建完成', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'items_count' => $totalItemsCount,
                'inventory_method' => 'immediate_on_payment'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建库存事务记录失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取订单商品的仓库ID
     *
     * @param \App\Order\Models\OrderItem $item
     * @return int
     */
    protected function getWarehouseIdForOrderItem($item): int
    {
        // 1. 如果订单商品已经有仓库ID，直接使用
        if ($item->warehouse_id) {
            return $item->warehouse_id;
        }

        // 2. 获取商品模型
        $product = \App\Product\Models\Product::find($item->product_id);
        if (!$product) {
            Log::warning('订单商品对应的产品不存在，使用默认仓库', [
                'order_item_id' => $item->id,
                'product_id' => $item->product_id
            ]);
            return 1; // 默认仓库
        }

        // 3. 使用商品的默认仓库
        $defaultWarehouse = $product->getDefaultWarehouse();
        if ($defaultWarehouse) {
            Log::info('使用商品默认仓库', [
                'order_item_id' => $item->id,
                'product_id' => $item->product_id,
                'warehouse_id' => $defaultWarehouse->id,
                'warehouse_name' => $defaultWarehouse->name
            ]);
            return $defaultWarehouse->id;
        }

        // 4. 使用系统默认仓库
        $defaultWarehouseId = config('flycloud.warehouse_assignment.default_warehouse_id', 1);

        Log::warning('商品没有默认仓库，使用系统默认仓库', [
            'order_item_id' => $item->id,
            'product_id' => $item->product_id,
            'default_warehouse_id' => $defaultWarehouseId
        ]);

        return $defaultWarehouseId;
    }
}