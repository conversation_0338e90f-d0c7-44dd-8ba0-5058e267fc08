<?php

namespace App\Billing\Services;

use App\Billing\Models\Bill;
use App\Billing\Models\PaymentRecord;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 统一账单处理服务
 * 
 * 目标：
 * 1. 统一所有支付方式的账单创建逻辑
 * 2. 简化退款补款处理流程
 * 3. 提供清晰的支付处理接口
 */
class UnifiedBillingService
{
    private BillingService $billingService;
    
    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }
    
    /**
     * 统一的账单创建入口
     */
    public function createBillForOrder(Order $order, array $options = []): Bill
    {
        $strategy = $this->getBillCreationStrategy($order);
        
        Log::info('使用统一账单创建服务', [
            'order_id' => $order->id,
            'payment_method' => $order->payment_method,
            'strategy' => $strategy,
            'order_status' => $order->status,
            'correction_status' => $order->correction_status
        ]);
        
        return $this->executeBillCreation($order, $strategy, $options);
    }
    
    /**
     * 统一的支付差额处理入口
     */
    public function processPaymentDifference(OrderCorrection $correction, float $differenceAmount, int $operatedBy): array
    {
        $paymentStrategy = $this->getPaymentStrategy($correction->order->payment_method);
        
        Log::info('使用统一支付差额处理', [
            'correction_id' => $correction->id,
            'payment_method' => $correction->order->payment_method,
            'difference_amount' => $differenceAmount,
            'strategy' => $paymentStrategy
        ]);
        
        return $this->executePaymentDifference($correction, $differenceAmount, $operatedBy, $paymentStrategy);
    }
    
    /**
     * 获取账单创建策略
     */
    private function getBillCreationStrategy(Order $order): string
    {
        // 1. 检查订单确认状态
        if (!$this->isOrderReadyForBilling($order)) {
            return 'pending_confirmation';
        }
        
        // 2. 根据支付方式确定策略
        switch ($order->payment_method) {
            case 'wechat':
            case 'alipay':
                return 'online_payment';
                
            case 'cod':
            case 'cash':
                return 'offline_payment';
                
            case 'balance':
            case 'mixed':
                return 'account_payment';
                
            default:
                return 'standard';
        }
    }
    
    /**
     * 获取支付策略
     */
    private function getPaymentStrategy(string $paymentMethod): string
    {
        switch ($paymentMethod) {
            case 'wechat':
                return 'wechat_refund_supplement';
                
            case 'alipay':
                return 'alipay_refund_supplement';
                
            case 'cod':
            case 'cash':
                return 'direct_adjustment';
                
            case 'balance':
                return 'balance_adjustment';
                
            case 'mixed':
                return 'mixed_adjustment';
                
            default:
                return 'standard_adjustment';
        }
    }
    
    /**
     * 执行账单创建
     */
    private function executeBillCreation(Order $order, string $strategy, array $options): Bill
    {
        switch ($strategy) {
            case 'pending_confirmation':
                throw new \Exception('订单尚未确认，无法创建账单');
                
            case 'online_payment':
                return $this->createOnlinePaymentBill($order, $options);
                
            case 'offline_payment':
                return $this->createOfflinePaymentBill($order, $options);
                
            case 'account_payment':
                return $this->createAccountPaymentBill($order, $options);
                
            default:
                return $this->createStandardBill($order, $options);
        }
    }
    
    /**
     * 执行支付差额处理
     */
    private function executePaymentDifference(OrderCorrection $correction, float $differenceAmount, int $operatedBy, string $strategy): array
    {
        switch ($strategy) {
            case 'wechat_refund_supplement':
                return $this->processWechatDifference($correction, $differenceAmount, $operatedBy);
                
            case 'alipay_refund_supplement':
                return $this->processAlipayDifference($correction, $differenceAmount, $operatedBy);
                
            case 'direct_adjustment':
                return $this->processDirectAdjustment($correction, $differenceAmount, $operatedBy);
                
            case 'balance_adjustment':
                return $this->processBalanceAdjustment($correction, $differenceAmount, $operatedBy);
                
            case 'mixed_adjustment':
                return $this->processMixedAdjustment($correction, $differenceAmount, $operatedBy);
                
            default:
                return $this->processStandardAdjustment($correction, $differenceAmount, $operatedBy);
        }
    }
    
    /**
     * 创建在线支付账单
     */
    private function createOnlinePaymentBill(Order $order, array $options): Bill
    {
        // 在线支付订单的账单创建逻辑
        $billAmount = $order->total;
        
        return $this->billingService->createBillFromConfirmedOrder($order, array_merge($options, [
            'bill_amount' => $billAmount,
            'payment_status' => 'paid', // 在线支付通常已付款
            'notes' => "在线支付订单账单 - {$order->payment_method}"
        ]));
    }
    
    /**
     * 创建线下支付账单
     */
    private function createOfflinePaymentBill(Order $order, array $options): Bill
    {
        // 线下支付订单的账单创建逻辑
        $billAmount = $order->final_payment_amount ?? $order->total;
        
        return $this->billingService->createBillFromConfirmedOrder($order, array_merge($options, [
            'bill_amount' => $billAmount,
            'payment_status' => 'unpaid', // 线下支付需要后续收款
            'notes' => "线下支付订单账单 - {$order->payment_method}"
        ]));
    }
    
    /**
     * 创建账户支付账单
     */
    private function createAccountPaymentBill(Order $order, array $options): Bill
    {
        // 账户支付（余额、混合支付）的账单创建逻辑
        $billAmount = $order->total;
        
        return $this->billingService->createBillFromConfirmedOrder($order, array_merge($options, [
            'bill_amount' => $billAmount,
            'payment_status' => 'paid', // 账户支付通常已付款
            'notes' => "账户支付订单账单 - {$order->payment_method}"
        ]));
    }
    
    /**
     * 创建标准账单
     */
    private function createStandardBill(Order $order, array $options): Bill
    {
        return $this->billingService->createBillFromConfirmedOrder($order, $options);
    }
    
    /**
     * 处理微信支付差额
     */
    private function processWechatDifference(OrderCorrection $correction, float $differenceAmount, int $operatedBy): array
    {
        if ($differenceAmount < 0) {
            // 退款
            return $this->processRefund($correction, abs($differenceAmount), $operatedBy, 'wechat');
        } else {
            // 补款
            return $this->processSupplement($correction, $differenceAmount, $operatedBy, 'wechat');
        }
    }
    
    /**
     * 处理支付宝差额
     */
    private function processAlipayDifference(OrderCorrection $correction, float $differenceAmount, int $operatedBy): array
    {
        if ($differenceAmount < 0) {
            // 退款
            return $this->processRefund($correction, abs($differenceAmount), $operatedBy, 'alipay');
        } else {
            // 补款
            return $this->processSupplement($correction, $differenceAmount, $operatedBy, 'alipay');
        }
    }
    
    /**
     * 处理直接调整（货到付款、现金）
     */
    private function processDirectAdjustment(OrderCorrection $correction, float $differenceAmount, int $operatedBy): array
    {
        // 直接按更正后金额收款，不涉及退款补款
        return [
            'type' => 'direct_adjustment',
            'amount' => $correction->corrected_total,
            'method' => 'direct_payment',
            'message' => '直接按更正后金额收款'
        ];
    }
    
    /**
     * 处理余额调整
     */
    private function processBalanceAdjustment(OrderCorrection $correction, float $differenceAmount, int $operatedBy): array
    {
        if ($differenceAmount < 0) {
            // 退回余额
            return $this->processBalanceRefund($correction, abs($differenceAmount), $operatedBy);
        } else {
            // 扣减余额
            return $this->processBalanceDeduction($correction, $differenceAmount, $operatedBy);
        }
    }
    
    /**
     * 处理混合支付调整
     */
    private function processMixedAdjustment(OrderCorrection $correction, float $differenceAmount, int $operatedBy): array
    {
        // 混合支付的复杂逻辑处理
        return [
            'type' => 'mixed_adjustment',
            'amount' => $differenceAmount,
            'method' => 'mixed_payment',
            'message' => '混合支付调整需要人工处理'
        ];
    }
    
    /**
     * 处理标准调整
     */
    private function processStandardAdjustment(OrderCorrection $correction, float $differenceAmount, int $operatedBy): array
    {
        return [
            'type' => 'standard_adjustment',
            'amount' => $differenceAmount,
            'method' => 'standard',
            'message' => '标准支付调整'
        ];
    }
    
    /**
     * 统一的退款处理
     */
    private function processRefund(OrderCorrection $correction, float $refundAmount, int $operatedBy, string $paymentMethod): array
    {
        // 调用原有的退款逻辑
        return [
            'type' => 'refund',
            'amount' => $refundAmount,
            'method' => $paymentMethod,
            'message' => "通过{$paymentMethod}退款 ¥{$refundAmount}"
        ];
    }
    
    /**
     * 统一的补款处理
     */
    private function processSupplement(OrderCorrection $correction, float $supplementAmount, int $operatedBy, string $paymentMethod): array
    {
        // 调用原有的补款逻辑
        return [
            'type' => 'supplement',
            'amount' => $supplementAmount,
            'method' => $paymentMethod,
            'message' => "通过{$paymentMethod}补款 ¥{$supplementAmount}"
        ];
    }
    
    /**
     * 处理余额退款
     */
    private function processBalanceRefund(OrderCorrection $correction, float $refundAmount, int $operatedBy): array
    {
        return [
            'type' => 'balance_refund',
            'amount' => $refundAmount,
            'method' => 'balance',
            'message' => "退回余额 ¥{$refundAmount}"
        ];
    }
    
    /**
     * 处理余额扣减
     */
    private function processBalanceDeduction(OrderCorrection $correction, float $deductionAmount, int $operatedBy): array
    {
        return [
            'type' => 'balance_deduction',
            'amount' => $deductionAmount,
            'method' => 'balance',
            'message' => "扣减余额 ¥{$deductionAmount}"
        ];
    }
    
    /**
     * 检查订单是否准备好创建账单
     */
    private function isOrderReadyForBilling(Order $order): bool
    {
        // 1. 订单必须是已确认状态
        if (!in_array($order->status, ['confirmed', 'paid', 'shipped', 'delivered'])) {
            return false;
        }
        
        // 2. 如果有更正记录，必须是已确认的更正
        if ($order->corrections()->exists()) {
            return $order->correction_status === 'confirmed';
        }
        
        return true;
    }
}
