<?php

namespace App\Billing\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PaymentRecordResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'payment_no' => $this->payment_no,
            'payment_method' => $this->payment_method,
            'payment_method_text' => $this->payment_method_text,
            'payment_amount' => $this->payment_amount,
            'payment_type' => $this->payment_type,
            'payment_type_text' => $this->payment_type_text,
            'status' => $this->status,
            'status_text' => $this->status_text,

            // 🔥 新增：前端需要的扩展字段
            'payment_scenario' => $this->payment_scenario,
            'original_payment_method' => $this->original_payment_method,
            'count_as_paid' => $this->count_as_paid,
            'correction_id' => $this->correction_id,
            'payment_context' => $this->payment_context,

            // 余额支付信息
            'balance_info' => [
                'balance_used' => $this->balance_used,
                'balance_before' => $this->balance_before,
                'balance_after' => $this->balance_after,
                'has_balance_payment' => $this->payment_method === 'balance' || !empty($this->balance_used),
            ],

            // 🔥 新增：扁平化余额字段（前端直接访问）
            'balance_used' => $this->balance_used,


            'transaction_id' => $this->transaction_id,
            'external_payment_no' => $this->external_payment_no,
            'payment_details' => $this->payment_details,
            'notes' => $this->notes,

            // 时间信息
            'payment_time' => $this->payment_time?->format('Y-m-d H:i:s'),
            'confirmed_at' => $this->confirmed_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),

            // 收款员信息
            'receiver' => $this->whenLoaded('receiver', function () {
                return [
                    'id' => $this->receiver->id,
                    'name' => $this->receiver->name,
                ];
            }),

            'is_mixed_payment' => $this->payment_method === 'mixed',
        ];
    }
} 