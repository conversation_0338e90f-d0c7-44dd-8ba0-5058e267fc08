<?php

namespace App\Billing\Models;

use App\Product\Models\Product;
use App\Order\Models\OrderItem;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BillItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'bill_id',
        'product_id',
        'item_name',
        'item_type',
        'item_description',
        'quantity',
        'unit',
        'unit_price',
        'total_price',
        'discount_amount',
        'final_amount',
        'order_item_id',
        'pricing_details',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'pricing_details' => 'array',
    ];

    // 项目类型常量
    const TYPE_PRODUCT = 'product';
    const TYPE_SERVICE = 'service';
    const TYPE_ADJUSTMENT = 'adjustment';
    const TYPE_FEE = 'fee';
    const TYPE_DISCOUNT = 'discount';

    /**
     * 关联账单
     */
    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class);
    }

    /**
     * 关联商品
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联订单项
     */
    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(OrderItem::class);
    }

    /**
     * 获取项目类型文本
     */
    public function getItemTypeTextAttribute(): string
    {
        return match($this->item_type) {
            self::TYPE_PRODUCT => '商品',
            self::TYPE_SERVICE => '服务',
            self::TYPE_ADJUSTMENT => '调整',
            self::TYPE_FEE => '费用',
            self::TYPE_DISCOUNT => '优惠',
            default => '其他'
        };
    }

    /**
     * 计算总价
     */
    public function calculateTotalPrice(): float
    {
        return $this->quantity * $this->unit_price;
    }

    /**
     * 计算最终金额
     */
    public function calculateFinalAmount(): float
    {
        return $this->total_price - $this->discount_amount;
    }

    /**
     * 更新金额
     */
    public function updateAmounts(): void
    {
        $this->total_price = $this->calculateTotalPrice();
        $this->final_amount = $this->calculateFinalAmount();
        $this->save();
    }

    /**
     * 作用域：按账单筛选
     */
    public function scopeByBill($query, int $billId)
    {
        return $query->where('bill_id', $billId);
    }

    /**
     * 作用域：按商品筛选
     */
    public function scopeByProduct($query, int $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * 作用域：按项目类型筛选
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('item_type', $type);
    }

    /**
     * 作用域：商品项目
     */
    public function scopeProducts($query)
    {
        return $query->where('item_type', self::TYPE_PRODUCT);
    }

    /**
     * 作用域：服务项目
     */
    public function scopeServices($query)
    {
        return $query->where('item_type', self::TYPE_SERVICE);
    }

    /**
     * 作用域：调整项目
     */
    public function scopeAdjustments($query)
    {
        return $query->where('item_type', self::TYPE_ADJUSTMENT);
    }
} 