<?php

namespace App\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Employee\Http\Middleware\CheckEmployeeRole as EmployeeCheckEmployeeRole;

/**
 * 员工角色检查中间件（兼容层）
 * 
 * 这是一个兼容层，代理到新的模块化CheckEmployeeRole中间件
 * 用于保持向后兼容性，以支持旧代码引用
 */
class CheckEmployeeRole
{
    /**
     * 代理的中间件实例
     *
     * @var \App\Employee\Http\Middleware\CheckEmployeeRole
     */
    protected $middleware;

    /**
     * 创建一个新的中间件实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware = new EmployeeCheckEmployeeRole();
    }

    /**
     * 处理传入的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$roles
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // 记录使用旧中间件的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块CheckEmployeeRole中间件', [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->middleware->handle($request, $next, ...$roles);
    }
} 