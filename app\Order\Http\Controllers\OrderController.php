<?php

namespace App\Order\Http\Controllers;

use App\Constants\PaymentMethods;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Order\Models\OrderCorrection;
use App\Product\Models\Product;
use App\Delivery\Models\Delivery;
use App\Models\User;
use App\Order\Services\OrderService;
use App\Order\Services\AutoConfirmService;
use App\Shop\Services\DatabaseBusinessHoursService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    /**
     * 订单服务
     */
    protected $orderService;

    /**
     * 自动确认服务
     */
    protected $autoConfirmService;

    /**
     * 营业时间服务
     */
    protected $businessHoursService;

    /**
     * 构造函数
     */
    public function __construct(
        OrderService $orderService,
        AutoConfirmService $autoConfirmService,
        DatabaseBusinessHoursService $businessHoursService
    ) {
        $this->orderService = $orderService;
        $this->autoConfirmService = $autoConfirmService;
        $this->businessHoursService = $businessHoursService;
    }

    /**
     * 将配置值转换为布尔值
     */
    private function toBool($value): bool
    {
        if (is_bool($value)) return $value;
        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
        }
        return (bool)$value;
    }
    
    /**
     * 获取订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            Log::info('开始获取订单列表', [
                'user_id' => $request->user()?->id,
                'filters' => $request->only(['status', 'keyword', 'start_date', 'end_date', 'date_range', 'sort']),
                'per_page' => $request->input('per_page', 20)
            ]);
            
            $filters = [
                'status' => $request->input('status'),
                'keyword' => $request->input('keyword'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'date_range' => $request->input('date_range'),
                'sort' => $request->input('sort'),
                // 新增的筛选参数
                'delivery_date' => $request->input('delivery_date'),
                'correction_status' => $request->input('correction_status'),
                'correction_type' => $request->input('correction_type'),
                'correctable' => $request->input('correctable'),
                'exclude_cancelled' => $request->input('exclude_cancelled'),
                // 🔥 新增：配送员筛选
                'delivery_person_id' => $request->input('delivery_person_id'),
                // 合并订单相关筛选参数
                'show_merged_orders' => $request->input('show_merged_orders'),
                'only_mergeable' => $request->input('only_mergeable'),
                // 前端请求的特殊参数
                'with_corrections' => $request->input('with_corrections'),
                'with_payment_links' => $request->input('with_payment_links'),
                'calculate_correction_status' => $request->input('calculate_correction_status'),
            ];

            // 🔥 添加CRM专员权限控制
            $user = $request->user();
            if ($user instanceof \App\Employee\Models\Employee && $user->role === 'crm_agent') {
                // CRM专员只能查看分配给自己的客户的订单
                $filters['crm_agent_id'] = $user->id;
            }

            $orders = $this->orderService->getOrders(
                $filters,
                $request->user(),
                $request->input('per_page', 20)
            );
            
            // 🔥 总是计算打印状态，确保前端能正确显示
            $orders->getCollection()->transform(function ($order) {
                // 1. 🔥 添加打印状态信息，解决架构问题
                $order->printStatus = $this->calculatePrintStatus($order);

                return $order;
            });

            // 🔥 内存优化：只在需要时处理复杂数据
            $needsComplexProcessing = $request->input('with_corrections') ||
                                    $request->input('with_payment_links') ||
                                    $request->input('calculate_correction_status');

            if ($needsComplexProcessing) {
                $orders->getCollection()->transform(function ($order) {
                    // 处理合并订单的原订单信息
                    if ($order->is_merged && $order->orderMerge) {
                        // 获取原订单信息
                        $originalOrderIds = $order->orderMerge->original_order_ids;
                        if (!empty($originalOrderIds)) {
                            $originalOrders = \App\Order\Models\Order::whereIn('id', $originalOrderIds)
                                ->select('id', 'order_no')
                                ->get();

                            // 添加到 orderMerge 关联中
                            $order->orderMerge->setRelation('original_orders', $originalOrders);
                        }
                    }

                    return $order;
                });
            }
            
            Log::info('订单列表获取成功', [
                'total' => $orders->total(),
                'current_page' => $orders->currentPage(),
                'per_page' => $orders->perPage()
            ]);
            
            // 🔥 根据访问路径区分用户端和管理端显示
            $isAdminRoute = $request->is('api/admin/*');

            // 🔥 内存优化：简化数据处理，只在需要时进行复杂转换
            $needsUnitConversion = !$isAdminRoute; // 只有用户端需要单位转换

            if ($needsUnitConversion) {
                // 🔥 使用 OrderResource 确保单位数据正确序列化
                $ordersCollection = $orders->getCollection()->map(function ($order) use ($isAdminRoute) {
                    $orderArray = $order->toArray();

                    // 🔥 确保订单项的单位信息被正确序列化
                    if (isset($orderArray['items']) && is_array($orderArray['items'])) {
                        foreach ($orderArray['items'] as &$item) {
                            if (isset($order->items)) {
                                $originalItem = $order->items->firstWhere('id', $item['id']);
                                if ($originalItem) {
                                    // 🔥 用户端显示：如果是转换过的商品，显示原始斤单位
                                    if ($originalItem->isConvertedItem()) {
                                        $userDisplayUnit = $originalItem->getUserDisplayUnit();
                                        $item['quantity'] = $originalItem->getUserDisplayQuantity();
                                        $item['price'] = $originalItem->getUserDisplayPrice();
                                        $item['unit'] = [
                                            'id' => $userDisplayUnit->id,
                                            'name' => $userDisplayUnit->name,
                                            'symbol' => $userDisplayUnit->symbol,
                                        ];
                                        $item['unit_name'] = $userDisplayUnit->name;
                                        $item['unit_symbol'] = $userDisplayUnit->symbol;
                                        $item['is_converted'] = true;
                                    } else {
                                        // 非转换商品，显示原始单位
                                        if ($originalItem->unit) {
                                            $item['unit'] = [
                                                'id' => $originalItem->unit->id,
                                                'name' => $originalItem->unit->name,
                                                'symbol' => $originalItem->unit->symbol,
                                            ];
                                            $item['unit_name'] = $originalItem->unit->name;
                                            $item['unit_symbol'] = $originalItem->unit->symbol;
                                        }
                                        $item['is_converted'] = false;
                                    }
                                }
                            }
                        }
                    }

                    return $orderArray;
                });
            } else {
                // 管理端：直接使用原始数据，减少内存使用
                $ordersCollection = $orders->getCollection();
            }

            // 重新构建分页对象
            $orders->setCollection($ordersCollection);

            return response()->json(ApiResponse::success($orders));
        } catch (\Illuminate\Database\QueryException $e) {
            // 数据库查询异常的特殊处理
            Log::error('订单列表数据库查询失败', [
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'sql' => $e->getSql() ?? 'N/A',
                'bindings' => $e->getBindings() ?? [],
                'user_id' => $request->user()?->id,
                'filters' => $request->only(['status', 'keyword', 'start_date', 'end_date', 'date_range', 'sort'])
            ]);
            
            // 检查是否是连接问题
            if (strpos($e->getMessage(), 'mysql_native_password') !== false || 
                strpos($e->getMessage(), 'Connection refused') !== false ||
                strpos($e->getMessage(), 'server has gone away') !== false) {
                return response()->json(ApiResponse::error('数据库连接异常，请稍后重试', 503), 503);
            }
            
            return response()->json(ApiResponse::error('获取订单列表失败: 数据库查询错误', 500), 500);
        } catch (\Exception $e) {
            Log::error('订单列表获取失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id,
                'filters' => $request->only(['status', 'keyword', 'start_date', 'end_date', 'date_range', 'sort'])
            ]);
            
            return response()->json(ApiResponse::error('获取订单列表失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 创建订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|string|' . PaymentMethods::getValidationRule(),
            'user_address_id' => 'required|integer|exists:user_addresses,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|integer|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
            'region_id' => 'nullable|integer|exists:regions,id',
            // 添加购物车ID参数验证
            'cart_item_ids' => 'nullable|array',
            'cart_item_ids.*' => 'integer|exists:cart_items,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
        }

        // 🔥 新增：营业时间验证
        $businessValidation = $this->businessHoursService->validateOrderTime();
        if (!$businessValidation['valid']) {
            Log::warning('订单创建被拒绝：非营业时间', [
                'user_id' => $request->user()->id,
                'current_time' => $this->businessHoursService->getCurrentTime()->toISOString(),
                'message' => $businessValidation['message'],
                'next_open_time' => $businessValidation['next_open_time']
            ]);

            return response()->json(ApiResponse::error(
                $businessValidation['message'],
                403,
                [
                    'business_hours_error' => true,
                    'next_open_time' => $businessValidation['next_open_time'],
                    'current_time' => $this->businessHoursService->getCurrentTime()->format('Y-m-d H:i:s')
                ]
            ), 403);
        }

        try {
            // 订单数据
            $orderData = [
                'payment_method' => $request->input('payment_method'),
                'user_address_id' => $request->input('user_address_id'),
                'items' => $request->input('items'),
                'notes' => $request->input('notes'),
                'region_id' => $request->input('region_id'),
            ];

            // 记录营业时间验证通过日志
            Log::info('营业时间验证通过，开始创建订单', [
                'user_id' => $request->user()->id,
                'current_time' => $this->businessHoursService->getCurrentTime()->toISOString()
            ]);

            // 创建订单
            $order = $this->orderService->createOrder($orderData, $request->user());
            
            // 触发订单创建事件，传递购物车商品ID
            event(new \App\Order\Events\OrderCreated($order, $request->input('cart_item_ids', [])));
            
            // 记录日志
            Log::info('订单创建成功，已触发清理购物车事件', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'cart_item_ids' => $request->input('cart_item_ids', [])
            ]);
            
            return response()->json(ApiResponse::success($order, '订单创建成功', 200), 201);
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('创建订单失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 创建订单（别名方法，用于API调用）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        return $this->create($request);
    }

    /**
     * 🔥 新增：支付成功回调
     *
     * @param Request $request
     * @param int $id 订单ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function paymentSuccess(Request $request, $id)
    {
        try {
            $paymentInfo = $request->only(['transaction_id', 'payment_time', 'payment_amount']);
            $order = $this->orderService->confirmPayment($id, $paymentInfo);

            return response()->json(ApiResponse::success($order, '支付确认成功'));
        } catch (\Exception $e) {
            Log::error('支付确认失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'payment_info' => $request->all()
            ]);
            return response()->json(ApiResponse::error('支付确认失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 🔥 新增：支付失败回调
     *
     * @param Request $request
     * @param int $id 订单ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function paymentFailed(Request $request, $id)
    {
        try {
            $reason = $request->input('reason', '支付失败');
            $order = $this->orderService->cancelPayment($id, $reason);

            return response()->json(ApiResponse::success($order, '订单已取消'));
        } catch (\Exception $e) {
            Log::error('支付失败处理错误', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'reason' => $request->input('reason')
            ]);
            return response()->json(ApiResponse::error('处理失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 查看订单详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // 查找订单，预加载必要关联，包括配送信息和单位信息
            $order = Order::with([
                'items.product',
                'items.product.mainImage', // 简化：加载完整主图数据
                'items.unit:id,name,symbol', // 🔥 预加载单位信息
                'user:id,name,phone,merchant_name', // 明确指定需要的用户字段，包括商户名字
                'delivery.deliverer.employee',  // 加载配送信息和配送员信息
                'promotionRecords.promotion' // 🔧 新增：加载满减记录和活动信息
            ])->findOrFail($id);
            
            // 检查权限
            $user = $request->user();
            
            // 仅当用户对象存在时检查权限
            if ($user) {
                // 获取用户角色，如果没有role属性，则视为客户
                $userRole = $user->role ?? 'customer';
                
                // 如果是普通客户，只能查看自己的订单
                if ($userRole === 'customer' && $order->user_id !== $user->id) {
                    return response()->json(ApiResponse::error('没有权限查看此订单', 403), 403);
                }
            }
            
            // 预处理订单数据
            $orderData = $order->toArray();

            // 🔥 根据访问路径区分用户端和管理端显示
            $isAdminRoute = $request->is('api/admin/*');

            Log::info('📱 订单详情显示', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'is_admin_route' => $isAdminRoute,
                'route_path' => $request->path(),
                'display_mode' => $isAdminRoute ? '管理端(kg单位)' : '用户端(斤单位)'
            ]);

            // 🔥 确保订单项的单位信息被正确序列化
            if (isset($orderData['items']) && is_array($orderData['items'])) {
                foreach ($orderData['items'] as &$item) {
                    $originalItem = $order->items->firstWhere('id', $item['id']);
                    if ($originalItem) {
                        if ($isAdminRoute) {
                            // 🔥 管理端显示：始终显示系统单位（kg）
                            if ($originalItem->unit) {
                                $item['quantity'] = $originalItem->getSystemQuantity();
                                $item['price'] = $originalItem->getSystemPrice();
                                $item['unit'] = [
                                    'id' => $originalItem->unit->id,
                                    'name' => $originalItem->unit->name,
                                    'symbol' => $originalItem->unit->symbol,
                                ];
                                $item['unit_name'] = $originalItem->unit->name;
                                $item['unit_symbol'] = $originalItem->unit->symbol;
                                $item['is_converted'] = $originalItem->isConvertedItem();
                                if ($originalItem->isConvertedItem()) {
                                    $item['conversion_info'] = $originalItem->getConversionInfo();

                                    Log::info('🏪 管理端显示转换商品', [
                                        'order_id' => $order->id,
                                        'order_item_id' => $originalItem->id,
                                        'product_name' => $originalItem->product_name,
                                        'display_quantity' => $item['quantity'] . 'kg',
                                        'display_price' => '¥' . $item['price'] . '/kg',
                                        'user_original' => $originalItem->getUserDisplayQuantity() . '斤',
                                        'conversion_note' => '管理端显示kg单位'
                                    ]);
                                }
                            }
                        } else {
                            // 🔥 用户端显示：如果是转换过的商品，显示原始斤单位
                            if ($originalItem->isConvertedItem()) {
                                $userDisplayUnit = $originalItem->getUserDisplayUnit();
                                $item['quantity'] = $originalItem->getUserDisplayQuantity();
                                $item['price'] = $originalItem->getUserDisplayPrice();
                                $item['unit'] = [
                                    'id' => $userDisplayUnit->id,
                                    'name' => $userDisplayUnit->name,
                                    'symbol' => $userDisplayUnit->symbol,
                                ];
                                $item['unit_name'] = $userDisplayUnit->name;
                                $item['unit_symbol'] = $userDisplayUnit->symbol;
                                $item['is_converted'] = true;
                                $item['display_note'] = "用户端显示原始斤单位";

                                Log::info('👤 用户端显示转换商品', [
                                    'order_id' => $order->id,
                                    'order_item_id' => $originalItem->id,
                                    'product_name' => $originalItem->product_name,
                                    'display_quantity' => $item['quantity'] . '斤',
                                    'display_price' => '¥' . $item['price'] . '/斤',
                                    'system_stored' => $originalItem->getSystemQuantity() . 'kg',
                                    'conversion_note' => '用户端显示斤单位'
                                ]);
                            } else {
                                // 非转换商品，显示原始单位
                                if ($originalItem->unit) {
                                    $item['unit'] = [
                                        'id' => $originalItem->unit->id,
                                        'name' => $originalItem->unit->name,
                                        'symbol' => $originalItem->unit->symbol,
                                    ];
                                    $item['unit_name'] = $originalItem->unit->name;
                                    $item['unit_symbol'] = $originalItem->unit->symbol;
                                }
                                $item['is_converted'] = false;
                            }
                        }
                    }
                }
            }

            // 格式化金额字段（避免前端显示问题）
            $orderData['total'] = (float)$order->total;
            $orderData['subtotal'] = (float)($order->subtotal ?? $order->total);
            $orderData['discount'] = (float)($order->discount ?? 0);
            $orderData['original_total'] = (float)($order->original_total ?? $order->total);
            
            // 添加完整的优惠信息
            $orderData['discount_info'] = $order->getDiscountInfo();
            $orderData['payment_discount_info'] = $order->getPaymentDiscountInfo();
            
            // 添加优惠明细（用于管理后台显示）
            $orderData['discount_details'] = $this->formatDiscountDetails($order);
            
            // 添加状态文本
            $orderData['status_text'] = $order->getStatusTextAttribute();
            
            // 如果是货到付款订单，添加相关信息
            if ($order->isCashOnDelivery()) {
                $orderData['is_cod'] = true;
                $orderData['cod_status_text'] = $order->getCodStatusTextAttribute();
            }
            
            // 如果有配送信息，添加配送相关文本
            if ($order->delivery_method) {
                $orderData['delivery_method_text'] = $order->getDeliveryMethodTextAttribute();
                $orderData['delivery_time_text'] = $order->getDeliveryTimeTextAttribute();
            }
            
            // 添加配送员信息（如果有配送记录）
            if ($order->delivery && $order->delivery->deliverer && $order->delivery->deliverer->employee) {
                $orderData['deliverer_info'] = [
                    'id' => $order->delivery->deliverer->employee->id,
                    'name' => $order->delivery->deliverer->employee->name,
                    'phone' => $order->delivery->deliverer->employee->phone,
                    'delivery_status' => $order->delivery->status,
                ];
            }
            
            return response()->json(ApiResponse::success($orderData));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(ApiResponse::error('订单不存在', 404), 404);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取订单详情失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('获取订单详情失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 格式化优惠明细（用于管理后台显示）
     *
     * @param Order $order
     * @return array
     */
    private function formatDiscountDetails(Order $order): array
    {
        $details = [
            'has_discount' => false,
            'original_total' => round((float)($order->original_total ?? $order->total), 2),
            'final_total' => round((float)$order->total, 2),
            'total_discount_amount' => 0,
            'discount_breakdown' => [],
            'discount_summary' => []
        ];

        // 计算总优惠金额
        $totalDiscount = ($order->discount ?? 0) + ($order->payment_discount ?? 0);
        if ($order->original_total && $order->original_total > $order->total) {
            $totalDiscount = $order->original_total - $order->total;
        }

        $details['total_discount_amount'] = round((float)$totalDiscount, 2);
        $details['has_discount'] = $totalDiscount > 0;
        
        // 解析价格计算详情
        if ($order->pricing_info && is_array($order->pricing_info)) {
            foreach ($order->pricing_info as $index => $detail) {
                $discountType = $detail['type'] ?? 'unknown';
                $discountName = $detail['name'] ?? '未知优惠';
                $discountAmount = (float)($detail['discount_amount'] ?? 0);
                
                if ($discountAmount > 0) {
                    $formattedDetail = [
                        'id' => $index + 1,
                        'type' => $discountType,
                        'name' => $discountName,
                        'amount' => $discountAmount,
                        'description' => $this->getDiscountDescription($detail),
                        'color' => $this->getDiscountColor($discountType),
                        'icon' => $this->getDiscountIcon($discountType)
                    ];
                    
                    $details['discount_breakdown'][] = $formattedDetail;
                    
                    // 添加到汇总中
                    $categoryKey = $this->getDiscountCategory($discountType);
                    if (!isset($details['discount_summary'][$categoryKey])) {
                        $details['discount_summary'][$categoryKey] = [
                            'category' => $categoryKey,
                            'name' => $this->getDiscountCategoryName($categoryKey),
                            'total_amount' => 0,
                            'count' => 0,
                            'color' => $this->getDiscountColor($discountType)
                        ];
                    }
                    $details['discount_summary'][$categoryKey]['total_amount'] += $discountAmount;
                    $details['discount_summary'][$categoryKey]['count']++;
                }
            }
        }
        
        // 添加支付优惠
        if ($order->payment_discount > 0) {
            $paymentDiscountDetail = [
                'id' => count($details['discount_breakdown']) + 1,
                'type' => 'payment_discount',
                'name' => '支付优惠',
                'amount' => (float)$order->payment_discount,
                'description' => $this->getPaymentDiscountDescription($order),
                'color' => 'orange',
                'icon' => 'payment'
            ];
            
            $details['discount_breakdown'][] = $paymentDiscountDetail;
            
            // 添加到汇总
            $details['discount_summary']['payment'] = [
                'category' => 'payment',
                'name' => '支付优惠',
                'total_amount' => (float)$order->payment_discount,
                'count' => 1,
                'color' => 'orange'
            ];
        }
        
        // 转换汇总为数组
        $details['discount_summary'] = array_values($details['discount_summary']);
        
        return $details;
    }
    
    /**
     * 获取优惠描述
     */
    private function getDiscountDescription(array $detail): string
    {
        $type = $detail['type'] ?? '';
        $amount = $detail['discount_amount'] ?? 0;
        
        switch ($type) {
            case 'region_price':
                return "区域价格优惠 ¥{$amount}";
            case 'product_member_discount':
                return "商品会员折扣 ¥{$amount}";
            case 'category_member_discount':
                return "分类会员折扣 ¥{$amount}";
            case 'global_member_discount':
                $levelName = $detail['name'] ?? '会员';
                return "{$levelName} ¥{$amount}";
            case 'category_region_discount':
                return "区域优惠 ¥{$amount}";
            default:
                return "优惠 ¥{$amount}";
        }
    }
    
    /**
     * 获取优惠颜色
     */
    private function getDiscountColor(string $type): string
    {
        $colorMap = [
            'region_price' => 'blue',
            'product_member_discount' => 'gold',
            'category_member_discount' => 'gold',
            'global_member_discount' => 'gold',
            'category_region_discount' => 'green',
            'payment_discount' => 'orange'
        ];
        
        return $colorMap[$type] ?? 'default';
    }
    
    /**
     * 获取优惠图标
     */
    private function getDiscountIcon(string $type): string
    {
        $iconMap = [
            'region_price' => 'location',
            'product_member_discount' => 'crown',
            'category_member_discount' => 'crown',
            'global_member_discount' => 'crown',
            'category_region_discount' => 'tag',
            'payment_discount' => 'payment'
        ];
        
        return $iconMap[$type] ?? 'discount';
    }
    
    /**
     * 获取优惠分类
     */
    private function getDiscountCategory(string $type): string
    {
        if (in_array($type, ['product_member_discount', 'category_member_discount', 'global_member_discount'])) {
            return 'member';
        }
        if (in_array($type, ['region_price', 'category_region_discount'])) {
            return 'region';
        }
        return 'other';
    }
    
    /**
     * 获取优惠分类名称
     */
    private function getDiscountCategoryName(string $category): string
    {
        $nameMap = [
            'member' => '会员优惠',
            'region' => '区域优惠',
            'payment' => '支付优惠',
            'other' => '其他优惠'
        ];
        
        return $nameMap[$category] ?? '未知优惠';
    }
    
    /**
     * 获取支付优惠描述
     */
    private function getPaymentDiscountDescription(Order $order): string
    {
        $paymentMethod = $order->payment_method;
        $amount = $order->payment_discount;
        
        $methodNames = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'bank' => '银行转账',
            'cash' => '现金支付'
        ];
        
        $methodName = $methodNames[$paymentMethod] ?? '支付';
        return "{$methodName}优惠 ¥{$amount}";
    }
    
    /**
     * 🔥 计算订单打印状态，解决前端需要额外API调用的架构问题
     * 
     * @param Order $order
     * @return array
     */
    private function calculatePrintStatus(Order $order): array
    {
        // 如果没有打印记录，返回默认状态
        if (!$order->printRecords || $order->printRecords->isEmpty()) {
            return [
                'receipt' => [
                    'printed' => false,
                    'count' => 0,
                    'completedCount' => 0,
                    'failedCount' => 0,
                    'lastPrintedAt' => null,
                    'lastPrintedBy' => null
                ],
                'delivery' => [
                    'printed' => false,
                    'count' => 0,
                    'completedCount' => 0,
                    'failedCount' => 0,
                    'lastPrintedAt' => null,
                    'lastPrintedBy' => null
                ]
            ];
        }
        
        // 按打印类型分组统计
        $printTypes = ['receipt', 'delivery'];
        $status = [];
        
        foreach ($printTypes as $type) {
            $typeRecords = $order->printRecords->where('print_type', $type);
            $completedRecords = $typeRecords->where('status', 'completed');
            $failedRecords = $typeRecords->where('status', 'failed');
            $lastCompleted = $completedRecords->sortByDesc('printed_at')->first();
            
            // 尝试从员工表查找打印人，如果没有则从用户表查找
            $lastPrintedBy = null;
            if ($lastCompleted && $lastCompleted->printed_by) {
                $employee = \App\Employee\Models\Employee::find($lastCompleted->printed_by);
                if ($employee) {
                    $lastPrintedBy = $employee->name;
                } else {
                    $user = \App\Models\User::find($lastCompleted->printed_by);
                    $lastPrintedBy = $user ? $user->name : null;
                }
            }
            
            $status[$type] = [
                'printed' => $completedRecords->count() > 0,
                'count' => $typeRecords->count(),
                'completedCount' => $completedRecords->count(),
                'failedCount' => $failedRecords->count(),
                'lastPrintedAt' => $lastCompleted ? $lastCompleted->printed_at : null,
                'lastPrintedBy' => $lastPrintedBy
            ];
        }
        
        return $status;
    }
    
    /**
     * 更新订单状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,confirmed,preparing,ready,dispatched,delivered,correcting,completed,cancelled,refunded,paid,shipped',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = $request->user();
        $order = Order::findOrFail($id);
        
        // 🔐 权限检查：与其他API保持一致
        if ($user instanceof \App\Employee\Models\Employee) {
            // 员工系统的权限控制 - 与getStats等API保持一致
            switch ($user->role) {
                case 'admin':
                case 'manager':
                    // 管理员和经理有全部权限，无需额外检查
                    break;

                case 'staff':
                case 'warehouse_manager':
                case 'crm_agent':
                case 'delivery':
                    // 其他员工角色根据具体权限配置检查
                    $allowedStatuses = $this->getAllowedStatusesByRole($user->role, $order->status);
                    if (!in_array($request->status, $allowedStatuses)) {
                        return response()->json(ApiResponse::error("当前员工角色({$user->role})无权将订单从{$order->status}更新为{$request->status}", 400), 400);
                    }
                    break;

                default:
                    return response()->json(ApiResponse::error('无权限更新订单状态', 403), 403);
            }
        } else {
            // 普通用户权限检查
            if ($user->role === 'customer') {
                // 普通客户只能更新自己的订单
                if ($order->user_id !== $user->id) {
                    return response()->json(ApiResponse::error('没有权限更新此订单', 403), 403);
                }
                // 普通用户只能将订单从 pending 更新为 paid
                if ($order->status !== 'pending' || $request->status !== 'paid') {
                    return response()->json(ApiResponse::error('无法更新到此状态', 400), 400);
                }
            } else {
                return response()->json(ApiResponse::error('无权限更新订单状态', 403), 403);
            }
        }
        
        DB::beginTransaction();
        try {
            // 更新订单状态
            $oldStatus = $order->status;
            $newStatus = $request->status;
            $order->status = $newStatus;
            
            // 🥬 根据状态变更设置相应的时间戳（生鲜配送优化版）
            if ($oldStatus !== $newStatus) {
                switch ($newStatus) {
                    case 'confirmed':
                        $order->confirmed_at = now();
                        break;
                    case 'preparing':
                        $order->preparing_at = now();
                        break;
                    case 'ready':
                        $order->ready_at = now();
                        break;
                    case 'dispatched':
                        $order->dispatched_at = now();
                        break;
                    case 'delivered':
                        $order->delivered_at = now();
                        break;
                    case 'completed':
                        $order->completed_at = now();
                        break;
                    case 'cancelled':
                        $order->cancelled_at = now();
                        $order->cancelled_by = $user->id;
                        break;
                    // 兼容旧状态
                    case 'paid':
                        $order->paid_at = now();
                        break;
                    case 'shipped':
                        $order->shipped_at = now();
                        break;
                }
            }
            
            $order->save();
            
            // 🤖 自动确认逻辑：所有订单都自动确认，无需后端人工操作
            if ($oldStatus === 'pending' && $newStatus === 'paid') {
                // 在线支付完成后直接自动确认，无需人工干预
                $order->status = 'confirmed';
                $order->confirmed_at = now();
                $newStatus = 'confirmed'; // 更新新状态

                Log::info('订单自动确认（无需人工操作）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'payment_method' => $order->payment_method,
                    'auto_confirmed_at' => $order->confirmed_at,
                    'reason' => '系统设置为自动确认，无需后端操作'
                ]);
            }

            // 🔥 移除自动确认逻辑：所有订单都需要人工确认
            // 不再自动确认货到付款订单

            // 如果订单状态变为已付款或已确认，创建配送记录
            if (($oldStatus !== 'paid' && $newStatus === 'paid') ||
                ($oldStatus !== 'confirmed' && $newStatus === 'confirmed')) {
                // 检查是否已存在配送记录
                $existingDelivery = Delivery::where('order_id', $order->id)->first();
                if (!$existingDelivery) {
                    // 获取用户的默认配送员
                    $user = User::find($order->user_id);
                    $delivererId = null;
                    
                    // 如果用户设置了默认配送员，则自动分配
                    if ($user && $user->default_employee_deliverer_id) {
                        // 查找对应的deliverer记录
                        $deliverer = \App\Delivery\Models\Deliverer::where('employee_id', $user->default_employee_deliverer_id)->first();
                        if ($deliverer) {
                            $delivererId = $deliverer->id;
                            Log::info('订单自动分配给默认配送员', [
                                'order_id' => $order->id,
                                'order_no' => $order->order_no,
                                'user_id' => $user->id,
                                'employee_id' => $user->default_employee_deliverer_id,
                                'deliverer_id' => $delivererId
                            ]);
                        }
                    }
                    
                    Delivery::create([
                        'order_id' => $order->id,
                        'status' => 'pending', // 待分配配送员
                        'deliverer_id' => $delivererId, // 自动分配用户的默认配送员
                    ]);
                }
            }
            
            DB::commit();
            return response()->json(ApiResponse::success($order, '订单状态更新成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('更新订单状态失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 确认收货（用户确认订单完成）
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request, $id)
    {
        $user = $request->user();
        $order = Order::findOrFail($id);

        // 🥬 权限检查：只有订单所属用户可以确认收货
        if ($order->user_id !== $user->id) {
            return response()->json(ApiResponse::error('没有权限确认此订单', 403), 403);
        }

        // 🥬 状态检查：只有已送达的订单可以确认收货
        if ($order->status !== 'delivered') {
            return response()->json(ApiResponse::error('只有已送达的订单可以确认收货', 400), 400);
        }

        DB::beginTransaction();
        try {
            // 🥬 生鲜配送特点：delivered就是最终状态，确认收货只是更新时间戳
            $order->confirmed_at = now();
            $order->confirmed_by = $user->id;
            $order->save();

            Log::info('用户确认收货', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $user->id,
                'confirmed_at' => $order->confirmed_at,
                'payment_method' => $order->payment_method,
            ]);

            DB::commit();

            return response()->json(ApiResponse::success([
                'order' => $order,
                'message' => '确认收货成功，感谢您的购买！'
            ], '确认收货成功'));

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('确认收货失败', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json(ApiResponse::error('确认收货失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 🤖 手动触发自动确认
     *
     * @param Request $request
     * @param int $id 订单ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function autoConfirm(Request $request, $id)
    {
        $user = $request->user();
        $order = Order::findOrFail($id);

        // 🔐 权限检查：只有管理员可以手动触发自动确认
        $employeeRole = $user->getEmployeeRole();
        if (!in_array($employeeRole, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('无权限执行自动确认操作', 403), 403);
        }

        try {
            // 🤖 执行自动确认
            $result = $this->autoConfirmService->executeAutoConfirm($order, 'manual');

            if ($result) {
                $order->refresh();

                Log::info('手动触发自动确认成功', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'operator_id' => $user->id,
                    'operator_name' => $user->name
                ]);

                return response()->json(ApiResponse::success([
                    'order' => $order,
                    'message' => '自动确认成功'
                ], '订单已自动确认'));
            } else {
                // 获取不能自动确认的原因
                $checkResult = $this->autoConfirmService->canAutoConfirm($order);

                return response()->json(ApiResponse::error(
                    '自动确认失败：' . $checkResult['reason'],
                    400
                ), 400);
            }

        } catch (\Exception $e) {
            Log::error('手动触发自动确认失败', [
                'order_id' => $order->id,
                'operator_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(ApiResponse::error('自动确认失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 🔥 新增：管理员人工确认订单（创建出库单）
     *
     * @param Request $request
     * @param int $id 订单ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function manualConfirm(Request $request, $id)
    {
        $user = $request->user();
        $order = Order::findOrFail($id);

        // 🔐 权限检查：只有员工可以人工确认订单
        $employeeRole = $user->role;
        if (!in_array($employeeRole, ['admin', 'manager', 'staff'])) {
            return response()->json(ApiResponse::error('无权限执行人工确认操作', 403), 403);
        }

        // 🔍 状态检查：支持多种状态的订单确认
        $allowedStatuses = ['paid', 'pending']; // 支持已支付和待支付（货到付款、代客下单）订单
        if (!in_array($order->status, $allowedStatuses)) {
            return response()->json(ApiResponse::error('订单状态不允许确认，当前状态：' . $order->status . '，允许状态：' . implode(', ', $allowedStatuses), 400), 400);
        }

        // 🔥 代客下单订单特殊处理：无论支付方式都可以在pending状态确认
        if ($order->source === 'proxy') {
            if ($order->status !== 'pending') {
                return response()->json(ApiResponse::error('代客下单订单只能在待确认状态下确认，当前状态：' . $order->status, 400), 400);
            }
        } else {
            // 🔍 普通订单的支付方式检查
            if ($order->payment_method === 'cod' && $order->status !== 'pending') {
                return response()->json(ApiResponse::error('货到付款订单只能在待支付状态下确认，当前状态：' . $order->status, 400), 400);
            }

            if ($order->payment_method !== 'cod' && $order->status !== 'paid') {
                return response()->json(ApiResponse::error('在线支付订单只能在已支付状态下确认，当前状态：' . $order->status, 400), 400);
            }
        }

        DB::beginTransaction();
        try {
            // 1. 更新订单状态为已确认
            $order->update([
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'confirmed_by' => $user->id,
            ]);

            // 2. 🔥 出库单创建统一由Observer处理，避免重复创建
            Log::info('订单人工确认完成，出库单将由Observer统一处理', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'order_source' => $order->source, // 🔥 记录订单来源
                'is_proxy_order' => $order->source === 'proxy', // 🔥 标识是否为代客下单
                'created_by_employee' => $order->created_by_id, // 🔥 代客下单员工ID
                'confirmed_by' => $user->id,
                'confirmed_by_name' => $user->name,
                'payment_method' => $order->payment_method,
                'client_id' => $order->user_id,
                'outbound_handler' => 'OrderObserver::tryCreateOutboundDocumentOnConfirm'
            ]);

            // 4. 🔥 打印统一由Observer处理，避免重复打印
            Log::info('手动确认订单完成，打印将由Observer统一处理', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'confirmed_by' => $user->id,
                'confirmed_by_name' => $user->name,
                'print_handler' => 'OrderObserver::triggerWarehousePrint'
            ]);

            DB::commit();

            Log::info('管理员人工确认订单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'confirmed_by' => $user->id,
                'confirmed_by_name' => $user->name,
                'confirmed_at' => $order->confirmed_at,
                'print_handler' => 'OrderObserver'
            ]);

            $message = '订单确认成功，出库单和打印将自动处理';

            return response()->json(ApiResponse::success([
                'order' => $order->fresh(),
                'message' => $message
            ], '订单人工确认成功'));

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('管理员人工确认订单失败', [
                'order_id' => $order->id,
                'confirmed_by' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(ApiResponse::error('人工确认订单失败: ' . $e->getMessage(), 500), 500);
        }
    }



    /**
     * 🤖 批量自动确认
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchAutoConfirm(Request $request)
    {
        $user = $request->user();

        // 🔐 权限检查：只有管理员可以执行批量自动确认
        $employeeRole = $user->getEmployeeRole();
        if (!in_array($employeeRole, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('无权限执行批量自动确认操作', 403), 403);
        }

        $limit = $request->input('limit', 50); // 默认处理50个订单

        try {
            // 🤖 执行批量自动确认
            $stats = $this->autoConfirmService->batchAutoConfirm($limit);

            Log::info('批量自动确认完成', [
                'operator_id' => $user->id,
                'operator_name' => $user->name,
                'stats' => $stats
            ]);

            return response()->json(ApiResponse::success($stats, '批量自动确认完成'));

        } catch (\Exception $e) {
            Log::error('批量自动确认失败', [
                'operator_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(ApiResponse::error('批量自动确认失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 🔧 获取自动确认配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAutoConfirmConfig(Request $request)
    {
        $user = $request->user();

        // 🔐 权限检查：只有管理员可以查看配置
        $employeeRole = $user->getEmployeeRole();
        if (!in_array($employeeRole, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('无权限查看自动确认配置', 403), 403);
        }

        $config = $this->autoConfirmService->getAutoConfirmConfig();

        return response()->json(ApiResponse::success([
            'config' => $config,
            'description' => [
                'online_payment_auto_confirm' => '在线支付自动确认',
                'cod_auto_confirm' => '货到付款自动确认',
                'vip_customer_auto_confirm' => 'VIP客户自动确认',
                'new_customer_auto_confirm' => '新客户自动确认',
                'repeat_customer_auto_confirm' => '老客户自动确认',
                'small_order_auto_confirm' => '小额订单自动确认',
                'large_order_auto_confirm' => '大额订单自动确认',
                'small_order_threshold' => '小额订单阈值（元）',
                'large_order_threshold' => '大额订单阈值（元）',
                'business_hours_only' => '仅营业时间自动确认',
                'business_start_hour' => '营业开始时间',
                'business_end_hour' => '营业结束时间',
                'weekend_auto_confirm' => '周末自动确认',
                'holiday_auto_confirm' => '节假日自动确认'
            ]
        ], '获取自动确认配置成功'));
    }

    /**
     * 根据员工角色获取允许的状态更新列表
     *
     * @param string $role 员工角色
     * @param string $currentStatus 当前订单状态
     * @return array 允许的目标状态列表
     */
    private function getAllowedStatusesByRole(string $role, string $currentStatus): array
    {
        // 🥬 生鲜配送员工权限配置
        $rolePermissions = [
            'admin' => [
                // 管理员：全权限
                'pending' => ['confirmed', 'paid', 'cancelled'],
                'confirmed' => ['preparing', 'cancelled'],
                'preparing' => ['ready'],
                'ready' => ['dispatched'],
                'dispatched' => ['delivered'],
                'delivered' => ['completed', 'correcting'],
                'correcting' => ['completed'],
                'paid' => ['shipped', 'cancelled'], // 兼容旧状态
                'shipped' => ['delivered'],
            ],
            'manager' => [
                // 经理：除系统配置外的全权限
                'pending' => ['confirmed', 'paid', 'cancelled'],
                'confirmed' => ['preparing', 'cancelled'],
                'preparing' => ['ready'],
                'ready' => ['dispatched'],
                'dispatched' => ['delivered'],
                'delivered' => ['completed', 'correcting'],
                'correcting' => ['completed'],
                'paid' => ['shipped', 'cancelled'],
                'shipped' => ['delivered'],
            ],
            'staff' => [
                // 员工：基础操作权限
                'pending' => ['confirmed', 'cancelled'],
                'confirmed' => ['preparing'],
                'preparing' => ['ready'],
                'paid' => ['shipped'],
                'shipped' => ['delivered'],
            ],
            'warehouse_manager' => [
                // 仓库管理员：备货和发货权限
                'confirmed' => ['preparing'],
                'preparing' => ['ready'],
                'ready' => ['dispatched'],
                'paid' => ['shipped'],
                'shipped' => ['delivered'],
                'dispatched' => ['delivered'],
            ],
            'delivery' => [
                // 配送员：配送相关权限
                'ready' => ['dispatched'],
                'dispatched' => ['delivered'],
                'shipped' => ['delivered'], // 兼容旧状态
            ],
            'crm_agent' => [
                // CRM专员：客户服务权限
                'pending' => ['confirmed', 'cancelled'],
                'delivered' => ['correcting'],
                'correcting' => ['completed'],
            ],
        ];

        return $rolePermissions[$role][$currentStatus] ?? [];
    }

    /**
     * 取消订单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        $user = $request->user();
        $order = Order::with('items.product')->findOrFail($id);
        
        // 普通用户只能取消自己的订单
        if ($user->role === 'customer' && $order->user_id !== $user->id) {
            return response()->json(ApiResponse::error('没有权限取消此订单', 403), 403);
        }
        
        // 🔥 修复：允许待付款、待确认、已付款的订单取消（与前端UI保持一致）
        if (!in_array($order->status, ['pending_payment', 'pending', 'paid'])) {
            return response()->json(ApiResponse::error('只有待付款、待确认或已付款的订单可以取消', 400), 400);
        }
        
        DB::beginTransaction();
        try {
            // 检查是否需要退款
            $needsRefund = ($order->payment_method === 'wechat' && $order->status === 'paid');
            $refundAmount = $needsRefund ? $order->total : 0;

            Log::info('订单取消退款条件检查', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'payment_method' => $order->payment_method,
                'status' => $order->status,
                'paid_at' => $order->paid_at,
                'needs_refund' => $needsRefund,
                'refund_amount' => $refundAmount,
                'note' => '暂时跳过自动退款，需要手动处理'
            ]);

            $order->status = 'cancelled';
            $order->cancelled_at = now();
            $order->cancelled_by = $user->id; // 记录取消操作人
            $order->save();

            // 🔥 处理自动退款
            if ($needsRefund) {
                Log::info('开始处理订单自动退款', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'refund_amount' => $refundAmount,
                    'operator_id' => $user->id
                ]);

                try {
                    $this->processFullRefund($order, $user->id);
                    Log::info('订单自动退款处理完成', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no
                    ]);
                } catch (\Exception $e) {
                    Log::error('订单自动退款处理失败', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // 退款失败不影响订单取消，只记录错误
                }
            }

            // 库存恢复将通过 OrderStatusChanged 事件自动处理
            // 移除重复的库存处理代码，统一由事件监听器处理

            DB::commit();

            Log::info('订单取消成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'cancelled_by' => $user->id,
                'user_role' => $user->role,
                'refund_amount' => $refundAmount,
                'needs_refund' => $needsRefund
            ]);

            $message = $needsRefund ?
                "订单已取消，退款金额 ¥{$refundAmount} 将在1-3个工作日内到账" :
                '订单已取消';

            return response()->json(ApiResponse::success([
                'refund_amount' => $refundAmount,
                'needs_refund' => $needsRefund
            ], $message));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('订单取消失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => $user->id
            ]);
            return response()->json(ApiResponse::error('取消订单失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 处理订单取消的全额退款
     *
     * @param Order $order
     * @param int $operatorId
     * @return void
     */
    private function processFullRefund(Order $order, int $operatorId): void
    {
        try {
            Log::info('开始处理订单取消退款', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'refund_amount' => $order->total,
                'payment_method' => $order->payment_method,
                'operator_id' => $operatorId
            ]);

            // 查找原始支付记录
            $originalPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
                ->where('trade_state', 'SUCCESS')
                ->first();

            if (!$originalPayment) {
                throw new \Exception('未找到原始支付记录，无法执行退款');
            }

            // 🔥 检查是否已经有退款记录
            $existingRefund = \App\WechatPayment\Models\WechatServiceRefund::where('order_id', $order->id)
                ->whereIn('refund_status', ['SUCCESS', 'PROCESSING'])
                ->first();

            if ($existingRefund) {
                Log::warning('订单已存在退款记录，跳过重复退款', [
                    'order_id' => $order->id,
                    'existing_refund_id' => $existingRefund->id,
                    'refund_status' => $existingRefund->refund_status,
                    'out_refund_no' => $existingRefund->out_refund_no
                ]);
                return; // 直接返回，不抛出异常
            }

            Log::info('找到原始支付记录', [
                'payment_id' => $originalPayment->id,
                'transaction_id' => $originalPayment->transaction_id,
                'out_trade_no' => $originalPayment->out_trade_no,
                'total_fee' => $originalPayment->total_fee,
                'order_total' => $order->total,
                'refund_amount' => $order->total,
                'amounts_match' => ($originalPayment->total_fee == ($order->total * 100)) // 微信金额是分
            ]);

            // 获取微信支付服务商配置
            $provider = \App\WechatPayment\Models\WechatServiceProvider::where('is_active', 1)->first();
            if (!$provider) {
                throw new \Exception('微信支付服务商配置不可用');
            }

            // 获取子商户配置（如果有）
            $subMerchant = null;
            if ($originalPayment->sub_merchant_id) {
                $subMerchant = \App\WechatPayment\Models\WechatSubMerchant::find($originalPayment->sub_merchant_id);
            }

            // 创建微信退款服务
            $refundService = new \App\WechatPayment\Services\WechatRefundService($provider, $subMerchant);

            // 执行退款
            $refund = $refundService->processRefund([
                'order_id' => $order->id,
                'refund_amount' => $order->total,
                'refund_reason' => '用户取消订单，全额退款',
                'operator_id' => $operatorId,
            ]);

            Log::info('订单取消退款处理成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'refund_amount' => $order->total,
                'refund_id' => $refund->id,
                'out_refund_no' => $refund->out_refund_no,
                'operator_id' => $operatorId
            ]);

        } catch (\Exception $e) {
            Log::error('订单取消退款处理失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'refund_amount' => $order->total,
                'operator_id' => $operatorId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常，让上层处理
            throw new \Exception('退款处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 通知退款失败，需要人工处理
     *
     * @param Order $order
     * @param string $errorMessage
     * @return void
     */
    private function notifyRefundFailure(Order $order, string $errorMessage): void
    {
        // 这里可以发送通知给管理员或创建待处理任务
        Log::critical('订单取消退款失败，需要人工处理', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'user_id' => $order->user_id,
            'refund_amount' => $order->total,
            'error' => $errorMessage,
            'created_at' => now()
        ]);

        // TODO: 可以在这里添加发送邮件、短信或系统通知的逻辑
    }

    /**
     * 获取订单的支付记录
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentRecords($id): JsonResponse
    {
        try {
            $order = Order::findOrFail($id);

            // 查找订单对应的账单
            $bill = \App\Billing\Models\Bill::where('order_id', $order->id)->first();

            if (!$bill) {
                return response()->json(ApiResponse::success([], '该订单暂无支付记录'));
            }

            // 获取支付记录
            $paymentRecords = \App\Billing\Models\PaymentRecord::where('bill_id', $bill->id)
                ->with(['bill'])
                ->orderBy('created_at', 'desc')
                ->get();

            // 获取微信支付记录
            $wechatPayments = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
                ->orderBy('created_at', 'desc')
                ->get();

            // 获取微信退款记录
            $wechatRefunds = \App\WechatPayment\Models\WechatServiceRefund::where('order_id', $order->id)
                ->orderBy('created_at', 'desc')
                ->get();

            $data = [
                'order' => [
                    'id' => $order->id,
                    'order_no' => $order->order_no,
                    'total' => $order->total,
                    'payment_method' => $order->payment_method,
                    'payment_status' => $order->payment_status,
                    'status' => $order->status
                ],
                'bill' => $bill ? [
                    'id' => $bill->id,
                    'total_amount' => $bill->total_amount,
                    'paid_amount' => $bill->paid_amount,
                    'status' => $bill->status
                ] : null,
                'payment_records' => $paymentRecords,
                'wechat_payments' => $wechatPayments,
                'wechat_refunds' => $wechatRefunds
            ];

            return response()->json(ApiResponse::success($data, '获取支付记录成功'));

        } catch (\Exception $e) {
            Log::error('获取订单支付记录失败', [
                'order_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json(ApiResponse::error('获取支付记录失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取待确认订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPendingConfirmationOrders(Request $request)
    {
        try {
            $filters = [
                'pending_confirmation' => true, // 只获取需要人工确认的订单
                'per_page' => $request->input('per_page', 20),
                'keyword' => $request->input('keyword'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ];

            $orders = $this->orderService->getOrders($filters, $request->user());

            // 添加额外的确认信息
            $orders->getCollection()->transform(function ($order) {
                $order->confirmation_info = [
                    'is_proxy_order' => $order->source === 'proxy',
                    'is_cod_order' => $order->payment_method === 'cod',
                    'created_by_employee' => $order->created_by_id ? [
                        'id' => $order->created_by_id,
                        'name' => $order->createdBy->name ?? '未知员工'
                    ] : null,
                    'requires_confirmation' => true,
                    'confirmation_type' => $order->source === 'proxy' ? '代客下单确认' : '货到付款确认'
                ];
                return $order;
            });

            return response()->json(ApiResponse::success($orders, '获取待确认订单列表成功'));

        } catch (\Exception $e) {
            Log::error('获取待确认订单列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(ApiResponse::error('获取待确认订单列表失败：' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 构建代客下单备注信息
     *
     * @param string|null $originalNotes
     * @param \App\Employee\Models\Employee $employee
     * @return string
     */
    private function buildProxyOrderNotes($originalNotes, $employee)
    {
        $proxyInfo = "【代客下单】员工：{$employee->name}（{$employee->role}）";

        if ($originalNotes) {
            return $proxyInfo . "\n备注：" . $originalNotes;
        }

        return $proxyInfo;
    }

    /**
     * 代客下单 - 管理员或CRM专员为客户创建订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createForClient(Request $request)
    {
        // 获取当前登录的员工（通过employee.role中间件已经验证过权限）
        $currentEmployee = $request->user();
        
        // 添加详细的请求数据日志
        Log::info('代客下单请求开始', [
            'employee_id' => $currentEmployee->id ?? null,
            'employee_name' => $currentEmployee->name ?? null,
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl(),
            'request_headers' => $request->headers->all(),
            'request_data' => $request->all(),
            'content_type' => $request->header('Content-Type'),
            'request_size' => strlen($request->getContent())
        ]);
        
        // 添加权限检查日志
        Log::info('代客下单权限检查', [
            'employee_id' => $currentEmployee->id ?? null,
            'employee_name' => $currentEmployee->name ?? null,
            'employee_class' => get_class($currentEmployee),
            'employee_role' => $currentEmployee->role ?? null,
            'is_employee_instance' => $currentEmployee instanceof \App\Employee\Models\Employee,
            'request_data_keys' => array_keys($request->all()),
            'request_client_id' => $request->input('client_id'),
            'request_items_count' => count($request->input('items', [])),
            'middleware_passed' => true, // 如果到这里说明中间件已经通过
            'auth_guard' => $request->user() ? 'sanctum' : 'none',
            'bearer_token_exists' => $request->bearerToken() ? true : false
        ]);
        
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'client_id' => 'required|exists:users,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'nullable|numeric|min:0', // 允许指定价格（如折扣价）
            'items.*.unit' => 'nullable|string', // 允许指定商品单位
            'user_address_id' => 'nullable|exists:user_addresses,id',
            'shipping_address' => 'required|string',
            'contact_name' => 'required|string',
            'contact_phone' => 'required|string',
            'payment_method' => 'required|' . PaymentMethods::getValidationRule(),
            'delivery_method' => 'nullable|string',
            'delivery_date' => 'nullable|date',
            'delivery_time' => 'nullable|string',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string',
            'region_id' => 'nullable|integer', // 添加区域ID验证
        ]);
        
        // 添加验证结果日志
        Log::info('代客下单验证结果', [
            'validation_passed' => !$validator->fails(),
            'validation_errors' => $validator->fails() ? $validator->errors()->toArray() : null,
            'employee_id' => $currentEmployee->id ?? null
        ]);
        
        if ($validator->fails()) {
            // 添加详细的验证错误日志
            Log::error('代客下单验证失败', [
                'request_data' => $request->all(),
                'validation_errors' => $validator->errors()->toArray(),
                'employee_id' => $currentEmployee->id ?? null,
                'first_error' => $validator->errors()->first()
            ]);
            return response()->json(ApiResponse::error($validator->errors()->first(), 422, $validator->errors()->toArray()), 422);
        }
        
        // 查找客户
        $client = User::findOrFail($request->client_id);
        
        // CRM专员权限检查：只能为分配给自己的客户代下单
        if ($currentEmployee->role === 'crm_agent') {
            if ($client->crm_agent_id !== $currentEmployee->id) {
                return response()->json(ApiResponse::error('您只能为分配给您的客户代下单', 403), 403);
            }
        }
        
        DB::beginTransaction();
        try {
            // 🌍 获取客户的区域ID用于价格计算
            $clientRegionId = $this->getClientRegionId($client, $request);

            Log::info('代客下单区域信息', [
                'client_id' => $client->id,
                'client_region_id' => $clientRegionId,
                'client_province' => $client->province,
                'client_city' => $client->city,
                'client_district' => $client->district,
                'employee_id' => $currentEmployee->id
            ]);

            // 计算订单总金额并验证商品库存
            $subtotal = 0;
            $items = [];
            $allDiscountInfo = []; // 存储所有商品的价格计算详情

            Log::info('开始处理订单商品', [
                'items_count' => count($request->items),
                'client_region_id' => $clientRegionId,
                'employee_id' => $currentEmployee->id
            ]);

            foreach ($request->items as $index => $item) {
                Log::info("处理商品 #{$index}", [
                    'item_data' => $item,
                    'employee_id' => $currentEmployee->id
                ]);

                $product = Product::findOrFail($item['product_id']);

                Log::info("商品信息", [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_stock' => $product->getTotalStock(),
                    'requested_quantity' => $item['quantity'],
                    'inventory_policy' => $product->inventory_policy ?? 'strict',
                    'track_inventory' => $product->track_inventory ?? true,
                    'employee_id' => $currentEmployee->id
                ]);

                // 使用新的库存策略检查
                $stockCheckResult = $product->checkStockWithPolicy($item['quantity']);
                Log::info("库存策略检查结果", [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'requested_quantity' => $item['quantity'],
                    'check_result' => $stockCheckResult,
                    'employee_id' => $currentEmployee->id
                ]);

                if (!$stockCheckResult['allowed']) {
                    Log::error("库存策略检查失败", [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'requested_quantity' => $item['quantity'],
                        'current_stock' => $product->getTotalStock(),
                        'check_message' => $stockCheckResult['message'],
                        'employee_id' => $currentEmployee->id
                    ]);
                    return response()->json(ApiResponse::error($stockCheckResult['message'], 422), 422);
                }

                // 如果有警告，记录到日志
                if ($stockCheckResult['warning']) {
                    Log::warning("库存策略警告", [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'warning' => $stockCheckResult['warning'],
                        'employee_id' => $currentEmployee->id
                    ]);
                }

                // 🌍 计算商品的区域优惠价格
                $priceInfo = $this->calculateProductPriceWithRegion($product, $client, $clientRegionId, $item);

                $price = $priceInfo['final_price'];
                $itemTotal = $price * $item['quantity'];
                $subtotal += $itemTotal;

                // 记录价格计算详情
                $allDiscountInfo[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'base_price' => $priceInfo['base_price'],
                    'final_price' => $priceInfo['final_price'],
                    'discount_info' => $priceInfo['discount_info'],
                    'price_type' => $priceInfo['price_type'],
                    'quantity' => $item['quantity'],
                    'item_total' => $itemTotal
                ];

                // 🔧 获取销售单位ID和名称
                $saleUnit = $product->getSaleDefaultUnit();
                $unitId = $item['unit_id'] ?? $saleUnit?->id ?? null;
                $unitName = $saleUnit?->name ?? '';

                $items[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'price' => $price,
                    'unit_id' => $unitId, // 🔧 传递单位ID
                    'unit_name' => $unitName, // 🔧 传递单位名称用于日志
                    'total' => $itemTotal,
                ];

                Log::info("商品价格计算完成", [
                    'product_id' => $product->id,
                    'base_price' => $priceInfo['base_price'],
                    'final_price' => $priceInfo['final_price'],
                    'price_type' => $priceInfo['price_type'],
                    'item_total' => $itemTotal,
                    'running_subtotal' => $subtotal,
                    'discount_info' => $priceInfo['discount_info'],
                    'employee_id' => $currentEmployee->id
                ]);
            }
            
            // 应用折扣（如果有）
            $discount = 0;
            if ($request->has('discount_percentage') && $request->discount_percentage > 0) {
                $discount = $subtotal * ($request->discount_percentage / 100);
            }

            $total = $subtotal - $discount;

            // 创建订单数据
            $orderData = [
                'user_id' => $client->id,
                'order_no' => Order::generateOrderNo(),
                'subtotal' => $subtotal,
                'discount' => $discount,
                'total' => $total,
                'status' => 'pending', // 🔥 代客下单也从pending开始，需要人工确认
                'source' => 'proxy', // 代客下单标识
                'payment_method' => $request->payment_method,
                'delivery_method' => $request->delivery_method ?? null,
                'delivery_date' => $request->delivery_date ?? null,
                'delivery_time' => $request->delivery_time ?? null,
                'notes' => $this->buildProxyOrderNotes($request->notes, $currentEmployee), // 🔥 增强备注信息
                'created_by_id' => $currentEmployee->id, // 记录代下单员工ID
                'region_id' => $clientRegionId, // 🌍 保存客户区域ID
                'pricing_info' => $allDiscountInfo, // 🌍 保存价格计算详情
            ];
            
            // 处理地址信息
            if ($request->has('user_address_id')) {
                // 通过用户地址ID关联
                $orderData['user_address_id'] = $request->user_address_id;
                
                // 查找地址并填充其他地址字段（冗余，方便查询）
                $userAddress = \App\Models\UserAddress::findOrFail($request->user_address_id);
                $orderData['shipping_address'] = $userAddress->getFullAddressAttribute();
                $orderData['contact_name'] = $userAddress->contact_name;
                $orderData['contact_phone'] = $userAddress->contact_phone;
            } else {
                // 直接使用提交的地址信息
                $orderData['shipping_address'] = $request->shipping_address;
                $orderData['contact_name'] = $request->contact_name;
                $orderData['contact_phone'] = $request->contact_phone;
            }
            
            // 创建订单
            $order = Order::create($orderData);
            
            // 记录库存策略使用情况
            $inventoryPolicies = [];
            $stockWarnings = [];
            
            // 如果是货到付款订单，记录特殊标记
            if ($request->payment_method === 'cod') {
                // 添加货到付款特殊标记
                $order->is_cod = true;
                $order->cod_status = 'unpaid'; // 初始状态：未支付
                $order->save();
                
                // 记录货到付款日志
                \Illuminate\Support\Facades\Log::info('货到付款订单创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'client_id' => $client->id,
                    'client_name' => $client->name,
                    'total' => $order->total
                ]);
            }
            
            // 1. 先创建所有订单明细
            foreach ($items as $item) {
                // 修复字段映射问题，确保包含所有必需字段
                $orderItemData = [
                    'order_id' => $order->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'product_sku' => $item['product_sku'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'original_price' => $item['original_price'] ?? $item['price'], // 添加原价字段
                    'total' => $item['total'],
                    'unit_id' => $item['unit_id'], // 🔧 使用正确的单位ID
                    'region_id' => $request->region_id ?? null, // 添加区域ID字段
                ];

                try {
                    // 🔥 修复：代客下单也使用单位转换服务，与普通订单保持一致
                    $unitConversionService = app(\App\Order\Services\OrderUnitConversionService::class);
                    $processedItemData = $unitConversionService->processOrderItem($orderItemData);

                    // 如果进行了转换，记录转换信息
                    if ($processedItemData['is_converted']) {
                        Log::info('🔄 代客下单订单项单位转换', [
                            'order_id' => $order->id,
                            'product_id' => $item['product_id'],
                            'product_name' => $item['product_name'],
                            'original_quantity' => $processedItemData['user_display_quantity'],
                            'original_unit_id' => $processedItemData['user_display_unit_id'],
                            'converted_quantity' => $processedItemData['quantity'],
                            'converted_unit_id' => $processedItemData['unit_id'],
                            'conversion_note' => "代客下单斤单位转换为kg",
                            'employee_id' => $currentEmployee->id
                        ]);
                    }

                    $orderItem = $order->items()->create($processedItemData);

                    Log::info('✅ 代客下单明细创建成功', [
                        'order_id' => $order->id,
                        'order_item_id' => $orderItem->id,
                        'product_id' => $item['product_id'],
                        'unit_id' => $item['unit_id'], // 🔧 记录单位ID
                        'unit_name' => $item['unit_name'] ?? 'N/A', // 🔧 记录单位名称
                        'product_name' => $item['product_name'],
                        'quantity' => $item['quantity'],
                        'employee_id' => $currentEmployee->id
                    ]);
                } catch (\Exception $e) {
                    Log::error('代客下单明细创建失败', [
                        'order_id' => $order->id,
                        'product_id' => $item['product_id'],
                        'error' => $e->getMessage(),
                        'order_item_data' => $orderItemData,
                        'employee_id' => $currentEmployee->id
                    ]);
                    throw new \Exception("订单明细创建失败：{$e->getMessage()}");
                }
            }

            // 🔥 重要修改：代客下单不在创建时处理库存，与普通订单保持一致
            // 库存处理将在人工确认后由 OrderObserver 统一处理
            Log::info('🔥 代客下单创建完成，库存处理将在人工确认后执行', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'status' => 'pending',
                'next_step' => '等待管理员人工确认',
                'inventory_processed' => false,
                'employee_id' => $currentEmployee->id
            ]);

            
            // 添加代客下单日志
            \Illuminate\Support\Facades\Log::info('代客下单', [
                'admin_id' => $currentEmployee->id,
                'admin_name' => $currentEmployee->name,
                'client_id' => $client->id,
                'client_name' => $client->name,
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'total' => $order->total,
                'inventory_summary' => $order->getInventoryPolicyDisplayText(),
                'has_negative_stock' => $order->has_negative_stock_items,
                'negative_stock_approved' => $order->negative_stock_approved
            ]);
            
            DB::commit();
            
            // 返回包含订单明细的订单信息
            $order->load(['items', 'userAddress']);
            return response()->json(ApiResponse::success($order, '代客下单成功', 200), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('代客下单失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取订单统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        try {
            $user = $request->user();
            $query = Order::query();
            
            // 权限控制：根据员工角色限制数据访问范围
            if ($user) {
                // 检查是否是Employee实例（员工系统）
                if ($user instanceof \App\Employee\Models\Employee) {
                    // 员工系统的权限控制
                    switch ($user->role) {
                        case 'admin':
                        case 'manager':
                            // 管理员和经理可以查看所有订单统计
                            break;
                            
                        case 'crm_agent':
                        case 'crm':
                            // CRM专员只能查看分配给自己的客户的订单
                            $query->whereHas('user', function($q) use ($user) {
                                $q->where('crm_agent_id', $user->id);
                            });
                            break;
                            
                        case 'delivery':
                            // 配送员只能查看自己配送的订单
                            $query->whereHas('delivery', function($q) use ($user) {
                                $q->whereHas('deliverer', function($dq) use ($user) {
                                    $dq->where('employee_id', $user->id);
                                });
                            });
                            break;
                            
                        default:
                            // 其他员工角色暂时不允许查看统计
                            return response()->json(ApiResponse::error('没有权限查看订单统计', 403), 403);
                    }
                } else {
                    // 普通用户（客户）只能查看自己的订单统计
                    $query->where('user_id', $user->id);
                }
            } else {
                // 未登录用户不允许访问
                return response()->json(ApiResponse::error('未授权访问', 401), 401);
            }
            
            // 总订单数
            $total = $query->count();
            
            // 待处理订单数（待付款 + 已付款）
            $pending = $query->whereIn('status', ['pending', 'paid'])->count();
            
            // 总金额
            $totalAmount = $query->sum('total');
            
            // 今日订单数
            $today = $query->whereDate('created_at', today())->count();
            
            // 各状态订单数
            $statusStats = $query->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();
            
            // 本月订单趋势（最近7天）
            $weeklyTrend = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i);
                $dayQuery = clone $query;
                $count = $dayQuery->whereDate('created_at', $date->toDateString())->count();
                $weeklyTrend[] = [
                    'date' => $date->format('m-d'),
                    'count' => $count
                ];
            }
            
            $stats = [
                'total' => $total,
                'pending' => $pending,
                'total_amount' => $totalAmount,
                'today' => $today,
                'status_stats' => $statusStats,
                'weekly_trend' => $weeklyTrend
            ];
            
            return response()->json(ApiResponse::success($stats));
        } catch (\Exception $e) {
            Log::error('获取订单统计失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id
            ]);
            
            return response()->json(ApiResponse::error('获取统计数据失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 为代客下单创建简化的出库单
     */
    private function createSimpleOutboundDocumentForProxy(Order $order, array $items)
    {
        // 获取默认仓库
        $defaultWarehouseId = $this->getDefaultWarehouseForProxy();
        
        // 创建出库单
        $outboundDocument = \App\Inventory\Models\OutboundDocument::create([
            'document_no' => $this->generateOutboundDocumentNoForProxy(),
            'document_type' => 'sales', // 销售出库
            'reference_type' => 'order',
            'reference_id' => $order->id,
            'order_id' => $order->id,
            'warehouse_id' => $order->warehouse_id ?? $defaultWarehouseId,
            'status' => 'completed', // 直接设为已完成
            'confirmed_at' => now(),
            'completed_at' => now(),
            'confirmed_by' => 1, // 系统用户
            'completed_by' => 1, // 系统用户
            'created_by' => 1, // 系统用户
            'updated_by' => 1, // 系统用户
            'notes' => "代客下单 {$order->order_no} 自动创建出库单",
        ]);
        
        $totalCost = 0;
        $totalItems = 0;
        
        // 创建出库明细
        foreach ($items as $item) {
            $product = Product::find($item['product_id']);
            if (!$product) {
                continue;
            }
            
            // 计算成本价（使用商品的当前成本价或订单价格）
            $unitCost = $product->cost_price ?? $item['price'];
            $itemTotalCost = $item['quantity'] * $unitCost;
            
            \App\Inventory\Models\OutboundItem::create([
                'outbound_document_id' => $outboundDocument->id,
                'product_id' => $item['product_id'],
                'product_name' => $item['product_name'],
                'product_sku' => $item['product_sku'],
                'planned_quantity' => $item['quantity'],
                'actual_quantity' => $item['quantity'],
                'unit_id' => $item['unit_id'] ?? null,
                'unit_cost' => $unitCost,
                'total_cost' => $itemTotalCost,
                'notes' => "代客下单项出库",
            ]);
            
            $totalCost += $itemTotalCost;
            $totalItems += $item['quantity'];
            
            // 创建库存事务记录
            $this->createInventoryTransactionForProxyOrder($outboundDocument, $item, $order);
        }
        
        // 更新出库单总计
        $outboundDocument->update([
            'total_cost' => $totalCost,
            'total_items' => $totalItems,
        ]);
        
        return $outboundDocument;
    }
    
    /**
     * 为代客下单创建库存事务记录
     */
    private function createInventoryTransactionForProxyOrder($outboundDocument, array $item, Order $order)
    {
        // 获取或创建销售出库事务类型
        $transactionType = \App\Inventory\Models\InventoryTransactionType::firstOrCreate(
            ['code' => 'sales_out'],
            [
                'name' => '销售出库',
                'description' => '销售订单出库',
                'direction' => 'out',
                'is_active' => true,
            ]
        );
        
        \App\Inventory\Models\InventoryTransaction::create([
            'transaction_type_id' => $transactionType->id,
            'reference_type' => \App\Inventory\Models\OutboundDocument::class,
            'reference_id' => $outboundDocument->id,
            'product_id' => $item['product_id'],
            'warehouse_id' => $outboundDocument->warehouse_id,
            'quantity' => -$item['quantity'], // 负数表示出库
            'unit_id' => $item['unit_id'] ?? null,
            'unit_price' => $item['price'],
            'total_amount' => $item['total'],
            'notes' => "代客下单 {$order->order_no} 销售出库",
            'status' => 'completed',
            'created_by' => 1, // 系统用户
            'updated_by' => 1, // 系统用户
        ]);
    }
    
    /**
     * 获取默认仓库ID（代客下单专用）
     */
    private function getDefaultWarehouseForProxy(): int
    {
        // 尝试获取第一个活跃仓库
        $warehouse = DB::table('warehouses')
            ->where('status', 'active')
            ->first();
            
        if ($warehouse) {
            return $warehouse->id;
        }
        
        // 如果没有活跃仓库，获取第一个仓库
        $warehouse = DB::table('warehouses')->first();
        if ($warehouse) {
            return $warehouse->id;
        }
        
        // 如果没有仓库，抛出异常
        throw new \Exception('系统中没有可用的仓库');
    }
    
    /**
     * 生成出库单号（代客下单专用）
     */
    private function generateOutboundDocumentNoForProxy(): string
    {
        $prefix = 'POUT'; // 代客下单出库单前缀
        $date = now()->format('Ymd');
        
        // 获取今日最大序号
        $lastDocument = \App\Inventory\Models\OutboundDocument::where('document_no', 'like', "{$prefix}{$date}%")
            ->orderBy('document_no', 'desc')
            ->first();
            
        if ($lastDocument) {
            $lastNumber = (int) substr($lastDocument->document_no, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return $prefix . $date . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 为代客下单创建出库单
     */
    private function createOutboundDocumentForProxyOrder(Order $order, array $items)
    {
        $totalCost = 0;
        $totalItems = 0;
        
        // 创建出库单主记录
        $outboundDocument = \App\Inventory\Models\OutboundDocument::create([
            'document_type' => 'sales',
            'document_no' => 'OUT' . date('YmdHis') . rand(1000, 9999),
            'warehouse_id' => $order->warehouse_id ?? 1, // 默认仓库ID
            'status' => 'confirmed',
            'confirmed_at' => now(),
            'confirmed_by' => auth()->id(),
            'notes' => "代客下单 {$order->order_no} 自动创建的出库单",
            'order_id' => $order->id,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);
        
        foreach ($items as $item) {
            $product = Product::find($item['product_id']);
            if (!$product) {
                continue;
            }
            
            // 计算成本价（使用商品的当前成本价或订单价格）
            $unitCost = $product->cost_price ?? $item['price'];
            $itemTotalCost = $item['quantity'] * $unitCost;
            
            // 确保有单位ID
            $unitId = null;
            if (!empty($item['unit_id'])) {
                $unitId = $item['unit_id'];
            } else {
                // 尝试获取商品的默认销售单位
                $defaultUnit = $product->getSaleDefaultUnit();
                if ($defaultUnit) {
                    $unitId = $defaultUnit->id;
                } else {
                    // 如果没有默认单位，尝试获取第一个可用单位
                    $firstUnit = \App\Unit\Models\Unit::first();
                    if ($firstUnit) {
                        $unitId = $firstUnit->id;
                    } else {
                        // 如果仍然没有单位，使用默认单位ID 1
                        $unitId = 1;
                    }
                }
                
                // 记录单位ID修正
                Log::info('📦 代客下单出库单位ID修正', [
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'original_unit_id' => $item['unit_id'] ?? null,
                    'corrected_unit_id' => $unitId,
                    'order_id' => $order->id
                ]);
            }
            
            \App\Inventory\Models\OutboundItem::create([
                'outbound_document_id' => $outboundDocument->id,
                'product_id' => $item['product_id'],
                'product_name' => $item['product_name'],
                'product_sku' => $item['product_sku'],
                'planned_quantity' => $item['quantity'],
                'actual_quantity' => $item['quantity'],
                'unit_id' => $unitId,
                'unit_cost' => $unitCost,
                'total_cost' => $itemTotalCost,
                'notes' => "代客下单项出库",
            ]);
            
            $totalCost += $itemTotalCost;
            $totalItems += $item['quantity'];
            
            // 创建库存事务记录
            $this->createInventoryTransactionForProxyOrder($outboundDocument, $item, $order);
        }
        
        // 更新出库单总计
        $outboundDocument->update([
            'total_cost' => $totalCost,
            'total_items' => $totalItems,
        ]);

        return $outboundDocument;
    }

    /**
     * 🌍 获取客户的区域ID
     *
     * @param User $client 客户
     * @param Request $request 请求对象
     * @return int|null 区域ID
     */
    private function getClientRegionId($client, $request)
    {
        // 1. 优先使用请求中指定的区域ID（如果有）
        if ($request->has('region_id') && $request->region_id) {
            Log::info('使用请求指定的区域ID', [
                'region_id' => $request->region_id,
                'client_id' => $client->id
            ]);
            return $request->region_id;
        }

        // 2. 使用客户资料中的区域ID
        if ($client->region_id) {
            Log::info('使用客户资料中的区域ID', [
                'region_id' => $client->region_id,
                'client_id' => $client->id
            ]);
            return $client->region_id;
        }

        // 3. 如果指定了用户地址，尝试从地址获取区域ID
        if ($request->has('user_address_id') && $request->user_address_id) {
            $userAddress = \App\Models\UserAddress::find($request->user_address_id);
            if ($userAddress && $userAddress->province && $userAddress->city && $userAddress->district) {
                // 根据地址信息查找区域ID
                $region = \App\Region\Models\Region::where('name', $userAddress->district)
                    ->where('level', 3)
                    ->whereHas('parent', function($q) use ($userAddress) {
                        $q->where('name', $userAddress->city)
                          ->where('level', 2)
                          ->whereHas('parent', function($q2) use ($userAddress) {
                              $q2->where('name', $userAddress->province)
                                 ->where('level', 1);
                          });
                    })
                    ->first();

                if ($region) {
                    Log::info('从用户地址获取区域ID', [
                        'region_id' => $region->id,
                        'address_id' => $userAddress->id,
                        'province' => $userAddress->province,
                        'city' => $userAddress->city,
                        'district' => $userAddress->district,
                        'client_id' => $client->id
                    ]);
                    return $region->id;
                }
            }
        }

        // 4. 尝试从客户的省市区信息获取区域ID
        if ($client->province && $client->city && $client->district) {
            $region = \App\Region\Models\Region::where('name', $client->district)
                ->where('level', 3)
                ->whereHas('parent', function($q) use ($client) {
                    $q->where('name', $client->city)
                      ->where('level', 2)
                      ->whereHas('parent', function($q2) use ($client) {
                          $q2->where('name', $client->province)
                             ->where('level', 1);
                      });
                })
                ->first();

            if ($region) {
                Log::info('从客户省市区信息获取区域ID', [
                    'region_id' => $region->id,
                    'province' => $client->province,
                    'city' => $client->city,
                    'district' => $client->district,
                    'client_id' => $client->id
                ]);
                return $region->id;
            }
        }

        Log::info('无法获取客户区域ID，使用默认价格', [
            'client_id' => $client->id,
            'client_region_id' => $client->region_id,
            'client_province' => $client->province,
            'client_city' => $client->city,
            'client_district' => $client->district
        ]);

        return null;
    }

    /**
     * 🌍 计算商品的区域优惠价格
     *
     * @param Product $product 商品
     * @param User $client 客户
     * @param int|null $regionId 区域ID
     * @param array $item 商品项数据
     * @return array 价格信息
     */
    private function calculateProductPriceWithRegion($product, $client, $regionId, $item)
    {
        // 如果请求中指定了价格，优先使用指定价格
        if (isset($item['price']) && $item['price'] !== null) {
            Log::info('使用请求指定的商品价格', [
                'product_id' => $product->id,
                'specified_price' => $item['price'],
                'base_price' => $product->price,
                'client_id' => $client->id
            ]);

            return [
                'base_price' => $product->price,
                'final_price' => $item['price'],
                'discount_info' => [
                    [
                        'type' => 'manual_override',
                        'name' => '手动指定价格',
                        'discount_amount' => $product->price - $item['price'],
                        'discount_type' => 'fixed_amount',
                        'discount_value' => $item['price']
                    ]
                ],
                'price_type' => 'manual'
            ];
        }

        // 使用价格计算服务计算区域优惠价格
        $priceCalculationService = app(\App\Product\Services\PriceCalculationService::class);

        try {
            $priceInfo = $priceCalculationService->calculatePrice($product, $client, $regionId);

            Log::info('区域价格计算成功', [
                'product_id' => $product->id,
                'client_id' => $client->id,
                'region_id' => $regionId,
                'base_price' => $priceInfo['base_price'],
                'final_price' => $priceInfo['final_price'],
                'price_type' => $priceInfo['price_type'],
                'total_discount' => $priceInfo['total_discount']
            ]);

            return $priceInfo;

        } catch (\Exception $e) {
            Log::error('区域价格计算失败，使用商品基础价格', [
                'product_id' => $product->id,
                'client_id' => $client->id,
                'region_id' => $regionId,
                'error' => $e->getMessage(),
                'base_price' => $product->price
            ]);

            // 计算失败时返回基础价格
            return [
                'base_price' => $product->price,
                'final_price' => $product->price,
                'discount_info' => [],
                'price_type' => 'base',
                'total_discount' => 0
            ];
        }
    }

    /**
     * 🔧 新增：检查购物车商品库存
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkCartStock(Request $request)
    {
        try {
            $items = $request->input('items', []);

            if (empty($items)) {
                return response()->json(ApiResponse::error('商品列表不能为空', 400), 400);
            }

            Log::info('🔍 购物车库存检查开始', [
                'items_count' => count($items),
                'user_id' => $request->user()->id ?? 'guest'
            ]);

            $stockResults = [];
            $warnings = [];

            foreach ($items as $item) {
                $productId = $item['product_id'] ?? null;
                $quantity = $item['quantity'] ?? 0;
                $unitId = $item['unit_id'] ?? null;

                if (!$productId || $quantity <= 0) {
                    continue;
                }

                $product = Product::find($productId);
                if (!$product) {
                    $stockResults[$productId] = [
                        'allowed' => false,
                        'message' => '商品不存在',
                        'current_stock' => 0
                    ];
                    continue;
                }

                // 使用商品的库存策略检查
                $stockCheck = $product->checkStockWithPolicy($quantity, $unitId);
                $stockResults[$productId] = $stockCheck;

                // 收集警告信息
                if ($stockCheck['warning']) {
                    $warnings[] = [
                        'product_id' => $productId,
                        'product_name' => $product->name,
                        'warning' => $stockCheck['warning'],
                        'current_stock' => $product->getTotalStock()
                    ];
                }

                Log::info('📦 商品库存检查', [
                    'product_id' => $productId,
                    'product_name' => $product->name,
                    'requested_quantity' => $quantity,
                    'current_stock' => $product->getTotalStock(),
                    'check_result' => $stockCheck
                ]);
            }

            $responseData = [
                'stock_results' => $stockResults,
                'warnings' => $warnings,
                'checked_at' => now()->toISOString()
            ];

            Log::info('✅ 购物车库存检查完成', [
                'total_items' => count($items),
                'stock_issues' => count(array_filter($stockResults, fn($r) => !$r['allowed'])),
                'warnings_count' => count($warnings)
            ]);

            return response()->json(ApiResponse::success($responseData, '库存检查完成'));

        } catch (\Exception $e) {
            Log::error('❌ 购物车库存检查失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(ApiResponse::error('库存检查失败: ' . $e->getMessage(), 500), 500);
        }
    }
}