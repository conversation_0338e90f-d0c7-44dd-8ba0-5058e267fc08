<?php

namespace App\Billing\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Billing\Services\BillingService;
use App\Billing\Models\PaymentRecord;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class PaymentRecordController extends Controller
{
    protected $billingService;

    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * 获取支付记录列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'search' => 'nullable|string',
                'payment_method' => 'nullable|string',
                'payment_type' => 'nullable|string',
                'status' => 'nullable|string',
                'user_id' => 'nullable|integer',
                'bill_type' => 'nullable|string',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date',
                'amount_min' => 'nullable|numeric|min:0',
                'amount_max' => 'nullable|numeric|min:0',
                'payment_time_from' => 'nullable|date',
                'payment_time_to' => 'nullable|date'
            ]);

            $query = PaymentRecord::with(['bill', 'bill.user'])
                ->orderBy('created_at', 'desc');

            // 搜索过滤
            if (!empty($params['search'])) {
                $search = $params['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('payment_no', 'like', "%{$search}%")
                      ->orWhere('external_payment_no', 'like', "%{$search}%")
                      ->orWhereHas('bill', function ($billQuery) use ($search) {
                          $billQuery->where('bill_no', 'like', "%{$search}%");
                      });
                });
            }

            // 支付方式过滤
            if (!empty($params['payment_method'])) {
                $query->where('payment_method', $params['payment_method']);
            }

            // 支付类型过滤
            if (!empty($params['payment_type'])) {
                $query->where('payment_type', $params['payment_type']);
            }

            // 状态过滤
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 用户过滤
            if (!empty($params['user_id'])) {
                $query->whereHas('bill', function ($billQuery) use ($params) {
                    $billQuery->where('user_id', $params['user_id']);
                });
            }

            // 账单类型过滤
            if (!empty($params['bill_type'])) {
                $query->whereHas('bill', function ($billQuery) use ($params) {
                    $billQuery->where('bill_type', $params['bill_type']);
                });
            }

            // 日期范围过滤
            if (!empty($params['date_from'])) {
                $query->where('created_at', '>=', $params['date_from']);
            }
            if (!empty($params['date_to'])) {
                $query->where('created_at', '<=', $params['date_to'] . ' 23:59:59');
            }

            // 金额范围过滤
            if (!empty($params['amount_min'])) {
                $query->where('payment_amount', '>=', $params['amount_min']);
            }
            if (!empty($params['amount_max'])) {
                $query->where('payment_amount', '<=', $params['amount_max']);
            }

            // 支付时间范围过滤
            if (!empty($params['payment_time_from'])) {
                $query->where('payment_time', '>=', $params['payment_time_from']);
            }
            if (!empty($params['payment_time_to'])) {
                $query->where('payment_time', '<=', $params['payment_time_to'] . ' 23:59:59');
            }

            $perPage = $params['per_page'] ?? 20;
            $payments = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $payments->items(),
                'meta' => [
                    'total' => $payments->total(),
                    'per_page' => $payments->perPage(),
                    'current_page' => $payments->currentPage(),
                    'last_page' => $payments->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取支付记录失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取支付记录统计
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'date_from' => 'date',
                'date_to' => 'date',
                'payment_method' => 'string',
                'bill_type' => 'string'
            ]);

            // 基础查询
            $query = PaymentRecord::query();

            // 日期范围过滤
            if (!empty($params['date_from'])) {
                $query->where('created_at', '>=', $params['date_from']);
            }
            if (!empty($params['date_to'])) {
                $query->where('created_at', '<=', $params['date_to'] . ' 23:59:59');
            }

            // 支付方式过滤
            if (!empty($params['payment_method'])) {
                $query->where('payment_method', $params['payment_method']);
            }

            // 账单类型过滤
            if (!empty($params['bill_type'])) {
                $query->whereHas('bill', function ($billQuery) use ($params) {
                    $billQuery->where('bill_type', $params['bill_type']);
                });
            }

            // 统计数据
            $totalRecords = $query->count();
            $totalAmount = $query->where('status', 'success')->sum('payment_amount');
            $successCount = $query->where('status', 'success')->count();
            $failedCount = $query->where('status', 'failed')->count();
            $pendingCount = $query->where('status', 'pending')->count();

            // 支付方式统计
            $paymentMethods = PaymentRecord::selectRaw('payment_method, COUNT(*) as count, SUM(payment_amount) as total_amount')
                ->where('status', 'success')
                ->when(!empty($params['date_from']), function ($q) use ($params) {
                    return $q->where('created_at', '>=', $params['date_from']);
                })
                ->when(!empty($params['date_to']), function ($q) use ($params) {
                    return $q->where('created_at', '<=', $params['date_to'] . ' 23:59:59');
                })
                ->groupBy('payment_method')
                ->get();

            // 每日统计（最近30天）
            $dailyStats = PaymentRecord::selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(payment_amount) as total_amount')
                ->where('status', 'success')
                ->where('created_at', '>=', now()->subDays(30))
                ->groupBy('date')
                ->orderBy('date', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_records' => $totalRecords,
                    'total_amount' => $totalAmount,
                    'success_count' => $successCount,
                    'failed_count' => $failedCount,
                    'pending_count' => $pendingCount,
                    'success_rate' => $totalRecords > 0 ? round(($successCount / $totalRecords) * 100, 2) : 0,
                    'payment_methods' => $paymentMethods,
                    'daily_stats' => $dailyStats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取支付记录详情
     */
    public function show($id): JsonResponse
    {
        try {
            $payment = PaymentRecord::with(['bill', 'bill.user', 'bill.items'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $payment
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取支付记录详情失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重试支付
     */
    public function retry($id): JsonResponse
    {
        try {
            $payment = PaymentRecord::findOrFail($id);
            
            if ($payment->status !== 'failed') {
                return response()->json([
                    'success' => false,
                    'message' => '只能重试失败的支付记录'
                ], 422);
            }

            // 重新发起支付
            $result = $this->billingService->retryPayment($payment->id);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => '支付重试成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '支付重试失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 取消支付
     */
    public function cancel($id): JsonResponse
    {
        try {
            $payment = PaymentRecord::findOrFail($id);
            
            if (!in_array($payment->status, ['pending', 'processing'])) {
                return response()->json([
                    'success' => false,
                    'message' => '只能取消待处理或处理中的支付记录'
                ], 422);
            }

            // 取消支付
            $result = $this->billingService->cancelPayment($payment->id);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => '支付取消成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '支付取消失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 导出支付记录
     */
    public function export(Request $request)
    {
        try {
            $params = $request->validate([
                'payment_method' => 'string',
                'payment_type' => 'string',
                'status' => 'string',
                'user_id' => 'integer',
                'bill_type' => 'string',
                'date_from' => 'date',
                'date_to' => 'date',
                'format' => 'string'
            ]);

            return $this->billingService->exportPaymentRecords($params);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取支付记录报告
     */
    public function reports(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'start_date' => 'required|date',
                'end_date' => 'required|date',
                'payment_method' => 'string',
                'payment_type' => 'string',
            ]);

            $reports = $this->billingService->getPaymentReports($params);

            return response()->json([
                'success' => true,
                'data' => $reports
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取支付报告失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理微信支付通知
     */
    public function wechatNotify(Request $request): JsonResponse
    {
        try {
            // 获取微信通知的XML数据
            $xml = $request->getContent();
            
            if (empty($xml)) {
                return response()->json([
                    'return_code' => 'FAIL',
                    'return_msg' => '通知数据为空'
                ], 400);
            }

            // 解析XML数据
            $data = $this->xmlToArray($xml);
            
            // 验证通信是否成功
            if ($data['return_code'] !== 'SUCCESS') {
                return response()->json([
                    'return_code' => 'FAIL',
                    'return_msg' => '通信失败'
                ], 400);
            }

            // 调用BillingService处理支付通知
            $result = $this->billingService->handleWechatPaymentNotify($data);

            // 返回成功响应给微信
            return response()->json([
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK'
            ]);

        } catch (\Exception $e) {
            Log::error('微信支付通知处理失败', [
                'error' => $e->getMessage(),
                'xml' => $request->getContent(),
            ]);

            return response()->json([
                'return_code' => 'FAIL',
                'return_msg' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * XML转数组
     */
    private function xmlToArray(string $xml): array
    {
        $data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        return $data ?: [];
    }

    /*
    |--------------------------------------------------------------------------
    | 用户端专用方法
    |--------------------------------------------------------------------------
    | 以下方法专门为小程序用户端提供支付记录查询功能
    */

    /**
     * 获取用户支付记录
     * GET /api/mp/payment-records
     */
    public function getUserPaymentRecords(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:50',
                'payment_method' => 'string|in:wechat,alipay,cash,bank_transfer',
                'status' => 'string|in:pending,success,failed,cancelled',
                'date_from' => 'date',
                'date_to' => 'date',
            ]);

            $perPage = $params['per_page'] ?? 15;

            $query = PaymentRecord::whereHas('bill', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->with(['bill.order']);

            // 支付方式过滤
            if (isset($params['payment_method'])) {
                $query->where('payment_method', $params['payment_method']);
            }

            // 状态过滤
            if (isset($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 日期过滤
            if (isset($params['date_from'])) {
                $query->whereDate('created_at', '>=', $params['date_from']);
            }

            if (isset($params['date_to'])) {
                $query->whereDate('created_at', '<=', $params['date_to']);
            }

            $paymentRecords = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'payment_records' => $paymentRecords->items() ? collect($paymentRecords->items())->map(function ($record) {
                        return [
                            'id' => $record->id,
                            'payment_no' => $record->payment_no,
                            'payment_method' => $record->payment_method,
                            'payment_method_text' => $this->getPaymentMethodText($record->payment_method),
                            'payment_amount' => $record->payment_amount,
                            'amount' => $record->payment_amount, // 保留amount字段以兼容旧代码
                            'status' => $record->status,
                            'status_text' => $this->getPaymentStatusText($record->status),
                            'bill' => [
                                'id' => $record->bill->id,
                                'bill_no' => $record->bill->bill_no,
                                'bill_type' => $record->bill->bill_type,
                                'order_no' => $record->bill->order?->order_no,
                            ],
                            'paid_at' => $record->paid_at,
                            'created_at' => $record->created_at,
                        ];
                    }) : [],
                    'pagination' => [
                        'total' => $paymentRecords->total(),
                        'per_page' => $paymentRecords->perPage(),
                        'current_page' => $paymentRecords->currentPage(),
                        'last_page' => $paymentRecords->lastPage(),
                        'from' => $paymentRecords->firstItem(),
                        'to' => $paymentRecords->lastItem(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户支付记录失败', [
                'user_id' => auth()->id(),
                'params' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取支付记录失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户支付统计
     * GET /api/mp/payment-records/statistics
     */
    public function getUserPaymentStatistics(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            $params = $request->validate([
                'period' => 'string|in:week,month,quarter,year',
                'date_from' => 'date',
                'date_to' => 'date',
            ]);

            $query = PaymentRecord::whereHas('bill', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('status', 'success');

            // 时间范围过滤 - 使用created_at替代paid_at
            if (isset($params['date_from'])) {
                $query->whereDate('created_at', '>=', $params['date_from']);
            }

            if (isset($params['date_to'])) {
                $query->whereDate('created_at', '<=', $params['date_to']);
            }

            // 基础统计
            $totalPayments = $query->count();
            $totalAmount = $query->sum('payment_amount');
            $avgAmount = $totalPayments > 0 ? $totalAmount / $totalPayments : 0;

            // 支付方式统计
            $paymentMethodsCollection = PaymentRecord::whereHas('bill', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('status', 'success')
            ->selectRaw('payment_method, COUNT(*) as count, SUM(payment_amount) as total_amount')
            ->groupBy('payment_method')
            ->get();

            // 将集合转换为数组格式
            $paymentMethods = [];
            foreach ($paymentMethodsCollection as $item) {
                $paymentMethods[$item->payment_method] = [
                    'count' => $item->count,
                    'total_amount' => $item->total_amount,
                    'percentage' => 0, // 将在下面计算
                ];
            }

            // 计算百分比
            foreach ($paymentMethods as $method => &$data) {
                $data['percentage'] = $totalAmount > 0 ? round(($data['total_amount'] / $totalAmount) * 100, 2) : 0;
            }

            // 月度趋势 - 使用created_at替代paid_at
            $monthlyTrend = [];
            for ($i = 5; $i >= 0; $i--) {
                $month = now()->subMonths($i);
                $monthData = PaymentRecord::whereHas('bill', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->where('status', 'success')
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->selectRaw('COUNT(*) as count, SUM(payment_amount) as total_amount')
                ->first();

                $monthlyTrend[] = [
                    'month' => $month->format('Y-m'),
                    'month_text' => $month->format('Y年m月'),
                    'count' => $monthData->count ?? 0,
                    'total_amount' => $monthData->total_amount ?? 0,
                ];
            }

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'overview' => [
                        'total_payments' => $totalPayments,
                        'total_amount' => round($totalAmount, 2),
                        'avg_amount' => round($avgAmount, 2),
                    ],
                    'payment_methods' => $paymentMethods,
                    'monthly_trend' => $monthlyTrend,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户支付统计失败', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取支付统计失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取支付方式文本
     */
    private function getPaymentMethodText(string $method): string
    {
        $methods = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            'bank_transfer' => '银行转账',
            'credit' => '赊账支付',
            'balance' => '余额支付',
            'mixed' => '混合支付',
            'other' => '其他方式',
            'cod' => '货到付款',
        ];

        return $methods[$method] ?? '未知方式';
    }

    /**
     * 获取支付状态文本
     */
    private function getPaymentStatusText(string $status): string
    {
        $statuses = [
            'pending' => '待支付',
            'success' => '支付成功',
            'failed' => '支付失败',
            'cancelled' => '已取消',
        ];

        return $statuses[$status] ?? '未知状态';
    }
}