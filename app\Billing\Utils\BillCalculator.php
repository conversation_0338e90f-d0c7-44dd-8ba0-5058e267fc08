<?php

namespace App\Billing\Utils;

use App\Billing\Models\Bill;
use App\Models\User;

class BillCalculator
{
    /**
     * 计算可用余额支付金额
     */
    public static function calculateMaxBalancePayment(Bill $bill): float
    {
        $user = $bill->user;
        if (!$user || $user->balance <= 0) {
            return 0;
        }

        return min($user->balance, $bill->pending_amount);
    }



    /**
     * 计算混合支付方案
     */
    public static function calculateMixedPayment(Bill $bill, array $preferences = []): array
    {
        $user = $bill->user;
        $pendingAmount = $bill->pending_amount;
        
        $result = [
            'total_amount' => $pendingAmount,
            'balance_used' => 0,
            'cash_needed' => $pendingAmount,
            'payment_methods' => []
        ];

        // 优先使用余额
        if (isset($preferences['use_balance']) && $preferences['use_balance'] && $user->balance > 0) {
            $balanceUsed = min($user->balance, $pendingAmount);
            $result['balance_used'] = $balanceUsed;
            $pendingAmount -= $balanceUsed;
            $result['payment_methods'][] = [
                'method' => 'balance',
                'amount' => $balanceUsed,
                'description' => '余额支付'
            ];
        }

        $result['cash_needed'] = $pendingAmount;
        
        // 如果还有剩余金额，需要现金支付
        if ($pendingAmount > 0) {
            $result['payment_methods'][] = [
                'method' => $preferences['cash_method'] ?? 'wechat',
                'amount' => $pendingAmount,
                'description' => '现金支付'
            ];
        }

        return $result;
    }

    /**
     * 验证支付金额
     */
    public static function validatePaymentAmount(Bill $bill, array $paymentData): array
    {
        $errors = [];
        $totalAmount = $paymentData['payment_amount'] ?? 0;
        $balanceUsed = $paymentData['balance_used'] ?? 0;
        $user = $bill->user;

        // 检查支付金额是否合理
        if ($totalAmount <= 0) {
            $errors[] = '支付金额必须大于0';
        }

        if ($totalAmount > $bill->pending_amount * 1.1) { // 允许10%的超额支付
            $errors[] = '支付金额过大，可能存在错误';
        }

        // 检查余额是否足够
        if ($balanceUsed > 0) {
            if (!$user || $user->balance < $balanceUsed) {
                $errors[] = '用户余额不足';
            }
            
            // 检查余额是否超过支付金额
            if ($balanceUsed > $totalAmount) {
                $errors[] = '余额使用金额不能超过支付金额';
            }
        }

        return $errors;
    }

    /**
     * 计算账单汇总信息
     */
    public static function calculateBillSummary(array $bills): array
    {
        $summary = [
            'total_count' => count($bills),
            'total_amount' => 0,
            'paid_amount' => 0,
            'pending_amount' => 0,
            'overdue_count' => 0,
            'overdue_amount' => 0,
            'by_status' => [],
            'by_type' => [],
        ];

        foreach ($bills as $bill) {
            $summary['total_amount'] += $bill->final_amount ?? 0;
            $summary['paid_amount'] += $bill->paid_amount ?? 0;
            $summary['pending_amount'] += $bill->pending_amount ?? 0;

            // 统计逾期账单
            if ($bill->due_date && $bill->due_date < now() && in_array($bill->status, ['pending', 'partial_paid'])) {
                $summary['overdue_count']++;
                $summary['overdue_amount'] += $bill->pending_amount ?? 0;
            }

            // 按状态统计
            $status = $bill->status ?? 'unknown';
            if (!isset($summary['by_status'][$status])) {
                $summary['by_status'][$status] = ['count' => 0, 'amount' => 0];
            }
            $summary['by_status'][$status]['count']++;
            $summary['by_status'][$status]['amount'] += $bill->final_amount ?? 0;

            // 按类型统计
            $type = $bill->bill_type ?? 'unknown';
            if (!isset($summary['by_type'][$type])) {
                $summary['by_type'][$type] = ['count' => 0, 'amount' => 0];
            }
            $summary['by_type'][$type]['count']++;
            $summary['by_type'][$type]['amount'] += $bill->final_amount ?? 0;
        }

        return $summary;
    }
} 