<?php

namespace App\Cart\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Cart\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class CartController extends Controller
{
    protected $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * 安全的日志记录方法
     * 如果日志写入失败，不会抛出异常
     */
    private function safeLog($level, $message, $context = [])
    {
        try {
            Log::$level($message, $context);
        } catch (\Exception $e) {
            // 如果日志写入失败，尝试写入错误日志
            try {
                error_log("Laravel Log Error: " . $e->getMessage() . " | Original: " . $message);
            } catch (\Exception $fallbackError) {
                // 如果连error_log都失败，就静默忽略
                // 这样可以确保应用程序继续运行
            }
        }
    }
    
    /**
     * 获取购物车列表
     */
    public function index(Request $request)
    {
        try {
            // 检查用户认证
            $user = auth()->user();
            if (!$user) {
                $this->safeLog('warning', '获取购物车失败：用户未认证');
                return response()->json([
                    'code' => 401,
                    'message' => '用户未认证',
                    'data' => ['items' => [], 'total_count' => 0]
                ], 401);
            }

            $this->safeLog('info', '获取购物车请求', ['user_id' => $user->id]);

            $cartData = $this->cartService->getCartItems();

            $this->safeLog('info', '获取购物车成功', [
                'user_id' => $user->id,
                'items_count' => count($cartData['items'] ?? [])
            ]);

            return response()->json([
                'code' => 200,
                'message' => '获取购物车成功',
                'data' => $cartData
            ]);
        } catch (\Exception $e) {
            $this->safeLog('error', '获取购物车失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取购物车失败: ' . $e->getMessage(),
                'data' => ['items' => [], 'total_count' => 0]
            ], 500);
        }
    }
    
    /**
     * 获取购物车商品数量
     */
    public function count(Request $request)
    {
        try {
            // 检查用户是否已认证
            $user = auth()->user();
            if (!$user) {
                Log::warning('获取购物车数量失败：用户未认证');
                return response()->json([
                    'code' => 401,
                    'message' => '用户未认证',
                    'data' => 0
                ], 401);
            }

            Log::info('获取购物车数量请求', ['user_id' => $user->id]);

            $count = $this->cartService->getCartItemsCount();

            Log::info('获取购物车数量成功', [
                'user_id' => $user->id,
                'count' => $count
            ]);

            return response()->json([
                'code' => 200,
                'message' => '获取购物车数量成功',
                'data' => $count
            ]);
        } catch (\Exception $e) {
            Log::error('获取购物车数量失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取购物车数量失败: ' . $e->getMessage(),
                'data' => 0
            ], 500);
        }
    }
    
    /**
     * 添加商品到购物车
     */
    public function store(Request $request)
    {
        // 打印请求数据和用户ID
        Log::info('添加购物车请求', [
            'user_id' => auth()->id() ?? null,
            'data' => $request->all()
        ]);
        
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'sku_id' => 'nullable|integer|exists:product_skus,id',
            'unit_id' => 'nullable|integer|exists:units,id',
        ]);
        
        if ($validator->fails()) {
            Log::error('添加购物车验证失败', [
                'errors' => $validator->errors()->toArray()
            ]);
            
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $productId = $request->input('product_id');
        $quantity = $request->input('quantity', 1);
        $skuId = $request->input('sku_id');
        $unitId = $request->input('unit_id');
        
        try {
            $cartItem = $this->cartService->addToCart($productId, $quantity, $skuId, $unitId);
            
            Log::info('添加购物车成功', [
                'cart_item' => $cartItem
            ]);
            
            // 检查是否有数量调整
            $message = '商品已加入购物车';
            $product = \App\Product\Models\Product::find($productId);
            $minSaleQuantity = $product->min_sale_quantity ?? 1;
            
            if ($quantity < $minSaleQuantity && $cartItem->quantity >= $minSaleQuantity) {
                $unit = $product->getSaleDefaultUnit();
                $unitName = $unit ? $unit->name : '件';
                $message = "该商品最小起购量为{$minSaleQuantity}{$unitName}，已为您调整数量";
            }
            
            return response()->json([
                'code' => 200,
                'message' => $message,
                'data' => [
                    'cart_item' => $cartItem,
                    'adjusted_quantity' => $cartItem->quantity,
                    'original_quantity' => $quantity,
                    'min_sale_quantity' => $minSaleQuantity
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('添加购物车异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '添加购物车失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新购物车商品数量
     * 🔥 新增：处理数量低于最小起购数量时的删除逻辑
     */
    public function updateItem(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $quantity = $request->input('quantity');
        
        try {
            $cartItem = $this->cartService->updateItemQuantity($id, $quantity);

            // 🔥 修改：检查商品是否被删除或调整
            if ($cartItem === null) {
                // 商品因数量低于最小起购数量而被删除（仅当最小起购数为1时）
                return response()->json([
                    'code' => 200,
                    'message' => '商品已从购物车中移除',
                    'data' => [
                        'deleted' => true,
                        'reason' => 'below_min_quantity',
                        'silent' => true // 标记为静默删除，前端不显示提示
                    ]
                ]);
            }

            // 检查是否因为低于最小起购数量而被调整
            if (isset($cartItem->adjusted) && $cartItem->adjusted) {
                $product = \App\Product\Models\Product::find($cartItem->product_id);
                $unit = $product->getSaleDefaultUnit();
                $unitName = $unit ? $unit->name : '件';

                return response()->json([
                    'code' => 200,
                    'message' => "数量已调整到最小起购量{$cartItem->quantity}{$unitName}",
                    'data' => [
                        'cart_item' => $cartItem,
                        'deleted' => false,
                        'adjusted' => true,
                        'adjusted_quantity' => $cartItem->quantity,
                        'min_sale_quantity' => $product->min_sale_quantity ?? 1
                    ]
                ]);
            }

            return response()->json([
                'code' => 200,
                'message' => '购物车商品数量已更新',
                'data' => [
                    'cart_item' => $cartItem,
                    'deleted' => false,
                    'adjusted' => false
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '购物车商品不存在'
            ], 404);
        }
    }
    
    /**
     * 从购物车中移除商品
     */
    public function removeItem(Request $request, $id)
    {
        try {
            $result = $this->cartService->removeItem($id);
            
            return response()->json([
                'code' => 200,
                'message' => '商品已从购物车移除',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '购物车商品不存在'
            ], 404);
        }
    }
    
    /**
     * 切换商品选中状态
     */
    public function toggleSelectItem(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'is_selected' => 'required|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $isSelected = $request->input('is_selected');
        
        try {
            $cartItem = $this->cartService->toggleItemSelected($id, $isSelected);
            
            return response()->json([
                'code' => 200,
                'message' => '商品选中状态已更新',
                'data' => $cartItem
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '购物车商品不存在'
            ], 404);
        }
    }
    
    /**
     * 全选/取消全选
     */
    public function toggleSelectAll(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'is_selected' => 'required|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $isSelected = $request->input('is_selected');
        
        $result = $this->cartService->toggleAllSelected($isSelected);
        
        return response()->json([
            'code' => 200,
            'message' => $isSelected ? '已全选购物车商品' : '已取消全选',
            'data' => $result
        ]);
    }
    
    /**
     * 清空购物车
     */
    public function clear(Request $request)
    {
        $result = $this->cartService->clearCart();
        
        return response()->json([
            'code' => 200,
            'message' => '购物车已清空',
            'data' => $result
        ]);
    }
    
    /**
     * 合并本地购物车
     */
    public function merge(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.id' => 'required|integer|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.sku_id' => 'nullable|integer',
            'items.*.unit_id' => 'nullable|integer',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $localCartItems = $request->input('items');
        
        $result = $this->cartService->mergeLocalCart($localCartItems);
        
        return response()->json([
            'code' => 200,
            'message' => '本地购物车已合并',
            'data' => $result
        ]);
    }
    
    /**
     * 管理后台 - 获取所有用户的购物车列表
     */
    public function adminIndex(Request $request)
    {
        // 这里只是示例，实际实现可能需要根据您的业务需求调整
        $carts = \App\Cart\Models\Cart::with(['user', 'items.product'])->paginate(15);
        
        return view('admin.cart.index', compact('carts'));
    }
    
    /**
     * 管理后台 - 获取特定用户的购物车
     */
    public function adminUserCart(Request $request, $userId)
    {
        // 这里只是示例，实际实现可能需要根据您的业务需求调整
        $user = \App\Models\User::findOrFail($userId);
        $cart = \App\Cart\Models\Cart::where('user_id', $userId)->with(['items.product'])->first();
        
        return view('admin.cart.user', compact('user', 'cart'));
    }
} 