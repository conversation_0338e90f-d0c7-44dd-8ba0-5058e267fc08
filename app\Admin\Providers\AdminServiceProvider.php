<?php

namespace App\Admin\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Routing\Router;
use App\Admin\Http\Middleware\CheckEmployeeRole;
use App\Admin\Http\Middleware\CheckBackendAccess;

class AdminServiceProvider extends ServiceProvider
{
    /**
     * 注册应用服务
     *
     * @return void
     */
    public function register()
    {
        // 注册服务
    }

    /**
     * 引导应用服务
     *
     * @return void
     */
    public function boot()
    {
        // 获取Router实例
        $router = $this->app->make(Router::class);
        
        // 注册中间件别名 - 只注册Admin模块特有的中间件
        $router->aliasMiddleware('backend.access', CheckBackendAccess::class);
        // employee.role 中间件由Employee模块统一注册
        
        // 加载路由
        $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
    }
} 