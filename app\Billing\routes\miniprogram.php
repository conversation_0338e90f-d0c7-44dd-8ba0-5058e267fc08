<?php

use Illuminate\Support\Facades\Route;
use App\Billing\Http\Controllers\Api\UserBillController;
use App\Billing\Http\Controllers\Api\PaymentRecordController;
use App\Order\Http\Controllers\Api\UserOrderCorrectionController;

/*
|--------------------------------------------------------------------------
| 小程序用户端账单路由
|--------------------------------------------------------------------------
|
| 专门为微信小程序用户端提供的账单和订单更正查询API路由
| 所有路由都需要用户认证 (auth:sanctum)
|
*/

Route::prefix('api/mp')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    /*
    |--------------------------------------------------------------------------
    | 用户账单相关路由
    |--------------------------------------------------------------------------
    */
    
    // 账单列表和统计
    Route::get('/bills', [UserBillController::class, 'index']); // 获取用户账单列表
    Route::get('/bills/statistics', [UserBillController::class, 'statistics']); // 获取用户账单统计
    Route::get('/bills/pending', [UserBillController::class, 'pendingBills']); // 获取待付款账单
    Route::get('/bills/recent-corrections', [UserBillController::class, 'recentCorrections']); // 获取最新更正账单
    
    // 账单详情和操作
    Route::get('/bills/{id}', [UserBillController::class, 'show']); // 获取账单详情
    Route::post('/bills/{id}/pay', [UserBillController::class, 'pay']); // 支付账单

    /*
    |--------------------------------------------------------------------------
    | 支付记录相关路由
    |--------------------------------------------------------------------------
    */

    // 支付记录查询
    Route::get('/payment-records', [PaymentRecordController::class, 'getUserPaymentRecords']); // 获取用户支付记录
    Route::get('/payment-records/statistics', [PaymentRecordController::class, 'getUserPaymentStatistics']); // 获取用户支付统计
    
    /*
    |--------------------------------------------------------------------------
    | 订单更正相关路由
    |--------------------------------------------------------------------------
    */
    
    // 订单更正查询
    Route::get('/orders/{orderId}/corrections', [UserOrderCorrectionController::class, 'getCorrectionResult']); // 获取订单更正结果
    Route::get('/corrections/statistics', [UserOrderCorrectionController::class, 'getUserStats']); // 获取用户更正统计
    Route::get('/corrections/recent', [UserOrderCorrectionController::class, 'getRecentCorrections']); // 获取最近更正记录
    
});

/*
|--------------------------------------------------------------------------
| 兼容性路由 - 支持旧版API调用
|--------------------------------------------------------------------------
*/

Route::prefix('api')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    // 用户账单兼容路由
    Route::get('/user/bills', [UserBillController::class, 'index']);
    Route::get('/user/bills/statistics', [UserBillController::class, 'statistics']);
    Route::get('/user/bills/{id}', [UserBillController::class, 'show']);
    Route::post('/user/bills/{id}/pay', [UserBillController::class, 'pay']);
    
    // 订单更正兼容路由
    Route::get('/user/orders/{orderId}/corrections', [UserOrderCorrectionController::class, 'getCorrectionResult']);
    Route::get('/user/corrections/statistics', [UserOrderCorrectionController::class, 'getUserStats']);
    
});

/*
|--------------------------------------------------------------------------
| 公共查询路由 - 不需要认证的统计信息
|--------------------------------------------------------------------------
*/

Route::prefix('public')->group(function () {
    
    // 公共统计信息（用于展示，不涉及用户隐私）
    Route::get('/billing/system-stats', function () {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'total_bills_processed' => 1000,
                'total_corrections_handled' => 150,
                'average_correction_rate' => 15.0,
                'system_uptime' => '99.9%',
                'last_updated' => now(),
            ]
        ]);
    });
    
});
