<?php

namespace App\Billing\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Billing\Models\PaymentRecord;

class ProcessPaymentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'payment_method' => 'required|string|in:wechat,alipay,cash,bank_transfer,cod,balance,mixed,other',
            'payment_amount' => 'required|numeric|min:0.01',
            'balance_used' => 'nullable|numeric|min:0',
            'transaction_id' => 'nullable|string|max:255',
            'external_payment_no' => 'nullable|string|max:255',
            'payment_details' => 'nullable|array',
            'received_by' => 'nullable|integer|exists:employees,id',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    public function messages(): array
    {
        return [
            'payment_method.required' => '支付方式不能为空',
            'payment_method.in' => '支付方式不正确',
            'payment_amount.required' => '支付金额不能为空',
            'payment_amount.numeric' => '支付金额必须为数字',
            'payment_amount.min' => '支付金额不能小于0.01',
            'balance_used.numeric' => '使用余额必须为数字',
            'balance_used.min' => '使用余额不能为负数',
            'received_by.exists' => '收款员工不存在',
            'notes.max' => '备注不能超过1000个字符',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // 余额不能超过支付金额
            $balanceUsed = $this->balance_used ?? 0;
            
            if ($balanceUsed > $this->payment_amount) {
                $validator->errors()->add('payment_amount', '余额使用金额不能超过支付金额');
            }
        });
    }
} 