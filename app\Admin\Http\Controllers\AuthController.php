<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Employee\Http\Controllers\AuthController as EmployeeAuthController;
use Illuminate\Http\Request;

/**
 * 员工认证控制器（兼容层）
 * 
 * 这是一个兼容层，代理到新的模块化AuthController
 * 用于保持向后兼容性，以支持旧代码引用
 */
class AuthController extends Controller
{
    /**
     * 代理的控制器实例
     *
     * @var \App\Employee\Http\Controllers\AuthController
     */
    protected $controller;

    /**
     * 创建一个新的控制器实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->controller = app()->make(EmployeeAuthController::class);
    }

    /**
     * 处理对不存在方法的调用
     *
     * @param string $method
     * @param array $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块AuthController', [
                'method' => $method,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return call_user_func_array([$this->controller, $method], $parameters);
    }

    /**
     * 员工登录
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块AuthController.login', [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->login($request);
    }

    /**
     * 获取当前登录员工信息
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function me(Request $request)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块AuthController.me', [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->me($request);
    }

    /**
     * 员工登出
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块AuthController.logout', [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->logout($request);
    }
} 