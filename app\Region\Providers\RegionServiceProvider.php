<?php

namespace App\Region\Providers;

use App\Region\Services\RegionService;
use App\Region\Services\RegionPriceService;
use App\Region\Repositories\RegionRepository;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class RegionServiceProvider extends ServiceProvider
{
    /**
     * 注册任何应用服务
     *
     * @return void
     */
    public function register()
    {
        // 注册区域仓库
        $this->app->bind(RegionRepository::class, function ($app) {
            return new RegionRepository();
        });
        
        // 注册区域服务
        $this->app->bind(RegionService::class, function ($app) {
            return new RegionService(
                $app->make(RegionRepository::class)
            );
        });
        
        // 注册区域价格服务
        $this->app->bind(RegionPriceService::class, function ($app) {
            return new RegionPriceService();
        });
    }
    
    /**
     * 引导任何应用服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutes();
        
        // 加载迁移
        $this->loadMigrations();
    }
    
    /**
     * 加载路由文件
     *
     * @return void
     */
    protected function loadRoutes()
    {
        // 加载API路由，添加api前缀
        Route::prefix('api')
            ->middleware('api')
            ->group(function () {
                $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
            });
    }
    
    /**
     * 加载迁移文件
     *
     * @return void
     */
    protected function loadMigrations()
    {
        $this->loadMigrationsFrom(__DIR__.'/../Database/Migrations');
    }
} 