<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateCategoryMapping extends Command
{
    protected $signature = 'migrate:category-mapping 
                           {--source-connection=mysql_old : 源数据库连接}
                           {--target-connection=mysql : 目标数据库连接}
                           {--dry-run : 预览模式}';

    protected $description = '迁移商品分类关联关系';

    public function handle()
    {
        $sourceConnection = $this->option('source-connection');
        $targetConnection = $this->option('target-connection');
        $dryRun = $this->option('dry-run');

        try {
            $this->info("开始迁移商品分类关联...");
            
            // 1. 构建分类映射表（老分类ID → 新分类ID）
            $categoryMapping = $this->buildCategoryMapping($sourceConnection, $targetConnection);
            
            if (empty($categoryMapping)) {
                $this->error("无法构建分类映射关系");
                return 1;
            }
            
            $this->info("成功构建 " . count($categoryMapping) . " 个分类映射关系");
            
            // 2. 获取商品分类关联数据
            $productCategories = $this->getProductCategoryRelations($sourceConnection, $categoryMapping);
            
            $this->info("找到 {$productCategories->count()} 个商品分类关联需要处理");
            
            if ($productCategories->count() == 0) {
                $this->info("没有商品分类关联需要迁移");
                return 0;
            }
            
            // 3. 显示示例数据
            $this->showSampleData($productCategories);
            
            if ($dryRun) {
                $this->info("🔍 预览模式完成");
                return 0;
            }
            
            if (!$this->confirm("确定要更新商品的分类关联吗？")) {
                $this->info("迁移已取消");
                return 0;
            }
            
            // 4. 执行迁移
            $this->updateProductCategories($targetConnection, $productCategories);
            
            $this->info("✅ 商品分类关联迁移完成！");
            
        } catch (Exception $e) {
            $this->error("❌ 迁移失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 构建分类映射表
     */
    private function buildCategoryMapping($sourceConnection, $targetConnection)
    {
        // 获取老数据库分类
        $oldCategories = DB::connection($sourceConnection)
            ->table('zjhj_bd_goods_cats')
            ->where('is_delete', 0)
            ->select('id', 'name')
            ->get()
            ->keyBy('name');
        
        // 获取新数据库分类
        $newCategories = DB::connection($targetConnection)
            ->table('categories')
            ->select('id', 'name')
            ->get()
            ->keyBy('name');
        
        $mapping = [];
        $matched = 0;
        $unmatched = [];
        
        foreach ($oldCategories as $name => $oldCat) {
            if ($newCategories->has($name)) {
                $mapping[$oldCat->id] = $newCategories[$name]->id;
                $matched++;
            } else {
                $unmatched[] = $name;
            }
        }
        
        $this->info("分类匹配结果:");
        $this->info("✅ 匹配成功: {$matched} 个");
        $this->info("❌ 无法匹配: " . count($unmatched) . " 个");
        
        if (!empty($unmatched)) {
            $this->warn("无法匹配的分类: " . implode(', ', $unmatched));
        }
        
        return $mapping;
    }
    
    /**
     * 获取商品分类关联数据
     */
    private function getProductCategoryRelations($connection, $categoryMapping)
    {
        // 获取分类信息
        $categoryInfo = $this->getCategoryInfo($connection);
        
        $relations = DB::connection($connection)
            ->table('zjhj_bd_goods_warehouse as w')
            ->join('zjhj_bd_goods_cat_relation as r', 'w.id', '=', 'r.goods_warehouse_id')
            ->where('w.is_delete', 0)
            ->where('r.is_delete', 0)
            ->whereIn('r.cat_id', array_keys($categoryMapping)) // 只处理能匹配的分类
            ->select([
                'w.id as warehouse_id',
                'w.name as product_name',
                'r.cat_id as old_category_id'
            ])
            ->get()
            ->filter(function ($item) use ($categoryInfo) {
                // 只保留二级分类的商品（parent_id > 0）
                $category = $categoryInfo[$item->old_category_id] ?? null;
                return $category && $category->parent_id > 0;
            })
            ->map(function ($item) use ($categoryMapping) {
                $item->new_category_id = $categoryMapping[$item->old_category_id];
                return $item;
            });
        
        // 按商品名称分组，为每个商品选择最相关的二级分类
        $uniqueProducts = collect();
        $groupedByProduct = $relations->groupBy('product_name');
        
        foreach ($groupedByProduct as $productName => $productRelations) {
            if ($productRelations->count() == 1) {
                // 只有一个二级分类，直接使用
                $uniqueProducts->push($productRelations->first());
            } else {
                // 多个二级分类，选择最相关的
                $bestCategory = $this->selectBestSecondLevelCategory($productName, $productRelations, $categoryInfo);
                $uniqueProducts->push($bestCategory);
            }
        }
        
        $this->info("原始关联数: " . $relations->count() . " 个（仅二级分类）");
        $this->info("去重后商品数: {$uniqueProducts->count()} 个");
        $this->info("多分类商品数: " . $groupedByProduct->filter(fn($group) => $group->count() > 1)->count() . " 个");
        
        return $uniqueProducts;
    }
    
    /**
     * 获取分类信息
     */
    private function getCategoryInfo($connection)
    {
        return DB::connection($connection)
            ->table('zjhj_bd_goods_cats')
            ->where('is_delete', 0)
            ->select('id', 'name', 'parent_id')
            ->get()
            ->keyBy('id');
    }
    
    /**
     * 为商品选择最相关的二级分类
     */
    private function selectBestSecondLevelCategory($productName, $relations, $categoryInfo)
    {
        $this->line("  商品 '{$productName}' 有 " . $relations->count() . " 个二级分类可选");
        
        // 按商品名称匹配最相关的二级分类
        $bestMatch = $this->matchByProductName($productName, $relations, $categoryInfo);
        if ($bestMatch) {
            $categoryName = $categoryInfo[$bestMatch->old_category_id]->name ?? '未知';
            $this->line("    选择: {$categoryName}");
            return $bestMatch;
        }
        
        // 如果没有匹配到，返回第一个二级分类
        $firstCategory = $relations->first();
        $categoryName = $categoryInfo[$firstCategory->old_category_id]->name ?? '未知';
        $this->line("    默认选择: {$categoryName}");
        return $firstCategory;
    }
    
    /**
     * 根据商品名称匹配最相关的分类
     */
    private function matchByProductName($productName, $relations, $categoryInfo)
    {
        // 分类关键词匹配规则
        $categoryKeywords = [
            '猪' => ['猪生鲜', '猪肉类', '猪'],
            '鸡' => ['鸡类', '禽肉类', '鸡', '鲜鸡'],
            '鸭' => ['鸭类', '禽肉类', '鸭'],
            '鹅' => ['鹅类', '禽肉类', '鹅'],
            '牛' => ['牛羊类', '牛'],
            '羊' => ['牛羊类', '羊'],
            '卤' => ['卤品类', '卤'],
            '腌' => ['腌腊类', '腌'],
            '腊' => ['腌腊类', '腊'],
            '干' => ['干调类', '干'],
            '翅' => ['鸡类', '禽肉类', '鸭类'], // 翅膀类产品
            '肠' => ['猪生鲜', '卤品类'], // 肠类产品
        ];
        
        $productNameLower = mb_strtolower($productName);
        
        // 根据商品名称关键词匹配分类
        foreach ($categoryKeywords as $keyword => $preferredCategories) {
            if (mb_strpos($productNameLower, $keyword) !== false) {
                // 在当前商品的分类中查找匹配的分类
                foreach ($relations as $relation) {
                    $category = $categoryInfo[$relation->old_category_id] ?? null;
                    if ($category && in_array($category->name, $preferredCategories)) {
                        return $relation;
                    }
                }
            }
        }
        
        return null; // 没有匹配到
    }
    
    /**
     * 显示示例数据
     */
    private function showSampleData($data)
    {
        $this->info("商品分类关联示例（前5个）:");
        
        $samples = $data->take(5);
        foreach ($samples as $index => $item) {
            $this->line("商品 " . ($index + 1) . ":");
            $this->line("  商品名称: {$item->product_name}");
            $this->line("  老分类ID: {$item->old_category_id}");
            $this->line("  新分类ID: {$item->new_category_id}");
            $this->line("");
        }
    }
    
    /**
     * 更新商品分类
     */
    private function updateProductCategories($connection, $productCategories)
    {
        $this->info("正在更新商品分类关联...");
        
        $totalProcessed = 0;
        $totalUpdated = 0;
        $totalSkipped = 0;
        
        foreach ($productCategories as $item) {
            // 通过商品名称在目标数据库中查找对应的商品
            $targetProduct = DB::connection($connection)
                ->table('products')
                ->where('name', $item->product_name)
                ->first();
                
            if (!$targetProduct) {
                $this->warn("商品名称 '{$item->product_name}' 在目标数据库中不存在，跳过");
                $totalSkipped++;
                continue;
            }
            
            // 更新商品的分类ID
            DB::connection($connection)
                ->table('products')
                ->where('id', $targetProduct->id)
                ->update([
                    'category_id' => $item->new_category_id,
                    'updated_at' => now(),
                ]);
            
            $totalUpdated++;
            $totalProcessed++;
            
            if ($totalProcessed % 50 == 0) {
                $this->info("已处理: {$totalProcessed}/{$productCategories->count()} 个商品");
            }
        }
        
        $this->info("✅ 分类关联更新完成！");
        $this->info("处理了 {$totalProcessed} 个商品");
        $this->info("成功更新 {$totalUpdated} 个商品");
        $this->info("跳过 {$totalSkipped} 个商品");
    }
} 