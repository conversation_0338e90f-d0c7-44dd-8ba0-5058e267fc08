# 账单管理模块 (Billing Module)

## 概述

账单管理模块提供完整的账单功能，包括账单创建、支付处理、调整、退款等核心功能。支持多种支付方式，包括余额支付、现金支付等。

## 主要功能

### 1. 账单管理
- 从订单自动创建账单
- 账单状态管理（草稿、待付款、已付款等）
- 账单调整（金额增减）
- 账单取消和退款

### 2. 支付处理
- 多种支付方式支持（微信、支付宝、现金、银行转账等）
- 余额支付
- 混合支付（余额+现金）
- 支付记录跟踪

### 3. 余额管理
- 用户余额变动记录
- 余额充值和消费
- 余额退款处理

### 4. 累计账单系统 🆕
- **手动累计**：用户选择多个订单合并为一个账单
- **自动累计**：系统定期自动生成累计账单
- **批量优惠**：累计订单支持批量折扣
- **智能推荐**：系统分析并推荐最佳累计方案
- **订单状态同步**：累计账单支付后自动更新所有关联订单状态

### 5. 报表统计
- 账单统计报告
- 逾期账单查询
- 支付记录统计
- 数据导出功能

## 目录结构

```
app/Billing/
├── Http/
│   ├── Controllers/
│   │   ├── BillController.php          # 账单管理控制器
│   │   └── OrderBillController.php     # 订单账单集成控制器
│   ├── Requests/
│   │   ├── CreateBillRequest.php       # 创建账单验证
│   │   ├── ProcessPaymentRequest.php   # 支付处理验证
│   │   └── AdjustBillRequest.php       # 账单调整验证
│   └── Resources/
│       ├── BillResource.php            # 账单API资源
│       ├── BillItemResource.php        # 账单明细资源
│       ├── PaymentRecordResource.php   # 支付记录资源
│       ├── BillAdjustmentResource.php  # 调整记录资源
│       └── BalanceTransactionResource.php # 余额变动资源
├── Models/
│   ├── Bill.php                        # 账单模型
│   ├── BillItem.php                    # 账单明细模型
│   ├── BillAdjustment.php              # 账单调整模型
│   ├── PaymentRecord.php               # 支付记录模型
│   └── BalanceTransaction.php          # 余额变动模型
├── Services/
│   └── BillingService.php              # 核心业务逻辑服务
├── Utils/
│   ├── BillCalculator.php              # 账单计算工具
│   └── BillExporter.php                # 数据导出工具
├── Providers/
│   └── BillingServiceProvider.php      # 服务提供者
├── routes/
│   ├── api.php                         # API路由
│   └── web.php                         # Web路由
├── config/
│   └── billing.php                     # 模块配置文件
└── README.md                           # 说明文档
```

## API 接口

### 账单管理

#### 获取账单列表
```http
GET /api/bills
```

#### 获取账单详情
```http
GET /api/bills/{bill}
```

#### 处理账单支付
```http
POST /api/bills/{bill}/payment
Content-Type: application/json

{
    "payment_method": "wechat",
    "payment_amount": 100.00,
    "balance_used": 50.00,
    "notes": "支付备注"
}
```

#### 调整账单
```http
POST /api/bills/{bill}/adjust
Content-Type: application/json

{
    "adjustment_amount": 10.00,
    "reason": "价格调整",
    "description": "调整说明"
}
```

#### 取消账单
```http
POST /api/bills/{bill}/cancel
Content-Type: application/json

{
    "reason": "用户取消"
}
```

#### 账单退款
```http
POST /api/bills/{bill}/refund
Content-Type: application/json

{
    "refund_amount": 50.00,
    "reason": "退款原因",
    "refund_method": "balance"
}
```

### 订单集成

#### 从订单创建账单
```http
POST /api/orders/{order}/create-bill
Content-Type: application/json

{
    "adjustment_amount": 0,
    "due_date": "2024-02-01 23:59:59",
    "notes": "订单账单"
}
```

#### 获取订单账单
```http
GET /api/orders/{order}/bills
```

### 累计账单 🆕

#### 获取可累计的订单列表
```http
GET /api/consolidated-bills/consolidatable-orders
Authorization: Bearer {token}
```

#### 创建累计账单
```http
POST /api/consolidated-bills/create
Authorization: Bearer {token}
Content-Type: application/json

{
    "order_ids": [1, 2, 3],
    "adjustment_amount": 10.00,
    "notes": "本月累计账单",
    "due_date": "2024-02-01"
}
```

#### 检查是否有可累计订单
```http
GET /api/consolidated-bills/check-consolidatable
Authorization: Bearer {token}
```

#### 创建周期性累计账单
```http
POST /api/consolidated-bills/create-periodic
Authorization: Bearer {token}
Content-Type: application/json

{
    "period": "monthly"
}
```

### 用户账单

#### 获取用户账单列表
```http
GET /api/user/bills
Authorization: Bearer {token}
```

### 统计查询

#### 获取逾期账单
```http
GET /api/bills/overdue/list
```

#### 获取账单统计
```http
GET /api/bills/statistics/summary
```

## 配置选项

在 `app/Billing/config/billing.php` 中可以配置：

- `default_due_days`: 默认账单到期天数
- `payment_methods`: 支持的支付方式
- `auto_processing`: 自动处理设置
- `notifications`: 通知设置

## 数据库表

账单模块使用以下数据库表：

- `bills`: 账单主表
- `bill_items`: 账单明细表
- `bill_adjustments`: 账单调整记录表
- `payment_records`: 支付记录表
- `balance_transactions`: 余额变动记录表

## 使用示例

### 创建账单服务实例
```php
use App\Billing\Services\BillingService;

$billingService = app(BillingService::class);
```

### 从订单创建账单
```php
$bill = $billingService->createBillFromOrder($order, [
    'adjustment_amount' => 10.00,
    'adjustment_reason' => '运费调整',
    'due_date' => now()->addDays(7),
    'notes' => '订单账单'
]);
```

### 处理支付
```php
$paymentRecord = $billingService->processPayment($bill, [
    'payment_method' => 'mixed',
    'payment_amount' => 100.00,
    'balance_used' => 50.00,
    'notes' => '混合支付'
]);
```

### 计算支付方案
```php
use App\Billing\Utils\BillCalculator;

$paymentPlan = BillCalculator::calculateMixedPayment($bill, [
    'use_balance' => true,
    'cash_method' => 'wechat'
]);
```

### 导出账单数据
```php
use App\Billing\Utils\BillExporter;

$bills = Bill::with(['user', 'items'])->get();
$csvContent = BillExporter::exportToCsv($bills);
```

### 累计账单操作 🆕

#### 创建累计账单
```php
use App\Billing\Services\ConsolidatedBillingService;

$consolidatedService = app(ConsolidatedBillingService::class);

// 手动创建累计账单
$consolidatedBill = $consolidatedService->createConsolidatedBill($user, [1, 2, 3], [
    'adjustment_amount' => 10.00,
    'notes' => '本月累计账单',
    'due_date' => now()->addDays(30)
]);

// 自动创建周期性累计账单
$periodicBill = $consolidatedService->createPeriodicConsolidatedBill($user, 'monthly');
```

#### 检查累计订单
```php
// 检查用户是否有可累计的订单
$hasConsolidatable = $consolidatedService->hasConsolidatableOrders($user, 2);

// 获取可累计的订单
$orders = $consolidatedService->getConsolidatableOrders($user);

// 获取累计统计信息
$statistics = $consolidatedService->getConsolidationStatistics($user);
```

#### 定时任务生成累计账单
```bash
# 生成本月累计账单
php artisan billing:generate-consolidated --period=monthly

# 为指定用户生成累计账单
php artisan billing:generate-consolidated --user-id=123

# 设置最少订单数量
php artisan billing:generate-consolidated --min-orders=3
```

## 注意事项

1. 所有金额计算都使用数据库事务确保一致性
2. 支付处理会自动更新用户余额
3. 账单状态变化会自动记录日志
4. 建议定期清理过期的草稿账单
5. 余额变动记录用于审计追踪
6. **累计账单特别说明**：
   - 累计账单支付完成后会自动更新所有关联订单状态
   - 只有待付款状态的订单才能加入累计账单
   - 累计账单退款会智能回滚关联订单状态
   - 建议设置定时任务自动生成周期性累计账单

## 扩展开发

要扩展账单模块功能，可以：

1. 在 `BillingService` 中添加新的业务方法
2. 创建新的控制器处理特定业务场景
3. 添加新的工具类处理计算逻辑
4. 扩展模型关系和属性
5. 创建定时任务处理自动化流程 