<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

/**
 * 系统设置控制器
 */
class SettingController extends Controller
{
    /**
     * 显示系统设置
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\View\View
     */
    public function index(Request $request)
    {
        // 从数据库获取所有系统设置
        $settings = DB::table('settings')->get()->keyBy('key');
        
        // 格式化设置，转换类型
        $formattedSettings = $this->formatSettings($settings);
        
        // 根据请求类型返回数据
        if ($request->expectsJson()) {
            return response()->json([
                'code' => 200,
                'message' => '获取系统设置成功',
                'data' => $formattedSettings
            ]);
        }
        
        return view('admin.settings.index', ['settings' => $formattedSettings]);
    }
    
    /**
     * 更新系统设置
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*' => 'required',
        ]);
        
        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'code' => 422,
                    'message' => '验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            return back()->withErrors($validator)->withInput();
        }
        
        $settings = $request->input('settings');
        
        // 更新设置
        foreach ($settings as $key => $value) {
            DB::table('settings')->updateOrInsert(
                ['key' => $key],
                [
                    'value' => $value,
                    'updated_at' => now()
                ]
            );
        }
        
        // 清除设置缓存
        Cache::forget('app_settings');
        
        if ($request->expectsJson()) {
            return response()->json([
                'code' => 200,
                'message' => '系统设置已更新',
                'data' => null
            ]);
        }
        
        return back()->with('success', '系统设置已更新');
    }
    
    /**
     * 格式化设置数据，转换类型
     *
     * @param  \Illuminate\Support\Collection  $settings
     * @return array
     */
    private function formatSettings($settings): array
    {
        $formatted = [];
        
        foreach ($settings as $key => $setting) {
            $value = $setting->value;
            
            // 根据键名转换类型
            if (in_array($key, ['site_maintenance', 'enable_tax', 'allow_guest_checkout'])) {
                $value = (bool) $value;
            } elseif (in_array($key, ['tax_rate', 'shipping_cost', 'min_order_amount'])) {
                $value = (float) $value;
            } elseif (in_array($key, ['items_per_page', 'session_lifetime'])) {
                $value = (int) $value;
            }
            
            $formatted[$key] = $value;
        }
        
        return $formatted;
    }
} 