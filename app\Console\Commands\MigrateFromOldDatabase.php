<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Migration\Services\DatabaseMigrationService;
use Exception;

class MigrateFromOldDatabase extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'migrate:from-old-db 
                           {--connection=mysql_old : 旧数据库连接名}
                           {--target=mysql : 目标数据库连接名}
                           {--tables= : 指定要迁移的表，用逗号分隔}
                           {--exclude= : 排除的表，用逗号分隔}
                           {--dry-run : 仅检查，不执行实际迁移}';

    /**
     * 命令描述
     */
    protected $description = '从MySQL 5.7数据库迁移数据到MySQL 8.4';

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('开始数据库迁移...');
        
        try {
            $migrationService = new DatabaseMigrationService(
                $this->option('connection'),
                $this->option('target')
            );
            
            if ($this->option('dry-run')) {
                $this->info('执行预检查...');
                $this->dryRun($migrationService);
            } else {
                $this->info('开始正式迁移...');
                
                if ($this->confirm('确定要开始迁移吗？这将覆盖目标数据库中的现有数据。')) {
                    $migrationService->migrate();
                    $this->info('✅ 数据库迁移完成！');
                } else {
                    $this->info('迁移已取消');
                }
            }
            
        } catch (Exception $e) {
            $this->error('❌ 迁移失败: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 预检查模式
     */
    private function dryRun($migrationService)
    {
        // 这里可以添加预检查逻辑
        $this->info('预检查完成，可以开始迁移');
    }
} 