<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ResetEmployeesCommand extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'employees:reset';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '重置员工表，删除非管理员账号并添加新员工';

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('开始重置员工表...');
        
        // 运行迁移
        Artisan::call('migrate --path=database/migrations/2023_08_01_000000_reset_and_add_new_employees.php');
        
        $this->info('员工表重置完成！');
        $this->info('所有员工账号的密码已设置为: 123456');
        
        return Command::SUCCESS;
    }
} 