<?php

namespace App\Billing\Console\Commands;

use Illuminate\Console\Command;
use App\Billing\Services\BillNotificationService;

/**
 * 发送账单提醒命令
 * 用于定期发送账单到期和逾期提醒
 */
class SendBillRemindersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:send-reminders 
                            {--type=all : 提醒类型 (overdue|due-soon|all)}
                            {--days-before=3 : 到期前几天发送提醒}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发送账单提醒通知（逾期提醒和即将到期提醒）';

    /**
     * 通知服务
     */
    protected $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(BillNotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $daysBefore = (int) $this->option('days-before');

        $this->info('开始发送账单提醒通知...');

        $overdueCount = 0;
        $dueSoonCount = 0;

        try {
            // 发送逾期提醒
            if ($type === 'overdue' || $type === 'all') {
                $this->info('正在发送逾期账单提醒...');
                $overdueCount = $this->notificationService->sendBatchOverdueReminders();
                $this->info("逾期提醒发送完成，成功发送 {$overdueCount} 条");
            }

            // 发送即将到期提醒
            if ($type === 'due-soon' || $type === 'all') {
                $this->info("正在发送即将到期提醒（{$daysBefore}天前）...");
                $dueSoonCount = $this->notificationService->sendBatchDueSoonReminders($daysBefore);
                $this->info("即将到期提醒发送完成，成功发送 {$dueSoonCount} 条");
            }

            $this->info('账单提醒发送任务完成！');
            $this->table(
                ['提醒类型', '发送数量'],
                [
                    ['逾期提醒', $overdueCount],
                    ['即将到期提醒', $dueSoonCount],
                    ['总计', $overdueCount + $dueSoonCount],
                ]
            );

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('发送账单提醒失败: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
