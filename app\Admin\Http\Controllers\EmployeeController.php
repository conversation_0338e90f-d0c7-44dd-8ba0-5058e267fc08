<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Employee\Http\Controllers\EmployeeController as ModularEmployeeController;
use Illuminate\Http\Request;

/**
 * 员工管理控制器（兼容层）
 * 
 * 这是一个兼容层，代理到新的模块化EmployeeController
 * 用于保持向后兼容性，以支持旧代码引用
 */
class EmployeeController extends Controller
{
    /**
     * 代理的控制器实例
     *
     * @var \App\Employee\Http\Controllers\EmployeeController
     */
    protected $controller;

    /**
     * 创建一个新的控制器实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->controller = app()->make(ModularEmployeeController::class);
    }

    /**
     * 处理对不存在方法的调用
     *
     * @param string $method
     * @param array $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块EmployeeController', [
                'method' => $method,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return call_user_func_array([$this->controller, $method], $parameters);
    }

    /**
     * 获取员工列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块EmployeeController.index', [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->index($request);
    }

    /**
     * 创建新员工
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块EmployeeController.store', [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->store($request);
    }

    /**
     * 获取指定员工
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块EmployeeController.show', [
                'employee_id' => $id,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->show($id);
    }

    /**
     * 更新指定员工
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块EmployeeController.update', [
                'employee_id' => $id,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->update($request, $id);
    }

    /**
     * 删除指定员工
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        // 记录使用旧控制器的情况，便于后续完全迁移
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Admin模块EmployeeController.destroy', [
                'employee_id' => $id,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }

        return $this->controller->destroy($request, $id);
    }
} 