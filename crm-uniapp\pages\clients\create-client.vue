<template>
	<view class="create-client-container">
		<view class="header">
			<text class="title">新增客户</text>
			<text class="subtitle" v-if="createdCount > 0">已创建 {{ createdCount }} 个</text>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>

				<!-- 商户名称 -->
				<view class="form-item">
					<text class="label required">商户名称</text>
					<input
						class="input"
						type="text"
						placeholder="请输入商户名称"
						v-model="formData.merchant_name"
						maxlength="100"
					/>
				</view>

				<!-- 联系人姓名 -->
				<view class="form-item">
					<text class="label required">联系人姓名</text>
					<input
						class="input"
						type="text"
						placeholder="请输入联系人姓名"
						v-model="formData.name"
						maxlength="50"
					/>
				</view>

				<!-- 手机号 -->
				<view class="form-item">
					<text class="label required">手机号</text>
					<input
						class="input"
						type="number"
						placeholder="请输入手机号"
						v-model="formData.phone"
						maxlength="11"
					/>
				</view>

			</view>

			<!-- 区域信息 -->
			<view class="form-section">
				<view class="section-title">区域信息</view>

				<!-- 省市区三级联动选择器 -->
				<view class="form-item">
					<text class="label">所在地区</text>
					<picker
						mode="multiSelector"
						:value="regionValue"
						:range="regionRange"
						range-key="name"
						@change="handleRegionChange"
						@columnchange="handleColumnChange"
					>
						<view class="region-picker">
							<text class="region-text" v-if="selectedRegion">{{ selectedRegion }}</text>
							<text class="region-placeholder" v-else>请选择省市区</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
			</view>

			<!-- 分配信息 -->
			<view class="form-section">
				<view class="section-title">分配信息</view>

				<!-- CRM专员 -->
				<view class="form-item">
					<text class="label">CRM专员</text>
					<picker
						:value="selectedCrmAgentIndex"
						:range="crmAgentList"
						range-key="name"
						@change="onCrmAgentChange"
					>
						<view class="picker-display">
							{{ selectedCrmAgent ? selectedCrmAgent.name : '请选择CRM专员' }}
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>

				<!-- 配送员 -->
				<view class="form-item">
					<text class="label">默认配送员</text>
					<picker
						:value="selectedDelivererIndex"
						:range="delivererList"
						range-key="name"
						@change="onDelivererChange"
					>
						<view class="picker-display">
							{{ selectedDeliverer ? selectedDeliverer.name : '请选择配送员' }}
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>

				<!-- 会员等级 -->
				<view class="form-item">
					<text class="label">会员等级</text>
					<picker
						:value="selectedMembershipLevelIndex"
						:range="membershipLevelList"
						range-key="name"
						@change="onMembershipLevelChange"
					>
						<view class="picker-display">
							{{ selectedMembershipLevel ? selectedMembershipLevel.name : '请选择会员等级' }}
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="bottom-actions">
			<button class="btn-cancel" @tap="onCancel">取消</button>
			<button class="btn-submit" @tap="onSubmit" :disabled="submitting">
				{{ submitting ? '创建中...' : '创建客户' }}
			</button>
		</view>

		<!-- 加载提示 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import clientApi from '../../api/client.js'
import membershipLevelApi from '../../api/membership-level.js'

export default {
	data() {
		return {
			loading: false,
			submitting: false,

			// 表单数据
			formData: {
				merchant_name: '',
				name: '',
				phone: '',
				province: '',
				city: '',
				district: '',
				region_id: '',
				crm_agent_id: '',
				default_employee_deliverer_id: '',
				membership_level_id: ''
			},

			// 区域选择相关数据
			regionData: [], // 从后端获取的区域数据
			regionRange: [[], [], []], // 三级选择器的数据范围
			regionValue: [0, 0, 0], // 默认选中索引：第1个省，第1个市，第1个区
			selectedRegion: '', // 显示的区域文本

			// 下拉选项数据
			crmAgentList: [],
			delivererList: [],
			membershipLevelList: [],
			selectedCrmAgent: null,
			selectedDeliverer: null,
			selectedMembershipLevel: null,
			selectedCrmAgentIndex: 0,
			selectedDelivererIndex: 0,
			selectedMembershipLevelIndex: 0,

			// 创建统计
			createdCount: 0
		}
	},

	onLoad() {
		console.log('新增客户页面加载')
		this.initData()
	},

	methods: {
		/**
		 * 初始化数据
		 */
		async initData() {
			this.loading = true
			try {
				// 加载数据
				await Promise.all([
					this.loadRegionData(),
					this.loadCrmAgents(),
					this.loadDeliverers(),
					this.loadMembershipLevels()
				])
			} catch (error) {
				console.error('初始化数据失败:', error)
				const errorMessage = this.handleApiError(error)
				uni.showToast({
					title: errorMessage || '加载数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		/**
		 * 加载区域数据
		 */
		async loadRegionData() {
			console.log('🌍 开始加载区域数据...')
			try {
				const response = await clientApi.getRegionTree({ status: true })
				console.log('📊 区域API响应:', response)

				// 处理嵌套的data结构
				let regionData = []
				if (response.data && response.data.data) {
					regionData = response.data.data || []
				} else if (response.data) {
					regionData = response.data || []
				}

				console.log('📊 处理后的区域数据:', regionData)

				if (!regionData || !Array.isArray(regionData) || regionData.length === 0) {
					console.error('❌ 区域数据无效')
					uni.showToast({
						title: '获取区域数据失败',
						icon: 'none'
					})
					return
				}

				// 保存区域数据
				this.regionData = regionData
				this.initRegionPicker()

			} catch (error) {
				console.error('❌ 加载区域数据失败:', error)
				const errorMessage = this.handleApiError(error)
				uni.showToast({
					title: errorMessage || '获取区域数据失败',
					icon: 'none'
				})
			}
		},

		/**
		 * 初始化区域选择器
		 */
		initRegionPicker() {
			console.log('🎯 初始化区域选择器...')

			if (!this.regionData || this.regionData.length === 0) {
				console.error('❌ 区域数据为空')
				return
			}

			try {
				// 使用Vue.set确保响应式更新
				this.regionRange = [[], [], []]

				// 初始化省级数据
				this.regionRange[0] = [...this.regionData]
				console.log('🏛️ 省级数据设置完成:', this.regionRange[0].length, '个省份')

				// 初始化市级数据（默认选择第一个省）
				if (this.regionData[0] && this.regionData[0].children && Array.isArray(this.regionData[0].children)) {
					this.regionRange[1] = [...this.regionData[0].children]
					console.log('🏙️ 市级数据设置完成:', this.regionRange[1].length, '个城市')

					// 初始化区级数据（默认选择第一个市）
					if (this.regionData[0].children[0] && this.regionData[0].children[0].children && Array.isArray(this.regionData[0].children[0].children)) {
						this.regionRange[2] = [...this.regionData[0].children[0].children]
						console.log('🏘️ 区级数据设置完成:', this.regionRange[2].length, '个区县')

						// 设置默认选中的区域显示文本
						const province = this.regionData[0]
						const city = this.regionData[0].children[0]
						const district = this.regionData[0].children[0].children[0]

						this.selectedRegion = `${province.name} ${city.name} ${district.name}`
						console.log('📍 默认选中区域:', this.selectedRegion)

						// 设置默认的表单数据
						this.formData.province = province.name
						this.formData.city = city.name
						this.formData.district = district.name
						this.formData.region_id = district.id || district.code

						console.log('📝 表单数据设置:', {
							province: this.formData.province,
							city: this.formData.city,
							district: this.formData.district,
							region_id: this.formData.region_id
						})
					}
				}

				// 强制更新视图
				this.$forceUpdate()
				console.log('✅ 区域选择器初始化完成')

			} catch (error) {
				console.error('❌ 初始化区域选择器失败:', error)
			}
		},

		/**
		 * 加载CRM专员列表
		 */
		async loadCrmAgents() {
			try {
				const response = await clientApi.getAvailableCrmAgents()
				console.log('CRM专员API响应:', response)

				// 处理嵌套的data结构
				if (response.data && response.data.data) {
					this.crmAgentList = response.data.data || []
				} else if (response.data) {
					this.crmAgentList = response.data || []
				} else {
					this.crmAgentList = []
				}

				console.log('CRM专员列表:', this.crmAgentList)
			} catch (error) {
				console.error('加载CRM专员列表失败:', error)
			}
		},

		/**
		 * 加载配送员列表
		 */
		async loadDeliverers() {
			try {
				const response = await clientApi.getAvailableDeliverers()
				console.log('配送员API响应:', response)

				// 处理嵌套的data结构
				if (response.data && response.data.data) {
					this.delivererList = response.data.data || []
				} else if (response.data) {
					this.delivererList = response.data || []
				} else {
					this.delivererList = []
				}

				console.log('配送员列表:', this.delivererList)
			} catch (error) {
				console.error('加载配送员列表失败:', error)
			}
		},

		/**
		 * 加载会员等级列表
		 */
		async loadMembershipLevels() {
			try {
				const response = await membershipLevelApi.getMembershipLevels()
				console.log('会员等级API响应:', response)

				// 处理嵌套的data结构
				if (response.data && response.data.data) {
					this.membershipLevelList = response.data.data || []
				} else if (response.data) {
					this.membershipLevelList = response.data || []
				} else {
					this.membershipLevelList = []
				}

				console.log('会员等级列表:', this.membershipLevelList)
			} catch (error) {
				console.error('加载会员等级列表失败:', error)
			}
		},

		/**
		 * 处理区域选择变化
		 */
		handleRegionChange(e) {
			console.log('📍 区域选择变化:', e.detail)
			const [provinceIndex, cityIndex, districtIndex] = e.detail.value

			try {
				// 更新选中值
				this.regionValue = [provinceIndex, cityIndex, districtIndex]

				// 获取选中的区域名称
				const province = this.regionRange[0] && this.regionRange[0][provinceIndex]
				const city = this.regionRange[1] && this.regionRange[1][cityIndex]
				const district = this.regionRange[2] && this.regionRange[2][districtIndex]

				console.log('选中的区域:', {
					province: province ? province.name : 'undefined',
					city: city ? city.name : 'undefined',
					district: district ? district.name : 'undefined'
				})

				if (province && city && district) {
					this.selectedRegion = `${province.name} ${city.name} ${district.name}`

					// 设置表单数据
					this.formData.province = province.name
					this.formData.city = city.name
					this.formData.district = district.name
					this.formData.region_id = district.id || district.code

					console.log('✅ 区域选择完成:', {
						selectedRegion: this.selectedRegion,
						region_id: this.formData.region_id
					})
				} else {
					console.warn('⚠️ 区域数据不完整')
				}
			} catch (error) {
				console.error('❌ 处理区域选择失败:', error)
			}
		},

		/**
		 * 处理列变化（三级联动）
		 */
		handleColumnChange(e) {
			console.log('🔄 列变化:', e.detail)
			const { column, value } = e.detail

			try {
				if (column === 0) {
					// 省份变化，更新市级数据
					console.log('🏛️ 省份变化，选择索引:', value)
					const province = this.regionData[value]
					if (province && province.children && Array.isArray(province.children)) {
						this.regionRange[1] = [...province.children]
						this.regionValue[1] = 0 // 重置市级选择
						console.log('🏙️ 更新市级数据:', province.children.length, '个城市')

						// 更新区级数据
						if (province.children[0] && province.children[0].children && Array.isArray(province.children[0].children)) {
							this.regionRange[2] = [...province.children[0].children]
							this.regionValue[2] = 0 // 重置区级选择
							console.log('🏘️ 更新区级数据:', province.children[0].children.length, '个区县')
						} else {
							this.regionRange[2] = []
							this.regionValue[2] = 0
						}
					}
				} else if (column === 1) {
					// 市级变化，更新区级数据
					console.log('🏙️ 市级变化，选择索引:', value)
					const provinceIndex = this.regionValue[0]
					const province = this.regionData[provinceIndex]
					if (province && province.children && province.children[value]) {
						const city = province.children[value]
						if (city && city.children && Array.isArray(city.children)) {
							this.regionRange[2] = [...city.children]
							this.regionValue[2] = 0 // 重置区级选择
							console.log('🏘️ 更新区级数据:', city.children.length, '个区县')
						} else {
							this.regionRange[2] = []
							this.regionValue[2] = 0
						}
					}
				}

				// 强制更新视图
				this.$forceUpdate()

			} catch (error) {
				console.error('❌ 处理列变化失败:', error)
			}
		},

		/**
		 * CRM专员选择
		 */
		onCrmAgentChange(e) {
			const index = e.detail.value
			this.selectedCrmAgentIndex = index
			this.selectedCrmAgent = this.crmAgentList[index]
			this.formData.crm_agent_id = this.selectedCrmAgent.id

			console.log('👤 CRM专员选择:', {
				index: index,
				selectedCrmAgent: this.selectedCrmAgent,
				crm_agent_id: this.formData.crm_agent_id
			})
		},

		/**
		 * 配送员选择
		 */
		onDelivererChange(e) {
			const index = e.detail.value
			this.selectedDelivererIndex = index
			this.selectedDeliverer = this.delivererList[index]
			this.formData.default_employee_deliverer_id = this.selectedDeliverer.id

			console.log('🚚 配送员选择:', {
				index: index,
				selectedDeliverer: this.selectedDeliverer,
				default_employee_deliverer_id: this.formData.default_employee_deliverer_id
			})
		},

		/**
		 * 会员等级选择
		 */
		onMembershipLevelChange(e) {
			const index = e.detail.value
			this.selectedMembershipLevelIndex = index
			this.selectedMembershipLevel = this.membershipLevelList[index]
			this.formData.membership_level_id = this.selectedMembershipLevel.id

			console.log('👑 会员等级选择:', {
				index: index,
				selectedMembershipLevel: this.selectedMembershipLevel,
				membership_level_id: this.formData.membership_level_id
			})
		},

		/**
		 * 处理API错误信息
		 */
		handleApiError(error) {
			let errorMessage = '操作失败'

			// 优先处理HTTP状态码错误
			if (error.statusCode === 422 || (error.data && error.data.code === 422)) {
				// 422 验证错误
				if (error.data && error.data.message) {
					const backendMessage = error.data.message

					// 将英文错误信息转换为中文提示
					if (backendMessage.includes('phone has already been taken')) {
						errorMessage = '该手机号码已被注册，请使用其他手机号'
					} else if (backendMessage.includes('merchant_name has already been taken')) {
						errorMessage = '该商户名称已存在，请使用其他名称'
					} else if (backendMessage.includes('name has already been taken')) {
						errorMessage = '该联系人姓名已存在，请使用其他姓名'
					} else if (backendMessage.includes('region id field must be an integer')) {
						errorMessage = '请选择有效的所在地区'
					} else if (backendMessage.includes('crm_agent_id') && backendMessage.includes('integer')) {
						errorMessage = '请选择有效的CRM专员'
					} else if (backendMessage.includes('default_employee_deliverer_id') && backendMessage.includes('integer')) {
						errorMessage = '请选择有效的配送员'
					} else if (backendMessage.includes('required')) {
						errorMessage = '请填写所有必填信息'
					} else if (backendMessage.includes('invalid')) {
						errorMessage = '输入信息格式不正确，请检查后重试'
					} else if (backendMessage.includes('must be an integer')) {
						errorMessage = '数据格式错误，请重新选择相关选项'
					} else {
						// 如果有其他中文错误信息，直接使用
						errorMessage = backendMessage || '输入信息有误，请检查后重试'
					}
				} else {
					errorMessage = '输入信息有误，请检查后重试'
				}
			} else if (error.statusCode === 401) {
				errorMessage = '登录已过期，请重新登录'
			} else if (error.statusCode === 403) {
				errorMessage = '没有权限执行此操作'
			} else if (error.statusCode === 500) {
				errorMessage = '服务器错误，请稍后重试'
			} else if (error.data && error.data.message) {
				// 处理其他后端返回的错误信息
				errorMessage = error.data.message
			} else if (error.message) {
				errorMessage = error.message
			}

			return errorMessage
		},

		/**
		 * 表单验证
		 */
		validateForm() {
			if (!this.formData.merchant_name.trim()) {
				uni.showToast({
					title: '请输入商户名称',
					icon: 'none'
				})
				return false
			}

			if (!this.formData.name.trim()) {
				uni.showToast({
					title: '请输入联系人姓名',
					icon: 'none'
				})
				return false
			}

			if (!this.formData.phone.trim()) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				})
				return false
			}

			// 手机号格式验证
			const phoneRegex = /^1[3-9]\d{9}$/
			if (!phoneRegex.test(this.formData.phone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return false
			}

			return true
		},
		


		/**
		 * 提交表单
		 */
		async onSubmit() {
			if (this.submitting) return

			if (!this.validateForm()) return

			this.submitting = true

			try {
				// 准备提交数据，添加默认密码并确保ID字段为整数
				const submitData = {
					...this.formData,
					password: '12345678', // 默认密码
					// 确保ID字段为整数类型
					region_id: this.formData.region_id ? parseInt(this.formData.region_id) : null,
					crm_agent_id: this.formData.crm_agent_id ? parseInt(this.formData.crm_agent_id) : null,
					default_employee_deliverer_id: this.formData.default_employee_deliverer_id ? parseInt(this.formData.default_employee_deliverer_id) : null,
					membership_level_id: this.formData.membership_level_id ? parseInt(this.formData.membership_level_id) : null
				}

				console.log('📤 提交数据详情:', {
					formData: this.formData,
					submitData: submitData,
					配送员信息: {
						selectedDeliverer: this.selectedDeliverer,
						deliverer_id: this.formData.default_employee_deliverer_id
					},
					CRM专员信息: {
						selectedCrmAgent: this.selectedCrmAgent,
						crm_agent_id: this.formData.crm_agent_id
					},
					会员等级信息: {
						selectedMembershipLevel: this.selectedMembershipLevel,
						membership_level_id: this.formData.membership_level_id
					},
					区域信息: {
						province: this.formData.province,
						city: this.formData.city,
						district: this.formData.district,
						region_id: this.formData.region_id
					}
				})

				const response = await clientApi.createClient(submitData)

				// 检查响应是否成功（code为200表示成功）
				if (response.code === 200) {
					// 增加创建计数
					this.createdCount++

					// 构建提示内容
					let content = '客户创建成功！'
					if (this.createdCount > 1) {
						content += `\n本次会话已创建 ${this.createdCount} 个客户。`
					}
					content += '\n是否继续创建新客户？'

					// 显示创建成功的确认对话框
					uni.showModal({
						title: '创建成功',
						content: content,
						confirmText: '继续创建',
						cancelText: '返回工作台',
						success: (res) => {
							if (res.confirm) {
								// 用户选择继续创建，重置表单
								this.resetForm()

								let toastTitle = '已重置表单，可继续创建'
								if (this.createdCount > 1) {
									toastTitle = `已创建${this.createdCount}个客户，继续创建`
								}

								uni.showToast({
									title: toastTitle,
									icon: 'success',
									duration: 2000
								})
							} else {
								// 用户选择返回工作台
								let toastTitle = '创建成功'
								if (this.createdCount > 1) {
									toastTitle = `成功创建${this.createdCount}个客户`
								}

								uni.showToast({
									title: toastTitle,
									icon: 'success',
									duration: 1500
								})

								setTimeout(() => {
									uni.switchTab({
										url: '/pages/index/index'
									})
								}, 1500)
							}
						}
					})
				} else {
					throw new Error(response.message || '创建失败')
				}
			} catch (error) {
				console.error('创建客户失败:', error)

				// 使用统一的错误处理方法
				const errorMessage = this.handleApiError(error)

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.submitting = false
			}
		},

		/**
		 * 重置表单
		 */
		resetForm() {
			console.log('🔄 开始重置表单...')

			// 重置表单数据
			this.formData = {
				merchant_name: '',
				name: '',
				phone: '',
				province: '',
				city: '',
				district: '',
				region_id: '',
				crm_agent_id: '',
				default_employee_deliverer_id: '',
				membership_level_id: ''
			}

			// 重置选择器状态
			this.selectedRegion = ''
			this.regionValue = [0, 0, 0]
			this.selectedCrmAgent = null
			this.selectedDeliverer = null
			this.selectedMembershipLevel = null
			this.selectedCrmAgentIndex = 0
			this.selectedDelivererIndex = 0
			this.selectedMembershipLevelIndex = 0

			// 重新初始化区域选择器（设置默认选中）
			if (this.regionData && this.regionData.length > 0) {
				this.initRegionPicker()
			}

			// 强制更新视图
			this.$forceUpdate()

			console.log('✅ 表单重置完成')
		},

		/**
		 * 取消
		 */
		onCancel() {
			// 如果表单有内容，询问是否确认取消
			if (this.hasFormData()) {
				uni.showModal({
					title: '确认取消',
					content: '表单中有未保存的内容，确认要取消吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack()
						}
					}
				})
			} else {
				uni.navigateBack()
			}
		},

		/**
		 * 检查表单是否有数据
		 */
		hasFormData() {
			return this.formData.merchant_name.trim() ||
				   this.formData.name.trim() ||
				   this.formData.phone.trim()
		}
	}
}
</script>

<style scoped>
.create-client-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

.header {
	background-color: #fff;
	padding: 20rpx;
	text-align: center;
	border-bottom: 1rpx solid #e9ecef;
}

.title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.subtitle {
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
	opacity: 0.8;
}

.form-container {
	padding: 20rpx;
}

.form-section {
	background-color: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.section-title {
	padding: 30rpx 30rpx 20rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	border-bottom: 1rpx solid #f0f0f0;
}

.form-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
	border-bottom: none;
}

.label {
	width: 200rpx;
	font-size: 28rpx;
	color: #333;
	flex-shrink: 0;
}

.label.required::after {
	content: '*';
	color: #ff4757;
	margin-left: 4rpx;
}

.input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	padding: 0 20rpx;
	height: 60rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	border: 1rpx solid #e9ecef;
}

.input:focus {
	border-color: #1976D2;
	background-color: #fff;
}

.picker-display {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
	height: 60rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	border: 1rpx solid #e9ecef;
	font-size: 28rpx;
	color: #333;
}

.picker-display.disabled {
	background-color: #f5f5f5;
	color: #999;
}

.picker-arrow {
	font-size: 24rpx;
	color: #999;
}

/* 区域选择器样式 */
.region-picker {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
	height: 60rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	border: 1rpx solid #e9ecef;
	font-size: 28rpx;
}

.region-text {
	color: #333;
	flex: 1;
}

.region-placeholder {
	color: #999;
	flex: 1;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	padding: 20rpx;
	background-color: #fff;
	border-top: 1rpx solid #e9ecef;
	z-index: 100;
}

.btn-cancel {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	font-size: 28rpx;
	color: #666;
	background-color: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
	margin-right: 20rpx;
}

.btn-submit {
	flex: 2;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	font-size: 28rpx;
	color: #fff;
	background-color: #1976D2;
	border: none;
	border-radius: 8rpx;
}

.btn-submit:disabled {
	background-color: #ccc;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-content {
	background-color: #fff;
	padding: 40rpx;
	border-radius: 12rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #333;
}
</style>
