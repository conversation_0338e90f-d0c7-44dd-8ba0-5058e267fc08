<?php

namespace App\Billing\Services;

use App\Billing\Models\Bill;
use App\Billing\Models\PaymentRecord;
use App\Order\Models\OrderCorrection;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

/**
 * 账单通知推送服务
 * 处理账单相关的用户通知推送
 */
class BillNotificationService
{
    /**
     * 发送账单创建通知
     */
    public function sendBillCreatedNotification(Bill $bill): bool
    {
        try {
            $user = $bill->user;
            
            $message = [
                'title' => '新账单通知',
                'content' => "您有一张新的账单待付款，账单号：{$bill->bill_no}，金额：¥{$bill->final_amount}",
                'type' => 'bill_created',
                'data' => [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'amount' => $bill->final_amount,
                    'due_date' => $bill->due_date,
                ]
            ];

            return $this->sendNotification($user, $message);

        } catch (Exception $e) {
            Log::error('发送账单创建通知失败', [
                'bill_id' => $bill->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送支付成功通知
     */
    public function sendPaymentSuccessNotification(PaymentRecord $paymentRecord): bool
    {
        try {
            $bill = $paymentRecord->bill;
            $user = $bill->user;
            
            $message = [
                'title' => '支付成功通知',
                'content' => "您的账单支付成功，支付金额：¥{$paymentRecord->amount}，账单号：{$bill->bill_no}",
                'type' => 'payment_success',
                'data' => [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'payment_id' => $paymentRecord->id,
                    'payment_amount' => $paymentRecord->amount,
                    'payment_method' => $paymentRecord->payment_method,
                    'paid_at' => $paymentRecord->paid_at,
                ]
            ];

            return $this->sendNotification($user, $message);

        } catch (Exception $e) {
            Log::error('发送支付成功通知失败', [
                'payment_record_id' => $paymentRecord->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送订单更正通知
     */
    public function sendOrderCorrectionNotification(OrderCorrection $correction): bool
    {
        try {
            $order = $correction->order;
            $user = $order->user;
            
            $adjustmentAmount = $correction->corrected_total_amount - $correction->original_total_amount;
            $adjustmentText = $adjustmentAmount > 0 ? '需补款' : '已退款';
            $adjustmentAmount = abs($adjustmentAmount);
            
            $message = [
                'title' => '订单更正通知',
                'content' => "您的订单已更正，订单号：{$order->order_no}，{$adjustmentText}：¥{$adjustmentAmount}",
                'type' => 'order_correction',
                'data' => [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'correction_id' => $correction->id,
                    'correction_no' => $correction->correction_no,
                    'original_amount' => $correction->original_total_amount,
                    'corrected_amount' => $correction->corrected_total_amount,
                    'adjustment_amount' => $correction->corrected_total_amount - $correction->original_total_amount,
                    'correction_reason' => $correction->correction_reason,
                ]
            ];

            return $this->sendNotification($user, $message);

        } catch (Exception $e) {
            Log::error('发送订单更正通知失败', [
                'correction_id' => $correction->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送账单逾期提醒
     */
    public function sendBillOverdueReminder(Bill $bill): bool
    {
        try {
            $user = $bill->user;
            $overdueDays = now()->diffInDays($bill->due_date);
            
            $message = [
                'title' => '账单逾期提醒',
                'content' => "您的账单已逾期{$overdueDays}天，请及时付款。账单号：{$bill->bill_no}，金额：¥{$bill->final_amount}",
                'type' => 'bill_overdue',
                'data' => [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'amount' => $bill->final_amount,
                    'due_date' => $bill->due_date,
                    'overdue_days' => $overdueDays,
                ]
            ];

            return $this->sendNotification($user, $message);

        } catch (Exception $e) {
            Log::error('发送账单逾期提醒失败', [
                'bill_id' => $bill->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送账单即将到期提醒
     */
    public function sendBillDueSoonReminder(Bill $bill): bool
    {
        try {
            $user = $bill->user;
            $daysUntilDue = $bill->due_date ? now()->diffInDays($bill->due_date, false) : 0;
            
            $message = [
                'title' => '账单到期提醒',
                'content' => "您的账单将在{$daysUntilDue}天后到期，请及时付款。账单号：{$bill->bill_no}，金额：¥{$bill->final_amount}",
                'type' => 'bill_due_soon',
                'data' => [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'amount' => $bill->final_amount,
                    'due_date' => $bill->due_date,
                    'days_until_due' => $daysUntilDue,
                ]
            ];

            return $this->sendNotification($user, $message);

        } catch (Exception $e) {
            Log::error('发送账单到期提醒失败', [
                'bill_id' => $bill->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 批量发送逾期账单提醒
     */
    public function sendBatchOverdueReminders(): int
    {
        $overdueBills = Bill::where('status', 'pending')
            ->where('due_date', '<', now())
            ->with('user')
            ->get();

        $successCount = 0;

        foreach ($overdueBills as $bill) {
            if ($this->sendBillOverdueReminder($bill)) {
                $successCount++;
            }
        }

        Log::info('批量发送逾期账单提醒完成', [
            'total_bills' => $overdueBills->count(),
            'success_count' => $successCount,
        ]);

        return $successCount;
    }

    /**
     * 批量发送即将到期提醒
     */
    public function sendBatchDueSoonReminders(int $daysBefore = 3): int
    {
        $dueSoonBills = Bill::where('status', 'pending')
            ->whereBetween('due_date', [now(), now()->addDays($daysBefore)])
            ->with('user')
            ->get();

        $successCount = 0;

        foreach ($dueSoonBills as $bill) {
            if ($this->sendBillDueSoonReminder($bill)) {
                $successCount++;
            }
        }

        Log::info('批量发送即将到期提醒完成', [
            'total_bills' => $dueSoonBills->count(),
            'success_count' => $successCount,
            'days_before' => $daysBefore,
        ]);

        return $successCount;
    }

    /**
     * 发送通知到用户
     */
    private function sendNotification(User $user, array $message): bool
    {
        try {
            // 1. 发送小程序模板消息
            $this->sendMiniProgramNotification($user, $message);
            
            // 2. 发送短信通知（如果用户开启了短信通知）
            if ($user->sms_notification_enabled ?? false) {
                $this->sendSmsNotification($user, $message);
            }
            
            // 3. 保存站内消息
            $this->saveInAppNotification($user, $message);
            
            return true;

        } catch (Exception $e) {
            Log::error('发送通知失败', [
                'user_id' => $user->id,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送小程序模板消息
     */
    private function sendMiniProgramNotification(User $user, array $message): bool
    {
        try {
            // 这里应该调用微信小程序模板消息API
            // 暂时记录日志
            Log::info('发送小程序模板消息', [
                'user_id' => $user->id,
                'openid' => $user->wechat_openid ?? '',
                'template_id' => $this->getTemplateId($message['type']),
                'data' => $message['data'],
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('发送小程序模板消息失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送短信通知
     */
    private function sendSmsNotification(User $user, array $message): bool
    {
        try {
            // 这里应该调用短信服务API
            // 暂时记录日志
            Log::info('发送短信通知', [
                'user_id' => $user->id,
                'phone' => $user->phone ?? '',
                'content' => $message['content'],
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('发送短信通知失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 保存站内消息
     */
    private function saveInAppNotification(User $user, array $message): bool
    {
        try {
            // 这里应该保存到消息表
            // 暂时记录日志
            Log::info('保存站内消息', [
                'user_id' => $user->id,
                'title' => $message['title'],
                'content' => $message['content'],
                'type' => $message['type'],
                'data' => $message['data'],
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('保存站内消息失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取模板消息ID
     */
    private function getTemplateId(string $type): string
    {
        $templates = [
            'bill_created' => 'TEMPLATE_BILL_CREATED',
            'payment_success' => 'TEMPLATE_PAYMENT_SUCCESS',
            'order_correction' => 'TEMPLATE_ORDER_CORRECTION',
            'bill_overdue' => 'TEMPLATE_BILL_OVERDUE',
            'bill_due_soon' => 'TEMPLATE_BILL_DUE_SOON',
        ];

        return $templates[$type] ?? 'TEMPLATE_DEFAULT';
    }
}
