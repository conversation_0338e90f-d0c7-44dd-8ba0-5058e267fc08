<?php

namespace App\Billing\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BalanceTransactionResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'transaction_no' => $this->transaction_no,
            'amount' => $this->amount,
            'balance_before' => $this->balance_before,
            'balance_after' => $this->balance_after,
            'transaction_type' => $this->transaction_type,
            'transaction_type_text' => $this->transaction_type_text,
            'source' => $this->source,
            'source_id' => $this->source_id,
            'description' => $this->description,
            'status' => $this->status,
            'status_text' => $this->status_text,
            
            // 时间信息
            'transaction_time' => $this->transaction_time?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            
            // 操作员信息
            'operator' => $this->whenLoaded('operator', function () {
                return [
                    'id' => $this->operator->id,
                    'name' => $this->operator->name,
                ];
            }),
            
            // 业务逻辑状态
            'is_increasing' => $this->isIncreasing(),
            'is_decreasing' => $this->isDecreasing(),
        ];
    }
} 