<?php

namespace App\Cart\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Cart extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'user_id',
    ];
    
    /**
     * 购物车与用户的关系
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * 购物车与购物车项的关系
     */
    public function items()
    {
        return $this->hasMany(CartItem::class);
    }
} 