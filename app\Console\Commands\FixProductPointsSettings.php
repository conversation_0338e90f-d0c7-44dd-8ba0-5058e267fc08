<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Product\Models\Product;

class FixProductPointsSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:fix-points-settings 
                            {--check : 只检查不修复}
                            {--product-id= : 指定商品ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查和修复商品积分设置问题';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 检查积分字段是否存在
        if (!Schema::hasColumn('products', 'points_reward_enabled')) {
            $this->error('积分功能尚未启用，请先运行数据库迁移');
            return 1;
        }

        $checkOnly = $this->option('check');
        $productId = $this->option('product-id');

        if ($checkOnly) {
            $this->info('🔍 检查模式：只检查问题，不进行修复');
        } else {
            $this->info('🔧 修复模式：检查并修复问题');
        }

        // 构建查询
        $query = Product::query();
        if ($productId) {
            $query->where('id', $productId);
            $this->info("🎯 检查指定商品 ID: {$productId}");
        }

        $products = $query->get();
        $this->info("📊 总共检查 {$products->count()} 个商品");

        $issues = [];
        $fixedCount = 0;

        foreach ($products as $product) {
            $productIssues = $this->checkProduct($product);
            
            if (!empty($productIssues)) {
                $issues[$product->id] = $productIssues;
                
                if (!$checkOnly) {
                    $fixed = $this->fixProduct($product, $productIssues);
                    if ($fixed) {
                        $fixedCount++;
                    }
                }
            }
        }

        // 显示结果
        $this->displayResults($issues, $checkOnly, $fixedCount);

        return 0;
    }

    /**
     * 检查单个商品的积分设置
     */
    private function checkProduct(Product $product): array
    {
        $issues = [];

        // 检查积分类型
        if (empty($product->points_reward_type)) {
            $issues[] = '积分奖励类型为空';
        }

        // 检查启用状态
        if (is_null($product->points_reward_enabled)) {
            $issues[] = '积分启用状态为空';
        }

        // 检查比例类型的设置
        if ($product->points_reward_type === 'rate') {
            if (is_null($product->points_reward_rate) || $product->points_reward_rate <= 0) {
                $issues[] = '积分奖励比例为空或无效';
            } elseif ($product->points_reward_rate > 1) {
                $issues[] = '积分奖励比例过大（>100%）';
            } elseif ($product->points_reward_rate < 0.0001) {
                $issues[] = '积分奖励比例过小（<0.01%）';
            }
        }

        // 检查固定类型的设置
        if ($product->points_reward_type === 'fixed') {
            if (is_null($product->points_reward_fixed) || $product->points_reward_fixed < 0) {
                $issues[] = '固定积分奖励为空或无效';
            }
        }

        return $issues;
    }

    /**
     * 修复单个商品的积分设置
     */
    private function fixProduct(Product $product, array $issues): bool
    {
        $updated = false;

        // 修复积分类型
        if (empty($product->points_reward_type)) {
            $product->points_reward_type = 'rate';
            $updated = true;
        }

        // 修复启用状态
        if (is_null($product->points_reward_enabled)) {
            $product->points_reward_enabled = true;
            $updated = true;
        }

        // 修复比例类型的设置
        if ($product->points_reward_type === 'rate') {
            if (is_null($product->points_reward_rate) || 
                $product->points_reward_rate <= 0 || 
                $product->points_reward_rate > 1 || 
                $product->points_reward_rate < 0.0001) {
                $product->points_reward_rate = 0.0100; // 默认100元获得1积分
                $updated = true;
            }
        }

        // 修复固定类型的设置
        if ($product->points_reward_type === 'fixed') {
            if (is_null($product->points_reward_fixed) || $product->points_reward_fixed < 0) {
                $product->points_reward_fixed = 1; // 默认每件商品奖励1积分
                $updated = true;
            }
        }

        if ($updated) {
            $product->save();
        }

        return $updated;
    }

    /**
     * 显示检查和修复结果
     */
    private function displayResults(array $issues, bool $checkOnly, int $fixedCount): void
    {
        if (empty($issues)) {
            $this->info('✅ 所有商品的积分设置都正常！');
            return;
        }

        $this->warn("⚠️  发现 " . count($issues) . " 个商品存在积分设置问题：");
        
        foreach ($issues as $productId => $productIssues) {
            $this->line("商品 ID {$productId}:");
            foreach ($productIssues as $issue) {
                $this->line("  - {$issue}");
            }
        }

        if (!$checkOnly) {
            if ($fixedCount > 0) {
                $this->info("✅ 已修复 {$fixedCount} 个商品的积分设置");
            } else {
                $this->warn("⚠️  没有商品被修复");
            }
        } else {
            $this->info("💡 运行 'php artisan products:fix-points-settings' 来修复这些问题");
        }
    }
}
