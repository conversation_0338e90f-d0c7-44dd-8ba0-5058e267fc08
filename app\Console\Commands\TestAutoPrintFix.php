<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\Order;
use App\Order\Observers\OrderObserver;
use App\FlyCloud\Services\FlyCloudService;
use App\FlyCloud\Jobs\AutoPrintOrderJob;
use Illuminate\Support\Facades\Log;

class TestAutoPrintFix extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:auto-print-fix {order_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试自动打印修复效果';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 自动打印修复效果测试');
        $this->info(str_repeat('=', 50));

        // 1. 查找测试订单
        $orderId = $this->argument('order_id');
        
        if ($orderId) {
            $order = Order::with(['items.product'])->find($orderId);
            if (!$order) {
                $this->error("❌ 订单 ID {$orderId} 不存在");
                return 1;
            }
        } else {
            $this->info('1️⃣ 查找测试订单:');
            $order = Order::with(['items.product'])
                ->where('status', 'confirmed')
                ->whereHas('items')
                ->first();

            if (!$order) {
                $this->error('❌ 没有找到合适的测试订单');
                $this->warn('💡 请确保有已确认的订单且包含商品');
                return 1;
            }
        }

        $this->info("✅ 找到测试订单: ID {$order->id}, 订单号: {$order->order_no}");
        $this->info("   状态: {$order->status}");
        $this->info("   商品数量: " . $order->items->count());
        $this->newLine();

        // 2. 测试手动打印方法（作为对照组）
        $this->info('2️⃣ 测试手动打印方法（对照组）:');
        try {
            $flyCloudService = app(FlyCloudService::class);
            $manualResult = $flyCloudService->printOrderReceipt($order, [
                'copies' => 1,
                'test_mode' => true
            ]);
            
            if ($manualResult) {
                $this->info('✅ 手动打印方法正常工作');
            } else {
                $this->error('❌ 手动打印方法失败');
            }
        } catch (\Exception $e) {
            $this->error('❌ 手动打印方法异常: ' . $e->getMessage());
        }
        $this->newLine();

        // 3. 测试修复后的自动打印（OrderObserver方法）
        $this->info('3️⃣ 测试修复后的自动打印（OrderObserver）:');
        try {
            $observer = new OrderObserver();
            
            // 使用反射调用受保护的方法
            $reflection = new \ReflectionClass($observer);
            $method = $reflection->getMethod('triggerWarehousePrint');
            $method->setAccessible(true);
            
            $this->info('   🖨️ 调用 triggerWarehousePrint 方法...');
            $method->invoke($observer, $order, '测试修复');
            
            $this->info('✅ 自动打印方法调用成功（检查日志查看详细结果）');
            
        } catch (\Exception $e) {
            $this->error('❌ 自动打印方法异常: ' . $e->getMessage());
        }
        $this->newLine();

        // 4. 测试修复后的队列任务
        $this->info('4️⃣ 测试修复后的队列任务（AutoPrintOrderJob）:');
        try {
            $job = new AutoPrintOrderJob($order->id, '测试修复');
            
            $this->info('   🖨️ 执行队列任务...');
            $job->handle($flyCloudService);
            
            $this->info('✅ 队列任务执行成功（检查日志查看详细结果）');
            
        } catch (\Exception $e) {
            $this->error('❌ 队列任务异常: ' . $e->getMessage());
        }
        $this->newLine();

        // 5. 对比修复前后的方法调用
        $this->info('5️⃣ 修复前后对比:');
        $this->info('   修复前: 调用 printOrderByWarehouses() - 只返回临时成功');
        $this->info('   修复后: 调用 printOrderReceipt() - 执行真正的分仓库打印');
        $this->info('   结果: 自动打印现在使用与手动打印相同的逻辑');
        $this->newLine();

        // 6. 验证打印机配置
        $this->info('6️⃣ 验证打印机配置:');
        try {
            $warehouseGroups = $flyCloudService->getOrderWarehouses($order);
            $this->info('   涉及仓库: ' . implode(', ', $warehouseGroups));
            
            foreach ($warehouseGroups as $warehouseId) {
                $bindings = $flyCloudService->getWarehousePrinterBindings($warehouseId);
                if (empty($bindings)) {
                    $this->warn("   ⚠️  仓库 {$warehouseId} 无绑定打印机");
                } else {
                    foreach ($bindings as $binding) {
                        $status = $binding['printer']['status'] ?? 'unknown';
                        $icon = $status === 'online' ? '✅' : '❌';
                        $this->info("   {$icon} 仓库 {$warehouseId}: {$binding['printer']['name']} ({$status})");
                    }
                }
            }
        } catch (\Exception $e) {
            $this->error('   ❌ 获取打印机配置失败: ' . $e->getMessage());
        }
        $this->newLine();

        // 7. 总结
        $this->info('7️⃣ 修复总结:');
        $this->info('   ✅ 已修复 OrderObserver::triggerWarehousePrint() 方法');
        $this->info('   ✅ 已修复 AutoPrintOrderJob::handle() 方法');
        $this->info('   ✅ 自动打印现在使用与手动打印相同的逻辑');
        $this->info('   ✅ 移除了对未实现的 printOrderByWarehouses() 方法的依赖');
        $this->newLine();

        $this->info('🎉 自动打印修复测试完成！');
        $this->warn('💡 建议: 在生产环境中测试一个真实的订单确认流程');

        return 0;
    }
}
