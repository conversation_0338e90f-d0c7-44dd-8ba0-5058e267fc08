<?php

namespace App\Billing\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Billing\Services\BillingService;
use App\Billing\Http\Resources\UserBillResource;
use App\Billing\Http\Resources\PaymentRecordResource;
use App\Order\Models\OrderCorrection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 用户端账单API控制器
 * 专门为小程序用户端提供账单查询、支付等功能
 */
class UserBillController extends Controller
{
    protected $billingService;

    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * 获取用户账单列表
     * GET /api/mp/bills
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:50',
                'bill_type' => 'string|in:order,adjustment,refund,supplement',
                'status' => 'string|in:draft,pending,paid,cancelled,consolidated',
                'payment_status' => 'string|in:unpaid,partial,paid,refunded',
                'has_correction' => 'boolean', // 是否有更正
                'date_from' => 'date',
                'date_to' => 'date',
            ]);

            $perPage = $params['per_page'] ?? 15;
            $filters = array_merge($params, ['user_id' => $user->id]);

            // 获取用户账单（包含更正信息）
            $bills = $this->billingService->getUserBillsWithCorrections($user, $filters);
            $paginatedBills = $bills->paginate($perPage);

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'bills' => UserBillResource::collection($paginatedBills),
                    'pagination' => [
                        'total' => $paginatedBills->total(),
                        'per_page' => $paginatedBills->perPage(),
                        'current_page' => $paginatedBills->currentPage(),
                        'last_page' => $paginatedBills->lastPage(),
                        'from' => $paginatedBills->firstItem(),
                        'to' => $paginatedBills->lastItem(),
                    ]
                ]
            ]);

        } catch (Exception $e) {
            Log::error('获取用户账单列表失败', [
                'user_id' => Auth::id(),
                'params' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取账单列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取账单详情
     * GET /api/mp/bills/{id}
     */
    public function show(Request $request, int $billId): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // 获取账单详情（包含更正轨迹）
            $bill = $this->billingService->getUserBillDetail($user, $billId);
            
            if (!$bill) {
                return response()->json([
                    'code' => 404,
                    'message' => '账单不存在或无权访问'
                ], 404);
            }

            // 获取更正轨迹
            $correctionTrail = $this->billingService->getBillCorrectionTrail($billId);

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'bill' => new UserBillResource($bill),
                    'correction_trail' => $correctionTrail,
                    'payment_records' => PaymentRecordResource::collection($bill->paymentRecords)
                ]
            ]);

        } catch (Exception $e) {
            Log::error('获取用户账单详情失败', [
                'user_id' => Auth::id(),
                'bill_id' => $billId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取账单详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户账单统计概览
     * GET /api/mp/bills/statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $params = $request->validate([
                'period' => 'string|in:week,month,quarter,year',
                'date_from' => 'date',
                'date_to' => 'date',
            ]);

            // 获取用户账单统计
            $statistics = $this->billingService->getUserBillStatistics($user, $params);
            
            // 获取用户更正统计
            $correctionStats = $this->billingService->getUserCorrectionStatistics($user, $params);

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'overview' => [
                        'total_bills' => $statistics['total_bills'] ?? 0,
                        'pending_amount' => $statistics['pending_amount'] ?? 0,
                        'paid_amount' => $statistics['paid_amount'] ?? 0,
                        'refunded_amount' => $statistics['refunded_amount'] ?? 0,
                        'pending_bills_count' => $statistics['pending_bills_count'] ?? 0,
                    ],
                    'correction_stats' => [
                        'total_corrections' => $correctionStats['total_corrections'] ?? 0,
                        'correction_rate' => $correctionStats['correction_rate'] ?? 0,
                        'average_adjustment' => $correctionStats['average_adjustment'] ?? 0,
                        'total_saved' => $correctionStats['total_saved'] ?? 0,
                        'recent_corrections' => $correctionStats['recent_corrections'] ?? [],
                    ],
                    'payment_methods' => $statistics['payment_methods'] ?? [],
                    'monthly_trend' => $statistics['monthly_trend'] ?? [],
                ]
            ]);

        } catch (Exception $e) {
            Log::error('获取用户账单统计失败', [
                'user_id' => Auth::id(),
                'params' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 用户支付账单
     * POST /api/mp/bills/{id}/pay
     */
    public function pay(Request $request, int $billId): JsonResponse
    {
        try {
            $user = Auth::user();

            $params = $request->validate([
                'payment_method' => 'required|string|in:wechat,alipay,cash,bank_transfer',
                'payment_type' => 'string|in:full,partial',
                'amount' => 'numeric|min:0.01', // 部分支付时需要
                'remark' => 'string|max:500',
            ]);

            // 验证账单归属
            $bill = $this->billingService->getUserBillDetail($user, $billId);
            if (!$bill) {
                return response()->json([
                    'code' => 404,
                    'message' => '账单不存在或无权访问'
                ], 404);
            }

            // 检查账单状态
            if ($bill->status !== 'pending') {
                return response()->json([
                    'code' => 400,
                    'message' => '账单状态不允许支付'
                ], 400);
            }

            // 处理支付
            $paymentResult = $this->billingService->processUserPayment($bill, $params);

            return response()->json([
                'code' => 200,
                'message' => '支付处理成功',
                'data' => $paymentResult
            ]);

        } catch (Exception $e) {
            Log::error('用户支付账单失败', [
                'user_id' => Auth::id(),
                'bill_id' => $billId,
                'params' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '支付处理失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取最新更正账单
     * GET /api/mp/bills/recent-corrections
     */
    public function recentCorrections(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $params = $request->validate([
                'limit' => 'integer|min:1|max:20',
            ]);

            $limit = $params['limit'] ?? 5;

            // 获取最近的更正账单
            $recentCorrections = $this->billingService->getUserRecentCorrections($user, $limit);

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $recentCorrections->map(function ($bill) {
                    return [
                        'bill_id' => $bill->id,
                        'bill_no' => $bill->bill_no,
                        'order_id' => $bill->order_id,
                        'order_no' => $bill->order->order_no ?? '',
                        'original_amount' => $bill->original_amount,
                        'final_amount' => $bill->final_amount,
                        'adjustment_amount' => $bill->final_amount - $bill->original_amount,
                        'correction_reason' => $bill->correction_reason ?? '',
                        'corrected_at' => $bill->updated_at,
                        'payment_status' => $bill->payment_status,
                        'status' => $bill->status,
                    ];
                })
            ]);

        } catch (Exception $e) {
            Log::error('获取最新更正账单失败', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取最新更正失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取待付款账单
     * GET /api/mp/bills/pending
     */
    public function pendingBills(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $params = $request->validate([
                'limit' => 'integer|min:1|max:50',
                'sort' => 'string|in:amount_asc,amount_desc,due_date_asc,due_date_desc,created_asc,created_desc',
            ]);

            $limit = $params['limit'] ?? 10;
            $sort = $params['sort'] ?? 'due_date_asc';

            // 获取待付款账单
            $pendingBills = $this->billingService->getUserPendingBills($user, $limit, $sort);

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => UserBillResource::collection($pendingBills)
            ]);

        } catch (Exception $e) {
            Log::error('获取待付款账单失败', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取待付款账单失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
