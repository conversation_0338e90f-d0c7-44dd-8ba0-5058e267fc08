<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class UpdateCrmSpecialistForUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:crm-specialist
                            {--dry-run : 只显示将要更新的数据，不实际执行}
                            {--limit=100 : 限制处理的用户数量}
                            {--offset=0 : 跳过的记录数}
                            {--show-structure : 显示老系统用户表结构}
                            {--recommender-id=18 : 要查找的推荐人ID}
                            {--target-agent-id=11 : 设置的目标CRM代理ID}
                            {--check-old : 直接检查老系统中是否有推荐人ID为指定值的用户}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新从旧系统推荐人ID为指定值的用户到新系统的CRM代理ID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始更新用户CRM代理关系...');
        
        $dryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');
        $offset = (int) $this->option('offset');
        $showStructure = $this->option('show-structure');
        $recommenderId = (int) $this->option('recommender-id');
        $targetAgentId = (int) $this->option('target-agent-id');
        $checkOld = $this->option('check-old');
        
        if ($dryRun) {
            $this->warn('这是预览模式，不会实际修改数据');
        }
        
        $this->info("要查找的推荐人ID: {$recommenderId}");
        $this->info("要设置的CRM代理ID: {$targetAgentId}");
        
        try {
            // 测试老数据库连接
            $this->info('测试老数据库连接...');
            $oldConnection = DB::connection('mysql_old');
            $oldConnection->getPdo();
            $this->info('✅ 老数据库连接成功');
            
            // 显示表结构
            if ($showStructure) {
                $this->info('获取老系统用户信息表结构...');
                
                // 检查表是否存在
                $tables = DB::connection('mysql_old')->select("SHOW TABLES LIKE 'zjhj_bd_user_info'");
                if (empty($tables)) {
                    $this->error('老系统中不存在zjhj_bd_user_info表');
                    return 1;
                }
                
                $tableColumns = DB::connection('mysql_old')
                    ->select('SHOW COLUMNS FROM zjhj_bd_user_info');
                
                $this->info('老系统用户信息表结构：');
                foreach ($tableColumns as $column) {
                    $this->line("- {$column->Field}: {$column->Type}");
                }
                
                // 获取一条用户记录并显示所有字段
                $sampleUserInfo = DB::connection('mysql_old')->table('zjhj_bd_user_info')
                    ->first();
                
                if ($sampleUserInfo) {
                    $this->info('样例用户信息记录字段：');
                    foreach ((array)$sampleUserInfo as $field => $value) {
                        $this->line("- {$field}: " . (is_null($value) ? 'NULL' : $value));
                    }
                    
                    // 关联查询主用户表信息
                    $sampleUser = DB::connection('mysql_old')->table('zjhj_bd_user')
                        ->where('id', $sampleUserInfo->user_id)
                        ->first();
                        
                    if ($sampleUser) {
                        $this->info("对应的用户表信息 (ID: {$sampleUserInfo->user_id}):");
                        $this->line("- username (openid): {$sampleUser->username}");
                        $this->line("- mobile: " . ($sampleUser->mobile ?? 'NULL'));
                        $this->line("- nickname: {$sampleUser->nickname}");
                    }
                }
                
                // 尝试查询推荐人为特定ID的用户
                $this->info("尝试在zjhj_bd_user_info表中查找parent_id为{$recommenderId}的记录...");
                
                $count = DB::connection('mysql_old')->table('zjhj_bd_user_info')
                    ->where('parent_id', $recommenderId)
                    ->where('is_delete', 0)
                    ->count();
                    
                $this->info("找到 {$count} 个推荐人ID为 {$recommenderId} 的用户");
                
                if ($count > 0) {
                    $referredUsers = DB::connection('mysql_old')->table('zjhj_bd_user_info')
                        ->where('parent_id', $recommenderId)
                        ->where('is_delete', 0)
                        ->limit(5)
                        ->get();
                        
                    $this->info("示例用户（仅显示前5个）:");
                    foreach ($referredUsers as $userInfo) {
                        // 获取用户基本信息
                        $user = DB::connection('mysql_old')->table('zjhj_bd_user')
                            ->where('id', $userInfo->user_id)
                            ->first();
                            
                        $username = $user ? $user->username : 'N/A';
                        $mobile = $user ? ($user->mobile ?? 'N/A') : 'N/A';
                        $nickname = $user ? $user->nickname : 'N/A';
                        
                        $this->line("- 用户信息ID: {$userInfo->id}, 用户ID: {$userInfo->user_id}, 用户名: {$username}, 手机: {$mobile}, 昵称: {$nickname}");
                    }
                }
                
                return 0;
            }
            
            // 直接检查老系统
            if ($checkOld) {
                $this->info('直接在老系统中查找推荐人ID为指定值的用户...');
                
                // 查找老系统中推荐人ID为指定值的用户
                $userInfos = DB::connection('mysql_old')->table('zjhj_bd_user_info')
                    ->where('parent_id', $recommenderId)
                    ->where('is_delete', 0)
                    ->limit($limit)
                    ->offset($offset)
                    ->get();
                
                if ($userInfos->isEmpty()) {
                    $this->warn("老系统中没有找到推荐人ID为 {$recommenderId} 的用户");
                    return 0;
                }
                
                $this->info("老系统中找到 {$userInfos->count()} 个推荐人ID为 {$recommenderId} 的用户");
                
                $updatedCount = 0;
                $notFoundInNewCount = 0;
                $alreadyAssignedCount = 0;
                
                foreach ($userInfos as $userInfo) {
                    // 获取用户基本信息
                    $oldUser = DB::connection('mysql_old')->table('zjhj_bd_user')
                        ->where('id', $userInfo->user_id)
                        ->first();
                        
                    if (!$oldUser) {
                        $this->line("用户信息ID {$userInfo->id} 对应的用户ID {$userInfo->user_id} 在用户表中未找到");
                        $notFoundInNewCount++;
                        continue;
                    }
                    
                    // 通过openid或手机号在新系统查找对应用户
                    $newUser = null;
                    if (!empty($oldUser->username)) {
                        $newUser = User::where('openid', $oldUser->username)->first();
                    }
                    
                    if (!$newUser && !empty($oldUser->mobile)) {
                        $newUser = User::where('phone', $oldUser->mobile)->first();
                    }
                    
                    if (!$newUser) {
                        $this->line("老系统用户ID: {$oldUser->id}, 用户名: {$oldUser->username}, 手机: {$oldUser->mobile} 在新系统中未找到");
                        $notFoundInNewCount++;
                        continue;
                    }
                    
                    // 检查是否已经设置了CRM代理
                    if (!is_null($newUser->crm_agent_id)) {
                        $this->line("用户 {$newUser->id} ({$newUser->name}) 已经设置了CRM代理ID: {$newUser->crm_agent_id}");
                        $alreadyAssignedCount++;
                        continue;
                    }
                    
                    // 更新CRM代理ID
                    if ($dryRun) {
                        $this->info("将更新用户 {$newUser->id} ({$newUser->name}) 的CRM代理ID为 {$targetAgentId}");
                    } else {
                        $newUser->crm_agent_id = $targetAgentId;
                        $newUser->save();
                        $this->info("✅ 已更新用户 {$newUser->id} ({$newUser->name}) 的CRM代理ID为 {$targetAgentId}");
                    }
                    $updatedCount++;
                }
                
                $this->info('老系统更新完成统计:');
                $this->info("符合条件并更新: {$updatedCount}");
                $this->info("在新系统中未找到: {$notFoundInNewCount}");
                $this->info("已经设置了CRM代理: {$alreadyAssignedCount}");
                
                return 0;
            }
            
            // 查找新数据库中未绑定CRM代理的用户
            $this->info('查找新数据库中未绑定CRM代理的用户...');
            $users = User::whereNull('crm_agent_id')
                ->whereNotNull('openid')
                ->limit($limit)
                ->offset($offset)
                ->get();
                
            if ($users->isEmpty()) {
                $this->warn('没有找到未绑定CRM代理且有openid的用户');
                return 0;
            }
            
            $this->info("找到 {$users->count()} 个未绑定CRM代理的用户");
            
            $updatedCount = 0;
            $notFoundCount = 0;
            $notReferredByTargetCount = 0;
            
            foreach ($users as $index => $user) {
                // 在老系统中查找对应用户
                $oldUser = DB::connection('mysql_old')->table('zjhj_bd_user')
                    ->where('username', $user->openid)
                    ->first();
                
                if (!$oldUser) {
                    $this->line("用户 {$user->id} (openid: {$user->openid}) 在老系统中未找到");
                    $notFoundCount++;
                    continue;
                }
                
                // 获取用户的推荐人信息
                $userInfo = DB::connection('mysql_old')->table('zjhj_bd_user_info')
                    ->where('user_id', $oldUser->id)
                    ->first();
                    
                if (!$userInfo) {
                    $this->line("用户 {$user->id} ({$user->name}) 在老系统用户信息表中未找到");
                    $notFoundCount++;
                    continue;
                }
                
                // 如果是第一个用户，显示详细信息以便调试
                if ($index === 0) {
                    $this->info("示例用户 {$user->id} 的老系统数据:");
                    foreach ((array)$oldUser as $field => $value) {
                        $this->line("- user.{$field}: " . (is_null($value) ? 'NULL' : $value));
                    }
                    
                    $this->info("示例用户 {$user->id} 的老系统用户信息数据:");
                    foreach ((array)$userInfo as $field => $value) {
                        $this->line("- user_info.{$field}: " . (is_null($value) ? 'NULL' : $value));
                    }
                }
                
                // 检查推荐人ID是否为目标值
                if ($userInfo->parent_id == $recommenderId) {
                    if ($dryRun) {
                        $this->info("将更新用户 {$user->id} ({$user->name}) 的CRM代理ID为 {$targetAgentId}");
                    } else {
                        $user->crm_agent_id = $targetAgentId;
                        $user->save();
                        $this->info("✅ 已更新用户 {$user->id} ({$user->name}) 的CRM代理ID为 {$targetAgentId}");
                    }
                    $updatedCount++;
                } else {
                    $this->line("用户 {$user->id} ({$user->name}) 的推荐人ID为 {$userInfo->parent_id}，不是目标ID {$recommenderId}");
                    $notReferredByTargetCount++;
                }
            }
            
            $this->info('更新完成统计:');
            $this->info("符合条件并更新: {$updatedCount}");
            $this->info("在老系统中未找到: {$notFoundCount}");
            $this->info("推荐人ID不是目标值: {$notReferredByTargetCount}");
            
        } catch (\Exception $e) {
            $this->error("更新失败: " . $e->getMessage());
            $this->error("错误位置: " . $e->getFile() . " 第 " . $e->getLine() . " 行");
            $this->error("堆栈跟踪: " . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
} 