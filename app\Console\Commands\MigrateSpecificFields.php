<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateSpecificFields extends Command
{
    protected $signature = 'migrate:fields 
                           {--source-connection=mysql_old : 源数据库连接}
                           {--target-connection=mysql : 目标数据库连接}
                           {--source-table= : 源表名}
                           {--target-table= : 目标表名}
                           {--fields= : 要迁移的字段，用逗号分隔}
                           {--key-field=id : 主键字段}
                           {--where= : WHERE条件}
                           {--dry-run : 预览模式}';

    protected $description = '按指定字段迁移数据';

    public function handle()
    {
        $sourceConnection = $this->option('source-connection');
        $targetConnection = $this->option('target-connection');
        $sourceTable = $this->option('source-table') ?: 'zjhj_bd_goods_warehouse';
        $targetTable = $this->option('target-table') ?: 'products';
        $fields = $this->option('fields') ?: 'name,subtitle';
        $keyField = $this->option('key-field');
        $whereCondition = $this->option('where') ?: 'is_delete = 0';
        $dryRun = $this->option('dry-run');

        try {
            $this->info("开始字段迁移...");
            $this->info("源表: {$sourceTable}");
            $this->info("目标表: {$targetTable}");
            $this->info("迁移字段: {$fields}");
            
            $fieldsArray = array_map('trim', explode(',', $fields));
            $fieldsArray[] = $keyField; // 添加主键字段
            
            // 检查源表和目标表
            $this->checkTables($sourceConnection, $targetConnection, $sourceTable, $targetTable);
            
            // 获取源数据
            $sourceData = $this->getSourceData($sourceConnection, $sourceTable, $fieldsArray, $whereCondition);
            
            $this->info("找到 {$sourceData->count()} 条记录需要迁移");
            
            if ($sourceData->count() == 0) {
                $this->info("没有数据需要迁移");
                return 0;
            }
            
            // 显示前几条数据示例
            $this->showDataSamples($sourceData, $fieldsArray);
            
            if ($dryRun) {
                $this->info("🔍 预览模式 - 将迁移 {$sourceData->count()} 条记录");
                return 0;
            }
            
            if (!$this->confirm("确定要迁移这些数据吗？")) {
                $this->info("迁移已取消");
                return 0;
            }
            
            // 执行迁移
            $this->migrateData($targetConnection, $targetTable, $sourceData, $fieldsArray, $keyField);
            
            $this->info("✅ 字段迁移完成！");
            
        } catch (Exception $e) {
            $this->error("❌ 迁移失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 检查表是否存在
     */
    private function checkTables($sourceConnection, $targetConnection, $sourceTable, $targetTable)
    {
        // 检查源表
        $sourceExists = DB::connection($sourceConnection)
            ->select("SHOW TABLES LIKE '{$sourceTable}'");
        if (empty($sourceExists)) {
            throw new Exception("源表 {$sourceTable} 不存在");
        }
        
        // 检查目标表
        $targetExists = DB::connection($targetConnection)
            ->select("SHOW TABLES LIKE '{$targetTable}'");
        if (empty($targetExists)) {
            throw new Exception("目标表 {$targetTable} 不存在");
        }
        
        $this->info("✅ 表检查通过");
    }
    
    /**
     * 获取源数据
     */
    private function getSourceData($connection, $table, $fields, $whereCondition)
    {
        $query = DB::connection($connection)->table($table);
        
        if ($whereCondition) {
            $query->whereRaw($whereCondition);
        }
        
        return $query->select($fields)->get();
    }
    
    /**
     * 显示数据示例
     */
    private function showDataSamples($data, $fields)
    {
        $this->info("数据示例（前5条）:");
        
        $samples = $data->take(5);
        foreach ($samples as $index => $row) {
            $this->line("记录 " . ($index + 1) . ":");
            foreach ($fields as $field) {
                $value = $row->$field ?? 'NULL';
                $this->line("  {$field}: {$value}");
            }
            $this->line("");
        }
    }
    
    /**
     * 迁移数据
     */
    private function migrateData($connection, $table, $data, $fields, $keyField)
    {
        $this->info("正在迁移数据...");
        
        $batchSize = 100;
        $totalCount = $data->count();
        $processedCount = 0;
        
        $chunks = $data->chunk($batchSize);
        
        foreach ($chunks as $chunk) {
            DB::connection($connection)->transaction(function () use ($connection, $table, $chunk, $fields, $keyField) {
                foreach ($chunk as $row) {
                    $updateData = [];
                    
                    foreach ($fields as $field) {
                        if ($field !== $keyField) {
                            $updateData[$field] = $row->$field;
                        }
                    }
                    
                                         // 添加必需字段的默认值
                     $insertData = array_merge($updateData, [
                         $keyField => $row->$keyField
                     ]);
                     
                     // 如果是products表，添加必需的默认值
                     if ($table === 'products') {
                         $insertData['base_unit_id'] = 7; // 使用默认单位ID
                         $insertData['price'] = 0.00; // 默认价格
                         $insertData['cost_price'] = 0.00; // 默认成本价
                         $insertData['status'] = 1; // 默认状态（启用）
                         $insertData['sort'] = 0; // 默认排序
                         $insertData['sales_count'] = 0; // 默认销量
                         $insertData['views_count'] = 0; // 默认浏览次数
                         $insertData['is_featured'] = 0; // 默认不推荐
                         $insertData['min_sale_quantity'] = 1.00; // 默认最小销售量
                         $insertData['inventory_policy'] = 'strict'; // 默认库存策略
                         $insertData['track_inventory'] = 1; // 默认启用库存追踪
                         $insertData['inventory_type'] = 'physical'; // 默认实物类型
                         $insertData['auto_reorder'] = 0; // 默认不自动补货
                         $insertData['points_reward_type'] = 'rate'; // 默认积分奖励类型
                         $insertData['points_reward_fixed'] = 0; // 默认固定积分
                         $insertData['points_reward_rate'] = 0.0100; // 默认积分比例
                         $insertData['points_min_amount'] = 0.00; // 默认最小积分金额
                         $insertData['points_reward_enabled'] = 1; // 默认启用积分奖励
                         $insertData['created_at'] = now();
                         $insertData['updated_at'] = now();
                     }
                     
                     // 使用 updateOrInsert 来更新或插入数据
                     DB::connection($connection)
                         ->table($table)
                         ->updateOrInsert(
                             [$keyField => $row->$keyField],
                             $insertData
                         );
                }
            });
            
            $processedCount += $chunk->count();
            $this->info("已处理: {$processedCount}/{$totalCount}");
        }
        
        $this->info("✅ 数据迁移完成");
    }
} 