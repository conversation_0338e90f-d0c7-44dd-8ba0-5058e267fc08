<?php

namespace App\Billing\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Billing\Services\BillingService;
use App\Billing\Models\Bill;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BillController extends Controller
{
    protected $billingService;

    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * 获取账单列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'search' => 'string',
                'bill_type' => 'string',
                'status' => 'string',
                'user_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date',
            ]);

            $result = $this->billingService->getBillList($params);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单列表失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取账单详情
     */
    public function show($id): JsonResponse
    {
        try {
            $bill = Bill::with(['items', 'adjustments', 'payments'])->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $bill
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单详情失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取账单统计信息
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'start_date' => 'date',
                'end_date' => 'date',
                'bill_type' => 'string',
                'status' => 'string',
            ]);

            $stats = $this->billingService->getBillStatistics($params);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计信息失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取账单报告
     */
    public function reports(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'start_date' => 'required|date',
                'end_date' => 'required|date',
                'chart_type' => 'string|in:day,week,month',
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
            ]);

            $reports = $this->billingService->getBillReports($params);

            return response()->json([
                'success' => true,
                'data' => $reports
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单报告失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取合并账单统计
     */
    public function consolidatedStats(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'start_date' => 'date',
                'end_date' => 'date',
            ]);

            $stats = $this->billingService->getConsolidatedStats($params);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取合并统计失败：' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * 支付账单
     */
    public function pay($id, Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'payment_method' => 'required|string',
                'amount' => 'required|numeric|min:0',
                'payment_details' => 'array',
            ]);

            $result = $this->billingService->payBill($id, $params);

            return response()->json([
                'success' => true,
                'message' => '支付成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '支付失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 取消账单
     */
    public function cancel($id): JsonResponse
    {
        try {
            $result = $this->billingService->cancelBillById($id);

            return response()->json([
                'success' => true,
                'message' => '账单已取消',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '取消账单失败：' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * 获取账单项目
     */
    public function items($id): JsonResponse
    {
        try {
            $bill = Bill::with('items')->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $bill->items
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取账单项目失败：' . $e->getMessage()
            ], 500);
        }
    }
} 