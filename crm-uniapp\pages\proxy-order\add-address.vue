
<template>
	<view class="add-address-container">
		<!-- 表单卡片 -->
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">新增收货地址</text>
			</view>
			
			
			<view class="form-content">
				<!-- 联系人信息 -->
				<view class="form-section">
					<text class="section-title">联系人信息</text>
					<view class="form-item">
						<text class="form-label">联系人姓名 <text class="required">*</text></text>
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入联系人姓名"
							v-model="addressForm.contact_name"
							maxlength="20"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">联系电话 <text class="required">*</text></text>
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入联系电话"
							v-model="addressForm.contact_phone"
							maxlength="11"
						/>
					</view>
				</view>
				
				<!-- 地址信息 -->
				<view class="form-section">
					<text class="section-title">地址信息</text>
					<view class="form-item">
						<text class="form-label">所在地区 <text class="required">*</text></text>
						<!-- 使用原生picker组件 -->
						<picker
							mode="multiSelector"
							:value="regionValue"
							:range="regionRange"
							range-key="name"
							@change="handleRegionChange"
							@columnchange="handleColumnChange"
						>
							<view class="region-picker">
								<text class="region-text" v-if="selectedRegion">{{ selectedRegion }}</text>
								<text class="region-placeholder" v-else>请选择省市区</text>
								<text class="arrow-icon">></text>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="form-label">详细地址 <text class="required">*</text></text>
						<textarea 
							class="form-textarea" 
							placeholder="请输入详细地址（街道、门牌号等）"
							v-model="addressForm.detail_address"
							maxlength="100"
						></textarea>
					</view>
					<view class="form-item">
						<text class="form-label">邮政编码</text>
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入邮政编码（可选）"
							v-model="addressForm.postal_code"
							maxlength="6"
						/>
					</view>
				</view>
				
				<!-- 地址标签 -->
				<view class="form-section">
					<text class="section-title">地址标签</text>
					<view class="tag-list">
						<view 
							class="tag-item" 
							:class="{ active: addressForm.tag === tag.value }"
							v-for="tag in addressTags" 
							:key="tag.value"
							@tap="selectTag(tag.value)"
						>
							<text class="tag-text">{{ tag.label }}</text>
						</view>
					</view>
					<view class="form-item" v-if="addressForm.tag === 'custom'">
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入自定义标签"
							v-model="addressForm.custom_tag"
							maxlength="10"
						/>
					</view>
				</view>
				
				<!-- 设置选项 -->
				<view class="form-section">
					<view class="form-item checkbox-item">
						<view class="checkbox-wrapper" @tap="toggleDefault">
							<view class="checkbox" :class="{ checked: addressForm.is_default }">
								<text class="checkbox-icon" v-if="addressForm.is_default">✓</text>
							</view>
							<text class="checkbox-label">设为默认地址</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 调试信息 -->
		<view class="debug-section" style="padding: 20rpx; background: #f0f0f0; margin: 20rpx;">
			<button
				class="debug-btn"
				style="background: #007aff; color: white; margin-bottom: 10rpx;"
				@tap="testRegionApi"
			>
				测试区域API
			</button>
			<view style="font-size: 24rpx; color: #666; margin-top: 10rpx;">
				<text>区域数据数量: {{ regionData.length }}</text><br>
				<text>省份数量: {{ regionRange[0].length }}</text><br>
				<text>城市数量: {{ regionRange[1].length }}</text><br>
				<text>区县数量: {{ regionRange[2].length }}</text><br>
				<text>当前选中: {{ selectedRegion }}</text><br>
				<text>loading状态: {{ loading }}</text>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-section">
			<button
				class="submit-btn"
				:class="{ disabled: !canSubmit }"
				:disabled="!canSubmit || submitting"
				@tap="submitAddress"
			>
				<text v-if="submitting">保存中...</text>
				<text v-else>保存地址</text>
			</button>
		</view>

		<!-- 加载提示（与新增客户页面保持一致） -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import addressApi from '../../api/address.js'
import clientApi from '../../api/client.js'

export default {
	data() {
		return {
			loading: false, // 添加loading状态（与新增客户页面保持一致）
			submitting: false,
			clientId: null,
			addressForm: {
				contact_name: '',
				contact_phone: '',
				province: '',
				city: '',
				district: '',
				detail_address: '',
				postal_code: '',
				tag: 'home',
				custom_tag: '',
				is_default: false
				// 注意：用户收货地址不需要region_id字段
			},

			// 地址标签选项
			addressTags: [
				{ value: 'home', label: '家' },
				{ value: 'company', label: '公司' },
				{ value: 'school', label: '学校' },
				{ value: 'custom', label: '自定义' }
			],

			// 区域选择相关数据（使用与新增客户页面相同的结构）
			regionData: [], // 从后端获取的区域数据
			regionRange: [[], [], []], // 三级选择器的数据范围
			regionValue: [0, 0, 0], // 默认选中索引：第1个省，第1个市，第1个区
			selectedRegion: '', // 显示的区域文本
			submitting: false
		}
	},

onLoad(options) {
	console.log('📱 添加地址页面加载开始')
	console.log('📱 页面参数:', options)
	console.log('📱 clientApi是否存在:', typeof clientApi)

	try {
		// 处理页面参数
		if (options && options.clientId) {
			this.clientId = parseInt(options.clientId) || null
			console.log('📱 设置clientId:', this.clientId)
		}
		if (options && options.clientName) {
			this.addressForm.contact_name = decodeURIComponent(options.clientName || '')
			console.log('📱 设置联系人姓名:', this.addressForm.contact_name)
		}
		if (options && options.clientPhone) {
			this.addressForm.contact_phone = options.clientPhone || ''
			console.log('📱 设置联系人电话:', this.addressForm.contact_phone)
		}

		// 初始化数据
		console.log('🚀 准备调用initData...')
		this.initData()
		console.log('🚀 initData调用完成')

	} catch (error) {
		console.error('❌ onLoad执行失败:', error)
	}
},

	computed: {
		canSubmit() {
			try {
				// 安全检查每个字段
				const form = this.addressForm || {}
				
				const contactName = (form.contact_name || '').trim()
				const contactPhone = (form.contact_phone || '').trim()
				const province = form.province || ''
				const city = form.city || ''
				const district = form.district || ''
				const detailAddress = (form.detail_address || '').trim()
				const tag = form.tag || ''
				const customTag = (form.custom_tag || '').trim()
				
				return contactName && 
					   contactPhone && 
					   province && 
					   city && 
					   district && 
					   detailAddress && 
					   (tag !== 'custom' || customTag)
			} catch (error) {
				console.error('表单验证计算失败:', error)
				return false
			}
		}
	},
	onError(error) {
		console.error('页面错误:', error)
		// 防止错误导致整个APP崩溃
		return true
	},
	
	methods: {
		/**
		 * 初始化数据
		 */
		async initData() {
			console.log('🌍 initData方法开始执行')
			console.log('🌍 当前loading状态:', this.loading)

			this.loading = true
			console.log('🌍 设置loading为true')

			try {
				console.log('🌍 准备调用loadRegionData...')
				await this.loadRegionData()
				console.log('✅ loadRegionData执行完成')
			} catch (error) {
				console.error('❌ initData执行失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				console.log('🌍 设置loading为false')
			}
		},

		/**
		 * 测试区域API调用
		 */
		async testRegionApi() {
			console.log('🧪 开始测试区域API...')
			console.log('🔗 测试API路径: /regions/tree')
			console.log('🔗 完整URL: http://192.168.0.167/api/regions/tree')

			// 显示加载提示
			uni.showLoading({
				title: '测试API中...'
			})

			try {
				// 直接调用API，不使用任何缓存
				const response = await clientApi.getRegionTree({
					status: true,
					_t: Date.now() // 添加时间戳防止缓存
				})

				console.log('✅ API测试成功!')
				console.log('📊 完整响应:', JSON.stringify(response, null, 2))
				console.log('📊 响应类型:', typeof response)
				console.log('📊 响应data:', response?.data)
				console.log('📊 响应data.data:', response?.data?.data)

				uni.hideLoading()

				let dataCount = 0
				if (response?.data?.data && Array.isArray(response.data.data)) {
					dataCount = response.data.data.length
				} else if (response?.data && Array.isArray(response.data)) {
					dataCount = response.data.length
				}

				uni.showToast({
					title: `API调用成功，获取到${dataCount}条数据`,
					icon: 'success',
					duration: 3000
				})

				// 如果获取到数据，尝试手动初始化
				if (dataCount > 0) {
					console.log('🔄 手动触发数据初始化...')
					await this.loadRegionData()
				}

			} catch (error) {
				uni.hideLoading()
				console.error('❌ API测试失败:', error)
				console.error('❌ 错误详情:', JSON.stringify(error, null, 2))

				uni.showToast({
					title: 'API调用失败: ' + (error.message || error.errMsg || '未知错误'),
					icon: 'none',
					duration: 5000
				})
			}
		},

		/**
		 * 加载区域数据（使用与新增客户页面相同的API）
		 */

		/**
		 * 加载区域数据（完全复制新增客户页面的逻辑）
		 */
		async loadRegionData() {
			console.log('🌍 开始加载区域数据...')
			try {
				const response = await clientApi.getRegionTree({ status: true })
				console.log('📊 区域API响应:', response)

				// 处理嵌套的data结构
				let regionData = []
				if (response.data && response.data.data) {
					regionData = response.data.data || []
				} else if (response.data) {
					regionData = response.data || []
				}

				console.log('📊 处理后的区域数据:', regionData)

				if (!regionData || !Array.isArray(regionData) || regionData.length === 0) {
					console.error('❌ 区域数据无效')
					uni.showToast({
						title: '获取区域数据失败',
						icon: 'none'
					})
					return
				}

				// 保存区域数据
				this.regionData = regionData
				this.initRegionPicker()

			} catch (error) {
				console.error('❌ 加载区域数据失败:', error)
				const errorMessage = this.handleApiError(error)
				uni.showToast({
					title: errorMessage || '获取区域数据失败',
					icon: 'none'
				})
			}
		},

		/**
		 * 初始化区域选择器（完全复制新增客户页面的逻辑）
		 */
		initRegionPicker() {
			console.log('🎯 初始化区域选择器...')

			if (!this.regionData || this.regionData.length === 0) {
				console.error('❌ 区域数据为空')
				return
			}

			try {
				// 使用Vue.set确保响应式更新
				this.regionRange = [[], [], []]

				// 初始化省级数据 - 直接使用原始数据，不重新映射
				this.regionRange[0] = [...this.regionData]
				console.log('🏛️ 省级数据设置完成:', this.regionRange[0].length, '个省份')

				// 初始化市级数据（默认选择第一个省）
				if (this.regionData[0] && this.regionData[0].children && Array.isArray(this.regionData[0].children)) {
					this.regionRange[1] = [...this.regionData[0].children]
					console.log('🏙️ 市级数据设置完成:', this.regionRange[1].length, '个城市')

					// 初始化区级数据（默认选择第一个市）
					if (this.regionData[0].children[0] && this.regionData[0].children[0].children && Array.isArray(this.regionData[0].children[0].children)) {
						this.regionRange[2] = [...this.regionData[0].children[0].children]
						console.log('🏘️ 区级数据设置完成:', this.regionRange[2].length, '个区县')

						// 设置默认选中的区域显示文本
						const province = this.regionData[0]
						const city = this.regionData[0].children[0]
						const district = this.regionData[0].children[0].children[0]

						this.selectedRegion = `${province.name} ${city.name} ${district.name}`
						console.log('📍 默认选中区域:', this.selectedRegion)

						// 设置默认的表单数据（用户收货地址只需要省市区名称）
						this.addressForm.province = province.name
						this.addressForm.city = city.name
						this.addressForm.district = district.name

						console.log('📝 添加地址页面 - 表单数据设置:', {
							province: this.addressForm.province,
							city: this.addressForm.city,
							district: this.addressForm.district
						})
					}
				}

				// 强制更新视图
				this.$forceUpdate()
				console.log('✅ 区域选择器初始化完成')

			} catch (error) {
				console.error('❌ 初始化区域选择器失败:', error)
			}
		},

		/**
		 * 处理区域选择变化（完全复制新增客户页面的逻辑）
		 */
		handleRegionChange(e) {
			console.log('📍 区域选择变化:', e.detail)
			const [provinceIndex, cityIndex, districtIndex] = e.detail.value

			try {
				// 更新选中值
				this.regionValue = [provinceIndex, cityIndex, districtIndex]

				// 获取选中的区域名称
				const province = this.regionRange[0] && this.regionRange[0][provinceIndex]
				const city = this.regionRange[1] && this.regionRange[1][cityIndex]
				const district = this.regionRange[2] && this.regionRange[2][districtIndex]

				console.log('选中的区域:', {
					province: province ? province.name : 'undefined',
					city: city ? city.name : 'undefined',
					district: district ? district.name : 'undefined'
				})

				if (province && city && district) {
					this.selectedRegion = `${province.name} ${city.name} ${district.name}`

					// 设置表单数据（用户收货地址只需要省市区名称）
					this.addressForm.province = province.name
					this.addressForm.city = city.name
					this.addressForm.district = district.name

					console.log('✅ 区域选择完成:', {
						selectedRegion: this.selectedRegion
					})
				} else {
					console.warn('⚠️ 区域数据不完整')
				}
			} catch (error) {
				console.error('❌ 处理区域选择失败:', error)
			}
		},

		/**
		 * 处理列变化（三级联动，完全复制新增客户页面的逻辑）
		 */
		handleColumnChange(e) {
			console.log('🔄 列变化:', e.detail)
			const { column, value } = e.detail

			try {
				if (column === 0) {
					// 省份变化，更新市级数据
					console.log('🏛️ 省份变化，选择索引:', value)
					const province = this.regionData[value]
					if (province && province.children && Array.isArray(province.children)) {
						this.regionRange[1] = [...province.children]
						this.regionValue[1] = 0 // 重置市级选择
						console.log('🏙️ 更新市级数据:', province.children.length, '个城市')

						// 更新区级数据
						if (province.children[0] && province.children[0].children && Array.isArray(province.children[0].children)) {
							this.regionRange[2] = [...province.children[0].children]
							this.regionValue[2] = 0 // 重置区级选择
							console.log('🏘️ 更新区级数据:', province.children[0].children.length, '个区县')
						} else {
							this.regionRange[2] = []
							this.regionValue[2] = 0
						}
					}
				} else if (column === 1) {
					// 市级变化，更新区级数据
					console.log('🏙️ 市级变化，选择索引:', value)
					const provinceIndex = this.regionValue[0]
					const province = this.regionData[provinceIndex]
					if (province && province.children && province.children[value]) {
						const city = province.children[value]
						if (city && city.children && Array.isArray(city.children)) {
							this.regionRange[2] = [...city.children]
							this.regionValue[2] = 0 // 重置区级选择
							console.log('🏘️ 更新区级数据:', city.children.length, '个区县')
						} else {
							this.regionRange[2] = []
							this.regionValue[2] = 0
						}
					}
				}

				// 强制更新视图
				this.$forceUpdate()

			} catch (error) {
				console.error('❌ 处理列变化失败:', error)
			}
		},
		
		// 选择标签
		selectTag(tag) {
			this.addressForm.tag = tag
			if (tag !== 'custom') {
				this.addressForm.custom_tag = ''
			}
		},
		
		// 切换默认地址
		toggleDefault() {
			this.addressForm.is_default = !this.addressForm.is_default
		},
		
		// 提交地址
		async submitAddress() {
			if (!this.canSubmit || this.submitting) return
			
			// 安全的手机号验证
			try {
				const phone = this.addressForm.contact_phone || ''
				const phoneRegex = /^1[3-9]\d{9}$/
				if (!phoneRegex.test(phone.trim())) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}
			} catch (validationError) {
				console.error('手机号验证失败:', validationError)
				uni.showToast({
					title: '手机号格式错误',
					icon: 'none'
				})
				return
			}
			
			this.submitting = true
			
			try {
				// 准备提交数据（用户收货地址不需要region_id）
				const submitData = {
					contact_name: (this.addressForm.contact_name || '').trim(),
					contact_phone: (this.addressForm.contact_phone || '').trim(),
					province: this.addressForm.province || '',
					city: this.addressForm.city || '',
					district: this.addressForm.district || '',
					address: (this.addressForm.detail_address || '').trim(),
					postal_code: (this.addressForm.postal_code || '').trim() || null,
					notes: this.addressForm.tag === 'custom' ? (this.addressForm.custom_tag || '').trim() : this.addressForm.tag,
					is_default: Boolean(this.addressForm.is_default)
					// 注意：用户收货地址不需要传递region_id
				}

				console.log('📤 提交地址数据:', submitData)
				console.log('🌍 区域信息:', {
					province: this.addressForm.province,
					city: this.addressForm.city,
					district: this.addressForm.district
				})
				
				// 调用API创建地址
				const response = await addressApi.createAddress(this.clientId, submitData)
				
				if (response && response.data) {
					uni.showToast({
						title: '地址保存成功',
						icon: 'success',
						duration: 1500
					})
					
					// 安全的页面栈操作
					try {
						const pages = getCurrentPages()
						if (pages && pages.length >= 2) {
							const prevPage = pages[pages.length - 2]
							if (prevPage && prevPage.$vm) {
								prevPage.$vm.data = prevPage.$vm.data || {}
								prevPage.$vm.data.newAddress = response.data
							}
						}
					} catch (pageError) {
						console.warn('页面栈数据传递失败:', pageError)
					}
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 1500)
				} else {
					throw new Error('保存失败：服务器返回数据异常')
				}
				
			} catch (error) {
				console.error('保存地址失败:', error)
				
				// 安全的错误消息提取
				let errorMessage = '保存失败，请重试'
				try {
					if (error && typeof error === 'object') {
						if (error.response && error.response.data && error.response.data.message) {
							errorMessage = String(error.response.data.message)
						} else if (error.message) {
							errorMessage = String(error.message)
						}
					}
				} catch (msgError) {
					console.warn('错误消息解析失败:', msgError)
				}
				
				// 确保错误消息不会太长，防止显示问题
				if (errorMessage.length > 50) {
					errorMessage = errorMessage.substring(0, 50) + '...'
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.submitting = false
			}
		},

		/**
		 * 处理API错误信息（复制自新增客户页面）
		 */
		handleApiError(error) {
			let errorMessage = '操作失败'

			// 优先处理HTTP状态码错误
			if (error.statusCode === 422 || (error.data && error.data.code === 422)) {
				// 422 验证错误
				if (error.data && error.data.message) {
					const backendMessage = error.data.message

					// 将英文错误信息转换为中文提示
					if (backendMessage.includes('phone has already been taken')) {
						errorMessage = '该手机号码已被注册，请使用其他手机号'
					} else if (backendMessage.includes('required')) {
						errorMessage = '请填写所有必填信息'
					} else if (backendMessage.includes('invalid')) {
						errorMessage = '输入信息格式不正确，请检查后重试'
					} else if (backendMessage.includes('must be an integer')) {
						errorMessage = '数据格式错误，请重新选择相关选项'
					} else {
						// 如果有其他中文错误信息，直接使用
						errorMessage = backendMessage || '输入信息有误，请检查后重试'
					}
				} else {
					errorMessage = '输入信息有误，请检查后重试'
				}
			} else if (error.statusCode === 401) {
				errorMessage = '登录已过期，请重新登录'
			} else if (error.statusCode === 403) {
				errorMessage = '没有权限执行此操作'
			} else if (error.statusCode === 500) {
				errorMessage = '服务器错误，请稍后重试'
			} else if (error.data && error.data.message) {
				// 处理其他后端返回的错误信息
				errorMessage = error.data.message
			} else if (error.message) {
				errorMessage = error.message
			}

			return errorMessage
		}
	}
}
</script>

<style scoped>
.add-address-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 100rpx;
}

.form-card {
	background: #ffffff;
	margin: 16rpx;
	border-radius: 8rpx;
	border: 1rpx solid #f0f0f0;
}

.form-header {
	padding: 20rpx 24rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.form-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #262626;
}

.form-content {
	padding: 20rpx 24rpx;
}

.form-section {
	margin-bottom: 24rpx;
}

.form-section:last-child {
	margin-bottom: 0;
}

.section-title {
	display: block;
	font-size: 26rpx;
	font-weight: 600;
	color: #262626;
	margin-bottom: 16rpx;
}

.form-item {
	margin-bottom: 16rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	display: block;
	font-size: 24rpx;
	color: #595959;
	margin-bottom: 8rpx;
}

.required {
	color: #ff4757;
}

.form-input {
	width: 100%;
	padding: 16rpx 20rpx;
	background: #f8f9fa;
	border-radius: 6rpx;
	font-size: 26rpx;
	color: #262626;
	border: 1rpx solid #e9ecef;
}

.form-input:focus {
	border-color: #1890ff;
	background: #ffffff;
}

.form-textarea {
	width: 100%;
	min-height: 100rpx;
	padding: 16rpx 20rpx;
	background: #f8f9fa;
	border-radius: 6rpx;
	font-size: 26rpx;
	color: #262626;
	border: 1rpx solid #e9ecef;
	resize: none;
}

.form-textarea:focus {
	border-color: #1890ff;
	background: #ffffff;
}

.region-picker {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 20rpx;
	background: #f8f9fa;
	border-radius: 6rpx;
	border: 1rpx solid #e9ecef;
}

.region-text {
	font-size: 26rpx;
	color: #262626;
}

.region-placeholder {
	font-size: 26rpx;
	color: #8c8c8c;
}

.arrow-icon {
	font-size: 20rpx;
	color: #bfbfbf;
}

.tag-list {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
	margin-bottom: 16rpx;
}

.tag-item {
	padding: 12rpx 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 1rpx solid #e9ecef;
}

.tag-item.active {
	background: #1890ff;
	border-color: #1890ff;
}

.tag-text {
	font-size: 24rpx;
	color: #595959;
}

.tag-item.active .tag-text {
	color: #ffffff;
}

.checkbox-item {
	margin-bottom: 0;
}

.checkbox-wrapper {
	display: flex;
	align-items: center;
}

.checkbox {
	width: 32rpx;
	height: 32rpx;
	border: 1rpx solid #d9d9d9;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12rpx;
}

.checkbox.checked {
	background: #1890ff;
	border-color: #1890ff;
}

.checkbox-icon {
	color: #ffffff;
	font-size: 20rpx;
	font-weight: 600;
}

.checkbox-label {
	font-size: 26rpx;
	color: #262626;
}

.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 16rpx 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
	width: 100%;
	padding: 16rpx;
	background: #1890ff;
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
	font-weight: 500;
}

.submit-btn.disabled {
	background: #d9d9d9;
	color: #8c8c8c;
}

/* 加载状态样式（与新增客户页面保持一致） */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	background: #ffffff;
	padding: 40rpx;
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style>