<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateProductImages extends Command
{
    protected $signature = 'migrate:product-images 
                           {--source-connection=mysql_old : 源数据库连接}
                           {--target-connection=mysql : 目标数据库连接}
                           {--source-table=zjhj_bd_goods_warehouse : 源表名}
                           {--dry-run : 预览模式}';

    protected $description = '迁移商品图片从老数据库到新的product_images表';

    public function handle()
    {
        $sourceConnection = $this->option('source-connection');
        $targetConnection = $this->option('target-connection');
        $sourceTable = $this->option('source-table');
        $dryRun = $this->option('dry-run');

        try {
            $this->info("开始迁移商品图片...");
            
            // 获取有图片的商品数据
            $sourceData = $this->getSourceData($sourceConnection, $sourceTable);
            
            $this->info("找到 {$sourceData->count()} 个商品有图片需要迁移");
            
            if ($sourceData->count() == 0) {
                $this->info("没有图片需要迁移");
                return 0;
            }
            
            // 显示数据示例
            $this->showDataSamples($sourceData);
            
            if ($dryRun) {
                $this->info("🔍 预览模式完成");
                return 0;
            }
            
            if (!$this->confirm("确定要迁移这些图片数据吗？")) {
                $this->info("迁移已取消");
                return 0;
            }
            
            // 执行迁移
            $this->migrateImages($targetConnection, $sourceData);
            
            $this->info("✅ 图片迁移完成！");
            
        } catch (Exception $e) {
            $this->error("❌ 迁移失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 获取源数据
     */
    private function getSourceData($connection, $table)
    {
        return DB::connection($connection)
            ->table($table)
            ->where('is_delete', 0)
            ->whereNotNull('pic_url')
            ->where('pic_url', '!=', '')
            ->select('id', 'name', 'pic_url')
            ->get();
    }
    
    /**
     * 显示数据示例
     */
    private function showDataSamples($data)
    {
        $this->info("图片数据示例（前3个）:");
        
        $samples = $data->take(3);
        foreach ($samples as $index => $row) {
            $this->line("商品 " . ($index + 1) . ":");
            $this->line("  ID: {$row->id}");
            $this->line("  名称: {$row->name}");
            
            // 解析pic_url
            $images = $this->parsePicUrl($row->pic_url);
            $this->line("  轮播图数量: " . count($images));
            foreach ($images as $imgIndex => $imageUrl) {
                $this->line("    图片" . ($imgIndex + 1) . ": {$imageUrl}");
            }
            $this->line("");
        }
    }
    
    /**
     * 解析pic_url字段
     */
    private function parsePicUrl($picUrl)
    {
        $images = [];
        
        if (empty($picUrl)) {
            return $images;
        }
        
        // 去除首尾空格
        $picUrl = trim($picUrl);
        
        // 方法1: 尝试解析JSON格式（如果是JSON数组）
        if ($this->isJsonFormat($picUrl)) {
            $images = $this->parseJsonFormat($picUrl);
        }
        // 方法2: 尝试按不同分隔符分割
        else {
            $images = $this->parseDelimitedFormat($picUrl);
        }
        
        // 方法3: 如果以上都没找到，检查是否是单个URL
        if (empty($images) && $this->isValidImageUrl($picUrl)) {
            $images[] = $picUrl;
        }
        
        return array_unique(array_filter($images));
    }
    
    /**
     * 检查是否是JSON格式
     */
    private function isJsonFormat($str)
    {
        $str = trim($str);
        return (substr($str, 0, 1) === '[' && substr($str, -1) === ']') || 
               (substr($str, 0, 1) === '{' && substr($str, -1) === '}');
    }
    
    /**
     * 解析JSON格式的图片数据
     */
    private function parseJsonFormat($jsonStr)
    {
        $images = [];
        
        try {
            $data = json_decode($jsonStr, true);
            
            if (is_array($data)) {
                foreach ($data as $item) {
                    if (is_string($item)) {
                        // 直接是URL字符串
                        if ($this->isValidImageUrl($item)) {
                            $images[] = $item;
                        }
                    } elseif (is_array($item)) {
                        // 是对象格式，查找URL字段
                        $urlFields = ['pic_url', 'url', 'image_url', 'src', 'path'];
                        foreach ($urlFields as $field) {
                            if (isset($item[$field]) && $this->isValidImageUrl($item[$field])) {
                                $images[] = $item[$field];
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception $e) {
            // JSON解析失败，返回空数组
        }
        
        return $images;
    }
    
    /**
     * 解析分隔符格式的图片数据
     */
    private function parseDelimitedFormat($str)
    {
        $images = [];
        
        // 常见的分隔符
        $separators = [',', ';', '|', "\n", "\r\n", '||', ',,'];
        
        foreach ($separators as $separator) {
            $parts = explode($separator, $str);
            if (count($parts) > 1) {
                foreach ($parts as $part) {
                    $part = trim($part);
                    if (!empty($part)) {
                        // 尝试从每个部分提取URL
                        $extractedUrls = $this->extractUrlsFromString($part);
                        $images = array_merge($images, $extractedUrls);
                    }
                }
                break;
            }
        }
        
        return $images;
    }
    
    /**
     * 从字符串中提取URL
     */
    private function extractUrlsFromString($str)
    {
        $urls = [];
        
        // 正则表达式匹配HTTP/HTTPS URL
        $pattern = '/https?:\/\/[^\s<>"\']+\.(jpg|jpeg|png|gif|webp|bmp)(\?[^\s<>"\']*)?/i';
        
        if (preg_match_all($pattern, $str, $matches)) {
            foreach ($matches[0] as $url) {
                if ($this->isValidImageUrl($url)) {
                    $urls[] = $url;
                }
            }
        }
        
        // 如果没有匹配到，但整个字符串看起来像URL
        if (empty($urls) && $this->isValidImageUrl($str)) {
            $urls[] = $str;
        }
        
        return $urls;
    }
    
    /**
     * 验证是否为有效的图片URL
     */
    private function isValidImageUrl($url)
    {
        $url = trim($url);
        
        // 基本URL格式检查
        if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // 检查是否包含常见图片扩展名
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
        
        return in_array($extension, $imageExtensions);
    }
    
    /**
     * 迁移图片数据
     */
    private function migrateImages($connection, $sourceData)
    {
        $this->info("正在迁移图片数据...");
        
        $totalProcessed = 0;
        $totalImages = 0;
        
        foreach ($sourceData as $product) {
            // 通过商品名称在目标数据库中查找对应的商品ID
            $targetProduct = DB::connection($connection)
                ->table('products')
                ->where('name', $product->name)
                ->first();
                
            if (!$targetProduct) {
                $this->warn("商品名称 '{$product->name}' 在目标数据库中不存在，跳过");
                continue;
            }
            
            $targetProductId = $targetProduct->id;
            
            // 清除该商品的现有图片
            DB::connection($connection)
                ->table('product_images')
                ->where('product_id', $targetProductId)
                ->delete();
            
            $images = [];
            
            // 只处理轮播图 pic_url
            $picUrls = $this->parsePicUrl($product->pic_url);
            
            foreach ($picUrls as $index => $imageUrl) {
                $images[] = [
                    'product_id' => $targetProductId,
                    'url' => $imageUrl,
                    'path' => $this->extractPath($imageUrl),
                    'sort' => $index,
                    'is_main' => ($index === 0) ? 1 : 0, // 第一张图设为主图
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            
            // 批量插入图片
            if (!empty($images)) {
                DB::connection($connection)
                    ->table('product_images')
                    ->insert($images);
                    
                $totalImages += count($images);
            }
            
            $totalProcessed++;
            
            if ($totalProcessed % 50 == 0) {
                $this->info("已处理: {$totalProcessed}/{$sourceData->count()} 个商品");
            }
        }
        
        $this->info("✅ 迁移完成！");
        $this->info("处理了 {$totalProcessed} 个商品，共迁移 {$totalImages} 张图片");
    }
    
    /**
     * 从URL中提取路径
     */
    private function extractPath($url)
    {
        $parsedUrl = parse_url($url);
        return $parsedUrl['path'] ?? '';
    }
} 