<?php

namespace App\Common\Services;

use App\Common\Enums\OrderStatus;
use App\Order\Models\Order;
use App\Delivery\Models\Delivery;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 生鲜配送状态管理服务
 * 专门处理生鲜配送业务的状态流转和时间管理
 */
class FreshDeliveryStatusService
{
    /**
     * 订单状态自动推进（基于时间和条件）
     */
    public function autoAdvanceOrderStatus(Order $order): bool
    {
        $currentStatus = $order->status;
        $paymentMethod = $order->payment_method;
        $paymentStatus = $order->bill?->payment_status ?? OrderStatus::PAYMENT_UNPAID;

        Log::info('检查订单状态自动推进', [
            'order_id' => $order->id,
            'current_status' => $currentStatus,
            'payment_method' => $paymentMethod,
            'payment_status' => $paymentStatus,
        ]);

        // 🥬 根据不同状态检查是否需要自动推进
        switch ($currentStatus) {
            case OrderStatus::PENDING:
                return $this->handlePendingStatus($order, $paymentMethod, $paymentStatus);
                
            case OrderStatus::CONFIRMED:
                return $this->handleConfirmedStatus($order);
                
            case OrderStatus::PREPARING:
                return $this->handlePreparingStatus($order);
                
            case OrderStatus::READY:
                return $this->handleReadyStatus($order);
                
            case OrderStatus::DISPATCHED:
                return $this->handleDispatchedStatus($order);
                
            case OrderStatus::DELIVERED:
                return $this->handleDeliveredStatus($order);
        }

        return false;
    }

    /**
     * 处理待确认状态
     */
    protected function handlePendingStatus(Order $order, string $paymentMethod, string $paymentStatus): bool
    {
        // 🥬 在线支付：付款后自动确认
        if ($paymentMethod !== 'cod' && $paymentStatus === OrderStatus::PAYMENT_PAID) {
            return $this->updateOrderStatus($order, OrderStatus::CONFIRMED, '付款完成，自动确认订单');
        }

        // 🥬 货到付款：超过30分钟未确认，发送提醒（不自动推进）
        if ($order->created_at->diffInMinutes(now()) > 30) {
            $this->sendMerchantReminder($order, '订单待确认超过30分钟');
        }

        return false;
    }

    /**
     * 处理已确认状态
     */
    protected function handleConfirmedStatus(Order $order): bool
    {
        // 🥬 确认后立即开始备货（生鲜特点：时间紧迫）
        return $this->updateOrderStatus($order, OrderStatus::PREPARING, '订单确认，开始备货');
    }

    /**
     * 处理备货中状态
     */
    protected function handlePreparingStatus(Order $order): bool
    {
        // 🥬 备货超过60分钟，发送提醒
        if ($order->updated_at->diffInMinutes(now()) > 60) {
            $this->sendMerchantReminder($order, '备货时间过长，请检查');
        }

        // 备货完成需要人工确认，不自动推进
        return false;
    }

    /**
     * 处理待配送状态
     */
    protected function handleReadyStatus(Order $order): bool
    {
        // 🥬 检查是否已分配配送员
        $delivery = $order->delivery;
        if (!$delivery) {
            // 自动分配配送员
            return $this->autoAssignDeliverer($order);
        }

        // 🥬 分配配送员后，等待配送员取货
        if ($delivery->status === OrderStatus::DELIVERY_ASSIGNED) {
            // 超过15分钟未取货，重新分配
            if ($delivery->updated_at->diffInMinutes(now()) > 15) {
                return $this->reassignDeliverer($order);
            }
        }

        return false;
    }

    /**
     * 处理配送中状态
     */
    protected function handleDispatchedStatus(Order $order): bool
    {
        $delivery = $order->delivery;
        if (!$delivery) return false;

        // 🥬 配送超时检查（根据配送距离和时间）
        $expectedDeliveryTime = $this->calculateExpectedDeliveryTime($order);
        if (now()->gt($expectedDeliveryTime)) {
            $this->sendDeliveryDelayAlert($order, $delivery);
        }

        return false;
    }

    /**
     * 处理已送达状态
     */
    protected function handleDeliveredStatus(Order $order): bool
    {
        // 🥬 送达后24小时内未确认收货，自动完成
        if ($order->delivered_at && $order->delivered_at->diffInHours(now()) > 24) {
            return $this->updateOrderStatus($order, OrderStatus::COMPLETED, '送达24小时后自动完成');
        }

        // 🥬 检查是否需要订单更正
        if ($this->needsOrderCorrection($order)) {
            return $this->updateOrderStatus($order, OrderStatus::CORRECTING, '检测到需要订单更正');
        }

        return false;
    }

    /**
     * 更新订单状态
     */
    protected function updateOrderStatus(Order $order, string $newStatus, string $reason): bool
    {
        if (!OrderStatus::canTransitionTo($order->status, $newStatus)) {
            Log::warning('订单状态转换不合法', [
                'order_id' => $order->id,
                'from_status' => $order->status,
                'to_status' => $newStatus,
                'reason' => $reason,
            ]);
            return false;
        }

        $oldStatus = $order->status;
        $updateData = ['status' => $newStatus];

        // 🥬 设置相应的时间戳
        switch ($newStatus) {
            case OrderStatus::CONFIRMED:
                $updateData['confirmed_at'] = now();
                break;
            case OrderStatus::PREPARING:
                $updateData['preparing_at'] = now();
                break;
            case OrderStatus::READY:
                $updateData['ready_at'] = now();
                break;
            case OrderStatus::DISPATCHED:
                $updateData['dispatched_at'] = now();
                break;
            case OrderStatus::DELIVERED:
                $updateData['delivered_at'] = now();
                break;
            case OrderStatus::COMPLETED:
                $updateData['completed_at'] = now();
                break;
            case OrderStatus::CANCELLED:
                $updateData['cancelled_at'] = now();
                break;
        }

        $order->update($updateData);

        Log::info('订单状态自动推进', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'from_status' => $oldStatus,
            'to_status' => $newStatus,
            'reason' => $reason,
        ]);

        // 🥬 触发相应的业务逻辑
        $this->handleStatusChangeEffects($order, $oldStatus, $newStatus);

        return true;
    }

    /**
     * 处理状态变更的副作用
     */
    protected function handleStatusChangeEffects(Order $order, string $oldStatus, string $newStatus): void
    {
        switch ($newStatus) {
            case OrderStatus::CONFIRMED:
                // 发送确认通知给客户
                $this->sendCustomerNotification($order, '订单已确认，开始为您准备新鲜商品');
                break;
                
            case OrderStatus::READY:
                // 通知配送系统
                $this->notifyDeliverySystem($order);
                break;
                
            case OrderStatus::DISPATCHED:
                // 发送配送通知给客户
                $this->sendCustomerNotification($order, '配送员已出发，请准备收货');
                break;
                
            case OrderStatus::DELIVERED:
                // 发送送达通知
                $this->sendCustomerNotification($order, '商品已送达，请确认收货');
                break;
        }
    }

    /**
     * 自动分配配送员
     */
    protected function autoAssignDeliverer(Order $order): bool
    {
        // 这里应该调用配送系统的自动分配逻辑
        // 暂时返回false，表示需要人工分配
        Log::info('需要分配配送员', ['order_id' => $order->id]);
        return false;
    }

    /**
     * 重新分配配送员
     */
    protected function reassignDeliverer(Order $order): bool
    {
        Log::info('配送员取货超时，需要重新分配', ['order_id' => $order->id]);
        return false;
    }

    /**
     * 计算预期配送时间
     */
    protected function calculateExpectedDeliveryTime(Order $order): Carbon
    {
        // 基础配送时间：30分钟
        $baseDeliveryTime = 30;
        
        // 根据配送距离调整（这里简化处理）
        $distanceAdjustment = 0; // 实际应该根据配送地址计算
        
        return $order->dispatched_at->addMinutes($baseDeliveryTime + $distanceAdjustment);
    }

    /**
     * 检查是否需要订单更正
     */
    protected function needsOrderCorrection(Order $order): bool
    {
        // 检查是否有待处理的更正记录
        return $order->corrections()->where('status', 'pending')->exists();
    }

    /**
     * 发送商家提醒
     */
    protected function sendMerchantReminder(Order $order, string $message): void
    {
        Log::info('发送商家提醒', [
            'order_id' => $order->id,
            'message' => $message,
        ]);
        // 实际实现：发送短信、微信通知等
    }

    /**
     * 发送客户通知
     */
    protected function sendCustomerNotification(Order $order, string $message): void
    {
        Log::info('发送客户通知', [
            'order_id' => $order->id,
            'customer_id' => $order->user_id,
            'message' => $message,
        ]);
        // 实际实现：发送短信、微信通知等
    }

    /**
     * 通知配送系统
     */
    protected function notifyDeliverySystem(Order $order): void
    {
        Log::info('通知配送系统', ['order_id' => $order->id]);
        // 实际实现：调用配送系统API
    }

    /**
     * 发送配送延迟警报
     */
    protected function sendDeliveryDelayAlert(Order $order, Delivery $delivery): void
    {
        Log::warning('配送延迟警报', [
            'order_id' => $order->id,
            'delivery_id' => $delivery->id,
            'deliverer_id' => $delivery->deliverer_id,
        ]);
        // 实际实现：通知配送员和客户
    }

    /**
     * 获取订单的完整状态信息（用于前端显示）
     */
    public function getOrderStatusInfo(Order $order): array
    {
        $delivery = $order->delivery;
        $paymentStatus = $order->bill?->payment_status ?? OrderStatus::PAYMENT_UNPAID;
        
        $userDisplay = OrderStatus::getUserDisplayStatus(
            $order->status,
            $paymentStatus,
            $order->payment_method,
            $delivery?->status
        );

        return array_merge($userDisplay, [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'current_status' => $order->status,
            'payment_status' => $paymentStatus,
            'payment_method' => $order->payment_method,
            'delivery_status' => $delivery?->status,
            'delivery_info' => $delivery ? [
                'deliverer_name' => $delivery->deliverer?->name,
                'deliverer_phone' => $delivery->deliverer?->phone,
                'estimated_arrival' => $this->calculateExpectedDeliveryTime($order)->format('H:i'),
            ] : null,
            'next_possible_actions' => OrderStatus::getNextPossibleStatuses($order->status),
            'timeline' => $this->getOrderTimeline($order),
        ]);
    }

    /**
     * 获取订单时间线
     */
    protected function getOrderTimeline(Order $order): array
    {
        $timeline = [];
        
        if ($order->created_at) {
            $timeline[] = ['status' => '订单创建', 'time' => $order->created_at, 'completed' => true];
        }
        
        if ($order->confirmed_at) {
            $timeline[] = ['status' => '订单确认', 'time' => $order->confirmed_at, 'completed' => true];
        }
        
        if ($order->preparing_at) {
            $timeline[] = ['status' => '开始备货', 'time' => $order->preparing_at, 'completed' => true];
        }
        
        if ($order->ready_at) {
            $timeline[] = ['status' => '备货完成', 'time' => $order->ready_at, 'completed' => true];
        }
        
        if ($order->dispatched_at) {
            $timeline[] = ['status' => '开始配送', 'time' => $order->dispatched_at, 'completed' => true];
        }
        
        if ($order->delivered_at) {
            $timeline[] = ['status' => '配送完成', 'time' => $order->delivered_at, 'completed' => true];
        }
        
        if ($order->completed_at) {
            $timeline[] = ['status' => '订单完成', 'time' => $order->completed_at, 'completed' => true];
        }
        
        return $timeline;
    }
}
