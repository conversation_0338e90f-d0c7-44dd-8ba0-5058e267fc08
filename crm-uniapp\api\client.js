import request from '../utils/request.js'

// 客户相关API
export default {
	// 获取客户列表
	getClientList(params = {}) {
		return request.get('/crm/users', params)
	},
	
	// 获取客户详情
	getClientDetail(clientId) {
		return request.get(`/crm/users/${clientId}`)
	},
	
	// 获取客户统计信息
	getClientStatistics(clientId) {
		return request.get(`/crm/users/${clientId}/statistics`)
	},
	
	// 获取客户订单列表
	getClientOrders(clientId, params = {}) {
		return request.get(`/crm/users/${clientId}/orders`, params)
	},
	
	// 获取客户地址列表
	getClientAddresses(clientId) {
		return request.get(`/crm/users/${clientId}/addresses`)
	},
	
	// 获取客户跟进记录
	getClientFollowUps(clientId) {
		return request.get(`/crm/users/${clientId}/follow-ups`)
	},
	
	// 创建客户
	createClient(data) {
		return request.post('/crm/users', data)
	},
	
	// 更新客户信息
	updateClient(clientId, data) {
		return request.put(`/crm/users/${clientId}`, data)
	},
	
	// 删除客户
	deleteClient(clientId) {
		return request.delete(`/crm/users/${clientId}`)
	},
	
	// 搜索客户
	searchClients(keyword, params = {}) {
		return request.get('/crm/users/search', {
			keyword,
			...params
		})
	},
	
	// 根据手机号查找客户
	findByPhone(phone) {
		return request.get(`/crm/users/by-phone/${phone}`)
	},
	
	// 分配客户给CRM专员
	assignAgent(clientId, agentId) {
		return request.post(`/crm/users/${clientId}/assign-agent`, { agent_id: agentId })
	},
	
	// 更新客户状态
	updateClientStatus(clientId, status) {
		return request.put(`/crm/users/${clientId}/status`, { status })
	},
	
	// 更新客户余额
	updateClientBalance(clientId, balance) {
		return request.put(`/crm/users/${clientId}/balance`, { balance })
	},
	
	// 更新客户积分
	updateClientPoints(clientId, points) {
		return request.put(`/crm/users/${clientId}/points`, { points })
	},
	
	// 更新客户会员等级
	updateMembershipLevel(clientId, levelId) {
		return request.put(`/crm/users/${clientId}/membership-level`, { level_id: levelId })
	},
	
	// 刷新客户会员等级
	refreshMembershipLevel(clientId) {
		return request.post(`/crm/users/${clientId}/refresh-level`)
	},

	// 获取客户统计数据（用于筛选条件统计）
	getClientStats() {
		return request.get('/crm/users/statistics')
	},

	// 批量分配专员
	batchAssignAgent(clientIds, agentId) {
		return request.post('/crm/users/batch-assign-agent', {
			client_ids: clientIds,
			agent_id: agentId
		})
	},

	// 批量添加标签
	batchAddTags(clientIds, tagIds) {
		return request.post('/crm/users/batch-add-tags', {
			client_ids: clientIds,
			tag_ids: tagIds
		})
	},

	// 批量导出客户数据
	batchExport(clientIds) {
		return request.post('/crm/users/batch-export', {
			client_ids: clientIds
		})
	},

	// 获取搜索建议
	getSearchSuggestions(keyword) {
		return request.get('/crm/users/search-suggestions', { keyword })
	},

	// 获取客户地址列表
	getClientAddresses(clientId) {
		return request.get(`/crm/users/${clientId}/addresses`)
	},

	// 获取可用的员工列表
	getAvailableEmployees() {
		return request.get('/crm/users/available-employees')
	},

	// 获取可用的配送员列表
	getAvailableDeliverers() {
		return request.get('/crm/users/available-deliverers')
	},

	// 获取可用的CRM专员列表
	getAvailableCrmAgents() {
		return request.get('/crm/users/available-crm-agents')
	},

	// 获取区域列表
	getRegions(params = {}) {
		return request.get('/regions', params)
	},

	// 获取区域树形结构
	getRegionTree(params = {}) {
		return request.get('/regions/tree', params)
	}
}