<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Order\Models\OrderCorrection;
use App\Product\Models\Product;
use App\Models\User;
use App\Crm\Models\UserAddress;
use App\Region\Services\RegionPriceService;
use App\Payment\Services\PaymentOfferService;
use App\Inventory\Services\InventoryService;
use App\Order\Services\OrderUnitConversionService;
use App\Order\Services\OrderPromotionService;
use App\Order\Exceptions\InventoryInsufficientException;
use App\Order\Exceptions\OutboundDocumentCreationException;
use App\Order\Exceptions\StockReductionException;
use App\Order\Exceptions\InventoryProcessingStateException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderService
{
    /**
     * 区域价格服务
     *
     * @var RegionPriceService
     */
    protected $regionPriceService;

    /**
     * 支付优惠服务
     *
     * @var PaymentOfferService
     */
    protected $paymentOfferService;

    /**
     * 订单单位转换服务
     *
     * @var OrderUnitConversionService
     */
    protected $unitConversionService;

    /**
     * 订单满减服务
     *
     * @var OrderPromotionService
     */
    protected $orderPromotionService;

    /**
     * 构造函数
     *
     * @param RegionPriceService $regionPriceService
     * @param PaymentOfferService $paymentOfferService
     * @param OrderUnitConversionService $unitConversionService
     * @param OrderPromotionService $orderPromotionService
     */
    public function __construct(
        RegionPriceService $regionPriceService = null,
        PaymentOfferService $paymentOfferService = null,
        OrderUnitConversionService $unitConversionService = null,
        OrderPromotionService $orderPromotionService = null
    ) {
        $this->regionPriceService = $regionPriceService ?? app(RegionPriceService::class);
        $this->paymentOfferService = $paymentOfferService ?? app(PaymentOfferService::class);
        $this->unitConversionService = $unitConversionService ?? app(OrderUnitConversionService::class);
        $this->orderPromotionService = $orderPromotionService ?? app(OrderPromotionService::class);
    }

    /**
     * 获取订单列表
     *
     * @param array $filters 过滤条件
     * @param User|null $user 当前用户
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getOrders($filters = [], $user = null, $perPage = 20)
    {
        // 🔥 内存优化：根据请求类型选择性加载关联数据
        $isDetailedRequest = isset($filters['with_corrections']) ||
                           isset($filters['with_payment_links']) ||
                           isset($filters['calculate_correction_status']);

        // 🔥 修复：基础查询包含商品图片，解决订单列表商品无图片问题
        $baseWith = [
            'user:id,name,phone,merchant_name',
            'items:id,order_id,product_id,product_name,product_sku,quantity,price,total,unit_id,is_converted,user_display_quantity,user_display_unit_id',
            'items.product:id,name,price', // 基础商品信息
            'items.product.mainImage:id,product_id,url,path,is_main,status', // 🔥 明确指定mainImage字段
            'printRecords:id,printable_type,printable_id,print_type,status,printed_at,printed_by', // 🔥 添加打印记录关联
        ];

        // 根据需要添加额外的关联
        if ($isDetailedRequest) {
            $baseWith = array_merge($baseWith, [
                // 移除重复的 'items.product:id,name,price' - 已在基础配置中包含
                'items.unit:id,name,symbol',
                'items.userDisplayUnit:id,name,symbol',
                'orderMerge:id,merged_order_id,original_order_ids',
                'delivery.deliverer.employee:id,name,phone'
            ]);
        }

        // 🔥 内存优化：简化查询，移除复杂的子查询
        $query = Order::with($baseWith);

        // 只在需要时添加计数和复杂查询
        if ($isDetailedRequest) {
            $query->withCount('items as items_count')
                  ->withCount([
                      'corrections as corrections_count' => function($query) {
                          $query->where('status', '!=', 'cancelled');
                      }
                  ]);
        }
        
        // 状态筛选
        if (isset($filters['status']) && !empty($filters['status'])) {
            if ($filters['status'] === 'merged') {
                // 显示被合并的原订单
                $query->where('is_merged_from', true);
            } else {
                $query->where('status', $filters['status']);
            }
        }

        // 🔥 新增：订单来源筛选（支持代客下单筛选）
        if (isset($filters['source']) && !empty($filters['source'])) {
            $query->where('source', $filters['source']);
        }

        // 🔥 新增：代客下单专用筛选
        if (isset($filters['proxy_orders_only']) && $filters['proxy_orders_only']) {
            $query->where('source', 'proxy');
        }

        // 🔥 新增：需要人工确认的订单筛选
        if (isset($filters['pending_confirmation']) && $filters['pending_confirmation']) {
            $query->where('status', 'pending')
                  ->where(function($q) {
                      // 代客下单订单 或 货到付款订单
                      $q->where('source', 'proxy')
                        ->orWhere('payment_method', 'cod');
                  });
        }

        // 合并订单筛选 - 默认不显示被合并的原订单
        if (!isset($filters['show_merged_orders']) || !$filters['show_merged_orders']) {
            $query->where('is_merged_from', false);
        }
        
        // 只看可合并订单筛选
        if (isset($filters['only_mergeable']) && $filters['only_mergeable']) {
            $this->applyMergeableFilter($query);
        }
        
        // 关键词搜索
        if (isset($filters['keyword']) && !empty($filters['keyword'])) {
            $keyword = $filters['keyword'];
            $query->where(function($q) use ($keyword) {
                $q->where('order_no', 'like', "%{$keyword}%")
                  ->orWhere('contact_name', 'like', "%{$keyword}%")
                  ->orWhere('contact_phone', 'like', "%{$keyword}%")
                  // 🔥 新增：支持商户名称搜索
                  ->orWhereHas('user', function($userQuery) use ($keyword) {
                      $userQuery->where('merchant_name', 'like', "%{$keyword}%");
                  });
            });
        }
        
        // 商家名称筛选
        if (isset($filters['merchant_name']) && !empty($filters['merchant_name'])) {
            $merchantName = $filters['merchant_name'];
            $query->whereHas('user', function($q) use ($merchantName) {
                $q->where('merchant_name', 'like', "%{$merchantName}%");
            });
        }
        
        // 🔥 新增：客户类型筛选（商户客户 vs 个人客户）
        if (isset($filters['has_merchant_name'])) {
            if ($filters['has_merchant_name'] === true) {
                // 只显示有商户名称的客户
                $query->whereHas('user', function($q) {
                    $q->whereNotNull('merchant_name')->where('merchant_name', '!=', '');
                });
            } elseif ($filters['has_merchant_name'] === false) {
                // 只显示没有商户名称的个人客户
                $query->where(function($q) {
                    $q->whereDoesntHave('user')
                      ->orWhereHas('user', function($userQuery) {
                          $userQuery->whereNull('merchant_name')
                                   ->orWhere('merchant_name', '');
                      });
                });
            }
        }
        
        // 日期范围筛选 - 支持多种日期字段
        if (isset($filters['start_date']) && !empty($filters['start_date']) && 
            isset($filters['end_date']) && !empty($filters['end_date'])) {
            // 确保日期格式正确，添加时间部分
            $startDateTime = $filters['start_date'] . ' 00:00:00';
            $endDateTime = $filters['end_date'] . ' 23:59:59';
            $query->whereBetween('created_at', [$startDateTime, $endDateTime]);
            
            // 添加调试日志
            Log::info('订单日期筛选', [
                'start_date' => $filters['start_date'],
                'end_date' => $filters['end_date'],
                'start_datetime' => $startDateTime,
                'end_datetime' => $endDateTime
            ]);
        }
        
        // 支持按送达日期筛选
        if (isset($filters['delivery_date']) && !empty($filters['delivery_date'])) {
            $query->whereDate('delivery_date', $filters['delivery_date']);
        }
        
        // 🔥 新增：按配送员筛选
        if (isset($filters['delivery_person_id']) && !empty($filters['delivery_person_id'])) {
            $query->whereHas('delivery', function($q) use ($filters) {
                // 支持两种方式：直接匹配deliverer_id 或者 匹配employee_id
                $deliveryPersonId = $filters['delivery_person_id'];

                $q->where(function($subQuery) use ($deliveryPersonId) {
                    // 方式1：直接匹配deliverer_id（新的配送员表）
                    $subQuery->where('deliverer_id', $deliveryPersonId)
                             // 方式2：通过deliverer关联匹配employee_id（兼容模式）
                             ->orWhereHas('deliverer', function($delivererQuery) use ($deliveryPersonId) {
                                 $delivererQuery->where('employee_id', $deliveryPersonId);
                             });
                });
            });

            Log::info('🚚 应用配送员筛选', [
                'delivery_person_id' => $filters['delivery_person_id'],
                'filter_type' => 'delivery_person'
            ]);
        }

        // 🔥 新增：CRM专员权限筛选
        if (isset($filters['crm_agent_id']) && !empty($filters['crm_agent_id'])) {
            $query->whereHas('user', function($q) use ($filters) {
                $q->where('crm_agent_id', $filters['crm_agent_id']);
            });

            Log::info('👤 应用CRM专员筛选', [
                'crm_agent_id' => $filters['crm_agent_id'],
                'filter_type' => 'crm_agent'
            ]);
        }
        
        // 更正状态筛选
        if (isset($filters['correction_status']) && !empty($filters['correction_status'])) {
            switch ($filters['correction_status']) {
                case 'none':
                    $query->doesntHave('corrections');
                    break;
                case 'confirmed':
                    $query->whereHas('corrections', function($q) {
                        $q->where('status', 'confirmed');
                    });
                    break;
                case 'cancelled':
                    $query->whereHas('corrections', function($q) {
                        $q->where('status', 'cancelled');
                    });
                    break;
                case 'has_corrections':
                    $query->has('corrections');
                    break;
            }
        }

        // 更正类型筛选
        if (isset($filters['correction_type']) && !empty($filters['correction_type'])) {
            $query->whereHas('corrections', function($q) use ($filters) {
                $q->where('correction_type', $filters['correction_type']);
            });
        }
        
        // 支持只显示可更正的订单（所有订单都可以更正）
        if (isset($filters['correctable']) && $filters['correctable']) {
            // 移除状态限制，所有订单都可以更正
            // $query->where('status', 'delivered');
        }
        
        // 🔥 修复：只有在不是明确查询已取消订单时，才排除已取消订单
        if (isset($filters['exclude_cancelled']) && $filters['exclude_cancelled'] &&
            (!isset($filters['status']) || $filters['status'] !== 'cancelled')) {
            $query->where('status', '!=', 'cancelled');
            Log::info('📋 [OrderService] 应用排除已取消订单过滤', [
                'exclude_cancelled' => $filters['exclude_cancelled'],
                'status_filter' => $filters['status'] ?? 'none'
            ]);
        }

        // 包含更正信息（前端请求的特殊参数）
        if (isset($filters['with_corrections']) && $filters['with_corrections']) {
            // 已经在上面的with中加载了corrections关联
        }

        // 包含付款链接信息（已迁移到账单系统）
        if (isset($filters['with_payment_links']) && $filters['with_payment_links']) {
            $query->with(['bills.billingPaymentLinks' => function($q) {
                $q->latest();
            }]);
        }

        // 计算更正状态（前端请求的特殊参数）
        if (isset($filters['calculate_correction_status']) && $filters['calculate_correction_status']) {
            // 已经在上面的selectRaw中计算了correction_status字段
        }
        
        // 处理前端传递的date_range参数
        if (isset($filters['date_range']) && !empty($filters['date_range'])) {
            $dateRange = $filters['date_range'];
            $now = now();
            
            switch ($dateRange) {
                case 'today':
                    $query->whereDate('created_at', $now->toDateString());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [
                        $now->startOfWeek()->toDateTimeString(),
                        $now->endOfWeek()->toDateTimeString()
                    ]);
                    break;
                case 'month':
                    $query->whereBetween('created_at', [
                        $now->startOfMonth()->toDateTimeString(),
                        $now->endOfMonth()->toDateTimeString()
                    ]);
                    break;
            }
        }
        
        // 排序处理
        if (isset($filters['sort']) && !empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'created_at_desc':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'created_at_asc':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'total_desc':
                    $query->orderBy('total', 'desc');
                    break;
                case 'total_asc':
                    $query->orderBy('total', 'asc');
                    break;
                default:
                    $query->latest('created_at'); // 默认按创建时间倒序
                    break;
            }
        } else {
            $query->latest('created_at'); // 默认按创建时间倒序
        }
        
        // 权限控制逻辑
        if ($user) {
            // 检查是否是员工模型（Employee）
            if ($user instanceof \App\Employee\Models\Employee) {
                // 员工系统：员工可以查看所有订单
                // 如果员工有角色限制，可以在这里添加
                $allowedEmployeeRoles = ['admin', 'manager', 'staff', 'warehouse_manager', 'crm_agent'];

                // 如果员工有role属性且不在允许的角色中，限制访问
                if (isset($user->role) && !empty($user->role) && !in_array($user->role, $allowedEmployeeRoles)) {
                    $query->whereRaw('1 = 0');
                }
                // 否则允许员工查看所有订单（包括没有role属性的员工）
            } else {
                // 普通用户系统：根据角色控制访问权限
                $adminRoles = ['admin', 'manager', 'staff', 'warehouse_manager', 'crm_agent', 'crm'];

                // 如果用户有管理员角色，允许查看所有订单
                if (isset($user->role) && in_array($user->role, $adminRoles)) {
                    // 管理员可以查看所有订单，不添加任何限制
                } else {
                    // 普通用户只能查看自己的订单
                    $query->where('user_id', $user->id);
                }
            }
        } else {
            // 如果没有传递用户参数，为了安全起见，不返回任何订单
            // 但在测试环境或特殊情况下，可能需要允许查看所有订单
            // 这里我们允许在没有用户时查看所有订单（用于测试和管理界面）
            // 在生产环境中，建议总是传递用户参数
            $query->whereRaw('1 = 0'); // 返回空结果
        }
        


        // 添加索引优化：按创建时间倒序
        $result = $query->paginate($perPage);



        return $result;
    }
    
    /**
     * 应用可合并订单筛选
     */
    private function applyMergeableFilter($query)
    {
        // 只显示待付款状态的订单
        $query->where('status', 'pending')
              ->where('is_merged_from', false)
              ->whereDate('created_at', now()->toDateString()) // 只看当日订单
              ->whereHas('user') // 确保有用户信息
              ->whereExists(function($subQuery) {
                  // 存在同一用户的其他待付款订单
                  $subQuery->select(DB::raw(1))
                           ->from('orders as o2')
                           ->whereRaw('o2.user_id = orders.user_id')
                           ->whereRaw('o2.id != orders.id')
                           ->where('o2.status', 'pending')
                           ->where('o2.is_merged_from', false)
                           ->whereDate('o2.created_at', now()->toDateString());
              });
    }
    
    /**
     * 检查订单是否可合并
     */
    public function isOrderMergeable($order): bool
    {
        // 1. 必须是待付款状态
        if ($order->status !== 'pending') {
            return false;
        }
        
        // 2. 不能已经被合并过
        if ($order->is_merged_from) {
            return false;
        }
        
        // 3. 必须是当日订单
        if (!$order->created_at->isToday()) {
            return false;
        }
        
        // 4. 必须有用户信息
        if (!$order->user_id) {
            return false;
        }
        
        // 5. 同一用户当日必须有其他待付款订单
        $sameUserOrders = Order::where('user_id', $order->user_id)
            ->where('id', '!=', $order->id)
            ->where('status', 'pending')
            ->where('is_merged_from', false)
            ->whereDate('created_at', now()->toDateString())
            ->count();
            
        return $sameUserOrders > 0;
    }
    
    /**
     * 创建订单
     *
     * @param array $data 订单数据
     * @param User $user 用户
     * @return Order 创建的订单
     */
    public function createOrder(array $data, User $user)
    {
        DB::beginTransaction();
        try {
            // 获取订单区域ID（如果有）
            $regionId = $data['region_id'] ?? null;
            
            // 使用商品模型的价格计算方法计算订单价格
            $calculatedItems = [];
            $subtotal = 0;
            $totalDiscount = 0;
            $allDiscountInfo = [];
            
            foreach ($data['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);
                $quantity = $item['quantity'];

                // 调试：记录商品名称信息
                // 🔥 统一数据源：详细记录价格计算过程
                Log::info('🔍 订单商品价格计算开始', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'requested_quantity' => $quantity,
                    'base_price' => $product->price,
                    'user_id' => $user->id,
                    'region_id' => $regionId,
                    'calculation_source' => 'order_creation'
                ]);

                // 🔥 统一数据源：使用统一的价格计算服务，禁用缓存确保数据一致性
                $priceCalculationService = app(\App\Product\Services\PriceCalculationService::class);
                $priceInfo = $priceCalculationService->calculatePrice($product, $user, $regionId, $quantity, [
                    'disable_cache' => true,  // 订单创建时禁用缓存，确保数据准确性
                    'force_recalculate' => true
                ]);

                // 🔥 统一数据源：验证价格计算结果
                Log::info('💰 订单商品价格计算完成', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity' => $quantity,
                    'base_price' => $priceInfo['base_price'],
                    'final_price' => $priceInfo['final_price'],
                    'item_total' => $priceInfo['item_total'],
                    'total_discount' => $priceInfo['total_discount'],
                    'discount_info' => $priceInfo['discount_info'],
                    'member_discount_info' => $priceInfo['member_discount_info'] ?? null,
                    'has_member_discount' => $priceInfo['has_member_discount'] ?? false,
                    'calculation_method' => 'unified_price_service',
                    'expected_item_total' => $priceInfo['final_price'] * $quantity  // 🔥 验证计算
                ]);
                
                // 🚨 修复：使用库存策略检查而不是严格的可用性检查
                $stockCheck = $product->checkStockWithPolicy($quantity);
                if (!$stockCheck['allowed']) {
                    Log::warning('🚨 商品库存不足', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'requested_quantity' => $quantity,
                        'current_stock' => $product->getTotalStock(),
                        'inventory_policy' => $product->inventory_policy,
                        'error_message' => $stockCheck['message']
                    ]);
                    // 🔥 临时使用普通异常，待自动加载修复后切换到业务异常
                    throw new \Exception($stockCheck['message']);
                }
                
                // 如果有库存警告，记录到日志
                if ($stockCheck['warning']) {
                    Log::warning('📋 订单创建库存警告', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'requested_quantity' => $quantity,
                        'current_stock' => $product->getTotalStock(),
                        'inventory_policy' => $product->inventory_policy,
                        'warning' => $stockCheck['warning']
                    ]);
                }

                // 确定使用的单位ID：优先使用传入的单位ID，否则必须使用销售单位
                $unitId = $item['unit_id'] ?? null;
                if (!$unitId) {
                    $saleUnit = $product->getSaleDefaultUnit();
                    if (!$saleUnit) {
                        throw new \Exception("商品 {$product->name} 未设置销售单位，无法创建订单");
                    }
                    $unitId = $saleUnit->id;
                }

                $calculatedItems[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku ?? '',
                    'quantity' => $quantity,
                    'base_price' => $priceInfo['base_price'],
                    'final_price' => $priceInfo['final_price'],
                    'unit_price' => $priceInfo['final_price'], // 🔧 修复：添加满减服务需要的unit_price字段
                    'original_price' => $priceInfo['base_price'], // 修复：使用base_price作为original_price
                    'price_type' => $priceInfo['price_type'],
                    'item_total' => $priceInfo['item_total'],
                    'discount_amount' => $priceInfo['total_discount'] * $quantity,
                    'discount_info' => $priceInfo['discount_info'],
                    'pricing_info' => $priceInfo, // 🔧 添加完整的价格信息供满减服务使用
                    'unit_id' => $unitId,
                    'region_id' => $regionId,
                ];

                $subtotal += $priceInfo['item_total'];
                $totalDiscount += $priceInfo['total_discount'] * $quantity;
                $allDiscountInfo = array_merge($allDiscountInfo, $priceInfo['discount_info']);

                // 🔥 调试：记录每个商品对订单总价的贡献
                Log::info('📊 商品价格累加到订单总价', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'item_total_added' => $priceInfo['item_total'],
                    'current_subtotal' => $subtotal,
                    'item_discount_added' => $priceInfo['total_discount'] * $quantity,
                    'current_total_discount' => $totalDiscount
                ]);
            }

            // 🔥 新增：计算订单级别满减优惠
            Log::info('🎯 开始计算订单满减优惠', [
                'subtotal_before_promotion' => $subtotal,
                'total_items' => count($calculatedItems),
                'user_id' => $user->id,
                'region_id' => $regionId
            ]);

            $promotionResult = $this->orderPromotionService->calculateOrderPromotions(
                $calculatedItems,
                $user,
                $regionId
            );

            $promotionDiscount = $promotionResult['promotion_discount'];
            $promotionDetails = $promotionResult['promotion_details'];
            $subtotalAfterPromotion = $promotionResult['final_subtotal'];

            Log::info('🎯 订单满减优惠计算完成', [
                'original_subtotal' => $subtotal,
                'promotion_discount' => $promotionDiscount,
                'subtotal_after_promotion' => $subtotalAfterPromotion,
                'promotion_details' => $promotionDetails
            ]);

            // 计算支付优惠（基于满减优惠后的价格）
            $paymentOfferInfo = $this->paymentOfferService->calculatePaymentOffer(
                $data['payment_method'],
                $subtotalAfterPromotion, // 🔥 修改：使用满减后的金额作为基础
                true // 启用叠加优惠
            );

            $paymentDiscount = $paymentOfferInfo['offer_amount'] ?? 0;
            $finalTotal = max(0, $subtotalAfterPromotion - $paymentDiscount); // 最终总价 = 满减后小计 - 支付优惠
            
            // 🔥 新增：验证订单金额合理性
            $this->validateOrderAmount($calculatedItems, $subtotal, $finalTotal);

            Log::info('💰 订单支付优惠计算', [
                'payment_method' => $data['payment_method'],
                'original_total' => $subtotal + $totalDiscount, // 原始总价（基本单价）
                'subtotal_after_product_discount' => $subtotal, // 商品优惠后小计
                'product_discount' => $totalDiscount, // 商品优惠金额
                'payment_offer_info' => $paymentOfferInfo,
                'payment_discount' => $paymentDiscount, // 支付优惠金额
                'final_total' => $finalTotal // 最终应付金额
            ]);
            
            // 🔥 修改：根据支付方式设置合理的初始状态
            // 货到付款：pending 状态，等待人工确认
            // 微信支付：pending_payment 状态，等待付款（10分钟后自动取消）
            // 其他在线支付：pending_payment 状态，等待付款
            $initialStatus = in_array($data['payment_method'], ['wechat', 'alipay'])
                ? 'pending_payment'
                : 'pending';
            $confirmedAt = null; // 所有订单都不自动确认

            $orderData = [
                'user_id' => $user->id,
                'order_no' => Order::generateOrderNo(),
                'total' => round($finalTotal, 2), // 最终总价（已扣除满减和支付优惠）
                'subtotal' => round($subtotal, 2), // 商品小计（商品级优惠后）
                'discount' => round($totalDiscount, 2), // 商品优惠（会员、区域等）
                'promotion_discount' => round($promotionDiscount, 2), // 🔥 新增：满减优惠金额
                'promotion_details' => $promotionDetails, // 🔥 新增：满减优惠详情
                'payment_discount' => round($paymentDiscount, 2), // 支付优惠
                'payment_discount_info' => $paymentOfferInfo, // 支付优惠详情
                'original_total' => round($subtotal + $totalDiscount, 2), // 原始总价（商品优惠前）
                'status' => $initialStatus, // 🔥 修复：货到付款直接确认，在线支付等待支付
                'confirmed_at' => $confirmedAt, // 🔥 修复：只有货到付款才设置确认时间
                'payment_method' => $data['payment_method'],
                'notes' => $data['notes'] ?? '',
                'region_id' => $regionId, // 保存区域ID
                'pricing_info' => $allDiscountInfo, // 保存价格计算详情（模型会自动转换为JSON）
            ];
            
            // 处理地址信息
            if (isset($data['user_address_id'])) {
                // 通过用户地址ID关联
                $orderData['user_address_id'] = $data['user_address_id'];
                
                // 查找地址并填充其他地址字段（冗余，方便查询）
                $userAddress = UserAddress::findOrFail($data['user_address_id']);
                $orderData['shipping_address'] = $userAddress->getFullAddressAttribute();
                $orderData['contact_name'] = $userAddress->contact_name;
                $orderData['contact_phone'] = $userAddress->contact_phone;
            } else {
                // 直接使用提交的地址信息
                $orderData['shipping_address'] = $data['shipping_address'] ?? '';
                $orderData['contact_name'] = $data['contact_name'] ?? '';
                $orderData['contact_phone'] = $data['contact_phone'] ?? '';
            }
            
            // 创建订单
            $order = Order::create($orderData);

            // 🔥 记录订单创建日志
            $logMessage = $data['payment_method'] === 'wechat'
                ? '微信支付订单创建成功，等待付款（10分钟后自动取消）'
                : '订单创建成功，等待人工确认';

            Log::info($logMessage, [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $user->id,
                'user_name' => $user->name,
                'payment_method' => $data['payment_method'],
                'total' => $order->total,
                'status' => $order->status,
                'confirmed_at' => $order->confirmed_at,
                'auto_cancel_minutes' => $data['payment_method'] === 'wechat' ? 10 : null,
                'manual_confirm_required' => $data['payment_method'] !== 'wechat',
                'pricing_info' => $allDiscountInfo
            ]);

            // 如果是货到付款订单
            if ($data['payment_method'] === 'cod') {
                $order->is_cod = true;
                $order->cod_status = 'unpaid'; // 初始状态：未支付
                $order->save();

                // 记录货到付款日志
                Log::info('货到付款订单创建，等待人工确认', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'client_id' => $user->id,
                    'client_name' => $user->name,
                    'total' => $order->total,
                    'payment_method' => 'cod',
                    'status' => 'pending',
                    'manual_confirm_required' => true,
                    'pricing_info' => $allDiscountInfo
                ]);
            }

            // 🔥 新增：保存满减记录
            if ($promotionDiscount > 0 && !empty($promotionDetails)) {
                Log::info('💾 保存订单满减记录', [
                    'order_id' => $order->id,
                    'promotion_discount' => $promotionDiscount,
                    'promotion_details_count' => count($promotionDetails)
                ]);

                $this->orderPromotionService->savePromotionRecords($order->id, $promotionDetails);
            }

            // 1. 先创建所有订单明细（集成单位转换）
            Log::info('🛒 开始创建订单明细', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'total_items' => count($calculatedItems),
                'items_summary' => array_map(function($item) {
                    return [
                        'product_id' => $item['product_id'],
                        'product_name' => $item['product_name'],
                        'quantity' => $item['quantity'],
                        'unit_id' => $item['unit_id'],
                        'price' => $item['final_price'],
                        'total' => $item['item_total']
                    ];
                }, $calculatedItems)
            ]);

            foreach ($calculatedItems as $item) {
                // 准备订单项数据
                $orderItemData = [
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'product_sku' => $item['product_sku'],
                    'quantity' => $item['quantity'],
                    'price' => $item['final_price'],
                    'original_price' => $item['original_price'],
                    'total' => $item['item_total'],
                    'unit_id' => $item['unit_id'],
                    'region_id' => $item['region_id'],
                ];

                // 检查是否需要单位转换（斤→kg）
                try {
                    $processedItemData = $this->unitConversionService->processOrderItem($orderItemData);

                    // 如果进行了转换，记录转换信息
                    if ($processedItemData['is_converted']) {
                        Log::info('🔄 订单项单位转换', [
                            'order_id' => $order->id,
                            'product_id' => $item['product_id'],
                            'product_name' => $item['product_name'],
                            'original_quantity' => $processedItemData['user_display_quantity'],
                            'original_unit_id' => $processedItemData['user_display_unit_id'],
                            'converted_quantity' => $processedItemData['quantity'],
                            'converted_unit_id' => $processedItemData['unit_id'],
                            'conversion_note' => "斤单位转换为kg"
                        ]);
                    }

                    $orderItem = new OrderItem($processedItemData);

                } catch (\Exception $e) {
                    Log::error('❌ 订单项单位转换失败', [
                        'order_id' => $order->id,
                        'product_id' => $item['product_id'],
                        'error' => $e->getMessage()
                    ]);

                    // 转换失败时使用原始数据
                    $orderItem = new OrderItem($orderItemData);
                }

                $order->items()->save($orderItem);

                Log::info('✅ 订单明细创建成功', [
                    'order_id' => $order->id,
                    'order_item_id' => $orderItem->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'quantity' => $orderItem->quantity,
                    'unit_id' => $orderItem->unit_id,
                    'is_converted' => $orderItem->is_converted ?? false
                ]);
            }

            // 🔥 修复：只有确认状态的订单才处理库存
            $inventoryProcessed = false;
            if ($order->status === 'confirmed') {
                // 2. 统一库存处理：优先使用出库单方式
                try {
                    // 尝试创建出库单
                    $this->createSimpleOutboundDocument($order, $calculatedItems);

                    // 🔥 在事务内标记库存处理方式（出库单成功）
                    $order->inventory_method = 'outbound_document';
                    $order->inventory_processed = true;
                    $order->save();
                    $inventoryProcessed = true;

                Log::info('📦 订单自动创建出库单成功', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'inventory_method' => 'outbound_document'
                ]);

            } catch (\Exception $e) {
                Log::error('📦 订单创建出库单失败，回退到传统库存扣减', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'error' => $e->getMessage()
                ]);

                // 🔥 在事务内进行回退处理
                try {
                    // 回退到传统的库存扣减方式
                    foreach ($calculatedItems as $item) {
                        $product = Product::find($item['product_id']);
                        $reduceResult = $product->reduceStockWithPolicy($item['quantity'], $item['unit_id'] ?? null);

                        // 记录库存扣减结果
                        Log::info('📦 订单库存扣减（传统方式）', [
                            'order_id' => $order->id,
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'quantity_reduced' => $item['quantity'],
                            'inventory_policy' => $product->inventory_policy,
                            'reduce_result' => $reduceResult,
                            'stock_before' => $reduceResult['stock_before'] ?? 'N/A',
                            'stock_after' => $reduceResult['stock_after'] ?? 'N/A'
                        ]);

                        // 如果库存扣减失败，记录错误但不回滚（因为前面已经检查过了）
                        if (!$reduceResult['success']) {
                            Log::error('📦 订单库存扣减失败', [
                                'order_id' => $order->id,
                                'product_id' => $product->id,
                                'error_message' => $reduceResult['message'],
                                'inventory_policy' => $product->inventory_policy
                            ]);
                        }

                        // 如果有警告，记录到日志
                        if ($reduceResult['warning']) {
                            Log::warning('📦 订单库存扣减警告', [
                                'order_id' => $order->id,
                                'product_id' => $product->id,
                                'warning' => $reduceResult['warning'],
                                'inventory_policy' => $product->inventory_policy
                            ]);
                        }
                    }

                    // 🔥 在事务内标记库存处理方式（直接扣减成功）
                    $order->inventory_method = 'direct_reduction';
                    $order->inventory_processed = true;
                    $order->save();
                    $inventoryProcessed = true;

                    Log::info('📦 订单库存处理完成（直接扣减方式）', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'inventory_method' => 'direct_reduction'
                    ]);

                } catch (\Exception $fallbackException) {
                    // 🔥 如果回退也失败，记录错误并抛出异常
                    Log::error('📦 订单库存处理完全失败', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'outbound_error' => $e->getMessage(),
                        'fallback_error' => $fallbackException->getMessage()
                    ]);
                    // 🔥 临时使用普通异常，待自动加载修复后切换到业务异常
                    throw new \Exception("库存处理失败：出库单创建失败({$e->getMessage()})，直接扣减也失败({$fallbackException->getMessage()})");
                }
            }
            } else {
                // 🔥 修复：根据支付方式显示正确的日志信息
                $logMessage = $data['payment_method'] === 'cod'
                    ? '货到付款订单创建成功，等待人工确认后处理库存'
                    : '在线支付订单创建成功，等待支付确认后处理库存';

                Log::info($logMessage, [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'status' => $order->status,
                    'payment_method' => $data['payment_method']
                ]);
                $inventoryProcessed = true; // 标记为已处理，避免后续检查报错
            }

            // 🔥 修复：只有确认状态的订单才执行后续流程（库存、配送等）
            if ($order->status === 'confirmed') {
                // 🔥 确保库存处理状态已标记
                if (!$inventoryProcessed) {
                    Log::error('📦 订单库存处理状态未正确标记', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no
                    ]);
                    // 🔥 临时使用普通异常，待自动加载修复后切换到业务异常
                    throw new \Exception('订单库存处理状态异常');
                }

                // 自动创建配送记录并分配配送员
                if ($user->default_employee_deliverer_id) {
                    // 用户有绑定默认配送员，直接分配
                    $deliverer = \App\Delivery\Models\Deliverer::where('employee_id', $user->default_employee_deliverer_id)->first();
                    if ($deliverer) {
                        \App\Delivery\Models\Delivery::create([
                            'order_id' => $order->id,
                            'status' => 'pending',
                            'deliverer_id' => $deliverer->id,
                        ]);

                        Log::info('新订单自动分配给默认配送员', [
                            'order_id' => $order->id,
                            'order_no' => $order->order_no,
                            'user_id' => $user->id,
                            'employee_id' => $user->default_employee_deliverer_id,
                            'deliverer_id' => $deliverer->id
                        ]);
                    }
                } else {
                    // 用户没有绑定配送员，创建未分配的配送记录
                    \App\Delivery\Models\Delivery::create([
                        'order_id' => $order->id,
                        'status' => 'pending',
                        'deliverer_id' => null, // 暂时未分配配送员
                    ]);

                    Log::info('新订单创建，等待分配配送员', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'user_id' => $user->id,
                        'user_name' => $user->name,
                        'reason' => '用户未绑定默认配送员'
                    ]);
                }
            } else {
                // 🔥 修复：根据支付方式显示正确的日志信息
                $logMessage = $data['payment_method'] === 'cod'
                    ? '货到付款订单创建成功，等待人工确认后执行后续流程'
                    : '在线支付订单创建成功，等待支付确认后执行后续流程';

                Log::info($logMessage, [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'status' => $order->status,
                    'payment_method' => $data['payment_method']
                ]);
            }
            
            DB::commit();
            
            // 加载关联
            $order->load(['items', 'userAddress']);
            return $order;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建订单失败', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * 🔥 新增：确认支付成功，将订单状态更新为已确认并执行后续流程
     *
     * @param int $orderId 订单ID
     * @param array $paymentInfo 支付信息
     * @return Order 更新后的订单
     */
    public function confirmPayment($orderId, array $paymentInfo = [])
    {
        $order = Order::findOrFail($orderId);

        // 只有待支付状态的订单才能确认支付
        if ($order->status !== 'pending_payment') {
            throw new \Exception("订单状态不正确，无法确认支付。当前状态：{$order->status}");
        }

        DB::beginTransaction();
        try {
            // 1. 更新订单状态
            $order->update([
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'payment_confirmed_at' => now(),
                'payment_info' => $paymentInfo, // 保存支付信息
            ]);

            // 2. 🔥 执行后续流程：库存处理
            $this->processInventoryForConfirmedOrder($order);

            // 3. 🔥 执行后续流程：创建配送记录
            $this->createDeliveryForConfirmedOrder($order);

            Log::info('订单支付确认成功，后续流程已执行', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'payment_method' => $order->payment_method,
                'total' => $order->total
            ]);

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('订单支付确认失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 🔥 新增：为已确认订单处理库存
     *
     * @param Order $order 订单
     * @return void
     */
    private function processInventoryForConfirmedOrder(Order $order)
    {
        $calculatedItems = $order->items->map(function ($item) {
            return [
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'unit_id' => $item->unit_id,
            ];
        })->toArray();

        try {
            // 尝试创建出库单
            $this->createSimpleOutboundDocument($order, $calculatedItems);

            $order->update([
                'inventory_method' => 'outbound_document',
                'inventory_processed' => true,
            ]);

            Log::info('📦 支付确认后库存处理成功（出库单）', [
                'order_id' => $order->id,
                'order_no' => $order->order_no
            ]);

        } catch (\Exception $e) {
            Log::error('📦 支付确认后出库单创建失败，回退到传统库存扣减', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            // 回退到传统库存扣减
            foreach ($calculatedItems as $item) {
                $product = Product::find($item['product_id']);
                $product->reduceStockWithPolicy($item['quantity'], $item['unit_id'] ?? null);
            }

            $order->update([
                'inventory_method' => 'direct_reduction',
                'inventory_processed' => true,
            ]);

            Log::info('📦 支付确认后库存处理成功（传统扣减）', [
                'order_id' => $order->id,
                'order_no' => $order->order_no
            ]);
        }
    }

    /**
     * 🔥 新增：为已确认订单创建配送记录
     *
     * @param Order $order 订单
     * @return void
     */
    private function createDeliveryForConfirmedOrder(Order $order)
    {
        $user = $order->user;

        if ($user->default_employee_deliverer_id) {
            // 用户有绑定默认配送员，直接分配
            $deliverer = \App\Delivery\Models\Deliverer::where('employee_id', $user->default_employee_deliverer_id)->first();
            if ($deliverer) {
                \App\Delivery\Models\Delivery::create([
                    'order_id' => $order->id,
                    'status' => 'pending',
                    'deliverer_id' => $deliverer->id,
                ]);

                Log::info('支付确认后自动分配配送员', [
                    'order_id' => $order->id,
                    'deliverer_id' => $deliverer->id
                ]);
            }
        } else {
            // 用户没有绑定配送员，创建未分配的配送记录
            \App\Delivery\Models\Delivery::create([
                'order_id' => $order->id,
                'status' => 'pending',
                'deliverer_id' => null,
            ]);

            Log::info('支付确认后创建配送记录，等待分配配送员', [
                'order_id' => $order->id
            ]);
        }
    }

    /**
     * 🔥 新增：支付失败，取消订单
     *
     * @param int $orderId 订单ID
     * @param string $reason 失败原因
     * @return Order 更新后的订单
     */
    public function cancelPayment($orderId, string $reason = '支付失败')
    {
        $order = Order::findOrFail($orderId);

        // 只有待支付状态的订单才能取消
        if ($order->status !== 'pending_payment') {
            throw new \Exception("订单状态不正确，无法取消。当前状态：{$order->status}");
        }

        DB::beginTransaction();
        try {
            $order->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancel_reason' => $reason,
            ]);

            Log::info('订单支付失败已取消', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'reason' => $reason
            ]);

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新订单状态
     *
     * @param int $orderId 订单ID
     * @param string $status 新状态
     * @param User $user 操作用户
     * @return Order 更新后的订单
     */
    public function updateOrderStatus($orderId, $status, User $user)
    {
        $order = Order::findOrFail($orderId);
        
        // 验证状态转换是否有效
        switch ($status) {
            case 'paid':
                if (!$order->canBePaid()) {
                    throw new \Exception('该订单当前状态不允许标记为已支付');
                }
                $order->paid_at = now();
                break;
                
            case 'shipped':
                if (!$order->canBeShipped()) {
                    throw new \Exception('该订单当前状态不允许标记为已发货');
                }
                $order->shipped_at = now();
                break;
                
            case 'delivered':
                if (!$order->canBeDelivered()) {
                    throw new \Exception('该订单当前状态不允许标记为已送达');
                }
                $order->delivered_at = now();
                break;
                
            case 'cancelled':
                if (!$order->canBeCancelled()) {
                    throw new \Exception('该订单当前状态不允许取消');
                }
                $order->cancelled_at = now();
                
                // 恢复库存
                foreach ($order->items as $item) {
                    if ($item->product) {
                        $item->product->addStock($item->quantity, $item->unit_id);
                    }
                }
                break;
                
            default:
                throw new \Exception('无效的订单状态');
        }
        
        $order->status = $status;
        $order->save();
        
        // 记录状态变更日志
        Log::info('订单状态更新', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'old_status' => $order->getOriginal('status'),
            'new_status' => $status,
            'updated_by' => $user ? "{$user->id}:{$user->name}" : 'system'
        ]);
        
        return $order;
    }
    
    /**
     * 取消订单
     *
     * @param int $orderId 订单ID
     * @param User $user 操作用户
     * @param string $reason 取消原因
     * @return Order 取消后的订单
     */
    public function cancelOrder($orderId, User $user, $reason = '')
    {
        return $this->updateOrderStatus($orderId, 'cancelled', $user);
    }
    
    /**
     * 获取订单详情
     *
     * @param int $orderId 订单ID
     * @param User|null $user 请求用户
     * @return Order 订单详情
     */
    public function getOrderDetail($orderId, $user = null)
    {
        $order = Order::with([
            'items.product',
            'items.product.mainImage', // 简化：加载完整主图数据
            'items.unit',
            'user',
            'delivery'
        ])->findOrFail($orderId);
        
        // 如果不是管理员，验证是否为订单所有者
        if ($user && !in_array($user->role, ['admin', 'merchant']) && $order->user_id !== $user->id) {
            throw new \Exception('无权查看此订单');
        }
        
        return $order;
    }
    
    /**
     * 创建简化的出库单
     */
    private function createSimpleOutboundDocument(Order $order, array $calculatedItems)
    {
        // 获取默认仓库
        $defaultWarehouseId = $this->getDefaultWarehouse();
        
        // 创建出库单
        $outboundDocument = \App\Inventory\Models\OutboundDocument::create([
            'document_no' => $this->generateOutboundDocumentNo(),
            'document_type' => 'sales', // 销售出库
            'reference_type' => 'order',
            'reference_id' => $order->id,
            'order_id' => $order->id,
            'warehouse_id' => $order->warehouse_id ?? $defaultWarehouseId,
            'status' => 'completed', // 直接设为已完成
            'confirmed_at' => now(),
            'completed_at' => now(),
            'confirmed_by' => 1, // 系统用户
            'completed_by' => 1, // 系统用户
            'created_by' => 1, // 系统用户
            'updated_by' => 1, // 系统用户
            'notes' => "订单 {$order->order_no} 自动创建出库单",
        ]);
        
        $totalCost = 0;
        $totalItems = 0;
        
        // 创建出库明细
        foreach ($calculatedItems as $item) {
            $product = Product::find($item['product_id']);
            if (!$product) {
                continue;
            }
            
            // 计算成本价（使用商品的当前成本价或订单价格）
            $unitCost = $product->cost_price ?? $item['final_price'];
            $itemTotalCost = $item['quantity'] * $unitCost;
            
            \App\Inventory\Models\OutboundItem::create([
                'outbound_document_id' => $outboundDocument->id,
                'product_id' => $item['product_id'],
                'product_name' => $item['product_name'],
                'product_sku' => $item['product_sku'],
                'planned_quantity' => $item['quantity'],
                'actual_quantity' => $item['quantity'],
                'unit_id' => $item['unit_id'],
                'unit_cost' => $unitCost,
                'total_cost' => $itemTotalCost,
                'notes' => "订单项出库",
            ]);
            
            $totalCost += $itemTotalCost;
            $totalItems += $item['quantity'];
            
            // 创建库存事务记录
            $this->createInventoryTransactionForOrder($outboundDocument, $item, $order);
        }
        
        // 更新出库单总计
        $outboundDocument->update([
            'total_cost' => $totalCost,
            'total_items' => $totalItems,
        ]);
        
        return $outboundDocument;
    }
    
    /**
     * 为订单创建库存事务记录
     */
    private function createInventoryTransactionForOrder($outboundDocument, array $item, Order $order)
    {
        // 获取或创建销售出库事务类型
        $transactionType = \App\Inventory\Models\InventoryTransactionType::firstOrCreate(
            ['code' => 'sales_out'],
            [
                'name' => '销售出库',
                'description' => '销售订单出库',
                'direction' => 'out',
                'is_active' => true,
            ]
        );
        
        \App\Inventory\Models\InventoryTransaction::create([
            'transaction_type_id' => $transactionType->id,
            'reference_type' => 'order', // 修复：使用 'order' 而不是完整类名，以便打印系统能正确查询
            'reference_id' => $order->id, // 修复：使用订单ID而不是出库单ID，以便打印系统能正确查询
            'product_id' => $item['product_id'],
            'warehouse_id' => $outboundDocument->warehouse_id,
            'quantity' => -$item['quantity'], // 负数表示出库
            'unit_id' => $item['unit_id'],
            'unit_price' => $item['final_price'],
            'total_amount' => $item['item_total'],
            'notes' => "订单 {$order->order_no} 销售出库 (出库单: {$outboundDocument->document_no})",
            'status' => 'completed',
            'created_by' => 1, // 系统用户
            'updated_by' => 1, // 系统用户
        ]);
    }
    
    /**
     * 获取默认仓库ID
     */
    private function getDefaultWarehouse(): int
    {
        // 尝试获取第一个活跃仓库
        $warehouse = DB::table('warehouses')
            ->where('status', 'active')
            ->first();
            
        if ($warehouse) {
            return $warehouse->id;
        }
        
        // 如果没有活跃仓库，获取第一个仓库
        $warehouse = DB::table('warehouses')->first();
        if ($warehouse) {
            return $warehouse->id;
        }
        
        // 如果没有仓库，抛出异常
        throw new \Exception('系统中没有可用的仓库');
    }
    
    /**
     * 生成出库单号
     */
    private function generateOutboundDocumentNo(): string
    {
        $prefix = 'OUT';
        $date = now()->format('Ymd');
        
        // 获取今日最大序号
        $lastDocument = \App\Inventory\Models\OutboundDocument::where('document_no', 'like', "{$prefix}{$date}%")
            ->orderBy('document_no', 'desc')
            ->first();
            
        if ($lastDocument) {
            $lastNumber = (int) substr($lastDocument->document_no, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return $prefix . $date . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 🔥 新增：验证订单金额合理性
     */
    private function validateOrderAmount(array $calculatedItems, float $subtotal, float $finalTotal): void
    {
        // 重新计算总金额进行验证
        $recalculatedSubtotal = 0;
        $itemDetails = [];

        foreach ($calculatedItems as $item) {
            $itemTotal = round($item['final_price'] * $item['quantity'], 2);
            $recalculatedSubtotal += $itemTotal;

            $itemDetails[] = [
                'product_name' => $item['product_name'],
                'quantity' => $item['quantity'],
                'final_price' => $item['final_price'],
                'calculated_total' => $itemTotal,
                'stored_total' => $item['item_total']
            ];

            // 检查单个商品的金额是否合理
            if ($item['final_price'] < 0.01) {
                Log::warning('商品价格异常低', [
                    'product_name' => $item['product_name'],
                    'final_price' => $item['final_price'],
                    'base_price' => $item['base_price'],
                    'discount_amount' => $item['discount_amount']
                ]);
            }
        }

        $recalculatedSubtotal = round($recalculatedSubtotal, 2);
        $subtotalDiff = abs($recalculatedSubtotal - $subtotal);

        // 如果金额差异超过0.01元，记录警告
        if ($subtotalDiff > 0.01) {
            Log::warning('订单金额计算不一致', [
                'stored_subtotal' => $subtotal,
                'recalculated_subtotal' => $recalculatedSubtotal,
                'difference' => $subtotalDiff,
                'final_total' => $finalTotal,
                'item_details' => $itemDetails
            ]);
        }

        Log::info('📊 订单金额验证', [
            'stored_subtotal' => $subtotal,
            'recalculated_subtotal' => $recalculatedSubtotal,
            'difference' => $subtotalDiff,
            'final_total' => $finalTotal,
            'validation_passed' => $subtotalDiff <= 0.01
        ]);
    }
}