<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UpdateDeliveryEmployeesCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'employees:update-delivery';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '更新配送员员工数据，使用中文名作为用户名';

    // 员工角色常量
    const ROLE_DELIVERY = 'delivery';

    /**
     * 配送员信息
     * 
     * @var array
     */
    protected $deliveryEmployees = [
        ['name' => '周太军', 'phone' => '13730670672'],
        ['name' => '张正林', 'phone' => '18202857659'],
        ['name' => '方先红', 'phone' => '13648064232'],
        ['name' => '张金', 'phone' => '13880045187'],
        ['name' => '李焦', 'phone' => '13880522953'],
        ['name' => '熊启江', 'phone' => '13882147779'],
        ['name' => '熊江洪', 'phone' => '18188458175'],
        ['name' => '王水晶', 'phone' => '18583958997'],
        ['name' => '王银全', 'phone' => '13628012269'],
        ['name' => '石亚涛', 'phone' => '18980542028'],
        ['name' => '黄建', 'phone' => '13558650480'],
        ['name' => '龙水红', 'phone' => '18080041788'],
    ];

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('开始更新配送员员工数据...');
        
        foreach ($this->deliveryEmployees as $employee) {
            $this->updateOrCreateEmployee($employee['name'], $employee['phone']);
        }
        
        $this->info('配送员员工数据更新完成！');
        
        return Command::SUCCESS;
    }
    
    /**
     * 更新或创建员工记录
     * 
     * @param string $name 员工姓名
     * @param string $phone 员工手机号
     */
    private function updateOrCreateEmployee($name, $phone)
    {
        // 首先通过手机号查找员工
        $employee = DB::table('employees')
            ->where('phone', $phone)
            ->first();
        
        if ($employee) {
            // 更新现有员工的用户名为中文名
            DB::table('employees')
                ->where('id', $employee->id)
                ->update([
                    'username' => $name,
                    'updated_at' => now(),
                ]);
            
            $this->info("已更新配送员: {$name} ({$phone}) - ID: {$employee->id}");
        } else {
            // 创建新员工记录
            $id = DB::table('employees')->insertGetId([
                'name' => $name,
                'username' => $name, // 使用中文名作为用户名
                'password' => Hash::make('123456'),
                'phone' => $phone,
                'position' => '配送员',
                'role' => self::ROLE_DELIVERY,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            $this->info("已创建配送员: {$name} ({$phone}) - ID: {$id}");
        }
    }
} 