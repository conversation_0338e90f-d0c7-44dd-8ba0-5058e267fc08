<?php

namespace App\Admin\Controllers;

use App\Employee\Models\Employee;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class EmployeeManagementController extends Controller
{
    /**
     * 显示员工列表
     */
    public function index()
    {
        $employees = Employee::all();
        return view('admin.employees.index', compact('employees'));
    }
    
    /**
     * 显示创建员工表单
     */
    public function create()
    {
        return view('admin.employees.create');
    }
    
    /**
     * 存储新员工
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:employees',
            'password' => 'required|string|min:6',
            'phone' => 'nullable|string|max:20',
            'position' => 'required|string|max:255',
            'role' => 'required|string|in:admin,manager,staff,crm_agent,delivery,warehouse_manager',
        ]);
        
        $validated['password'] = Hash::make($validated['password']);
        
        Employee::create($validated);
        
        return redirect()->route('admin.employees.index')
            ->with('success', '员工创建成功！');
    }
    
    /**
     * 显示编辑员工表单
     */
    public function edit(Employee $employee)
    {
        return view('admin.employees.edit', compact('employee'));
    }
    
    /**
     * 更新员工信息
     */
    public function update(Request $request, Employee $employee)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => [
                'required',
                'string',
                'max:255',
                Rule::unique('employees')->ignore($employee->id),
            ],
            'password' => 'nullable|string|min:6',
            'phone' => 'nullable|string|max:20',
            'position' => 'required|string|max:255',
            'role' => 'required|string|in:admin,manager,staff,crm_agent,delivery,warehouse_manager',
        ]);
        
        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }
        
        $employee->update($validated);
        
        return redirect()->route('admin.employees.index')
            ->with('success', '员工信息更新成功！');
    }
    
    /**
     * 删除员工
     */
    public function destroy(Employee $employee)
    {
        // 防止删除最后一个管理员
        if ($employee->role === 'admin' && Employee::where('role', 'admin')->count() <= 1) {
            return redirect()->route('admin.employees.index')
                ->with('error', '无法删除最后一个管理员！');
        }
        
        $employee->delete();
        
        return redirect()->route('admin.employees.index')
            ->with('success', '员工删除成功！');
    }
    
    /**
     * 重置员工密码
     */
    public function resetPassword(Employee $employee)
    {
        $employee->update([
            'password' => Hash::make('123456')
        ]);
        
        return redirect()->route('admin.employees.index')
            ->with('success', '员工密码已重置为: 123456');
    }
} 