<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Product\Models\Product;
use App\Product\Services\PriceCalculationService;
use App\Models\User;
use App\Region\Models\Region;
use App\Crm\Models\MembershipLevel;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class WarmupPriceCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'price:warmup 
                            {--products= : 指定商品ID，多个用逗号分隔}
                            {--limit=100 : 预热商品数量限制}
                            {--regions= : 指定区域ID，多个用逗号分隔}
                            {--popular : 只预热热门商品}
                            {--force : 强制刷新缓存}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '预热商品价格缓存，提高系统响应速度';

    /**
     * 价格计算服务
     */
    protected $priceService;

    /**
     * Create a new command instance.
     */
    public function __construct(PriceCalculationService $priceService)
    {
        parent::__construct();
        $this->priceService = $priceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始预热价格缓存...');
        
        $startTime = microtime(true);
        $totalWarmed = 0;
        
        try {
            // 获取要预热的商品
            $products = $this->getProductsToWarmup();
            $this->info("找到 {$products->count()} 个商品需要预热");
            
            // 获取要预热的区域
            $regions = $this->getRegionsToWarmup();
            $this->info("将为 {$regions->count()} 个区域预热价格");
            
            // 获取活跃用户的会员等级（用于预热会员价格）
            $membershipLevels = $this->getActiveMembershipLevels();
            $this->info("将为 {$membershipLevels->count()} 个会员等级预热价格");
            
            // 创建进度条
            $totalOperations = $products->count() * ($regions->count() + 1) * ($membershipLevels->count() + 1);
            $progressBar = $this->output->createProgressBar($totalOperations);
            $progressBar->start();
            
            // 批量预热
            foreach ($products->chunk(20) as $productChunk) {
                $productIds = $productChunk->pluck('id')->toArray();
                
                // 预热游客价格（无区域）
                $this->warmupBatchPrices($productIds, null, null);
                $totalWarmed += count($productIds);
                $progressBar->advance(count($productIds));
                
                // 预热各区域的游客价格
                foreach ($regions as $region) {
                    $this->warmupBatchPrices($productIds, null, $region->id);
                    $totalWarmed += count($productIds);
                    $progressBar->advance(count($productIds));
                }
                
                // 预热会员价格
                foreach ($membershipLevels as $level) {
                    // 创建虚拟用户对象
                    $virtualUser = new User();
                    $virtualUser->id = 0; // 虚拟用户ID
                    $virtualUser->membership_level_id = $level->id;
                    $virtualUser->membershipLevel = $level;
                    
                    // 无区域会员价格
                    $this->warmupBatchPrices($productIds, $virtualUser, null);
                    $totalWarmed += count($productIds);
                    $progressBar->advance(count($productIds));
                    
                    // 各区域会员价格
                    foreach ($regions as $region) {
                        $virtualUser->region_id = $region->id;
                        $this->warmupBatchPrices($productIds, $virtualUser, $region->id);
                        $totalWarmed += count($productIds);
                        $progressBar->advance(count($productIds));
                    }
                }
            }
            
            $progressBar->finish();
            $this->newLine();
            
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            $this->info("价格缓存预热完成！");
            $this->info("预热商品数: {$products->count()}");
            $this->info("预热价格条目: {$totalWarmed}");
            $this->info("耗时: {$duration} 秒");
            
            // 记录日志
            Log::info('价格缓存预热完成', [
                'products_count' => $products->count(),
                'total_warmed' => $totalWarmed,
                'duration' => $duration,
                'regions_count' => $regions->count(),
                'membership_levels_count' => $membershipLevels->count()
            ]);
            
        } catch (\Exception $e) {
            $this->error("预热过程中发生错误: " . $e->getMessage());
            Log::error('价格缓存预热失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 获取要预热的商品
     */
    private function getProductsToWarmup()
    {
        $query = Product::where('status', 1);
        
        // 如果指定了商品ID
        if ($this->option('products')) {
            $productIds = explode(',', $this->option('products'));
            $query->whereIn('id', $productIds);
        } else {
            // 如果只预热热门商品
            if ($this->option('popular')) {
                $query->where('sales_count', '>', 0)
                      ->orderBy('sales_count', 'desc');
            } else {
                // 默认按创建时间排序，优先预热新商品
                $query->orderBy('created_at', 'desc');
            }
            
            // 应用数量限制
            $limit = (int) $this->option('limit');
            $query->limit($limit);
        }
        
        return $query->get();
    }
    
    /**
     * 获取要预热的区域
     */
    private function getRegionsToWarmup()
    {
        if ($this->option('regions')) {
            $regionIds = explode(',', $this->option('regions'));
            return Region::whereIn('id', $regionIds)->where('status', 1)->get();
        }
        
        // 默认获取所有活跃区域
        return Region::where('status', 1)->get();
    }
    
    /**
     * 获取活跃的会员等级
     */
    private function getActiveMembershipLevels()
    {
        return MembershipLevel::where('status', 1)
                              ->where('is_default', 0)
                              ->get();
    }
    
    /**
     * 批量预热价格
     */
    private function warmupBatchPrices(array $productIds, ?User $user, ?int $regionId)
    {
        try {
            // 使用批量计算方法预热
            $this->priceService->calculateBatchPrices($productIds, $user, $regionId);
        } catch (\Exception $e) {
            // 如果批量失败，尝试单个预热
            foreach ($productIds as $productId) {
                try {
                    $product = Product::find($productId);
                    if ($product) {
                        $options = $this->option('force') ? ['force_refresh' => true] : [];
                        $this->priceService->calculatePrice($product, $user, $regionId, 1, $options);
                    }
                } catch (\Exception $singleError) {
                    // 记录单个商品预热失败，但继续处理其他商品
                    Log::warning('单个商品价格预热失败', [
                        'product_id' => $productId,
                        'user_id' => $user?->id,
                        'region_id' => $regionId,
                        'error' => $singleError->getMessage()
                    ]);
                }
            }
        }
    }
} 