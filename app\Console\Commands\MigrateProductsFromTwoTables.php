<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateProductsFromTwoTables extends Command
{
    protected $signature = 'migrate:products-full 
                           {--source-connection=mysql_old : 源数据库连接}
                           {--target-connection=mysql : 目标数据库连接}
                           {--dry-run : 预览模式}';

    protected $description = '从两个老表联合迁移商品完整信息';

    public function handle()
    {
        $sourceConnection = $this->option('source-connection');
        $targetConnection = $this->option('target-connection');
        $dryRun = $this->option('dry-run');

        try {
            $this->info("开始联合迁移商品信息...");
            
            // 获取联合数据
            $sourceData = $this->getJoinedData($sourceConnection);
            
            $this->info("找到 {$sourceData->count()} 个商品需要更新");
            
            if ($sourceData->count() == 0) {
                $this->info("没有数据需要迁移");
                return 0;
            }
            
            // 显示数据示例
            $this->showDataSamples($sourceData);
            
            if ($dryRun) {
                $this->info("🔍 预览模式完成");
                return 0;
            }
            
            if (!$this->confirm("确定要更新这些商品信息吗？")) {
                $this->info("迁移已取消");
                return 0;
            }
            
            // 执行迁移
            $this->updateProducts($targetConnection, $sourceData);
            
            $this->info("✅ 商品信息更新完成！");
            
        } catch (Exception $e) {
            $this->error("❌ 迁移失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 获取联合查询数据
     */
    private function getJoinedData($connection)
    {
        return DB::connection($connection)
            ->table('zjhj_bd_goods_warehouse as w')
            ->join('zjhj_bd_goods as g', 'w.id', '=', 'g.goods_warehouse_id')
            ->where('w.is_delete', 0)
            ->where('g.is_delete', 0)
            ->where('g.sign', '') // 只获取普通商品，sign为空表示普通商品
            ->select([
                'w.id as warehouse_id',
                'w.name',
                'w.original_price',
                'w.cost_price',
                'w.detail',
                'g.id as goods_id',
                'g.price as sale_price',
                'g.min_number',
                'g.goods_stock',
                'g.status',
                'g.sort',
                'g.sales',
                'g.virtual_sales',
                'g.sign' // 添加sign字段用于调试
            ])
            ->get();
    }
    
    /**
     * 显示数据示例
     */
    private function showDataSamples($data)
    {
        $this->info("商品数据示例（前3个）:");
        
        $samples = $data->take(3);
        foreach ($samples as $index => $row) {
            $this->line("商品 " . ($index + 1) . ":");
            $this->line("  仓库ID: {$row->warehouse_id}");
            $this->line("  商品ID: {$row->goods_id}");
            $this->line("  名称: {$row->name}");
            $this->line("  原价: {$row->original_price}");
            $this->line("  售价: {$row->sale_price}");
            $this->line("  成本价: {$row->cost_price}");
            $this->line("  起售数量: {$row->min_number}");
            $this->line("  状态: {$row->status}");
            $this->line("");
        }
    }
    
    /**
     * 更新商品信息
     */
    private function updateProducts($connection, $sourceData)
    {
        $this->info("正在更新商品信息...");
        
        $totalProcessed = 0;
        $totalUpdated = 0;
        $totalSkipped = 0;
        
        foreach ($sourceData as $product) {
            // 通过商品名称在目标数据库中查找对应的商品
            $targetProduct = DB::connection($connection)
                ->table('products')
                ->where('name', $product->name)
                ->first();
                
            if (!$targetProduct) {
                $this->warn("商品名称 '{$product->name}' 在目标数据库中不存在，跳过");
                $totalSkipped++;
                continue;
            }
            
            // 准备更新数据 - 只更新商品基本信息，不包括库存和销量
            $updateData = [
                'price' => $product->sale_price ?: $product->original_price, // 优先使用售价，否则使用原价
                'cost_price' => $product->cost_price,
                'description' => $product->detail,
                'min_sale_quantity' => $product->min_number,
                'status' => $product->status, // 直接使用老表的状态
                'sort' => $product->sort,
                'updated_at' => now(),
            ];
            
            // 更新商品
            DB::connection($connection)
                ->table('products')
                ->where('id', $targetProduct->id)
                ->update($updateData);
            
            $totalUpdated++;
            $totalProcessed++;
            
            if ($totalProcessed % 50 == 0) {
                $this->info("已处理: {$totalProcessed}/{$sourceData->count()} 个商品");
            }
        }
        
        $this->info("✅ 更新完成！");
        $this->info("处理了 {$totalProcessed} 个商品");
        $this->info("成功更新 {$totalUpdated} 个商品");
        $this->info("跳过 {$totalSkipped} 个商品");
    }
} 