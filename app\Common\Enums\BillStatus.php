<?php

namespace App\Common\Enums;

/**
 * 账单状态统一管理
 */
class BillStatus
{
    // 🔥 账单状态
    const DRAFT = 'draft';               // 草稿
    const PENDING = 'pending';           // 待付款
    const PARTIAL_PAID = 'partial_paid'; // 部分付款
    const PAID = 'paid';                 // 已付款
    const OVERPAID = 'overpaid';         // 超额付款
    const CANCELLED = 'cancelled';       // 已取消
    const REFUNDED = 'refunded';         // 已退款
    const CONSOLIDATED = 'consolidated'; // 已合并

    // 🔥 付款状态
    const PAYMENT_UNPAID = 'unpaid';     // 未付款
    const PAYMENT_PARTIAL = 'partial';   // 部分付款
    const PAYMENT_PAID = 'paid';         // 已付款
    const PAYMENT_OVERPAID = 'overpaid'; // 超额付款
    const PAYMENT_REFUNDED = 'refunded'; // 已退款

    /**
     * 账单状态映射
     */
    public static function getStatusMap(): array
    {
        return [
            self::DRAFT => '草稿',
            self::PENDING => '待付款',
            self::PARTIAL_PAID => '部分付款',
            self::PAID => '已付款',
            self::OVERPAID => '超额付款',
            self::CANCELLED => '已取消',
            self::REFUNDED => '已退款',
            self::CONSOLIDATED => '已合并',
        ];
    }

    /**
     * 付款状态映射
     */
    public static function getPaymentStatusMap(): array
    {
        return [
            self::PAYMENT_UNPAID => '未付款',
            self::PAYMENT_PARTIAL => '部分付款',
            self::PAYMENT_PAID => '已付款',
            self::PAYMENT_OVERPAID => '超额付款',
            self::PAYMENT_REFUNDED => '已退款',
        ];
    }

    /**
     * 获取状态文本
     */
    public static function getStatusText(string $status): string
    {
        return self::getStatusMap()[$status] ?? '未知状态';
    }

    /**
     * 获取付款状态文本
     */
    public static function getPaymentStatusText(string $paymentStatus): string
    {
        return self::getPaymentStatusMap()[$paymentStatus] ?? '未知状态';
    }

    /**
     * 根据付款金额计算账单状态
     */
    public static function calculateStatus(float $finalAmount, float $paidAmount): array
    {
        $paymentStatus = '';
        $billStatus = '';

        if ($paidAmount == 0) {
            $paymentStatus = self::PAYMENT_UNPAID;
            $billStatus = self::PENDING;
        } elseif ($paidAmount < $finalAmount) {
            $paymentStatus = self::PAYMENT_PARTIAL;
            $billStatus = self::PARTIAL_PAID;
        } elseif ($paidAmount == $finalAmount) {
            $paymentStatus = self::PAYMENT_PAID;
            $billStatus = self::PAID;
        } else {
            $paymentStatus = self::PAYMENT_OVERPAID;
            $billStatus = self::OVERPAID;
        }

        return [
            'payment_status' => $paymentStatus,
            'bill_status' => $billStatus,
        ];
    }

    /**
     * 检查是否可以付款
     */
    public static function canPay(string $status): bool
    {
        return in_array($status, [
            self::PENDING,
            self::PARTIAL_PAID,
        ]);
    }

    /**
     * 检查是否可以取消
     */
    public static function canCancel(string $status): bool
    {
        return in_array($status, [
            self::DRAFT,
            self::PENDING,
            self::PARTIAL_PAID,
        ]);
    }

    /**
     * 检查是否可以退款
     */
    public static function canRefund(string $status): bool
    {
        return in_array($status, [
            self::PAID,
            self::OVERPAID,
        ]);
    }

    /**
     * 检查是否已完成付款
     */
    public static function isFullyPaid(string $paymentStatus): bool
    {
        return in_array($paymentStatus, [
            self::PAYMENT_PAID,
            self::PAYMENT_OVERPAID,
        ]);
    }

    /**
     * 检查是否有付款
     */
    public static function hasPaid(string $paymentStatus): bool
    {
        return $paymentStatus !== self::PAYMENT_UNPAID;
    }

    /**
     * 获取用户端显示信息
     */
    public static function getUserDisplayInfo(string $status, string $paymentStatus, float $finalAmount, float $paidAmount): array
    {
        $statusText = self::getStatusText($status);
        $paymentText = self::getPaymentStatusText($paymentStatus);
        $pendingAmount = max(0, $finalAmount - $paidAmount);

        return [
            'status' => $statusText,
            'payment_status' => $paymentText,
            'final_amount' => $finalAmount,
            'paid_amount' => $paidAmount,
            'pending_amount' => $pendingAmount,
            'can_pay' => self::canPay($status) && $pendingAmount > 0,
            'is_fully_paid' => self::isFullyPaid($paymentStatus),
        ];
    }

    /**
     * 获取管理端显示信息
     */
    public static function getAdminDisplayInfo(string $status, string $paymentStatus, float $finalAmount, float $paidAmount): array
    {
        $userInfo = self::getUserDisplayInfo($status, $paymentStatus, $finalAmount, $paidAmount);
        
        return array_merge($userInfo, [
            'can_cancel' => self::canCancel($status),
            'can_refund' => self::canRefund($status),
            'has_paid' => self::hasPaid($paymentStatus),
        ]);
    }
}
