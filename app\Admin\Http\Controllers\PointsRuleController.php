<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Points\Models\PointsRule;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class PointsRuleController extends Controller
{
    /**
     * 获取积分规则列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = PointsRule::query();

        // 按规则类型筛选
        if ($request->filled('rule_type')) {
            $query->where('rule_type', $request->rule_type);
        }

        // 按状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $rules = $query->orderBy('rule_type')
                      ->orderBy('created_at', 'desc')
                      ->paginate($request->get('per_page', 20));

        // 添加规则类型文本
        $rules->getCollection()->transform(function ($rule) {
            $ruleArray = $rule->toArray();
            $ruleArray['rule_type_text'] = $rule->rule_type_text;
            $ruleArray['status_text'] = $rule->status_text;
            return $ruleArray;
        });

        return response()->json([
            'success' => true,
            'data' => $rules
        ]);
    }

    /**
     * 创建积分规则
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'rule_type' => [
                'required',
                'string',
                Rule::in(array_keys(PointsRule::getRuleTypeOptions()))
            ],
            'points_amount' => 'required|integer|min:0',
            'max_times_per_day' => 'nullable|integer|min:0',
            'max_times_total' => 'nullable|integer|min:0',
            'status' => 'required|boolean',
            'valid_from' => 'nullable|date',
            'valid_to' => 'nullable|date|after:valid_from',
            'description' => 'nullable|string|max:1000',
            'conditions' => 'nullable|array',
        ]);

        // 检查同类型规则是否已存在
        $existingRule = PointsRule::where('rule_type', $request->rule_type)->first();
        if ($existingRule) {
            return response()->json([
                'success' => false,
                'message' => '该类型的积分规则已存在，请修改现有规则或选择其他类型'
            ], 422);
        }

        $rule = PointsRule::create([
            'name' => $request->name,
            'rule_type' => $request->rule_type,
            'points_amount' => $request->points_amount,
            'conditions' => $request->conditions,
            'max_times_per_day' => $request->max_times_per_day,
            'max_times_total' => $request->max_times_total,
            'status' => $request->status,
            'valid_from' => $request->valid_from,
            'valid_to' => $request->valid_to,
            'description' => $request->description,
        ]);

        return response()->json([
            'success' => true,
            'message' => '积分规则创建成功',
            'data' => $rule
        ]);
    }

    /**
     * 获取单个积分规则详情
     */
    public function show(int $id): JsonResponse
    {
        $rule = PointsRule::findOrFail($id);
        
        $ruleData = $rule->toArray();
        $ruleData['rule_type_text'] = $rule->rule_type_text;
        $ruleData['status_text'] = $rule->status_text;

        return response()->json([
            'success' => true,
            'data' => $ruleData
        ]);
    }

    /**
     * 更新积分规则
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $rule = PointsRule::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'rule_type' => [
                'required',
                'string',
                Rule::in(array_keys(PointsRule::getRuleTypeOptions()))
            ],
            'points_amount' => 'required|integer|min:0',
            'max_times_per_day' => 'nullable|integer|min:0',
            'max_times_total' => 'nullable|integer|min:0',
            'status' => 'required|boolean',
            'valid_from' => 'nullable|date',
            'valid_to' => 'nullable|date|after:valid_from',
            'description' => 'nullable|string|max:1000',
            'conditions' => 'nullable|array',
        ]);

        // 如果修改了规则类型，检查是否与其他规则冲突
        if ($request->rule_type !== $rule->rule_type) {
            $existingRule = PointsRule::where('rule_type', $request->rule_type)
                                     ->where('id', '!=', $id)
                                     ->first();
            if ($existingRule) {
                return response()->json([
                    'success' => false,
                    'message' => '该类型的积分规则已存在'
                ], 422);
            }
        }

        $rule->update([
            'name' => $request->name,
            'rule_type' => $request->rule_type,
            'points_amount' => $request->points_amount,
            'conditions' => $request->conditions,
            'max_times_per_day' => $request->max_times_per_day,
            'max_times_total' => $request->max_times_total,
            'status' => $request->status,
            'valid_from' => $request->valid_from,
            'valid_to' => $request->valid_to,
            'description' => $request->description,
        ]);

        return response()->json([
            'success' => true,
            'message' => '积分规则更新成功',
            'data' => $rule
        ]);
    }

    /**
     * 删除积分规则
     */
    public function destroy(int $id): JsonResponse
    {
        $rule = PointsRule::findOrFail($id);
        
        // 检查是否有相关的积分交易记录
        $hasTransactions = \App\Points\Models\PointsTransaction::where('source', $rule->getTransactionSource())->exists();
        
        if ($hasTransactions) {
            return response()->json([
                'success' => false,
                'message' => '该规则已有相关的积分交易记录，不能删除。建议将其设为禁用状态。'
            ], 422);
        }

        $rule->delete();

        return response()->json([
            'success' => true,
            'message' => '积分规则删除成功'
        ]);
    }

    /**
     * 批量更新规则状态
     */
    public function batchUpdateStatus(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:points_rules,id',
            'status' => 'required|boolean',
        ]);

        $count = PointsRule::whereIn('id', $request->ids)
                          ->update(['status' => $request->status]);

        $statusText = $request->status ? '启用' : '禁用';
        
        return response()->json([
            'success' => true,
            'message' => "成功{$statusText}了 {$count} 个积分规则"
        ]);
    }

    /**
     * 获取规则类型选项
     */
    public function ruleTypeOptions(): JsonResponse
    {
        $options = collect(PointsRule::getRuleTypeOptions())->map(function ($label, $value) {
            return [
                'value' => $value,
                'label' => $label
            ];
        })->values();

        return response()->json([
            'success' => true,
            'data' => $options
        ]);
    }

    /**
     * 快速创建默认积分规则
     */
    public function createDefaults(): JsonResponse
    {
        $defaultRules = [
            [
                'name' => '每日签到',
                'rule_type' => PointsRule::RULE_TYPE_SIGNIN,
                'points_amount' => 10,
                'max_times_per_day' => 1,
                'max_times_total' => null,
                'status' => true,
                'description' => '每日签到获得10积分',
                'conditions' => null,
            ],
            [
                'name' => '邀请好友注册',
                'rule_type' => PointsRule::RULE_TYPE_INVITE,
                'points_amount' => 100,
                'max_times_per_day' => 5,
                'max_times_total' => 50,
                'status' => true,
                'description' => '成功邀请好友注册获得100积分',
                'conditions' => ['require_friend_first_order' => true],
            ],
            [
                'name' => '商品评价',
                'rule_type' => PointsRule::RULE_TYPE_REVIEW,
                'points_amount' => 5,
                'max_times_per_day' => 10,
                'max_times_total' => null,
                'status' => true,
                'description' => '发表商品评价获得5积分',
                'conditions' => ['min_content_length' => 10],
            ],
            [
                'name' => '分享商品',
                'rule_type' => PointsRule::RULE_TYPE_SHARE,
                'points_amount' => 2,
                'max_times_per_day' => 20,
                'max_times_total' => null,
                'status' => true,
                'description' => '分享商品到社交媒体获得2积分',
                'conditions' => null,
            ],
            [
                'name' => '生日奖励',
                'rule_type' => PointsRule::RULE_TYPE_BIRTHDAY,
                'points_amount' => 50,
                'max_times_per_day' => 1,
                'max_times_total' => null,
                'status' => true,
                'description' => '生日当天获得50积分奖励',
                'conditions' => null,
            ],
        ];

        $created = 0;
        $skipped = 0;

        foreach ($defaultRules as $ruleData) {
            $existing = PointsRule::where('rule_type', $ruleData['rule_type'])->first();
            
            if (!$existing) {
                PointsRule::create($ruleData);
                $created++;
            } else {
                $skipped++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "成功创建 {$created} 个默认规则，跳过 {$skipped} 个已存在的规则",
            'data' => [
                'created' => $created,
                'skipped' => $skipped
            ]
        ]);
    }
} 