<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Employee\Models\Employee;

/**
 * 管理员仪表盘控制器
 */
class DashboardController extends Controller
{
    /**
     * 显示管理员仪表盘页面
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // 准备仪表盘数据
        $stats = [
            'employees' => 0,
            'customers' => 0,
            'orders' => 0,
            'products' => 0,
        ];
        
        // 安全获取统计数据
        try {
            $stats['employees'] = Employee::count();
        } catch (\Exception $e) {
            // 忽略错误
        }
        
        // 使用字符串类名动态获取数据
        $this->tryCount($stats, 'customers', 'App\\Crm\\Models\\Customer');
        $this->tryCount($stats, 'orders', 'App\\Order\\Models\\Order');
        $this->tryCount($stats, 'products', 'App\\Product\\Models\\Product');
        
        return view('admin.dashboard', compact('stats'));
    }
    
    /**
     * 尝试获取数据表记录数
     * 
     * @param array &$stats 统计数组
     * @param string $key 键名
     * @param string $className 类名
     * @return void
     */
    private function tryCount(array &$stats, string $key, string $className): void
    {
        try {
            if (class_exists($className)) {
                $stats[$key] = call_user_func([$className, 'count']);
            }
        } catch (\Exception $e) {
            // 忽略错误
        }
    }
} 