<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateProductUnits extends Command
{
    protected $signature = 'migrate:product-units 
                           {--source-connection=mysql_old : 源数据库连接}
                           {--target-connection=mysql : 目标数据库连接}
                           {--dry-run : 预览模式}';

    protected $description = '迁移商品单位信息，设置销售单位';

    public function handle()
    {
        $sourceConnection = $this->option('source-connection');
        $targetConnection = $this->option('target-connection');
        $dryRun = $this->option('dry-run');

        try {
            $this->info("开始迁移商品单位信息...");
            
            // 1. 构建单位映射表（老表单位名称 → 新表单位ID）
            $unitMapping = $this->buildUnitMapping($targetConnection);
            
            if (empty($unitMapping)) {
                $this->error("无法构建单位映射关系");
                return 1;
            }
            
            $this->info("成功构建 " . count($unitMapping) . " 个单位映射关系");
            
            // 2. 获取商品单位数据
            $productUnits = $this->getProductUnits($sourceConnection, $unitMapping);
            
            $this->info("找到 {$productUnits->count()} 个商品需要设置单位");
            
            if ($productUnits->count() == 0) {
                $this->info("没有商品单位需要迁移");
                return 0;
            }
            
            // 3. 显示示例数据
            $this->showSampleData($productUnits);
            
            if ($dryRun) {
                $this->info("🔍 预览模式完成");
                return 0;
            }
            
            if (!$this->confirm("确定要设置商品的销售单位吗？")) {
                $this->info("迁移已取消");
                return 0;
            }
            
            // 4. 执行单位设置
            $this->setProductUnits($targetConnection, $productUnits);
            
            $this->info("✅ 商品单位设置完成！");
            
        } catch (Exception $e) {
            $this->error("❌ 迁移失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 构建单位映射表
     */
    private function buildUnitMapping($connection)
    {
        // 获取新数据库所有单位
        $units = DB::connection($connection)
            ->table('units')
            ->select('id', 'name')
            ->get()
            ->keyBy('name');
        
        $mapping = [];
        foreach ($units as $name => $unit) {
            $mapping[$name] = $unit->id;
        }
        
        // 获取老表中所有实际使用的单位
        $oldUnits = DB::connection('mysql_old')
            ->table('zjhj_bd_goods_warehouse')
            ->where('is_delete', 0)
            ->groupBy('unit')
            ->selectRaw('unit, count(*) as count')
            ->get();
        
        $this->info("老表中的单位使用情况:");
        foreach ($oldUnits as $oldUnit) {
            $originalUnit = $oldUnit->unit;
            $cleanedUnit = $this->cleanUnitName($originalUnit);
            
            $this->line("  原始: '{$originalUnit}' → 清理后: '{$cleanedUnit}' → {$oldUnit->count}个商品");
            
            // 建立映射关系
            if (isset($units[$cleanedUnit])) {
                $mapping[$originalUnit] = $units[$cleanedUnit]->id;
            } else {
                $this->warn("  ⚠️ 无法映射单位: '{$originalUnit}' (清理后: '{$cleanedUnit}')");
            }
        }
        
        return array_filter($mapping); // 移除null值
    }
    
    /**
     * 清理单位名称（去除空格和特殊字符）
     */
    private function cleanUnitName($unitName)
    {
        // 去除所有空格
        $cleaned = trim($unitName);
        
        // 处理特殊字符
        $cleaned = str_replace(['·', '•', '．'], '', $cleaned);
        
        return $cleaned;
    }
    
    /**
     * 获取商品单位数据
     */
    private function getProductUnits($connection, $unitMapping)
    {
        // 获取所有有效商品，不限制单位
        $allProducts = DB::connection($connection)
            ->table('zjhj_bd_goods_warehouse')
            ->where('is_delete', 0)
            ->select('id', 'name', 'unit')
            ->get();
            
        $this->info("总共有 {$allProducts->count()} 个商品");
        
        // 过滤出有映射关系的商品
        $validProducts = $allProducts->filter(function ($item) use ($unitMapping) {
            return isset($unitMapping[$item->unit]);
        });
        
        $this->info("其中 {$validProducts->count()} 个商品有单位映射");
        
        // 显示无法映射的商品单位
        $invalidProducts = $allProducts->filter(function ($item) use ($unitMapping) {
            return !isset($unitMapping[$item->unit]);
        });
        
        if ($invalidProducts->count() > 0) {
            $this->warn("以下商品的单位无法映射:");
            $invalidProducts->groupBy('unit')->each(function ($products, $unit) {
                $this->line("  单位 '{$unit}': {$products->count()} 个商品");
            });
        }
        
        return $validProducts->map(function ($item) use ($unitMapping) {
            $item->unit_id = $unitMapping[$item->unit];
            return $item;
        });
    }
    
    /**
     * 显示示例数据
     */
    private function showSampleData($data)
    {
        $this->info("商品单位示例（前5个）:");
        
        $samples = $data->take(5);
        foreach ($samples as $index => $item) {
            $this->line("商品 " . ($index + 1) . ":");
            $this->line("  商品名称: {$item->name}");
            $this->line("  老单位: '{$item->unit}'");
            $this->line("  新单位ID: {$item->unit_id}");
            $this->line("");
        }
    }
    
    /**
     * 设置商品单位
     */
    private function setProductUnits($connection, $productUnits)
    {
        $this->info("正在设置商品单位...");
        
        $totalProcessed = 0;
        $totalUpdated = 0;
        $totalSkipped = 0;
        
        foreach ($productUnits as $item) {
            // 通过商品名称在目标数据库中查找对应的商品
            $targetProduct = DB::connection($connection)
                ->table('products')
                ->where('name', $item->name)
                ->first();
                
            if (!$targetProduct) {
                $this->warn("商品名称 '{$item->name}' 在目标数据库中不存在，跳过");
                $totalSkipped++;
                continue;
            }
            
            // 1. 设置基础单位（如果还没有设置）
            if (!$targetProduct->base_unit_id) {
                DB::connection($connection)
                    ->table('products')
                    ->where('id', $targetProduct->id)
                    ->update([
                        'base_unit_id' => $item->unit_id,
                        'updated_at' => now(),
                    ]);
            }
            
            // 2. 在product_units表中设置销售单位
            $existingUnit = DB::connection($connection)
                ->table('product_units')
                ->where('product_id', $targetProduct->id)
                ->where('unit_id', $item->unit_id)
                ->first();
                
            if (!$existingUnit) {
                DB::connection($connection)
                    ->table('product_units')
                    ->insert([
                        'product_id' => $targetProduct->id,
                        'unit_id' => $item->unit_id,
                        'conversion_factor' => 1.0, // 默认转换因子为1
                        'roles' => json_encode(['sales']), // 只设置为销售单位
                        'role_priority' => json_encode(['sales' => 1]), // 设置销售优先级
                        'is_active' => 1,           // 启用
                        'is_default' => 1,          // 设置为默认单位
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
            }
            
            $totalUpdated++;
            $totalProcessed++;
            
            if ($totalProcessed % 50 == 0) {
                $this->info("已处理: {$totalProcessed}/{$productUnits->count()} 个商品");
            }
        }
        
        $this->info("✅ 单位设置完成！");
        $this->info("处理了 {$totalProcessed} 个商品");
        $this->info("成功设置 {$totalUpdated} 个商品");
        $this->info("跳过 {$totalSkipped} 个商品");
    }
} 