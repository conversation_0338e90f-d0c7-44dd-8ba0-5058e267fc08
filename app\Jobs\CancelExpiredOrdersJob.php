<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Order\Models\Order;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CancelExpiredOrdersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务最大尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        // 设置队列名称
        $this->onQueue('orders');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('开始执行订单自动取消队列任务');

        // 检查是否启用自动取消
        if (!config('order.auto_cancel.enabled', true)) {
            Log::info('订单自动取消功能已禁用');
            return;
        }

        // 从配置文件获取超时时间
        $timeoutMinutes = config('order.auto_cancel.wechat_timeout_minutes', 10);
        $expiredTime = Carbon::now()->subMinutes($timeoutMinutes);
        
        // 查找需要取消的订单（待付款状态的微信支付订单）
        $expiredOrders = Order::where('status', 'pending_payment')
            ->where('payment_method', 'wechat')
            ->where('created_at', '<=', $expiredTime)
            ->whereNull('cancelled_at')
            ->limit(100) // 每次最多处理100个订单
            ->get();
            
        if ($expiredOrders->isEmpty()) {
            Log::info('没有找到需要取消的超时订单');
            return;
        }
        
        Log::info("找到 {$expiredOrders->count()} 个超时未付款订单");
        
        $cancelledCount = 0;
        $failedCount = 0;
        
        foreach ($expiredOrders as $order) {
            try {
                $this->cancelOrder($order);
                $cancelledCount++;
                
                Log::info("订单 {$order->order_no} 已自动取消", [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'user_id' => $order->user_id,
                    'total' => $order->total
                ]);
                
            } catch (\Exception $e) {
                $failedCount++;
                
                Log::error('自动取消订单失败', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        // 记录统计结果
        Log::info('订单自动取消任务完成', [
            'total_found' => $expiredOrders->count(),
            'cancelled_count' => $cancelledCount,
            'failed_count' => $failedCount,
            'timeout_minutes' => $timeoutMinutes
        ]);
    }

    /**
     * 取消订单
     */
    private function cancelOrder(Order $order): void
    {
        $timeoutMinutes = config('order.auto_cancel.wechat_timeout_minutes', 10);

        $order->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancelled_by' => null, // 系统自动取消
            'cancel_reason' => "微信支付超时未付款自动取消（{$timeoutMinutes}分钟）"
        ]);
        
        // 触发订单取消事件（如果需要）
        // event(new OrderCancelled($order, 'auto_timeout'));
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('订单自动取消队列任务失败', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
