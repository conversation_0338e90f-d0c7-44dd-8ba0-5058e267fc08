<?php

namespace App\Cart\Providers;

use Illuminate\Support\ServiceProvider;
use App\Cart\Services\CartService;
use App\Product\Services\PriceCalculationService;

class CartServiceProvider extends ServiceProvider
{
    /**
     * 注册应用服务
     *
     * @return void
     */
    public function register()
    {
        // 绑定服务到容器
        $this->app->bind('cart.service', function ($app) {
            return new CartService($app->make(PriceCalculationService::class));
        });
        
        // 直接绑定CartService类，以便在控制器等地方可以直接注入
        $this->app->bind(CartService::class, function ($app) {
            return new CartService($app->make(PriceCalculationService::class));
        });
    }

    /**
     * 引导应用服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载API路由
        $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        
        // 加载Web路由
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
    }
} 