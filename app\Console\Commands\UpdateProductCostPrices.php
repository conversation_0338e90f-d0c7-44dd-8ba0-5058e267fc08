<?php

namespace App\Console\Commands;

use App\Product\Models\Product;
use App\Inventory\Services\CostPriceService;
use App\Unit\Services\UnitService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateProductCostPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:update-cost-prices 
                            {--product-id= : 更新指定商品的成本价}
                            {--batch-size=100 : 批量处理大小}
                            {--force : 强制更新所有商品，即使没有库存}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '根据移动加权平均成本价动态更新商品表的成本价字段（转换为销售单位）';

    /**
     * 成本价服务
     */
    private CostPriceService $costPriceService;

    /**
     * 单位服务
     */
    private UnitService $unitService;

    /**
     * 创建命令实例
     */
    public function __construct(CostPriceService $costPriceService, UnitService $unitService)
    {
        parent::__construct();
        $this->costPriceService = $costPriceService;
        $this->unitService = $unitService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始更新商品成本价...');
        
        $productId = $this->option('product-id');
        $batchSize = (int) $this->option('batch-size');
        $force = $this->option('force');
        
        try {
            DB::beginTransaction();
            
            if ($productId) {
                // 更新指定商品
                $this->updateSingleProduct($productId);
            } else {
                // 批量更新所有商品
                $this->updateAllProducts($batchSize, $force);
            }
            
            DB::commit();
            $this->info('成本价更新完成！');
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('更新失败: ' . $e->getMessage());
            Log::error('更新商品成本价失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 更新单个商品的成本价
     */
    private function updateSingleProduct($productId)
    {
        $product = Product::with(['inventories', 'baseUnit', 'saleUnit'])->find($productId);
        
        if (!$product) {
            $this->error("商品 ID {$productId} 不存在");
            return;
        }
        
        $this->info("更新商品: {$product->name} (ID: {$productId})");
        
        $newCostPrice = $this->calculateProductCostPrice($product);
        
        if ($newCostPrice !== null) {
            $oldCostPrice = $product->cost_price;
            $product->update(['cost_price' => $newCostPrice]);
            
            $this->line("  原成本价: ¥{$oldCostPrice} -> 新成本价: ¥{$newCostPrice}");
            
            Log::info('商品成本价已更新', [
                'product_id' => $productId,
                'product_name' => $product->name,
                'old_cost_price' => $oldCostPrice,
                'new_cost_price' => $newCostPrice
            ]);
        } else {
            $this->line("  无法计算成本价（没有采购记录）");
        }
    }
    
    /**
     * 批量更新所有商品的成本价
     */
    private function updateAllProducts($batchSize, $force)
    {
        $query = Product::with(['inventories', 'baseUnit', 'saleUnit']);
        
        if (!$force) {
            // 只更新有库存的商品
            $query->whereHas('inventories', function($q) {
                $q->where('stock', '>', 0);
            });
        }
        
        $totalProducts = $query->count();
        $this->info("需要更新 {$totalProducts} 个商品");
        
        $updatedCount = 0;
        $skippedCount = 0;
        
        $progressBar = $this->output->createProgressBar($totalProducts);
        $progressBar->start();
        
        $query->chunk($batchSize, function ($products) use (&$updatedCount, &$skippedCount, $progressBar) {
            foreach ($products as $product) {
                $newCostPrice = $this->calculateProductCostPrice($product);
                
                if ($newCostPrice !== null) {
                    $product->update(['cost_price' => $newCostPrice]);
                    $updatedCount++;
                } else {
                    $skippedCount++;
                }
                
                $progressBar->advance();
            }
        });
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info("更新完成: {$updatedCount} 个商品已更新，{$skippedCount} 个商品跳过");
    }
    
    /**
     * 计算商品的成本价（销售单位）
     */
    private function calculateProductCostPrice($product)
    {
        if (!$product->inventories || $product->inventories->isEmpty()) {
            return null;
        }
        
        // 计算所有仓库的移动加权平均成本价（基础单位）
        $totalCostValue = 0;
        $totalQuantity = 0;
        
        foreach ($product->inventories as $inventory) {
            $costPrice30d = $this->costPriceService->calculateMovingAverageCostPrice($inventory->id);
            
            if ($costPrice30d && $inventory->stock > 0) {
                $totalCostValue += $costPrice30d * $inventory->stock;
                $totalQuantity += $inventory->stock;
            }
        }
        
        if ($totalQuantity <= 0) {
            return null;
        }
        
        // 基础单位的平均成本价
        $baseCostPrice = $totalCostValue / $totalQuantity;
        
        // 转换为销售单位的成本价
        $saleCostPrice = $this->convertToSaleUnitPrice($baseCostPrice, $product);
        
        return round($saleCostPrice, 2);
    }
    
    /**
     * 将基础单位的价格转换为销售单位的价格
     */
    private function convertToSaleUnitPrice($basePrice, $product)
    {
        // 如果没有设置销售单位，则使用基础单位
        if (!$product->sale_unit_id || $product->sale_unit_id == $product->base_unit_id) {
            return $basePrice;
        }
        
        try {
            $baseUnit = $product->baseUnit;
            $saleUnit = $product->saleUnit;
            
            if (!$baseUnit || !$saleUnit) {
                $this->warn("商品 {$product->name} 缺少单位信息，使用基础单位价格");
                return $basePrice;
            }
            
            // 使用UnitService转换1个基础单位到销售单位，得到转换因子
            $convertedQuantity = $this->unitService->convertValue(1, $baseUnit, $saleUnit);
            
            // 销售单位价格 = 基础单位价格 / 转换因子
            // 例如：1个 = 0.1盒，基础单位成本1元/个，那么销售单位成本10元/盒
            if ($convertedQuantity > 0) {
                return $basePrice / $convertedQuantity;
            }
            
            $this->warn("商品 {$product->name} 的单位转换结果为0，使用基础单位价格");
            return $basePrice;
            
        } catch (\Exception $e) {
            $this->warn("商品 {$product->name} 单位转换失败: " . $e->getMessage());
            return $basePrice;
        }
    }
} 