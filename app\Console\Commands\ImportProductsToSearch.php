<?php

namespace App\Console\Commands;

use App\Product\Models\Product;
use Illuminate\Console\Command;

class ImportProductsToSearch extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'search:import-products';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '将所有产品导入到搜索引擎索引中';

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始导入产品到搜索索引...');

        // 获取活跃产品数量
        $totalProducts = Product::where('status', 1)->where('allow_sale', true)->count();
        $this->info("找到 {$totalProducts} 个活跃产品");

        // 创建进度条
        $bar = $this->output->createProgressBar($totalProducts);
        $bar->start();

        // 分批处理产品导入
        $chunkSize = 100;
        $processedCount = 0;
        $errorCount = 0;

        Product::where('status', 1)
            ->where('allow_sale', true)
            ->chunkById($chunkSize, function ($products) use (&$processedCount, &$errorCount, $bar) {
                try {
                    // 导入到索引
                    $products->searchable();
                    
                    // 更新进度
                    $count = count($products);
                    $processedCount += $count;
                    $bar->advance($count);
                } catch (\Exception $e) {
                    $this->error("导入错误: " . $e->getMessage());
                    $errorCount++;
                }
            });

        $bar->finish();
        $this->newLine(2);

        // 显示结果
        $this->info("导入完成! 处理了 {$processedCount} 个产品");
        if ($errorCount > 0) {
            $this->warn("导入过程中发生了 {$errorCount} 个错误");
        }

        return Command::SUCCESS;
    }
} 