<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

class ManagePointsRules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'points:rules 
                            {action : 操作类型 (show|enable-proxy|disable-proxy|set-min-amount)}
                            {value? : 设置值}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '管理积分核算规则';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        $value = $this->argument('value');

        switch ($action) {
            case 'show':
                $this->showCurrentRules();
                break;
                
            case 'enable-proxy':
                $this->setProxyOrderPoints(true);
                break;
                
            case 'disable-proxy':
                $this->setProxyOrderPoints(false);
                break;
                
            case 'set-min-amount':
                if ($value === null) {
                    $this->error('请提供最小订单金额值');
                    return 1;
                }
                $this->setMinOrderAmount((float)$value);
                break;
                
            default:
                $this->error('无效的操作类型');
                $this->info('可用操作: show, enable-proxy, disable-proxy, set-min-amount');
                return 1;
        }

        return 0;
    }

    /**
     * 显示当前积分规则
     */
    private function showCurrentRules()
    {
        $rules = config('points.order_points_rules', []);
        
        $this->info('当前积分核算规则:');
        $this->line('');
        
        $this->table(
            ['规则', '当前值', '说明'],
            [
                [
                    '代客下单积分',
                    $rules['proxy_order_points_enabled'] ?? false ? '启用' : '禁用',
                    '代客下单订单是否核算积分'
                ],
                [
                    '排除订单来源',
                    implode(', ', $rules['excluded_order_sources'] ?? ['proxy']),
                    '不核算积分的订单来源'
                ],
                [
                    '排除支付方式',
                    implode(', ', $rules['excluded_payment_methods'] ?? []) ?: '无',
                    '不核算积分的支付方式'
                ],
                [
                    '最小订单金额',
                    ($rules['min_order_amount'] ?? 0) . ' 元',
                    '低于此金额不核算积分'
                ]
            ]
        );
    }

    /**
     * 设置代客下单积分规则
     */
    private function setProxyOrderPoints(bool $enabled)
    {
        $configPath = config_path('points.php');
        
        if (!file_exists($configPath)) {
            $this->error('积分配置文件不存在: ' . $configPath);
            return;
        }
        
        $content = file_get_contents($configPath);
        $pattern = "/'proxy_order_points_enabled'\s*=>\s*(true|false)/";
        $replacement = "'proxy_order_points_enabled' => " . ($enabled ? 'true' : 'false');
        
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
        } else {
            $this->error('无法找到 proxy_order_points_enabled 配置项');
            return;
        }
        
        file_put_contents($configPath, $content);
        
        $status = $enabled ? '启用' : '禁用';
        $this->info("已{$status}代客下单积分核算");
        
        // 清除配置缓存
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }

    /**
     * 设置最小订单金额
     */
    private function setMinOrderAmount(float $amount)
    {
        $configPath = config_path('points.php');
        
        if (!file_exists($configPath)) {
            $this->error('积分配置文件不存在: ' . $configPath);
            return;
        }
        
        $content = file_get_contents($configPath);
        $pattern = "/'min_order_amount'\s*=>\s*\d+(\.\d+)?/";
        $replacement = "'min_order_amount' => " . $amount;
        
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
        } else {
            $this->error('无法找到 min_order_amount 配置项');
            return;
        }
        
        file_put_contents($configPath, $content);
        
        $this->info("已设置最小订单金额为: {$amount} 元");
        
        // 清除配置缓存
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }
}
