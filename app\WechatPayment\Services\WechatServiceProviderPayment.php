<?php

namespace App\WechatPayment\Services;

use App\Order\Models\Order;
use App\WechatPayment\Models\WechatServicePayment;
use App\WechatPayment\Models\WechatServiceProvider;
use App\WechatPayment\Models\WechatServiceRefund;
use App\WechatPayment\Models\WechatSubMerchant;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WechatServiceProviderPayment
{
    /**
     * 支付服务商模型
     *
     * @var WechatServiceProvider
     */
    protected $provider;

    /**
     * 子商户模型（特约商户模式下可为null）
     *
     * @var WechatSubMerchant|null
     */
    protected $subMerchant;

    /**
     * GuzzleHttp 客户端
     *
     * @var Client
     */
    protected $httpClient;

    /**
     * 微信支付 API 根 URL
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * 构造函数
     *
     * @param WechatServiceProvider|null $provider 支付服务商
     * @param WechatSubMerchant|null $subMerchant 子商户（特约商户模式下可为null）
     */
    public function __construct(WechatServiceProvider $provider = null, WechatSubMerchant $subMerchant = null)
    {
        $this->provider = $provider;
        $this->subMerchant = $subMerchant;
        $this->httpClient = new Client([
            'timeout' => 10,
            'verify' => false, // 开发环境可能需要关闭SSL验证
        ]);
        
        $this->baseUrl = 'https://api.mch.weixin.qq.com';
        
        // 如果是沙箱环境
        if ($this->provider && $this->provider->is_sandbox) {
            $this->baseUrl = 'https://api.mch.weixin.qq.com/sandboxnew';
        }
    }

    /**
     * 设置支付服务商
     *
     * @param WechatServiceProvider $provider
     * @return $this
     */
    public function setProvider(WechatServiceProvider $provider)
    {
        $this->provider = $provider;
        
        // 更新API URL以匹配环境
        if ($provider->is_sandbox) {
            $this->baseUrl = 'https://api.mch.weixin.qq.com/sandboxnew';
        } else {
            $this->baseUrl = 'https://api.mch.weixin.qq.com';
        }
        
        return $this;
    }

    /**
     * 设置子商户
     *
     * @param WechatSubMerchant $subMerchant
     * @return $this
     */
    public function setSubMerchant(WechatSubMerchant $subMerchant)
    {
        $this->subMerchant = $subMerchant;
        return $this;
    }

    /**
     * 创建小程序支付
     *
     * @param Order $order 订单
     * @param string $openid 用户openid
     * @return array 支付参数
     * @throws Exception
     */
    public function createMiniAppPay(Order $order, string $openid)
    {
        // 对于特约商户模式，使用validateConfigForDirectMerchant
        if (!$this->subMerchant) {
            $this->validateConfigForDirectMerchant();
        } else {
            $this->validateConfig();
        }
        
        // 生成商户订单号
        $outTradeNo = $this->generateOutTradeNo($order->id);
        
        // 计算订单金额（单位为分）
        $totalFee = intval($order->total * 100);
        
        // 构建请求参数
        $params = [
            'appid' => $this->provider->appid,
            'mch_id' => $this->provider->mch_id,
            'nonce_str' => Str::random(32),
            'body' => '订单支付',
            'out_trade_no' => $outTradeNo,
            'total_fee' => $totalFee,
            'spbill_create_ip' => request()->ip(),
            'notify_url' => $this->provider->notify_url,
            'trade_type' => 'JSAPI',
        ];
        
        // 添加子商户信息（如果有）
        if ($this->subMerchant) {
            $params['sub_appid'] = $this->subMerchant->sub_appid;
            $params['sub_mch_id'] = $this->subMerchant->sub_mch_id;
            // 服务商模式下，如果有子商户AppID，使用sub_openid；否则使用openid
            if ($this->subMerchant->sub_appid) {
                $params['sub_openid'] = $openid; // 子商户模式下使用sub_openid
            } else {
                $params['openid'] = $openid; // 特约商户模式下使用openid
            }
        } else {
            // 直连模式下使用openid
            $params['openid'] = $openid;
        }
        
        // 签名
        $params['sign'] = $this->generateSign($params);
        
        // 转换为XML
        $xml = $this->arrayToXml($params);
        
        try {
            // 发送请求
            $response = $this->httpClient->post($this->baseUrl . '/pay/unifiedorder', [
                'body' => $xml,
                'headers' => [
                    'Content-Type' => 'text/xml',
                ]
            ]);
            
            $responseXml = $response->getBody()->getContents();
            $result = $this->xmlToArray($responseXml);
            
            // 详细记录微信返回结果用于调试
            Log::info('微信支付API返回结果', [
                'order_id' => $order->id,
                'return_code' => $result['return_code'] ?? 'null',
                'result_code' => $result['result_code'] ?? 'null',
                'return_msg' => $result['return_msg'] ?? 'null',
                'err_code' => $result['err_code'] ?? 'null',
                'err_code_des' => $result['err_code_des'] ?? 'null',
                'full_result' => $result
            ]);
            
            // 检查返回结果
            if ($result['return_code'] !== 'SUCCESS') {
                $errorMsg = $result['return_msg'] ?? '通信失败';
                throw new Exception('微信支付通信失败: ' . $errorMsg);
            }
            
            if ($result['result_code'] !== 'SUCCESS') {
                $errorMsg = $result['err_code_des'] ?? ($result['err_code'] ?? '业务失败');
                throw new Exception('微信支付业务失败: ' . $errorMsg);
            }

            // 存储支付记录
            $paymentData = [
                'provider_id' => $this->provider->id,
                'sub_merchant_id' => $this->subMerchant ? $this->subMerchant->id : null,
                'order_id' => $order->id,
                'out_trade_no' => $outTradeNo,
                'total_fee' => $order->total, // 使用正确的字段名
                'trade_type' => 'JSAPI',
                'trade_state' => 'NOTPAY', // 使用正确的字段名
                'prepay_data' => json_encode($result), // 使用正确的字段名
            ];

            $payment = WechatServicePayment::create($paymentData);
            
            // 组装小程序支付参数
            $payParams = [
                'appId' => $this->subMerchant ? ($this->subMerchant->sub_appid ?: $this->provider->appid) : $this->provider->appid,
                'timeStamp' => (string) time(),
                'nonceStr' => Str::random(32),
                'package' => 'prepay_id=' . $result['prepay_id'],
                'signType' => 'MD5',
            ];
            
            $payParams['paySign'] = $this->generateSign($payParams);
            
            // 更新订单状态
            $order->update([
                'payment_method' => 'wechat', // 使用简短的标识符而不是中文
                'payment_no' => $outTradeNo,
            ]);
            
            return $payParams;
            
        } catch (Exception $e) {
            Log::error('微信支付下单异常: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'params' => $params,
            ]);
            
            throw $e;
        }
    }

    /**
     * 查询订单支付状态
     *
     * @param string $outTradeNo 商户订单号
     * @return array 查询结果
     * @throws Exception
     */
    public function queryOrder(string $outTradeNo)
    {
        // 对于特约商户模式，使用validateConfigForDirectMerchant
        if (!$this->subMerchant) {
            $this->validateConfigForDirectMerchant();
        } else {
            $this->validateConfig();
        }
        
        // 构建请求参数
        $params = [
            'appid' => $this->provider->appid,
            'mch_id' => $this->provider->mch_id,
            'nonce_str' => Str::random(32),
            'out_trade_no' => $outTradeNo,
        ];
        
        // 添加子商户信息（如果有）
        if ($this->subMerchant) {
            $params['sub_appid'] = $this->subMerchant->sub_appid;
            $params['sub_mch_id'] = $this->subMerchant->sub_mch_id;
        }
        
        // 签名
        $params['sign'] = $this->generateSign($params);
        
        // 转换为XML
        $xml = $this->arrayToXml($params);
        
        try {
            // 发送请求
            $response = $this->httpClient->post($this->baseUrl . '/pay/orderquery', [
                'body' => $xml,
                'headers' => [
                    'Content-Type' => 'text/xml',
                ]
            ]);
            
            $responseXml = $response->getBody()->getContents();
            $result = $this->xmlToArray($responseXml);
            
            // 检查返回结果
            if ($result['return_code'] !== 'SUCCESS') {
                $errorMsg = $result['return_msg'] ?? '未知错误';
                throw new Exception('微信支付查询失败: ' . $errorMsg);
            }
            
            if ($result['result_code'] !== 'SUCCESS') {
                $errorMsg = $result['err_code_des'] ?? '未知错误';
                throw new Exception('微信支付查询业务失败: ' . $errorMsg);
            }
            
            // 如果查询到支付成功，更新支付记录
            if ($result['trade_state'] === 'SUCCESS') {
                // 查找对应的支付记录
                $payment = WechatServicePayment::where('out_trade_no', $outTradeNo)->first();
                
                if ($payment && $payment->trade_state !== 'SUCCESS') {
                    $payment->update([
                        'transaction_id' => $result['transaction_id'] ?? null,
                        'trade_state' => 'SUCCESS',
                        'pay_time' => now(),
                        'notify_data' => $result,
                    ]);
                    
                    // 更新订单状态
                    $order = Order::find($payment->order_id);
                    if ($order && $order->payment_status !== 'paid') {
                        $order->update([
                            'status' => 'paid', // 🔥 关键修复：更新订单主状态为paid
                            'payment_status' => 'paid',
                            'paid_at' => now(),
                        ]);

                        Log::info('微信支付查询更新订单状态成功', [
                            'order_id' => $order->id,
                            'order_no' => $order->order_no,
                            'old_status' => $order->getOriginal('status'),
                            'new_status' => 'paid',
                            'payment_method' => $order->payment_method,
                            'transaction_id' => $result['transaction_id'] ?? null
                        ]);
                    }
                }
            }
            
            return $result;
            
        } catch (Exception $e) {
            Log::error('微信支付查询异常: ' . $e->getMessage(), [
                'out_trade_no' => $outTradeNo,
            ]);
            
            throw $e;
        }
    }

    /**
     * 申请退款
     *
     * @param array $refundParams 退款参数
     * @return array 退款结果
     * @throws Exception
     */
    public function refund(array $refundParams)
    {
        // 对于特约商户模式，使用validateConfigForDirectMerchant
        if (!$this->subMerchant) {
            $this->validateConfigForDirectMerchant();
        } else {
            $this->validateConfig();
        }
        
        // 构建请求参数
        $params = [
            'appid' => $this->provider->appid,
            'mch_id' => $this->provider->mch_id,
            'nonce_str' => Str::random(32),
            'out_trade_no' => $refundParams['out_trade_no'],
            'out_refund_no' => $refundParams['out_refund_no'],
            'total_fee' => $refundParams['total_fee'],
            'refund_fee' => $refundParams['refund_fee'],
            'refund_desc' => $refundParams['refund_desc'] ?? '用户申请退款',
            'notify_url' => $this->provider->refund_notify_url,
        ];
        
        // 添加子商户信息（如果有）
        if ($this->subMerchant) {
            $params['sub_appid'] = $this->subMerchant->sub_appid;
            $params['sub_mch_id'] = $this->subMerchant->sub_mch_id;
        }
        
        // 签名
        $params['sign'] = $this->generateSign($params);
        
        // 转换为XML
        $xml = $this->arrayToXml($params);
        
        try {
            // 配置证书路径
            $options = [
                'body' => $xml,
                'headers' => [
                    'Content-Type' => 'text/xml',
                ],
            ];
            
            // 处理证书配置
            if ($this->provider->cert_path) {
                // 如果cert_path是文件路径
                if (file_exists(storage_path('app/' . $this->provider->cert_path))) {
                    $options['cert'] = storage_path('app/' . $this->provider->cert_path);
                } 
                // 如果cert_path是证书内容，创建临时文件
                elseif (strpos($this->provider->cert_path, '-----BEGIN CERTIFICATE-----') !== false) {
                    $tempCertFile = tempnam(sys_get_temp_dir(), 'wechat_cert_');
                    file_put_contents($tempCertFile, $this->provider->cert_path);
                    $options['cert'] = $tempCertFile;
                }
            }
            
            // 处理私钥配置
            if ($this->provider->key_path) {
                // 如果key_path是文件路径
                if (file_exists(storage_path('app/' . $this->provider->key_path))) {
                    $options['ssl_key'] = storage_path('app/' . $this->provider->key_path);
                }
                // 如果key_path是私钥内容，创建临时文件
                elseif (strpos($this->provider->key_path, '-----BEGIN PRIVATE KEY-----') !== false) {
                    $tempKeyFile = tempnam(sys_get_temp_dir(), 'wechat_key_');
                    file_put_contents($tempKeyFile, $this->provider->key_path);
                    $options['ssl_key'] = $tempKeyFile;
                }
            }
            
            // 发送请求
            $response = $this->httpClient->post($this->baseUrl . '/secapi/pay/refund', $options);
            
            $responseXml = $response->getBody()->getContents();
            $result = $this->xmlToArray($responseXml);
            
            // 检查返回结果
            if ($result['return_code'] !== 'SUCCESS') {
                $errorMsg = $result['return_msg'] ?? '未知错误';
                throw new Exception('微信支付退款请求失败: ' . $errorMsg);
            }
            
            if ($result['result_code'] !== 'SUCCESS') {
                $errorMsg = $result['err_code_des'] ?? '未知错误';
                throw new Exception('微信支付退款业务失败: ' . $errorMsg);
            }
            
            return $result;
            
        } catch (Exception $e) {
            Log::error('微信支付退款异常: ' . $e->getMessage(), [
                'params' => $refundParams,
            ]);
            
            throw $e;
        }
    }

    /**
     * 查询退款状态
     *
     * @param string $outRefundNo 商户退款单号
     * @return array 查询结果
     * @throws Exception
     */
    public function queryRefund(string $outRefundNo)
    {
        // 对于特约商户模式，使用validateConfigForDirectMerchant
        if (!$this->subMerchant) {
            $this->validateConfigForDirectMerchant();
        } else {
            $this->validateConfig();
        }
        
        // 构建请求参数
        $params = [
            'appid' => $this->provider->appid,
            'mch_id' => $this->provider->mch_id,
            'nonce_str' => Str::random(32),
            'out_refund_no' => $outRefundNo,
        ];
        
        // 添加子商户信息（如果有）
        if ($this->subMerchant) {
            $params['sub_appid'] = $this->subMerchant->sub_appid;
            $params['sub_mch_id'] = $this->subMerchant->sub_mch_id;
        }
        
        // 签名
        $params['sign'] = $this->generateSign($params);
        
        // 转换为XML
        $xml = $this->arrayToXml($params);
        
        try {
            // 发送请求
            $response = $this->httpClient->post($this->baseUrl . '/pay/refundquery', [
                'body' => $xml,
                'headers' => [
                    'Content-Type' => 'text/xml',
                ]
            ]);
            
            $responseXml = $response->getBody()->getContents();
            $result = $this->xmlToArray($responseXml);
            
            // 检查返回结果
            if ($result['return_code'] !== 'SUCCESS') {
                $errorMsg = $result['return_msg'] ?? '未知错误';
                throw new Exception('微信退款查询通信失败: ' . $errorMsg);
            }
            
            if ($result['result_code'] !== 'SUCCESS') {
                $errorMsg = $result['err_code_des'] ?? '未知错误';
                throw new Exception('微信退款查询业务失败: ' . $errorMsg);
            }
            
            return $result;
            
        } catch (Exception $e) {
            Log::error('微信退款查询异常: ' . $e->getMessage(), [
                'out_refund_no' => $outRefundNo,
            ]);
            
            throw $e;
        }
    }

    /**
     * 处理支付结果通知
     *
     * @param string $xml 通知XML
     * @return array 处理结果
     * @throws Exception
     */
    public function handlePayNotify(string $xml)
    {
        // 解析XML数据
        $data = $this->xmlToArray($xml);
        
        // 判断返回状态
        if ($data['return_code'] !== 'SUCCESS') {
            throw new Exception('微信支付通知通信失败: ' . ($data['return_msg'] ?? '未知错误'));
        }
        
        // 判断业务结果
        if ($data['result_code'] !== 'SUCCESS') {
            throw new Exception('微信支付通知业务失败: ' . ($data['err_code_des'] ?? '未知错误'));
        }
        
        // 查找对应的支付记录
        $payment = WechatServicePayment::where('out_trade_no', $data['out_trade_no'])->first();
        
        if (!$payment) {
            throw new Exception('未找到对应的支付记录: ' . $data['out_trade_no']);
        }
        
        // 如果支付记录已成功，直接返回
        if ($payment->trade_state === 'SUCCESS') {
            return [
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK',
            ];
        }
        
        // 更新支付记录
        $payment->update([
            'transaction_id' => $data['transaction_id'],
            'trade_state' => 'SUCCESS',
            'pay_time' => now(),
            'notify_data' => $data,
        ]);
        
        // 更新订单状态
        $order = Order::find($payment->order_id);
        if ($order && $order->payment_status !== 'paid') {
            $order->update([
                'status' => 'paid', // 🔥 关键修复：更新订单主状态为paid
                'payment_status' => 'paid',
                'paid_at' => now(),
            ]);

            Log::info('微信支付回调更新订单状态成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'old_status' => $order->getOriginal('status'),
                'new_status' => 'paid',
                'payment_method' => $order->payment_method,
                'transaction_id' => $data['transaction_id']
            ]);
        }
        
        return [
            'return_code' => 'SUCCESS',
            'return_msg' => 'OK',
        ];
    }

    /**
     * 处理退款结果通知
     *
     * @param string $xml 通知XML
     * @return array 处理结果
     * @throws Exception
     */
    public function handleRefundNotify(string $xml)
    {
        // 解析XML数据
        $data = $this->xmlToArray($xml);
        
        // 判断返回状态
        if ($data['return_code'] !== 'SUCCESS') {
            throw new Exception('微信退款通知通信失败: ' . ($data['return_msg'] ?? '未知错误'));
        }
        
        // 解密退款信息
        if (isset($data['req_info'])) {
            $refundInfo = $this->decryptRefundInfo($data['req_info']);
            
            // 查找对应的退款记录
            $refund = WechatServiceRefund::where('out_refund_no', $refundInfo['out_refund_no'])->first();
            
            if (!$refund) {
                throw new Exception('未找到对应的退款记录: ' . $refundInfo['out_refund_no']);
            }
            
            // 如果退款记录已成功，直接返回
            if ($refund->refund_status === 'SUCCESS') {
                return [
                    'return_code' => 'SUCCESS',
                    'return_msg' => 'OK',
                ];
            }
            
            // 更新退款记录
            $refund->update([
                'refund_id' => $refundInfo['refund_id'],
                'refund_status' => $refundInfo['refund_status'],
                'refund_time' => $refundInfo['refund_status'] === 'SUCCESS' ? now() : null,
                'notify_data' => array_merge($data, $refundInfo),
            ]);
            
            // 如果退款成功，更新订单状态（可根据业务需求调整）
            if ($refundInfo['refund_status'] === 'SUCCESS' && $refund->order_id) {
                $order = Order::find($refund->order_id);
                if ($order) {
                    $order->update([
                        'refund_status' => 'refunded',
                    ]);
                }
            }
        }
        
        return [
            'return_code' => 'SUCCESS',
            'return_msg' => 'OK',
        ];
    }

    /**
     * 生成商户订单号
     *
     * @param int $orderId 订单ID
     * @return string 商户订单号
     */
    protected function generateOutTradeNo(int $orderId): string
    {
        // 订单号前缀 + 订单ID + 随机字符 + 时间戳
        return 'WX' . str_pad($orderId, 8, '0', STR_PAD_LEFT) . Str::random(6) . time();
    }

    /**
     * 生成账单支付的商户订单号（确保不超过32字节）
     *
     * @param int $billId 账单ID
     * @param string $type 支付类型（JS/H5）
     * @return string 商户订单号
     */
    protected function generateBillOutTradeNo(int $billId, string $type = 'JS'): string
    {
        // 格式：B + 类型(2位) + 账单ID(8位) + 时间戳后6位 + 随机数(4位)
        // 总长度：1 + 2 + 8 + 6 + 4 = 21字节，远小于32字节限制
        $timestamp = substr(time(), -6); // 时间戳后6位
        $random = Str::random(4); // 4位随机字符

        return 'B' . $type . str_pad($billId, 8, '0', STR_PAD_LEFT) . $timestamp . $random;
    }

    /**
     * 生成请求签名
     *
     * @param array $params 请求参数
     * @return string 签名
     */
    protected function generateSign(array $params): string
    {
        // 移除空值和签名参数
        $params = array_filter($params, function ($value) {
            return $value !== '' && $value !== null && !is_array($value);
        });
        
        // 按照键名排序
        ksort($params);
        
        // 构建签名字符串
        $stringSign = '';
        foreach ($params as $key => $value) {
            $stringSign .= $key . '=' . $value . '&';
        }
        
        // 添加密钥
        $stringSign .= 'key=' . $this->provider->api_key;
        
        // 返回MD5签名
        return strtoupper(md5($stringSign));
    }

    /**
     * 将数组转换为XML
     *
     * @param array $array 数组
     * @return string XML
     */
    protected function arrayToXml(array $array): string
    {
        $xml = '<xml>';
        foreach ($array as $key => $val) {
            if (is_numeric($val)) {
                $xml .= '<' . $key . '>' . $val . '</' . $key . '>';
            } else {
                $xml .= '<' . $key . '><![CDATA[' . $val . ']]></' . $key . '>';
            }
        }
        $xml .= '</xml>';
        
        return $xml;
    }

    /**
     * 将XML转换为数组
     *
     * @param string $xml XML
     * @return array 数组
     */
    protected function xmlToArray(string $xml): array
    {
        libxml_disable_entity_loader(true);
        $data = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        return json_decode(json_encode($data), true);
    }

    /**
     * 解密退款信息
     *
     * @param string $reqInfo 加密的退款信息
     * @return array 解密后的退款信息
     */
    protected function decryptRefundInfo(string $reqInfo): array
    {
        // 解密过程
        $key = md5($this->provider->api_key);
        $encrypted = base64_decode($reqInfo);
        $decrypted = openssl_decrypt($encrypted, 'AES-256-ECB', $key, OPENSSL_RAW_DATA);
        
        if (!$decrypted) {
            throw new Exception('解密退款信息失败');
        }
        
        return $this->xmlToArray($decrypted);
    }

    /**
     * 验证特约商户配置是否完整（不需要子商户）
     *
     * @throws Exception
     */
    protected function validateConfigForDirectMerchant()
    {
        if (!$this->provider) {
            throw new Exception('未设置微信支付配置');
        }
        
        if (!$this->provider->appid || !$this->provider->mch_id || !$this->provider->api_key) {
            throw new Exception('微信支付配置不完整');
        }
    }

    /**
     * 验证配置是否完整
     *
     * @throws Exception
     */
    protected function validateConfig()
    {
        if (!$this->provider) {
            throw new Exception('未设置微信支付服务商');
        }
        
        if (!$this->provider->appid || !$this->provider->mch_id || !$this->provider->api_key) {
            throw new Exception('微信支付服务商配置不完整');
        }
        
        // 特约商户模式下可以不需要子商户
        if ($this->subMerchant && !$this->subMerchant->sub_mch_id) {
            throw new Exception('微信支付子商户配置不完整');
        }
    }

    /**
     * 🔥 新增：为账单创建微信支付
     */
    public function createPaymentForBill(\App\Billing\Models\Bill $bill): array
    {
        try {
            Log::info('开始为账单创建微信支付', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'amount' => $bill->pending_amount,
                'user_id' => $bill->user_id
            ]);

            // 生成商户订单号（确保不超过32字节）
            $outTradeNo = $this->generateBillOutTradeNo($bill->id, 'JS');

            // 构建支付参数
            $params = [
                'appid' => $this->subMerchant ?
                    ($this->subMerchant->sub_appid ?: $this->provider->appid) :
                    $this->provider->appid,
                'mch_id' => $this->provider->mch_id,
                'nonce_str' => Str::random(32),
                'body' => '账单支付-' . $bill->bill_no,
                'out_trade_no' => $outTradeNo,
                'total_fee' => intval($bill->pending_amount * 100), // 转换为分
                'spbill_create_ip' => request()->ip() ?: '127.0.0.1',
                'notify_url' => route('wechat.payment.notify'),
                'trade_type' => 'JSAPI',
                'openid' => $this->getOpenidForUser($bill->user_id),
            ];

            // 如果是子商户模式，添加子商户参数
            if ($this->subMerchant) {
                $params['sub_mch_id'] = $this->subMerchant->sub_mch_id;
                if ($this->subMerchant->sub_appid) {
                    $params['sub_appid'] = $this->subMerchant->sub_appid;
                    $params['sub_openid'] = $this->getOpenidForUser($bill->user_id, true);
                    unset($params['openid']);
                }
            }

            // 生成签名
            $params['sign'] = $this->generateSign($params);

            // 调用微信统一下单API
            $result = $this->unifiedOrder($params);

            if ($result['return_code'] !== 'SUCCESS') {
                throw new Exception('微信支付通信失败: ' . $result['return_msg']);
            }

            if ($result['result_code'] !== 'SUCCESS') {
                throw new Exception('微信支付业务失败: ' . $result['err_code_des']);
            }

            // 创建支付记录
            $payment = WechatServicePayment::create([
                'bill_id' => $bill->id,
                'out_trade_no' => $outTradeNo,
                'prepay_id' => $result['prepay_id'],
                'total_fee' => $bill->pending_amount,
                'trade_state' => 'NOTPAY',
                'provider_id' => $this->provider->id,
                'sub_merchant_id' => $this->subMerchant?->id,
            ]);

            // 组装小程序支付参数
            $payParams = [
                'appId' => $this->subMerchant ?
                    ($this->subMerchant->sub_appid ?: $this->provider->appid) :
                    $this->provider->appid,
                'timeStamp' => (string) time(),
                'nonceStr' => Str::random(32),
                'package' => 'prepay_id=' . $result['prepay_id'],
                'signType' => 'MD5',
            ];

            $payParams['paySign'] = $this->generateSign($payParams);

            Log::info('账单微信支付创建成功', [
                'bill_id' => $bill->id,
                'payment_id' => $payment->id,
                'out_trade_no' => $outTradeNo,
                'prepay_id' => $result['prepay_id']
            ]);

            return $payParams;

        } catch (Exception $e) {
            Log::error('账单微信支付创建失败', [
                'bill_id' => $bill->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 🔥 新增：为账单创建微信H5支付（无需openid）
     */
    public function createH5PaymentForBill(\App\Billing\Models\Bill $bill, string $userAgent = ''): array
    {
        try {
            Log::info('开始为账单创建微信H5支付', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'amount' => $bill->pending_amount,
                'user_id' => $bill->user_id,
                'user_agent' => $userAgent
            ]);

            // 生成商户订单号（确保不超过32字节）
            $outTradeNo = $this->generateBillOutTradeNo($bill->id, 'H5');

            // 构建H5支付参数
            $params = [
                'appid' => $this->provider->appid,
                'mch_id' => $this->provider->mch_id,
                'nonce_str' => Str::random(32),
                'body' => '账单支付-' . $bill->bill_no,
                'out_trade_no' => $outTradeNo,
                'total_fee' => intval($bill->pending_amount * 100), // 转换为分
                'spbill_create_ip' => request()->ip() ?: '127.0.0.1',
                'notify_url' => route('wechat.payment.notify'),
                'trade_type' => 'MWEB', // H5支付类型
                'scene_info' => json_encode([
                    'h5_info' => [
                        'type' => 'Wap',
                        'wap_url' => request()->getSchemeAndHttpHost(),
                        'wap_name' => '天心食品账单支付'
                    ]
                ])
            ];

            // 如果是子商户模式，添加子商户参数
            if ($this->subMerchant) {
                $params['sub_mch_id'] = $this->subMerchant->sub_mch_id;
                if ($this->subMerchant->sub_appid) {
                    $params['sub_appid'] = $this->subMerchant->sub_appid;
                }
            }

            // 生成签名
            $params['sign'] = $this->generateSign($params);

            // 调用微信统一下单API
            $result = $this->unifiedOrder($params);

            if ($result['return_code'] !== 'SUCCESS') {
                throw new Exception('微信支付通信失败: ' . $result['return_msg']);
            }

            if ($result['result_code'] !== 'SUCCESS') {
                throw new Exception('微信支付业务失败: ' . $result['err_code_des']);
            }

            // 创建支付记录
            $payment = WechatServicePayment::create([
                'bill_id' => $bill->id,
                'out_trade_no' => $outTradeNo,
                'prepay_id' => $result['prepay_id'] ?? null,
                'total_fee' => $bill->pending_amount,
                'trade_state' => 'NOTPAY',
                'trade_type' => 'MWEB',
                'provider_id' => $this->provider->id,
                'sub_merchant_id' => $this->subMerchant?->id,
            ]);

            Log::info('账单微信H5支付创建成功', [
                'bill_id' => $bill->id,
                'payment_id' => $payment->id,
                'out_trade_no' => $outTradeNo,
                'mweb_url' => $result['mweb_url'] ?? null
            ]);

            return [
                'mweb_url' => $result['mweb_url'] ?? null,
                'out_trade_no' => $outTradeNo,
                'payment_id' => $payment->id
            ];

        } catch (Exception $e) {
            Log::error('账单微信H5支付创建失败', [
                'bill_id' => $bill->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取用户的openid
     */
    private function getOpenidForUser(int $userId, bool $isSubOpenid = false): string
    {
        // 🔥 修复：直接从users表获取openid
        $user = \App\Models\User::find($userId);

        if (!$user) {
            throw new Exception('未找到用户信息');
        }

        if (!$user->openid) {
            throw new Exception('用户未绑定微信，无法使用微信支付');
        }

        // 注意：当前系统中users表只有openid字段，没有sub_openid
        // 如果需要支持子商户openid，需要扩展数据库结构
        return $user->openid;
    }

    /**
     * 调用微信统一下单API
     */
    protected function unifiedOrder(array $params): array
    {
        // 转换为XML
        $xml = $this->arrayToXml($params);

        try {
            // 发送请求
            $response = $this->httpClient->post($this->baseUrl . '/pay/unifiedorder', [
                'body' => $xml,
                'headers' => [
                    'Content-Type' => 'text/xml',
                ]
            ]);

            $responseXml = $response->getBody()->getContents();
            $result = $this->xmlToArray($responseXml);

            // 记录微信返回结果
            Log::info('微信统一下单API返回结果', [
                'return_code' => $result['return_code'] ?? 'null',
                'result_code' => $result['result_code'] ?? 'null',
                'return_msg' => $result['return_msg'] ?? 'null',
                'err_code' => $result['err_code'] ?? 'null',
                'err_code_des' => $result['err_code_des'] ?? 'null'
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('微信统一下单API调用失败', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);
            throw $e;
        }
    }
}