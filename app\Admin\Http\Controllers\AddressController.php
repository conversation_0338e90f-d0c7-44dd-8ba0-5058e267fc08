<?php

namespace App\Admin\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Crm\Models\UserAddress;
use App\Models\User;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AddressController extends Controller
{
    /**
     * 获取指定用户的所有地址
     */
    public function index(Request $request, $userId)
    {
        $user = User::findOrFail($userId);
        $addresses = $user->addresses()->get();
        
        return response()->json(ApiResponse::success($addresses));
    }
    
    /**
     * 为指定用户创建新地址
     */
    public function store(Request $request, $userId)
    {
        $validator = Validator::make($request->all(), [
            'contact_name' => 'required|string|max:50',
            'contact_phone' => 'required|string|max:20',
            'province' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'district' => 'nullable|string|max:50',
            'address' => 'required|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
            'is_default' => 'boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = User::findOrFail($userId);
        
        // 如果设置为默认地址，将其他地址设为非默认
        if ($request->is_default) {
            $user->addresses()->update(['is_default' => false]);
        }
        
        $address = $user->addresses()->create($request->all());
        
        return response()->json(ApiResponse::success($address, '地址添加成功'), 201);
    }
    
    /**
     * 获取指定用户的单个地址详情
     */
    public function show(Request $request, $userId, $addressId)
    {
        $user = User::findOrFail($userId);
        $address = $user->addresses()->findOrFail($addressId);
        
        return response()->json(ApiResponse::success($address));
    }
    
    /**
     * 更新指定用户的地址
     */
    public function update(Request $request, $userId, $addressId)
    {
        $validator = Validator::make($request->all(), [
            'contact_name' => 'string|max:50',
            'contact_phone' => 'string|max:20',
            'province' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'district' => 'nullable|string|max:50',
            'address' => 'string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
            'is_default' => 'boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = User::findOrFail($userId);
        $address = $user->addresses()->findOrFail($addressId);
        
        // 如果设置为默认地址，将其他地址设为非默认
        if ($request->has('is_default') && $request->is_default) {
            $user->addresses()->where('id', '!=', $addressId)->update(['is_default' => false]);
        }
        
        $address->update($request->all());
        
        return response()->json(ApiResponse::success($address, '地址更新成功'));
    }
    
    /**
     * 删除指定用户的地址
     */
    public function destroy(Request $request, $userId, $addressId)
    {
        $user = User::findOrFail($userId);
        $address = $user->addresses()->findOrFail($addressId);
        
        $address->delete();
        
        // 如果删除的是默认地址，设置最新的一个地址为默认
        if ($address->is_default) {
            $newDefault = $user->addresses()->first();
            if ($newDefault) {
                $newDefault->update(['is_default' => true]);
            }
        }
        
        return response()->json(ApiResponse::success(null, '地址删除成功'));
    }
    
    /**
     * 设置指定用户的默认地址
     */
    public function setDefault(Request $request, $userId, $addressId)
    {
        $user = User::findOrFail($userId);
        
        // 清除其他默认地址
        $user->addresses()->update(['is_default' => false]);
        
        // 设置新的默认地址
        $address = $user->addresses()->findOrFail($addressId);
        $address->update(['is_default' => true]);
        
        return response()->json(ApiResponse::success($address, '默认地址设置成功'));
    }
} 