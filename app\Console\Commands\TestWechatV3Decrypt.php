<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class TestWechatV3Decrypt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wechat:test-v3-decrypt 
                            {--key= : APIv3密钥}
                            {--test-data= : 测试加密数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试微信支付V3回调解密功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('测试微信支付V3解密功能...');

        // 检查V3密钥配置
        $v3Key = $this->option('key') ?: $this->getApiV3Key();

        if (empty($v3Key)) {
            $this->error('❌ APIv3密钥未配置');
            $this->line('请设置APIv3密钥：');
            $this->line('1. 在.env文件中设置 WECHAT_PAY_V3_KEY');
            $this->line('2. 或在数据库wechat_service_provider表中设置api_v3_key字段');
            return 1;
        }

        $this->info("✅ APIv3密钥已配置 (长度: " . strlen($v3Key) . " 字符)");

        // 检查OpenSSL支持
        if (!function_exists('openssl_decrypt')) {
            $this->error('❌ OpenSSL扩展未安装');
            return 1;
        }

        $this->info('✅ OpenSSL扩展已安装');

        // 检查AES-256-GCM支持
        $ciphers = openssl_get_cipher_methods();
        if (!in_array('aes-256-gcm', $ciphers)) {
            $this->error('❌ 不支持AES-256-GCM算法');
            return 1;
        }

        $this->info('✅ 支持AES-256-GCM算法');

        // 测试解密功能
        $this->testDecryption($v3Key);

        return 0;
    }

    /**
     * 测试解密功能
     */
    private function testDecryption($v3Key)
    {
        $this->info("\n开始测试解密功能...");

        // 创建测试数据
        $testData = [
            'transaction_id' => 'TEST_TRANSACTION_' . time(),
            'out_trade_no' => 'TEST_ORDER_' . time(),
            'trade_state' => 'SUCCESS',
            'amount' => ['total' => 100],
            'success_time' => now()->toISOString()
        ];

        $testJson = json_encode($testData);
        $this->line("原始数据: {$testJson}");

        // 加密测试
        $nonce = substr(md5(time()), 0, 12); // 12字节随机数
        $associatedData = 'transaction';

        try {
            // 加密
            $encrypted = $this->aesGcmEncrypt($testJson, $v3Key, $nonce, $associatedData);
            $this->info("✅ 加密成功");

            // 解密
            $decrypted = $this->aesGcmDecrypt($encrypted, $v3Key, $nonce, $associatedData);
            
            if ($decrypted === $testJson) {
                $this->info("✅ 解密成功，数据一致");
                $this->line("解密结果: {$decrypted}");
            } else {
                $this->error("❌ 解密失败，数据不一致");
                $this->line("期望: {$testJson}");
                $this->line("实际: {$decrypted}");
            }

        } catch (\Exception $e) {
            $this->error("❌ 测试失败: " . $e->getMessage());
        }
    }

    /**
     * AES-256-GCM加密（用于测试）
     */
    private function aesGcmEncrypt($data, $key, $nonce, $associatedData = ''): string
    {
        $tag = '';
        $encrypted = openssl_encrypt(
            $data,
            'aes-256-gcm',
            $key,
            OPENSSL_RAW_DATA,
            $nonce,
            $tag,
            $associatedData
        );

        if ($encrypted === false) {
            throw new \Exception('加密失败: ' . openssl_error_string());
        }

        // 将密文和标签合并
        return $encrypted . $tag;
    }

    /**
     * AES-256-GCM解密
     */
    private function aesGcmDecrypt($encryptedData, $key, $nonce, $associatedData = ''): string
    {
        // 分离密文和认证标签
        if (strlen($encryptedData) < 16) {
            throw new \Exception('加密数据长度不足');
        }

        $ciphertext = substr($encryptedData, 0, -16);
        $tag = substr($encryptedData, -16);

        $decrypted = openssl_decrypt(
            $ciphertext,
            'aes-256-gcm',
            $key,
            OPENSSL_RAW_DATA,
            $nonce,
            $tag,
            $associatedData
        );

        if ($decrypted === false) {
            throw new \Exception('解密失败: ' . openssl_error_string());
        }

        return $decrypted;
    }

    /**
     * 获取APIv3密钥
     */
    private function getApiV3Key(): string
    {
        // 优先从环境变量获取
        $apiV3Key = env('WECHAT_PAY_V3_KEY', '');

        if (!empty($apiV3Key)) {
            $this->line("从环境变量获取到V3密钥 (长度: " . strlen($apiV3Key) . ")");
            return $apiV3Key;
        }

        // 从数据库获取
        try {
            $provider = \App\WechatPayment\Models\WechatServiceProvider::where('is_active', true)->first();
            if ($provider && !empty($provider->api_v3_key)) {
                $this->line("从数据库获取到V3密钥 (服务商: {$provider->name}, 长度: " . strlen($provider->api_v3_key) . ")");
                return $provider->api_v3_key;
            } else {
                $this->line("数据库中未找到有效的V3密钥");
                if ($provider) {
                    $this->line("找到服务商: {$provider->name}, 但api_v3_key为空");
                } else {
                    $this->line("未找到激活的服务商配置");
                }
            }
        } catch (\Exception $e) {
            $this->error("从数据库获取V3密钥失败: " . $e->getMessage());
        }

        return '';
    }
}
