<?php

namespace App\Billing\Providers;

use App\Billing\Services\BillingService;
use App\Billing\Services\ConsolidatedBillingService;
use App\Billing\Services\BillNotificationService;
use App\Billing\Console\Commands\SendBillRemindersCommand;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class BillingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册服务
        $this->app->singleton(BillingService::class, function ($app) {
            return new BillingService();
        });
        
        $this->app->singleton(ConsolidatedBillingService::class, function ($app) {
            return new ConsolidatedBillingService();
        });

        $this->app->singleton(BillNotificationService::class, function ($app) {
            return new BillNotificationService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 加载路由
        $this->loadRoutes();
        
        // 加载数据库迁移（如果需要）
        $this->loadMigrations();
        
        // 加载配置文件（如果有）
        $this->loadConfig();

        // 注册控制台命令
        $this->registerCommands();
    }

    /**
     * 加载路由文件
     */
    protected function loadRoutes(): void
    {
        // API路由
        Route::middleware('api')
            ->prefix('api')
            ->group(function () {
                $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
            });

        // 小程序专用路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/miniprogram.php');

        // Web路由
        Route::middleware('web')
            ->group(function () {
                $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
            });
    }

    /**
     * 加载数据库迁移
     */
    protected function loadMigrations(): void
    {
        if ($this->app->runningInConsole()) {
            $this->loadMigrationsFrom(__DIR__ . '/../../database/migrations');
        }
    }

    /**
     * 加载配置文件
     */
    protected function loadConfig(): void
    {
        $configPath = __DIR__ . '/../config/billing.php';
        
        if (file_exists($configPath)) {
            $this->mergeConfigFrom($configPath, 'billing');
            
            if ($this->app->runningInConsole()) {
                $this->publishes([
                    $configPath => config_path('billing.php'),
                ], 'billing-config');
            }
        }
    }

    /**
     * 注册控制台命令
     */
    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                SendBillRemindersCommand::class,
            ]);
        }
    }
}