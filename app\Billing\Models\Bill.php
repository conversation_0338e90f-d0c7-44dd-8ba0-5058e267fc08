<?php

namespace App\Billing\Models;

use App\Models\User;
use App\Order\Models\Order;
use App\Employee\Models\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;

class Bill extends Model
{
    use HasFactory;

    protected $fillable = [
        'bill_no',
        'bill_type',
        'user_id',
        'order_id',
        'parent_bill_id',
        'original_amount',
        'adjustment_amount',
        'final_amount',
        'paid_amount',
        'pending_amount',
        'balance_amount',
        'online_amount',
        'cash_amount',
        'status',
        'payment_status',
        'due_date',
        'paid_at',
        'cancelled_at',
        'created_by',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'original_amount' => 'decimal:2',
        'adjustment_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'pending_amount' => 'decimal:2',
        'balance_amount' => 'decimal:2',
        'online_amount' => 'decimal:2',
        'cash_amount' => 'decimal:2',
        'due_date' => 'datetime',
        'paid_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'metadata' => 'array',
    ];

    protected $appends = [
        'amounts',
        'dates',
        'refund_amount',
        'pending_refund_amount'
    ];

    // 账单类型常量
    const TYPE_ORDER = 'order';
    const TYPE_ADJUSTMENT = 'adjustment';
    const TYPE_REFUND = 'refund';
    const TYPE_SUPPLEMENT = 'supplement';
    const TYPE_CONSOLIDATED = 'consolidated'; // 🔥 新增：累计账单类型

    // 账单状态常量
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING = 'pending';
    const STATUS_PARTIAL_PAID = 'partial_paid';
    const STATUS_PAID = 'paid';
    const STATUS_OVERPAID = 'overpaid';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_CONSOLIDATED = 'consolidated'; // 🔥 新增：已合并状态

    // 付款状态常量
    const PAYMENT_STATUS_UNPAID = 'unpaid';
    const PAYMENT_STATUS_PARTIAL = 'partial';
    const PAYMENT_STATUS_PAID = 'paid';
    const PAYMENT_STATUS_OVERPAID = 'overpaid';
    const PAYMENT_STATUS_REFUNDED = 'refunded';

    /**
     * 生成唯一账单号
     */
    public static function generateBillNo(): string
    {
        return 'B' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联父账单
     */
    public function parentBill(): BelongsTo
    {
        return $this->belongsTo(Bill::class, 'parent_bill_id');
    }

    /**
     * 关联子账单
     */
    public function childBills(): HasMany
    {
        return $this->hasMany(Bill::class, 'parent_bill_id');
    }

    /**
     * 关联创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    /**
     * 关联账单明细
     */
    public function items(): HasMany
    {
        return $this->hasMany(BillItem::class);
    }

    /**
     * 关联账单调整记录
     */
    public function adjustments(): HasMany
    {
        return $this->hasMany(BillAdjustment::class);
    }

    /**
     * 关联收款记录
     */
    public function paymentRecords(): HasMany
    {
        return $this->hasMany(PaymentRecord::class);
    }

    /**
     * 关联余额变动记录
     */
    public function balanceTransactions(): HasMany
    {
        return $this->hasMany(BalanceTransaction::class);
    }

    /**
     * 获取账单类型文本
     */
    public function getBillTypeTextAttribute(): string
    {
        return match($this->bill_type) {
            self::TYPE_ORDER => '订单账单',
            self::TYPE_ADJUSTMENT => '调整账单',
            self::TYPE_REFUND => '退款账单',
            self::TYPE_SUPPLEMENT => '补差账单',
            self::TYPE_CONSOLIDATED => '累计账单', // 🔥 新增
            default => '未知类型'
        };
    }

    /**
     * 🔥 新增：获取原始付款方式
     */
    public function getOriginalPaymentMethodAttribute(): ?string
    {
        // 如果是订单账单，从关联的订单获取付款方式
        if ($this->bill_type === self::TYPE_ORDER && $this->order) {
            return $this->order->payment_method;
        }

        // 如果有支付记录，从第一条支付记录获取原始付款方式
        if ($this->paymentRecords && $this->paymentRecords->isNotEmpty()) {
            $firstPayment = $this->paymentRecords->first();
            return $firstPayment->original_payment_method ?? $firstPayment->payment_method;
        }

        return null;
    }

    /**
     * 获取账单状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PENDING => '待付款',
            self::STATUS_PARTIAL_PAID => '部分付款',
            self::STATUS_PAID => '已付款',
            self::STATUS_OVERPAID => '超额付款',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_REFUNDED => '已退款',
            self::STATUS_CONSOLIDATED => '已合并', // 🔥 新增
            default => '未知状态'
        };
    }

    /**
     * 获取付款状态文本
     */
    public function getPaymentStatusTextAttribute(): string
    {
        return match($this->payment_status) {
            self::PAYMENT_STATUS_UNPAID => '未付款',
            self::PAYMENT_STATUS_PARTIAL => '部分付款',
            self::PAYMENT_STATUS_PAID => '已付款',
            self::PAYMENT_STATUS_OVERPAID => '超额付款',
            self::PAYMENT_STATUS_REFUNDED => '已退款',
            default => '未知状态'
        };
    }

    /**
     * 检查是否可以支付
     */
    public function canBePaid(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PARTIAL_PAID]) 
               && $this->pending_amount > 0;
    }

    /**
     * 检查是否可以取消
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING]);
    }

    /**
     * 检查是否可以调整
     */
    public function canBeAdjusted(): bool
    {
        return !in_array($this->status, [self::STATUS_CANCELLED, self::STATUS_REFUNDED]);
    }

    /**
     * 检查是否支持余额支付
     */
    public function supportsBalancePayment(): bool
    {
        return $this->user && $this->user->balance > 0;
    }

    /**
     * 计算可用余额支付金额
     */
    public function getMaxBalancePaymentAmount(): float
    {
        if (!$this->supportsBalancePayment()) {
            return 0;
        }

        return min($this->user->balance, $this->pending_amount);
    }

    /**
     * 🔥 修复：更新账单金额（正确处理各种支付场景和退款）
     */
    public function updateAmounts(): void
    {
        // 🔥 关键修复：区分支付和退款，正确计算账单总额

        // 1. 计算实际收到的支付金额（不包括退款）
        $actualPayments = $this->paymentRecords()
            ->where('status', PaymentRecord::STATUS_SUCCESS)
            ->where('count_as_paid', true)
            ->where('payment_amount', '>', 0) // 只计算正数支付
            ->sum('payment_amount');

        // 2. 计算退款金额（负数转正数）
        $refundAmount = abs($this->paymentRecords()
            ->where('status', PaymentRecord::STATUS_SUCCESS)
            ->where('payment_type', PaymentRecord::TYPE_REFUND)
            ->sum('payment_amount'));

        // 3. 🔥 重要修复：退款应该减少账单总额，而不是只影响已付金额
        if ($this->isConsolidatedBill()) {
            // 累计账单：重新计算总额，排除已退款的订单金额
            $this->recalculateConsolidatedBillAmounts();
        } else {
            // 单订单账单：如果有退款，调整账单总额
            if ($refundAmount > 0) {
                $this->adjustment_amount = ($this->adjustment_amount ?? 0) - $refundAmount;
                $this->final_amount = $this->original_amount + $this->adjustment_amount;
            }
        }

        // 4. 计算净已付金额
        $this->paid_amount = max(0, $actualPayments - $refundAmount);

        // 5. 重新计算待付金额
        $this->pending_amount = max(0, $this->final_amount - $this->paid_amount);

        // 更新支付状态
        if ($this->paid_amount == 0) {
            $this->payment_status = self::PAYMENT_STATUS_UNPAID;
            $this->status = self::STATUS_PENDING;
        } elseif ($this->paid_amount < $this->final_amount) {
            $this->payment_status = self::PAYMENT_STATUS_PARTIAL;
            $this->status = self::STATUS_PARTIAL_PAID;
        } elseif ($this->paid_amount == $this->final_amount) {
            $this->payment_status = self::PAYMENT_STATUS_PAID;
            $this->status = self::STATUS_PAID;
            $this->paid_at = $this->paid_at ?: now();
        } else {
            $this->payment_status = self::PAYMENT_STATUS_OVERPAID;
            $this->status = self::STATUS_OVERPAID;
            $this->paid_at = $this->paid_at ?: now();
        }

        $this->save();
    }



    /**
     * 🔥 新增：重新计算累计账单金额（正确处理订单状态变化）
     */
    protected function recalculateConsolidatedBillAmounts(): void
    {
        if (!$this->isConsolidatedBill()) {
            return;
        }

        $orderIds = $this->metadata['order_ids'] ?? [];
        if (empty($orderIds)) {
            return;
        }

        // 查找所有关联订单
        $orders = \App\Order\Models\Order::whereIn('id', $orderIds)->get();

        // 🔥 重要：区分不同的订单状态处理
        $validOrderTotal = 0;
        $cancelledOrderIds = [];
        $refundedOrderIds = [];

        foreach ($orders as $order) {
            if ($order->status === 'cancelled') {
                // 已取消订单：完全排除
                $cancelledOrderIds[] = $order->id;
            } elseif ($order->status === 'refunded') {
                // 已退款订单：完全排除
                $refundedOrderIds[] = $order->id;
            } else {
                // 有效订单：计入总额
                $validOrderTotal += $order->final_payment_amount ?? $order->total;
            }
        }

        // 🔥 关键修复：只有在订单状态确实发生变化时才调整总额
        $excludedOrderIds = array_merge($cancelledOrderIds, $refundedOrderIds);
        $previouslyExcluded = $this->metadata['excluded_orders'] ?? [];

        if (array_diff($excludedOrderIds, $previouslyExcluded) || array_diff($previouslyExcluded, $excludedOrderIds)) {
            // 订单状态有变化，重新计算
            $this->original_amount = $validOrderTotal;
            $this->final_amount = $validOrderTotal + ($this->adjustment_amount ?? 0);

            // 更新元数据
            $metadata = $this->metadata;
            $metadata['excluded_orders'] = $excludedOrderIds;
            $metadata['excluded_cancelled_orders'] = $cancelledOrderIds;
            $metadata['excluded_refunded_orders'] = $refundedOrderIds;
            $metadata['last_recalculated_at'] = now()->toISOString();
            $this->metadata = $metadata;

            Log::info('累计账单重新计算', [
                'bill_id' => $this->id,
                'cancelled_orders' => $cancelledOrderIds,
                'refunded_orders' => $refundedOrderIds,
                'new_total' => $validOrderTotal,
                'original_total' => $this->getOriginal('original_amount'),
                'adjustment' => $validOrderTotal - $this->getOriginal('original_amount')
            ]);
        }
    }

    /**
     * 🔥 新增：获取退款金额
     */
    public function getRefundAmountAttribute(): float
    {
        return abs($this->paymentRecords()
            ->where('status', PaymentRecord::STATUS_SUCCESS)
            ->where('payment_type', PaymentRecord::TYPE_REFUND)
            ->sum('payment_amount'));
    }

    /**
     * 🔥 新增：获取金额信息（包含退款金额）
     */
    public function getAmountsAttribute(): array
    {
        return [
            'original_amount' => $this->original_amount,
            'adjustment_amount' => $this->adjustment_amount,
            'final_amount' => $this->final_amount,
            'paid_amount' => $this->paid_amount,
            'pending_amount' => $this->pending_amount,
            'refund_amount' => $this->refund_amount, // 新增退款金额
        ];
    }

    /**
     * 🔥 新增：获取日期信息
     */
    public function getDatesAttribute(): array
    {
        return [
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'due_date' => $this->due_date,
            'paid_at' => $this->paid_at,
            'cancelled_at' => $this->cancelled_at,
        ];
    }

    /**
     * 🔥 新增：获取详细的支付信息
     */
    public function getPaymentBreakdown(): array
    {
        $breakdown = [
            'total_records' => 0,
            'actual_paid' => 0,
            'credit_amount' => 0,
            'by_scenario' => [],
            'by_method' => [],
        ];

        $records = $this->paymentRecords()
            ->where('status', PaymentRecord::STATUS_SUCCESS)
            ->get();

        foreach ($records as $record) {
            $breakdown['total_records']++;
            
            if ($record->count_as_paid) {
                $breakdown['actual_paid'] += $record->payment_amount;
            } else {
                $breakdown['credit_amount'] += $record->payment_amount;
            }

            // 按场景分组
            $scenario = $record->payment_scenario ?? 'unknown';
            if (!isset($breakdown['by_scenario'][$scenario])) {
                $breakdown['by_scenario'][$scenario] = 0;
            }
            $breakdown['by_scenario'][$scenario] += $record->payment_amount;

            // 按支付方式分组
            $method = $record->payment_method;
            if (!isset($breakdown['by_method'][$method])) {
                $breakdown['by_method'][$method] = 0;
            }
            $breakdown['by_method'][$method] += $record->payment_amount;
        }

        return $breakdown;
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('bill_type', $type);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待付款账单
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', [self::STATUS_PENDING, self::STATUS_PARTIAL_PAID]);
    }

    /**
     * 作用域：已付款账单
     */
    public function scopePaid($query)
    {
        return $query->whereIn('status', [self::STATUS_PAID, self::STATUS_OVERPAID]);
    }

    /**
     * 作用域：逾期账单
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereIn('status', [self::STATUS_PENDING, self::STATUS_PARTIAL_PAID]);
    }

    /**
     * 检查是否为累计账单
     */
    public function isConsolidatedBill(): bool
    {
        return $this->bill_type === self::TYPE_CONSOLIDATED;
    }

    /**
     * 检查是否为子账单
     */
    public function isChildBill(): bool
    {
        return $this->parent_bill_id !== null &&
               isset($this->metadata['is_child_bill']) && 
               $this->metadata['is_child_bill'] === true;
    }

    /**
     * 获取累计账单包含的订单ID列表
     */
    public function getConsolidatedOrderIds(): array
    {
        return $this->metadata['order_ids'] ?? [];
    }

    /**
     * 获取累计账单包含的订单
     */
    public function getConsolidatedOrders()
    {
        if (!$this->isConsolidatedBill()) {
            return collect();
        }

        $orderIds = $this->getConsolidatedOrderIds();
        return Order::whereIn('id', $orderIds)->get();
    }

    /**
     * 作用域：累计账单
     */
    public function scopeConsolidated($query)
    {
        return $query->where('bill_type', self::TYPE_CONSOLIDATED);
    }

    /**
     * 作用域：子账单
     */
    public function scopeChildBills($query)
    {
        return $query->whereNotNull('parent_bill_id')
                    ->whereJsonContains('metadata->is_child_bill', true);
    }

    /**
     * 作用域：主账单（非子账单）
     */
    public function scopeMainBills($query)
    {
        return $query->whereNull('parent_bill_id');
    }

    /**
     * 🔥 新增：获取关联的订单更正记录
     */
    public function getOrderCorrections()
    {
        if (!$this->order_id) {
            return collect();
        }

        return \App\Order\Models\OrderCorrection::where('order_id', $this->order_id)
            ->where('status', 'confirmed')
            ->orderBy('confirmed_at', 'desc')
            ->get();
    }

    /**
     * 🔥 新增：获取需要退款的订单更正
     */
    public function getPendingRefundCorrections()
    {
        return $this->getOrderCorrections()->filter(function($correction) {
            // 需要退款的条件：差异金额为负数且微信支付
            return $correction->difference_amount < 0 &&
                   $this->order &&
                   $this->order->payment_method === 'wechat' &&
                   !$this->hasRefundForCorrection($correction->id);
        });
    }

    /**
     * 🔥 新增：检查是否已为某个更正处理过退款
     */
    public function hasRefundForCorrection(int $correctionId): bool
    {
        return $this->paymentRecords()
            ->where('payment_type', 'refund')
            ->where('status', 'success')
            ->where(function($query) use ($correctionId) {
                $query->whereJsonContains('payment_details->correction_id', $correctionId)
                      ->orWhereJsonContains('extra_data->correction_id', $correctionId);
            })
            ->exists();
    }

    /**
     * 🔥 新增：获取总的待退款金额
     */
    public function getPendingRefundAmountAttribute(): float
    {
        return abs($this->getPendingRefundCorrections()->sum('difference_amount'));
    }
} 