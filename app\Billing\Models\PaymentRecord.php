<?php

namespace App\Billing\Models;

use App\Employee\Models\Employee;
use App\WechatPayment\Models\WechatServicePayment;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'bill_id',
        'correction_id',
        'payment_no',
        'payment_method',
        'original_payment_method',
        'payment_amount',
        'payment_type',
        'payment_scenario',
        'balance_used',
        'balance_before',
        'balance_after',
        'transaction_id',
        'external_payment_no',
        'payment_details',
        'payment_context',
        'count_as_paid',
        'status',
        'payment_time',
        'confirmed_at',
        'received_by',
        'notes',
    ];

    protected $casts = [
        'payment_amount' => 'decimal:2',
        'balance_used' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'payment_details' => 'array',
        'payment_context' => 'array',
        'count_as_paid' => 'boolean',
        'payment_time' => 'datetime',
        'confirmed_at' => 'datetime',
    ];

    // 收款方式常量
    const METHOD_WECHAT = 'wechat';
    const METHOD_ALIPAY = 'alipay';
    const METHOD_CASH = 'cash';
    const METHOD_BANK_TRANSFER = 'bank_transfer';
    const METHOD_COD = 'cod';
    const METHOD_BALANCE = 'balance';
    const METHOD_MIXED = 'mixed';

    const METHOD_OTHER = 'other';

    // 付款类型常量
    const TYPE_FULL = 'full';
    const TYPE_PARTIAL = 'partial';
    const TYPE_SUPPLEMENT = 'supplement';
    const TYPE_REFUND = 'refund';
    const TYPE_OVERPAYMENT = 'overpayment';
    const TYPE_INITIAL = 'initial'; // 🔥 修复：使用数据库中存在的枚举值
    const TYPE_PAYMENT = 'initial'; // 🔥 修复：映射到initial，保持向后兼容


    // 支付场景常量
    const SCENARIO_INITIAL_PAYMENT = 'initial_payment';
    const SCENARIO_CORRECTION_SUPPLEMENT = 'correction_supplement';
    const SCENARIO_BILL_PAYMENT = 'bill_payment';

    const SCENARIO_SUPPLEMENT = 'supplement';
    const SCENARIO_COD_SETTLEMENT = 'cod_settlement';

    // 收款状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_TRANSFERRED = 'transferred';

    /**
     * 生成唯一收款流水号
     */
    public static function generatePaymentNo(): string
    {
        return 'P' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 关联账单
     */
    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class);
    }

    /**
     * 关联收款员工
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'received_by');
    }

    /**
     * 🔥 新增：关联订单更正
     */
    public function correction(): BelongsTo
    {
        return $this->belongsTo(\App\Order\Models\OrderCorrection::class, 'correction_id');
    }

    /**
     * 获取收款方式文本
     */
    public function getPaymentMethodTextAttribute(): string
    {
        return match($this->payment_method) {
            self::METHOD_WECHAT => '微信支付',
            self::METHOD_ALIPAY => '支付宝',
            self::METHOD_CASH => '现金',
            self::METHOD_BANK_TRANSFER => '银行转账',
            self::METHOD_COD => '货到付款',
            self::METHOD_BALANCE => '余额支付',
            self::METHOD_MIXED => '混合支付',

            self::METHOD_OTHER => '其他方式',
            default => '未知方式'
        };
    }

    /**
     * 获取付款类型文本
     */
    public function getPaymentTypeTextAttribute(): string
    {
        return match($this->payment_type) {
            self::TYPE_FULL => '全额付款',
            self::TYPE_PARTIAL => '部分付款',
            self::TYPE_SUPPLEMENT => '补差付款',
            self::TYPE_REFUND => '退款',
            self::TYPE_OVERPAYMENT => '超额付款',
            self::TYPE_INITIAL => '初始付款', // 🔥 修复：使用正确的枚举值
            'payment' => '付款', // 🔥 兼容性：处理可能的旧值
            default => '未知类型'
        };
    }

    /**
     * 获取收款状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => '待确认',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_REFUNDED => '已退款',
            self::STATUS_TRANSFERRED => '已转移',
            default => '未知状态'
        };
    }

    /**
     * 🔥 新增：获取支付场景文本
     */
    public function getPaymentScenarioTextAttribute(): string
    {
        return match($this->payment_scenario) {
            self::SCENARIO_INITIAL_PAYMENT => '初始订单支付',
            self::SCENARIO_SUPPLEMENT => '补款支付',
            self::SCENARIO_COD_SETTLEMENT => '货到付款结算',
            default => '未知场景'
        };
    }

    /**
     * 🔥 新增：获取原订单支付方式文本
     */
    public function getOriginalPaymentMethodTextAttribute(): string
    {
        if (!$this->original_payment_method) {
            return '';
        }
        
        return match($this->original_payment_method) {
            self::METHOD_WECHAT => '微信支付',
            self::METHOD_ALIPAY => '支付宝',
            self::METHOD_CASH => '现金',
            self::METHOD_BANK_TRANSFER => '银行转账',
            self::METHOD_COD => '货到付款',
            self::METHOD_BALANCE => '余额支付',
            self::METHOD_MIXED => '混合支付',

            self::METHOD_OTHER => '其他方式',
            default => '未知方式'
        };
    }

    /**
     * 🔥 新增：是否为补款记录
     */
    public function isSupplementPayment(): bool
    {
        return $this->payment_scenario === self::SCENARIO_SUPPLEMENT;
    }

    /**
     * 🔥 新增：是否为退款记录
     */
    public function isRefundPayment(): bool
    {
        return $this->payment_type === self::TYPE_REFUND;
    }

    /**
     * 🔥 新增：是否为微信支付记录
     */
    public function isWechatPayment(): bool
    {
        return $this->payment_method === self::METHOD_WECHAT;
    }

    /**
     * 🔥 新增：是否为原始支付记录
     */
    public function isOriginalPayment(): bool
    {
        return in_array($this->payment_type, [self::TYPE_INITIAL, self::TYPE_FULL, 'payment']) && 
               in_array($this->payment_scenario, [self::SCENARIO_INITIAL_PAYMENT, self::SCENARIO_BILL_PAYMENT]);
    }

    /**
     * 🔥 新增：是否为订单更正相关的记录
     */
    public function isCorrectionRelated(): bool
    {
        return !is_null($this->correction_id) || 
               $this->payment_scenario === self::SCENARIO_CORRECTION_SUPPLEMENT ||
               (is_array($this->payment_details) && isset($this->payment_details['correction_id'])) ||
               (is_array($this->metadata) && isset($this->metadata['correction_id']));
    }



    /**
     * 🔥 新增：获取完整支付信息描述
     */
    public function getPaymentDescriptionAttribute(): string
    {
        $description = $this->payment_scenario_text;
        
        if ($this->original_payment_method && $this->original_payment_method !== $this->payment_method) {
            $description .= "（原{$this->original_payment_method_text}，补款{$this->payment_method_text}）";
        } else {
            $description .= "（{$this->payment_method_text}）";
        }
        
        return $description;
    }

    /**
     * 作用域：按账单筛选
     */
    public function scopeByBill($query, int $billId)
    {
        return $query->where('bill_id', $billId);
    }

    /**
     * 作用域：按收款方式筛选
     */
    public function scopeByMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：成功的收款
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * 作用域：待处理的收款
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', [self::STATUS_PENDING, self::STATUS_PROCESSING]);
    }

    /**
     * 作用域：已结算的记录
     */
    public function scopeSettled($query)
    {
        return $query->where('status', 'settled');
    }

    /**
     * 🔥 新增：作用域：微信支付记录
     */
    public function scopeWechatPayments($query)
    {
        return $query->where('payment_method', self::METHOD_WECHAT);
    }

    /**
     * 🔥 新增：作用域：退款记录
     */
    public function scopeRefunds($query)
    {
        return $query->where('payment_type', self::TYPE_REFUND);
    }

    /**
     * 🔥 新增：作用域：补款记录
     */
    public function scopeSupplements($query)
    {
        return $query->where('payment_type', self::TYPE_SUPPLEMENT);
    }

    /**
     * 🔥 新增：作用域：原始支付记录
     */
    public function scopeOriginalPayments($query)
    {
        return $query->whereIn('payment_type', [self::TYPE_INITIAL, self::TYPE_FULL, 'payment'])
                    ->whereIn('payment_scenario', [self::SCENARIO_INITIAL_PAYMENT, self::SCENARIO_BILL_PAYMENT]);
    }

    /**
     * 🔥 新增：作用域：订单更正相关记录
     */
    public function scopeCorrectionRelated($query)
    {
        return $query->where(function($q) {
            $q->whereNotNull('correction_id')
              ->orWhere('payment_scenario', self::SCENARIO_CORRECTION_SUPPLEMENT)
              ->orWhere('notes', 'like', '%订单更正%');
        });
    }
} 