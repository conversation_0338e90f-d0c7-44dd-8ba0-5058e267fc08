<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\Order;
use App\Delivery\Models\Delivery;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixProxyOrderDelivery extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'orders:fix-proxy-delivery {--limit=50 : 最大处理订单数} {--dry-run : 仅检查不执行修复}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '修复缺少配送记录的代客下单订单';

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $limit = (int)$this->option('limit');
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('运行在仅检查模式，不会执行实际修复');
        }
        
        $this->info('开始检查代客下单订单...');
        
        // 查找所有代客下单且需要配送但没有配送记录的订单
        $proxyOrders = Order::where('source', 'proxy')
            ->whereIn('delivery_method', ['delivery', 'standard', 'express'])
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('deliveries')
                    ->whereRaw('deliveries.order_id = orders.id');
            })
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
        
        $this->info("找到 {$proxyOrders->count()} 个代客下单且缺少配送记录的订单");
        
        $stats = [
            'total' => $proxyOrders->count(),
            'fixed' => 0,
            'skipped' => 0,
            'failed' => 0
        ];
        
        if ($proxyOrders->count() === 0) {
            $this->info('没有需要修复的订单');
            return 0;
        }
        
        $this->output->progressStart($proxyOrders->count());
        
        foreach ($proxyOrders as $order) {
            $this->output->progressAdvance();
            
            try {
                // 检查必要的配送信息
                if (empty($order->shipping_address) || empty($order->contact_phone)) {
                    $this->warn("订单 #{$order->id} ({$order->order_no}) 缺少必要的配送信息，跳过");
                    $stats['skipped']++;
                    continue;
                }
                
                if (!$dryRun) {
                    DB::beginTransaction();
                    
                    // 创建配送记录
                    $delivery = Delivery::create([
                        'order_id' => $order->id,
                        'status' => 'pending'
                    ]);
                    
                    DB::commit();
                    
                    $this->info("已为订单 #{$order->id} ({$order->order_no}) 创建配送记录 #{$delivery->id}");
                    $stats['fixed']++;
                    
                    // 记录日志
                    Log::info('修复代客下单配送记录', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'delivery_id' => $delivery->id
                    ]);
                } else {
                    $this->info("将为订单 #{$order->id} ({$order->order_no}) 创建配送记录 (仅检查模式)");
                    $stats['fixed']++;
                }
            } catch (\Exception $e) {
                if (!$dryRun) {
                    DB::rollBack();
                }
                
                $this->error("处理订单 #{$order->id} ({$order->order_no}) 时出错: " . $e->getMessage());
                $stats['failed']++;
                
                Log::error('修复代客下单配送记录失败', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        $this->output->progressFinish();
        
        // 输出统计信息
        $this->info('修复完成，统计信息:');
        $this->table(
            ['总数', '已修复', '已跳过', '失败'],
            [[$stats['total'], $stats['fixed'], $stats['skipped'], $stats['failed']]]
        );
        
        return 0;
    }
} 