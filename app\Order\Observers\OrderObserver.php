<?php

namespace App\Order\Observers;

use App\Order\Models\Order;
use App\Order\Events\OrderCompleted;
use App\Order\Events\OrderStatusChanged;
use App\FlyCloud\Services\FlyCloudService;
use App\FlyCloud\Jobs\AutoPrintOrderJob;
use App\FlyCloud\Jobs\AutoPrintReceiptJob;
use App\Shop\Services\ConfigService;
use App\Inventory\Jobs\CreateOrderInventoryTransactions;
use Illuminate\Support\Facades\Log;

class OrderObserver
{
    protected FlyCloudService $flyCloudService;

    public function __construct(FlyCloudService $flyCloudService)
    {
        $this->flyCloudService = $flyCloudService;
    }

    /**
     * 将配置值转换为布尔值
     */
    private function toBool($value): bool
    {
        if (is_bool($value)) return $value;
        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
        }
        return (bool)$value;
    }

    /**
     * 订单创建后触发
     */
    public function created(Order $order): void
    {
        // 🔥 根据配置决定是否在订单创建时打印
        $configService = app(ConfigService::class);
        if ($this->toBool($configService->get('flycloud_print_on_created', false))) {
            $this->triggerAutoPrint($order, '订单创建');
        }

        // 🔥 检查库存是否已处理，避免重复处理
        if ($order->inventory_processed) {
            Log::info('订单库存已在创建时处理，跳过Observer处理', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'inventory_method' => $order->inventory_method
            ]);
            return;
        }

        // 创建库存交易记录（仅对未处理库存的订单）
        // 🔥 重要修改：代客下单订单不在创建时处理库存，等待人工确认
        if ($order->source === 'proxy') {
            Log::info('🔥 代客下单订单创建，跳过库存处理，等待人工确认', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'source' => $order->source,
                'payment_method' => $order->payment_method,
                'next_step' => '等待管理员人工确认后处理库存'
            ]);
            return;
        }

        // 🔥 修复：普通货到付款订单立即创建库存事务记录，避免打印时序问题
        if ($order->payment_method === 'cod') {
            $this->createInventoryTransactionsImmediately($order);
        } else {
            $this->createInventoryTransactions($order);
        }
    }

    /**
     * 订单状态更新后触发
     */
    public function updated(Order $order): void
    {
        // 检查是否有状态变更
        if ($order->wasChanged('status')) {
            $oldStatus = $order->getOriginal('status');
            $newStatus = $order->status;

            // 触发订单状态变更事件（统一处理库存等操作）
            event(new OrderStatusChanged($order, $oldStatus, $newStatus));

            Log::info('订单状态变更', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);

            // 特定状态的处理
            switch ($newStatus) {
                case 'paid':
                    // 🔥 只处理微信支付订单的库存事务记录创建
                    if (!$order->inventory_processed && $order->payment_method === 'wechat') {
                        $this->createInventoryTransactionsImmediately($order);
                    }

                    // 🔥 根据配置决定是否在订单付款时打印
                    $configService = app(ConfigService::class);
                    if ($this->toBool($configService->get('flycloud_print_on_paid', false))) {
                        $this->triggerAutoPrint($order, '订单付款');
                    }

                    // 🔥 禁用：订单支付时不再创建出库单，改为确认时创建
                    // $this->tryCreateOutboundDocument($order);

                    Log::info('订单付款完成', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'old_status' => $oldStatus,
                        'new_status' => $newStatus,
                        'print_enabled' => $this->toBool($configService->get('flycloud_print_on_paid', false))
                    ]);
                    break;

                case 'confirmed':
                    // 🔥 优化：根据来源状态决定处理逻辑
                    if ($oldStatus === 'paid') {
                        // 从paid状态转来的（在线支付自动确认）
                        Log::info('在线支付订单自动确认', [
                            'order_id' => $order->id,
                            'order_no' => $order->order_no,
                            'payment_method' => $order->payment_method,
                            'old_status' => $oldStatus,
                            'new_status' => $newStatus
                        ]);
                    } else {
                        // 其他状态转来的（如货到付款人工确认）
                        // 🔥 确保有库存事务记录
                        if (!$order->inventory_processed) {
                            Log::info('订单确认时创建库存事务记录', [
                                'order_id' => $order->id,
                                'order_no' => $order->order_no,
                                'payment_method' => $order->payment_method,
                                'old_status' => $oldStatus,
                                'new_status' => $newStatus
                            ]);
                            $this->createInventoryTransactionsImmediately($order);
                        }
                    }

                    // 🔥 代客下单订单特殊处理：需要额外确认步骤
                    if ($order->source === 'proxy') {
                        Log::info('代客下单订单确认，需要仓库确认后才能配送', [
                            'order_id' => $order->id,
                            'order_no' => $order->order_no,
                            'created_by_employee' => $order->created_by_id,
                            'next_step' => '等待仓库确认出库'
                        ]);

                    }

                    // 🔥 统一处理：代客下单和普通订单使用完全相同的确认流程
                    $this->tryCreateOutboundDocumentOnConfirm($order);
                    $this->createDeliveryForConfirmedOrder($order);

                    // 🔥 代客下单特殊标记：记录订单来源用于后续处理
                    if ($order->source === 'proxy') {
                        Log::info('代客下单订单确认完成，使用与普通订单相同的处理流程', [
                            'order_id' => $order->id,
                            'order_no' => $order->order_no,
                            'created_by_employee' => $order->created_by_id,
                            'note' => '代客下单与普通订单使用相同的确认流程'
                        ]);
                    }

                    // 🔥 统一在Observer中处理打印，避免重复打印
                    $configService = app(ConfigService::class);
                    $printEnabled = $this->toBool($configService->get('flycloud_print_on_confirmed', true));

                    if ($printEnabled) {
                        $trigger = $oldStatus === 'paid' ? '微信支付订单确认' :
                                  ($order->source === 'proxy' ? '代客下单订单确认' : '货到付款订单确认');
                        $this->triggerWarehousePrint($order, $trigger);
                    }

                    Log::info('订单人工确认完成', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'order_source' => $order->source,
                        'is_proxy_order' => $order->source === 'proxy',
                        'confirmed_at' => $order->confirmed_at,
                        'old_status' => $oldStatus,
                        'new_status' => $newStatus,
                        'receipt_print_enabled' => $printEnabled
                    ]);
                    break;

                case 'delivered':
                    // 触发订单完成事件，给予积分奖励
                    event(new OrderCompleted($order));

                    // 🔥 修复：移除订单送达时的账单创建逻辑
                    // 账单应该在订单更正确认后创建，因为那时订单数据才是最终正确的
                    // $this->createBillForDeliveredOrder($order);

                    Log::info('订单送达完成，触发积分奖励事件', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'user_id' => $order->user_id,
                        'status' => $order->status,
                        'note' => '账单将在订单更正确认后创建'
                    ]);
                    break;
            }
        }
    }

    /**
     * 触发自动打印
     */
    protected function triggerAutoPrint(Order $order, string $trigger): void
    {
        try {
            // 检查系统是否启用自动打印
            $configService = app(ConfigService::class);
            if (!$this->toBool($configService->get('flycloud_auto_print_enabled', true))) {
                Log::info('自动打印已禁用', ['order_id' => $order->id]);
                return;
            }

            Log::info('调度自动打印任务', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'order_no' => $order->order_no
            ]);

            // 调度打印任务，延迟执行确保库存事务先完成
            // 库存事务延迟5-10秒，打印任务延迟25秒，确保依赖关系正确
            AutoPrintOrderJob::dispatch($order->id, $trigger)
                ->delay(now()->addSeconds(25))
                ->onQueue('printing');

        } catch (\Exception $e) {
            Log::error('自动打印触发失败', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 🔥 新增：触发小票打印
     */
    protected function triggerReceiptPrint(Order $order, string $trigger): void
    {
        try {
            // 检查系统是否启用自动打印
            $configService = app(ConfigService::class);
            if (!$this->toBool($configService->get('flycloud_auto_print_enabled', true))) {
                Log::info('小票自动打印已禁用', ['order_id' => $order->id]);
                return;
            }

            Log::info('调度小票打印任务', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'order_no' => $order->order_no
            ]);

            // 调度小票打印任务，立即执行
            AutoPrintReceiptJob::dispatch($order->id, $trigger)
                ->onQueue('printing');

        } catch (\Exception $e) {
            Log::error('小票打印任务调度失败', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 🔥 修复：触发分仓库打印（同步执行）- 使用正确的打印方法
     */
    protected function triggerWarehousePrint(Order $order, string $trigger): void
    {
        try {
            // 检查系统是否启用自动打印
            $configService = app(ConfigService::class);
            if (!$this->toBool($configService->get('flycloud_auto_print_enabled', true))) {
                Log::info('分仓库自动打印已禁用', ['order_id' => $order->id]);
                return;
            }

            Log::info('开始同步执行分仓库打印', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'order_no' => $order->order_no
            ]);

            // 🔥 修复：使用正确的分仓库打印方法（与手动打印相同）
            $flyCloudService = app(\App\FlyCloud\Services\FlyCloudService::class);
            $result = $flyCloudService->printOrderReceipt($order, [
                'copies' => 1,
                'auto_print' => true,
                'trigger' => $trigger
            ]);

            if ($result) {
                Log::info('分仓库自动打印成功', [
                    'trigger' => $trigger,
                    'order_id' => $order->id,
                    'order_no' => $order->order_no
                ]);
            } else {
                Log::warning('分仓库自动打印失败', [
                    'trigger' => $trigger,
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'reason' => '打印服务返回失败'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('同步分仓库打印失败', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }



    /**
     * 创建库存交易记录
     */
    protected function createInventoryTransactions(Order $order): void
    {
        try {
            // 使用 DB::afterCommit 确保在事务提交后执行
            \Illuminate\Support\Facades\DB::afterCommit(function () use ($order) {
                // 重新查询订单项数量（避免事务可见性问题）
                $itemsCount = \Illuminate\Support\Facades\DB::table('order_items')
                    ->where('order_id', $order->id)
                    ->count();

                if ($itemsCount == 0) {
                    Log::warning('订单无商品，跳过库存交易记录创建', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no
                    ]);
                    return;
                }

                // 确定延迟时间：代客下单延迟10秒，普通订单延迟5秒
                $isProxyOrder = $order->source === 'proxy';
                $delaySeconds = $isProxyOrder ? 10 : 5;

                Log::info('调度库存交易记录创建任务', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'is_proxy_order' => $isProxyOrder,
                    'delay_seconds' => $delaySeconds,
                    'items_count' => $itemsCount
                ]);

                // 异步创建库存交易记录
                CreateOrderInventoryTransactions::dispatch($order->id)
                    ->delay(now()->addSeconds($delaySeconds))
                    ->onQueue('inventory');
            });

        } catch (\Exception $e) {
            Log::error('调度库存交易记录创建任务失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 立即创建库存交易记录（用于货到付款订单）
     */
    protected function createInventoryTransactionsImmediately(Order $order): void
    {
        try {
            Log::info('🔍 开始立即创建库存交易记录', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'payment_method' => $order->payment_method,
                'status' => $order->status,
                'inventory_processed' => $order->inventory_processed
            ]);

            // 使用 DB::afterCommit 确保在事务提交后执行
            \Illuminate\Support\Facades\DB::afterCommit(function () use ($order) {
                // 重新查询订单项数量（避免事务可见性问题）
                $itemsCount = \Illuminate\Support\Facades\DB::table('order_items')
                    ->where('order_id', $order->id)
                    ->count();

                Log::info('🔍 事务提交后检查订单项', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'items_count' => $itemsCount
                ]);

                if ($itemsCount == 0) {
                    Log::warning('订单无商品，跳过库存交易记录创建', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no
                    ]);
                    return;
                }

                Log::info('🚀 立即执行库存交易记录创建任务', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'items_count' => $itemsCount
                ]);

                // 使用同步队列立即执行
                CreateOrderInventoryTransactions::dispatchSync($order->id);

                Log::info('✅ 库存交易记录创建任务执行完成', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no
                ]);
            });

        } catch (\Exception $e) {
            Log::error('❌ 立即创建库存交易记录失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 🔥 新增：订单确认时创建出库单
     */
    protected function tryCreateOutboundDocumentOnConfirm(Order $order): void
    {
        try {
            // 检查是否已存在出库单
            $existingOutbound = \App\Inventory\Models\OutboundDocument::where('order_id', $order->id)->first();
            if ($existingOutbound) {
                Log::info('订单已存在出库单，跳过创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'outbound_document_no' => $existingOutbound->document_no,
                    'trigger' => '订单确认'
                ]);
                return;
            }

            // 使用出库服务创建出库单
            $outboundService = app(\App\Inventory\Services\OutboundService::class);

            try {
                // 尝试使用标准出库单创建
                $outboundDocument = $outboundService->createOutboundFromOrder($order);

                // 标记库存已通过出库单处理
                $order->update([
                    'inventory_method' => 'outbound_document',
                    'inventory_processed' => true,
                ]);

                Log::info('订单确认时出库单创建成功', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'outbound_document_id' => $outboundDocument->id,
                    'outbound_document_no' => $outboundDocument->document_no,
                    'trigger' => '订单确认'
                ]);

            } catch (\Exception $e) {
                // 如果标准出库单创建失败，使用简化出库单
                Log::warning('标准出库单创建失败，使用简化出库单', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'error' => $e->getMessage()
                ]);

                $this->createSimpleOutboundForConfirm($order);
            }

        } catch (\Exception $e) {
            Log::error('订单确认时出库单创建失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 尝试为订单创建出库单（支付时触发）
     */
    protected function tryCreateOutboundDocument(Order $order): void
    {
        try {
            // 检查是否已存在出库单
            $existingOutbound = \App\Inventory\Models\OutboundDocument::where('order_id', $order->id)->first();
            if ($existingOutbound) {
                Log::info('订单已存在出库单，跳过创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'outbound_document_no' => $existingOutbound->document_no
                ]);
                return;
            }

            // 使用出库服务创建出库单
            $outboundService = app(\App\Inventory\Services\OutboundService::class);
            $outboundDocument = $outboundService->createOutboundFromOrder($order);

            Log::info('订单状态变更触发出库单创建成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'outbound_document_id' => $outboundDocument->id,
                'outbound_document_no' => $outboundDocument->document_no,
                'trigger' => '订单状态变为已付款'
            ]);

        } catch (\Exception $e) {
            Log::error('订单状态变更触发出库单创建失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 🔥 已废弃：为送达的订单创建账单
     *
     * 原因：账单应该在订单更正确认后创建，因为：
     * 1. 订单送达时的数据可能不是最终数据
     * 2. 需要等待更正确认后，订单数据才是正确的
     * 3. 微信支付订单也需要在更正确认后创建账单用于补款/退款处理
     *
     * 新的账单创建时机：
     * - 订单更正确认后，通过 BillingService::recordOrderCorrectionResult() 创建
     * - 支持所有支付方式（微信支付、货到付款等）
     */
    protected function createBillForDeliveredOrder_DEPRECATED(Order $order): void
    {
        // 🔥 此方法已废弃，账单创建逻辑已迁移到订单更正确认后
        Log::info('账单创建逻辑已迁移到订单更正确认后，跳过订单送达时的账单创建', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'payment_method' => $order->payment_method,
            'new_logic' => '账单将在订单更正确认后通过 BillingService::recordOrderCorrectionResult() 创建'
        ]);

        return;

        // 以下代码保留作为参考，但不再执行
        /*
        try {
            // 检查是否已存在账单
            $existingBill = \App\Billing\Models\Bill::where('order_id', $order->id)->first();
            if ($existingBill) {
                Log::info('订单已存在账单，跳过创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'bill_no' => $existingBill->bill_no
                ]);
                return;
            }

            // 只为货到付款订单创建账单
            if ($order->payment_method !== 'cod') {
                Log::info('非货到付款订单，跳过账单创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'payment_method' => $order->payment_method
                ]);
                return;
            }

            // 使用账单服务创建账单
            $billingService = app(\App\Billing\Services\BillingService::class);
            $bill = $billingService->createBillFromOrder($order, [
                'notes' => '订单送达时自动创建账单（货到付款）',
                'due_date' => now()->addDays(7),
                'created_by' => null
            ]);

            Log::info('订单送达时账单创建成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'bill_amount' => $bill->final_amount
            ]);

        } catch (\Exception $e) {
            Log::error('订单送达时账单创建失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        */
    }

    /**
     * 🔥 新增：为确认的订单创建配送记录
     */
    protected function createDeliveryForConfirmedOrder(Order $order): void
    {
        try {
            // 检查是否已存在配送记录
            $existingDelivery = \App\Delivery\Models\Delivery::where('order_id', $order->id)->first();
            if ($existingDelivery) {
                Log::info('订单已存在配送记录，跳过创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'delivery_id' => $existingDelivery->id,
                    'delivery_status' => $existingDelivery->status
                ]);
                return;
            }

            $user = $order->user;
            $delivererId = null;

            // 尝试分配默认配送员
            if ($user && $user->default_employee_deliverer_id) {
                $deliverer = \App\Delivery\Models\Deliverer::where('employee_id', $user->default_employee_deliverer_id)->first();
                if ($deliverer) {
                    $delivererId = $deliverer->id;
                    Log::info('订单确认时自动分配默认配送员', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'user_id' => $user->id,
                        'deliverer_id' => $deliverer->id,
                        'employee_id' => $user->default_employee_deliverer_id
                    ]);
                }
            }

            // 创建配送记录
            $delivery = \App\Delivery\Models\Delivery::create([
                'order_id' => $order->id,
                'status' => 'pending',
                'deliverer_id' => $delivererId,
            ]);

            if ($delivererId) {
                Log::info('订单确认后自动分配配送员成功', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'delivery_id' => $delivery->id,
                    'deliverer_id' => $delivererId,
                    'user_id' => $user->id ?? null
                ]);
            } else {
                Log::info('订单确认后创建配送记录成功，等待分配配送员', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'delivery_id' => $delivery->id,
                    'user_id' => $user->id ?? null,
                    'note' => '用户未绑定默认配送员，需要手动分配'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('订单确认时创建配送记录失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }





    /**
     * 🔥 新增：创建简化出库单（订单确认时使用）
     */
    private function createSimpleOutboundForConfirm(Order $order): void
    {
        try {
            // 获取默认仓库
            $defaultWarehouseId = 1; // 默认仓库ID

            // 创建出库单
            $outboundDocument = \App\Inventory\Models\OutboundDocument::create([
                'document_no' => 'OUT' . date('YmdHis') . rand(1000, 9999),
                'document_type' => 'sales', // 销售出库
                'reference_type' => 'order',
                'reference_id' => $order->id,
                'order_id' => $order->id,
                'warehouse_id' => $order->warehouse_id ?? $defaultWarehouseId,
                'status' => 'completed', // 直接设为已完成
                'confirmed_at' => now(),
                'completed_at' => now(),
                'confirmed_by' => 1, // 系统用户
                'completed_by' => 1, // 系统用户
                'created_by' => 1, // 系统用户
                'updated_by' => 1, // 系统用户
                'notes' => "订单确认 {$order->order_no} 自动创建出库单",
            ]);

            $totalCost = 0;
            $totalItems = 0;

            // 创建出库明细并扣减库存
            foreach ($order->items as $orderItem) {
                $product = \App\Product\Models\Product::find($orderItem->product_id);
                if (!$product) {
                    continue;
                }

                // 计算成本价
                $unitCost = $product->cost_price ?? $orderItem->price;
                $itemTotalCost = $orderItem->quantity * $unitCost;

                \App\Inventory\Models\OutboundItem::create([
                    'outbound_document_id' => $outboundDocument->id,
                    'product_id' => $orderItem->product_id,
                    'product_name' => $orderItem->product_name,
                    'product_sku' => $product->sku ?? '',
                    'planned_quantity' => $orderItem->quantity,
                    'actual_quantity' => $orderItem->quantity,
                    'unit_id' => $orderItem->unit_id,
                    'unit_cost' => $unitCost,
                    'total_cost' => $itemTotalCost,
                    'notes' => "订单确认项出库",
                ]);

                // 🔥 扣减库存
                $result = $product->reduceStockWithPolicy($orderItem->quantity, $orderItem->unit_id);

                if (!$result['success']) {
                    Log::warning('订单确认库存扣减失败', [
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity' => $orderItem->quantity,
                        'current_stock' => $product->getTotalStock(),
                        'error' => $result['message'] ?? '未知错误'
                    ]);
                }

                $totalCost += $itemTotalCost;
                $totalItems += $orderItem->quantity;
            }

            // 更新出库单总计
            $outboundDocument->update([
                'total_cost' => $totalCost,
                'total_items' => $totalItems,
            ]);

            // 标记库存已通过出库单处理
            $order->update([
                'inventory_method' => 'outbound_document',
                'inventory_processed' => true,
            ]);

            Log::info('订单确认简化出库单创建成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'outbound_id' => $outboundDocument->id,
                'outbound_no' => $outboundDocument->document_no,
                'total_cost' => $totalCost,
                'total_items' => $totalItems
            ]);

        } catch (\Exception $e) {
            Log::error('创建订单确认简化出库单失败', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
}