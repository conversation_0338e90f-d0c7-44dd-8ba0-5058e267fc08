<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ToggleWarehouseMode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'warehouse:toggle-mode 
                            {mode : The warehouse mode (single|multi)}
                            {--warehouse-id=1 : Default warehouse ID for single mode}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '切换仓库模式（单仓库/多仓库）';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $mode = $this->argument('mode');
        $warehouseId = $this->option('warehouse-id');

        if (!in_array($mode, ['single', 'multi'])) {
            $this->error('模式必须是 single 或 multi');
            return 1;
        }

        $this->info("正在切换到 {$mode} 仓库模式...");

        // 更新环境变量
        $envUpdates = [];
        
        if ($mode === 'single') {
            $envUpdates['ENABLE_MULTI_WAREHOUSE'] = 'false';
            $envUpdates['FORCE_SINGLE_WAREHOUSE'] = 'true';
            $envUpdates['DEFAULT_WAREHOUSE_ID'] = $warehouseId;
            
            $this->info("单仓库模式配置:");
            $this->line("- 禁用多仓库功能");
            $this->line("- 强制使用单仓库");
            $this->line("- 默认仓库ID: {$warehouseId}");
        } else {
            $envUpdates['ENABLE_MULTI_WAREHOUSE'] = 'true';
            $envUpdates['FORCE_SINGLE_WAREHOUSE'] = 'false';
            
            $this->info("多仓库模式配置:");
            $this->line("- 启用多仓库功能");
            $this->line("- 允许商品分布在多个仓库");
        }

        // 更新 .env 文件
        if ($this->updateEnvFile($envUpdates)) {
            $this->info('✅ 环境配置已更新');
        } else {
            $this->error('❌ 更新环境配置失败');
            return 1;
        }

        // 清除配置缓存
        $this->call('config:clear');
        $this->info('✅ 配置缓存已清除');

        // 清除库存缓存
        $this->call('cache:clear');
        $this->info('✅ 应用缓存已清除');

        $this->info("🎉 成功切换到 {$mode} 仓库模式！");
        
        if ($mode === 'single') {
            $this->warn('⚠️  注意：单仓库模式下，所有库存操作将只针对仓库ID: ' . $warehouseId);
            $this->warn('⚠️  请确保该仓库存在且包含必要的商品库存');
        }

        return 0;
    }

    /**
     * 更新 .env 文件
     *
     * @param array $updates
     * @return bool
     */
    private function updateEnvFile(array $updates): bool
    {
        $envFile = base_path('.env');
        
        if (!File::exists($envFile)) {
            $this->error('.env 文件不存在');
            return false;
        }

        $envContent = File::get($envFile);

        foreach ($updates as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                // 更新现有配置
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                // 添加新配置
                $envContent .= "\n{$replacement}";
            }
        }

        return File::put($envFile, $envContent) !== false;
    }
}
