<?php

namespace App\Billing\Http\Controllers;

use App\Billing\Services\ConsolidatedBillingService;
use App\Billing\Http\Resources\BillResource;
use App\Billing\Models\Bill;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ConsolidatedBillController extends Controller
{
    protected ConsolidatedBillingService $consolidatedBillingService;

    public function __construct(ConsolidatedBillingService $consolidatedBillingService)
    {
        $this->consolidatedBillingService = $consolidatedBillingService;
    }

    /**
     * 获取累计账单列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // 🔥 修复：只查询主累计账单，不包含子账单
            $query = Bill::where('bill_type', 'consolidated')
                ->whereNull('parent_bill_id') // 排除子账单
                ->with(['user', 'items', 'paymentRecords', 'childBills'])
                ->orderBy('created_at', 'desc');

            // 搜索过滤
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('bill_no', 'like', "%{$search}%")
                      ->orWhereHas('user', function ($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                      });
                });
            }

            // 状态过滤
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 日期范围过滤
            if ($request->filled('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }
            if ($request->filled('date_to')) {
                $query->where('created_at', '<=', $request->date_to);
            }

            // 订单数量范围过滤
            if ($request->filled('min_orders')) {
                $query->whereRaw('JSON_EXTRACT(metadata, "$.order_count") >= ?', [(int)$request->min_orders]);
            }
            if ($request->filled('max_orders')) {
                $query->whereRaw('JSON_EXTRACT(metadata, "$.order_count") <= ?', [(int)$request->max_orders]);
            }

            $bills = $query->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => BillResource::collection($bills),
                'pagination' => [
                    'current_page' => $bills->currentPage(),
                    'last_page' => $bills->lastPage(),
                    'per_page' => $bills->perPage(),
                    'total' => $bills->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取累计账单列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示累计账单详情
     */
    public function show(Bill $consolidatedBill): JsonResponse
    {
        try {
            // 确保这是一个累计账单
            if (!$this->isConsolidatedBill($consolidatedBill)) {
                return response()->json([
                    'success' => false,
                    'message' => '这不是一个累计账单'
                ], 404);
            }

            $consolidatedBill->load([
                'user',
                'items.order',
                'paymentRecords',
                'adjustments',
                'childBills.order.items'
            ]);

            return response()->json([
                'success' => true,
                'data' => new BillResource($consolidatedBill)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取累计账单详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取累计账单包含的订单列表
     */
    public function getIncludedOrders(Bill $consolidatedBill): JsonResponse
    {
        try {
            if (!$this->isConsolidatedBill($consolidatedBill)) {
                return response()->json([
                    'success' => false,
                    'message' => '这不是一个累计账单'
                ], 404);
            }

            // 从metadata中获取订单ID列表
            $orderIds = $consolidatedBill->metadata['order_ids'] ?? [];
            
            if (empty($orderIds)) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            $orders = \App\Order\Models\Order::whereIn('id', $orderIds)
                ->with(['items.product', 'user'])
                ->get();

            $ordersData = $orders->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_no' => $order->order_no,
                    'user_id' => $order->user_id,
                    'total' => $order->final_payment_amount ?? $order->total,
                    'final_payment_amount' => $order->final_payment_amount ?? $order->total,
                    'status' => $order->status,
                    'created_at' => $order->created_at->toISOString(),
                    'items' => $order->items->map(function ($item) {
                        return [
                            'id' => $item->id,
                            'order_id' => $item->order_id,
                            'product_id' => $item->product_id,
                            'product_name' => $item->product->name ?? $item->product_name,
                            'product_specification' => $item->product_specification,
                            'quantity' => $item->quantity,
                            'price' => $item->price,
                            'total_price' => $item->total_price,
                        ];
                    })
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $ordersData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取包含订单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取累计账单操作历史
     */
    public function getOperationHistory(Bill $consolidatedBill): JsonResponse
    {
        try {
            if (!$this->isConsolidatedBill($consolidatedBill)) {
                return response()->json([
                    'success' => false,
                    'message' => '这不是一个累计账单'
                ], 404);
            }

            // 模拟操作历史数据
            $history = [
                [
                    'operation_type' => 'create',
                    'operation_title' => '创建累计账单',
                    'operation_desc' => sprintf(
                        '%s累计账单，包含%d个订单', 
                        $consolidatedBill->metadata['auto_generated'] ? '系统自动生成' : '手动创建',
                        $consolidatedBill->metadata['order_count'] ?? 0
                    ),
                    'operator_name' => $consolidatedBill->metadata['auto_generated'] ? '系统' : '管理员',
                    'created_at' => $consolidatedBill->created_at->toISOString(),
                ]
            ];

            // 添加支付记录
            foreach ($consolidatedBill->paymentRecords as $payment) {
                $history[] = [
                    'operation_type' => 'payment',
                    'operation_title' => $payment->payment_type === 'partial' ? '部分支付' : '支付账单',
                    'operation_desc' => sprintf(
                        '通过%s支付了¥%s', 
                        $this->getPaymentMethodLabel($payment->payment_method),
                        number_format($payment->payment_amount, 2)
                    ),
                    'operator_name' => $consolidatedBill->user->name ?? '用户',
                    'created_at' => $payment->payment_time ?? $payment->created_at,
                ];
            }

            // 添加调整记录
            foreach ($consolidatedBill->adjustments as $adjustment) {
                $history[] = [
                    'operation_type' => 'adjust',
                    'operation_title' => '调整账单',
                    'operation_desc' => sprintf(
                        '%s¥%s，原因：%s', 
                        $adjustment->adjustment_amount > 0 ? '增加' : '减少',
                        number_format(abs($adjustment->adjustment_amount), 2),
                        $adjustment->reason
                    ),
                    'operator_name' => '管理员',
                    'created_at' => $adjustment->applied_at ?? $adjustment->created_at,
                ];
            }

            // 按时间排序
            usort($history, function ($a, $b) {
                return strtotime($a['created_at']) - strtotime($b['created_at']);
            });

            return response()->json([
                'success' => true,
                'data' => $history
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取操作历史失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 拆分累计账单
     */
    public function splitBill(Request $request, Bill $consolidatedBill): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'split_type' => 'required|in:all,partial',
            'selected_orders' => 'required_if:split_type,partial|array',
            'selected_orders.*' => 'exists:orders,id',
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if (!$this->isConsolidatedBill($consolidatedBill)) {
                return response()->json([
                    'success' => false,
                    'message' => '这不是一个累计账单'
                ], 404);
            }

            // 检查账单状态
            if (in_array($consolidatedBill->status, ['cancelled', 'refunded'])) {
                return response()->json([
                    'success' => false,
                    'message' => '已取消或已退款的账单无法拆分'
                ], 400);
            }

            // 这里应该调用服务层方法进行拆分
            // $result = $this->consolidatedBillingService->splitConsolidatedBill(
            //     $consolidatedBill,
            //     $request->split_type,
            //     $request->selected_orders ?? [],
            //     $request->reason
            // );

            // 模拟拆分成功
            return response()->json([
                'success' => true,
                'message' => '累计账单拆分成功',
                'data' => [
                    'split_type' => $request->split_type,
                    'reason' => $request->reason,
                    'created_bills_count' => $request->split_type === 'all' 
                        ? ($consolidatedBill->metadata['order_count'] ?? 0)
                        : count($request->selected_orders ?? [])
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '拆分累计账单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取累计账单统计信息
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $query = Bill::where('bill_type', 'consolidated');

            $totalBills = $query->count();
            $totalAmount = $query->sum('final_amount');
            $monthlyNew = $query->whereMonth('created_at', now()->month)->count();
            
            // 获取订单总数
            $totalOrders = $query->get()->sum(function ($bill) {
                return $bill->metadata['order_count'] ?? 0;
            });

            $totalUsers = $query->distinct('user_id')->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_bills' => $totalBills,
                    'monthly_new' => $monthlyNew,
                    'total_amount' => $totalAmount,
                    'avg_amount' => $totalBills > 0 ? $totalAmount / $totalBills : 0,
                    'total_orders' => $totalOrders,
                    'avg_orders_per_bill' => $totalBills > 0 ? $totalOrders / $totalBills : 0,
                    'total_users' => $totalUsers,
                    'active_rate' => $totalUsers > 0 ? ($monthlyNew / $totalUsers) * 100 : 0,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计信息失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户可累计的订单列表
     */
    public function getConsolidatableOrders(Request $request): JsonResponse
    {
        try {
            // 移除用户验证要求，使API可以在未登录状态下使用
            // 如果有用户ID参数，则按用户ID查询
            $userId = $request->get('user_id');
            
            $query = \App\Order\Models\Order::query()
                ->where('status', 'pending')
                ->whereDoesntHave('bills')
                ->with('items.product', 'user');
                
            // 如果提供了用户ID，则按用户过滤
            if ($userId) {
                $query->where('user_id', $userId);
            }
            
            $orders = $query->orderBy('created_at', 'desc')->get();
            
            // 计算统计数据
            $totalAmount = $orders->sum(function ($order) {
                return $order->final_payment_amount ?? $order->total;
            });
            
            $statistics = [
                'order_count' => $orders->count(),
                'total_amount' => $totalAmount,
                'earliest_order_date' => $orders->min('created_at'),
                'latest_order_date' => $orders->max('created_at'),
            ];

            return response()->json([
                'success' => true,
                'data' => $orders->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'order_no' => $order->order_no,
                        'user_id' => $order->user_id,
                        'user' => [
                            'id' => $order->user->id ?? null,
                            'name' => $order->user->name ?? '未知用户',
                            'phone' => $order->user->phone ?? '',
                        ],
                        'total' => $order->final_payment_amount ?? $order->total,
                        'final_payment_amount' => $order->final_payment_amount ?? $order->total,
                        'status' => $order->status,
                        'created_at' => $order->created_at->toISOString(),
                        'items' => $order->items->map(function ($item) {
                            return [
                                'id' => $item->id,
                                'order_id' => $item->order_id,
                                'product_id' => $item->product_id,
                                'product_name' => $item->product->name ?? $item->product_name,
                                'product_specification' => $item->product_specification,
                                'quantity' => $item->quantity,
                                'price' => $item->price,
                                'total_price' => $item->total_price,
                            ];
                        })
                    ];
                }),
                'statistics' => $statistics,
                'can_consolidate' => $statistics['order_count'] >= 2,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取可累计订单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建累计账单
     */
    public function createConsolidatedBill(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_ids' => 'required|array|min:2',
            'order_ids.*' => 'exists:orders,id',
            'adjustment_amount' => 'nullable|numeric',
            'notes' => 'nullable|string|max:500',
            'due_date' => 'nullable|date|after:today',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $options = array_filter([
                'adjustment_amount' => $request->adjustment_amount,
                'notes' => $request->notes,
                'due_date' => $request->due_date ? now()->parse($request->due_date) : null,
                'created_by' => auth()->id(),
            ]);

            $consolidatedBill = $this->consolidatedBillingService->createConsolidatedBill(
                $user, 
                $request->order_ids, 
                $options
            );

            return response()->json([
                'success' => true,
                'message' => '累计账单创建成功',
                'data' => new BillResource($consolidatedBill->load(['items', 'childBills.order']))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建累计账单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 检查是否有可累计的订单
     */
    public function checkConsolidatable(Request $request): JsonResponse
    {
        $user = $request->user();
        $minOrderCount = $request->get('min_order_count', 2);
        
        $hasConsolidatable = $this->consolidatedBillingService->hasConsolidatableOrders($user, $minOrderCount);
        $statistics = $this->consolidatedBillingService->getConsolidationStatistics($user);

        return response()->json([
            'success' => true,
            'data' => [
                'has_consolidatable' => $hasConsolidatable,
                'statistics' => $statistics,
                'recommendation' => $this->getConsolidationRecommendation($statistics),
            ]
        ]);
    }

    /**
     * 自动创建周期性累计账单
     */
    public function createPeriodicBill(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => 'required|in:weekly,monthly,quarterly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $consolidatedBill = $this->consolidatedBillingService->createPeriodicConsolidatedBill(
                $user, 
                $request->period
            );

            if (!$consolidatedBill) {
                return response()->json([
                    'success' => false,
                    'message' => '当前周期内没有可累计的订单'
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => '周期性累计账单创建成功',
                'data' => new BillResource($consolidatedBill->load(['items', 'childBills.order']))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建周期性累计账单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 判断是否为累计账单
     */
    protected function isConsolidatedBill(Bill $bill): bool
    {
        return $bill->bill_type === 'consolidated';
    }

    /**
     * 获取支付方式标签
     */
    protected function getPaymentMethodLabel(string $method): string
    {
        $labels = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            'bank_transfer' => '银行转账',
            'balance' => '余额支付',
            'other' => '其他',
        ];

        return $labels[$method] ?? $method;
    }

    /**
     * 获取累计建议
     */
    protected function getConsolidationRecommendation(array $statistics): array
    {
        $recommendation = [
            'should_consolidate' => false,
            'reason' => '',
            'benefits' => [],
        ];

        if ($statistics['order_count'] < 2) {
            $recommendation['reason'] = '订单数量不足，无法累计';
            return $recommendation;
        }

        $recommendation['should_consolidate'] = true;
        $recommendation['reason'] = "您有 {$statistics['order_count']} 个订单可以累计";

        // 添加好处说明（不包含批量优惠）
        $recommendation['benefits'][] = '简化支付流程，一次性支付多个订单';
        $recommendation['benefits'][] = '更好的资金管理和账目清晰';
        $recommendation['benefits'][] = '减少支付次数，提高效率';

        if ($statistics['order_count'] >= 5) {
            $recommendation['benefits'][] = '大批量订单，建议优先累计';
        }

        return $recommendation;
    }
} 