<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\Order;
use App\FlyCloud\Services\FlyCloudService;

class TestAutoPrint extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'flycloud:test-auto-print {order_id? : 订单ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试订单自动分单打印功能';

    protected FlyCloudService $flyCloudService;

    public function __construct(FlyCloudService $flyCloudService)
    {
        parent::__construct();
        $this->flyCloudService = $flyCloudService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $orderId = $this->argument('order_id');

        if ($orderId) {
            $this->testSpecificOrder($orderId);
        } else {
            $this->testRecentOrder();
        }
    }

    protected function testSpecificOrder($orderId)
    {
        $this->info("测试订单 #{$orderId} 的自动分单打印...");

        try {
            $order = Order::with(['items.product'])->findOrFail($orderId);
            
            $this->info("订单信息:");
            $this->info("  订单号: {$order->order_no}");
            $this->info("  状态: {$order->status}");
            $this->info("  商品数: " . $order->items->count());
            $this->info("  总金额: ¥{$order->total}");

            // 获取订单涉及的仓库
            $warehouseIds = $this->flyCloudService->getOrderWarehouses($order);
            
            if (empty($warehouseIds)) {
                $this->warn("该订单没有分配仓库，无法执行分单打印");
                return;
            }

            $this->info("涉及仓库: " . implode(', ', $warehouseIds));

            // 检查每个仓库的打印机绑定
            foreach ($warehouseIds as $warehouseId) {
                $this->info("\n检查仓库 {$warehouseId}:");
                
                $bindings = $this->flyCloudService->getWarehousePrinterBindings($warehouseId);
                
                if (empty($bindings)) {
                    $this->warn("  ❌ 仓库 {$warehouseId} 没有绑定打印机");
                } else {
                    foreach ($bindings as $binding) {
                        $status = $binding['printer']['status'] ?? 'unknown';
                        $statusIcon = $status === 'online' ? '✅' : '❌';
                        $this->info("  {$statusIcon} 打印机: {$binding['printer']['name']} ({$binding['printer']['sn']}) - {$status}");
                    }
                }
            }

            // 执行分单打印测试
            $this->info("\n执行分单打印测试...");
            
            $results = $this->flyCloudService->printOrderByWarehouses($order, [
                'print_type' => 'order',
                'copies' => 1,
                'test_mode' => true
            ]);

            $this->info("\n打印结果:");
            foreach ($results as $warehouseId => $result) {
                $icon = ($result['success'] ?? false) ? '✅' : '❌';
                $message = $result['message'] ?? '未知';
                $this->info("  {$icon} 仓库 {$warehouseId}: {$message}");
            }

        } catch (\Exception $e) {
            $this->error("测试失败: " . $e->getMessage());
        }
    }

    protected function testRecentOrder()
    {
        $this->info("测试最近的订单...");

        $order = Order::with(['items.product'])
            ->whereNotNull('order_no')
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$order) {
            $this->warn("没有找到可测试的订单");
            return;
        }

        $this->info("使用最近订单: #{$order->id}");
        $this->testSpecificOrder($order->id);
    }
} 