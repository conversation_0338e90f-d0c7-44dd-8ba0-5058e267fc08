<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\Billing\Models\Bill;

class OptimizeCodeLogic extends Command
{
    protected $signature = 'optimize:code-logic {--test : 运行测试模式}';
    protected $description = '优化代码逻辑和查询性能';

    public function handle()
    {
        $this->info('🚀 开始代码逻辑优化...');
        $this->newLine();

        if ($this->option('test')) {
            $this->runPerformanceTests();
        } else {
            $this->runOptimizations();
        }

        $this->newLine();
        $this->info('✅ 代码逻辑优化完成！');
    }

    private function runPerformanceTests()
    {
        $this->info('📊 运行性能测试...');
        
        // 1. 测试订单查询性能
        $this->testOrderQueries();
        
        // 2. 测试订单更正查询性能
        $this->testCorrectionQueries();
        
        // 3. 测试账单查询性能
        $this->testBillQueries();
        
        // 4. 测试关联查询性能
        $this->testJoinQueries();
    }

    private function testOrderQueries()
    {
        $this->info('🔍 测试订单查询性能...');
        
        $queries = [
            '按状态查询订单' => function() {
                return Order::where('status', 'delivered')->count();
            },
            '按支付方式查询订单' => function() {
                return Order::where('payment_method', 'wechat')->count();
            },
            '按区域+状态查询订单' => function() {
                return Order::where('region_id', 1)->where('status', 'paid')->count();
            },
            '按COD状态查询订单' => function() {
                return Order::where('is_cod', 1)->where('cod_status', 'paid')->count();
            }
        ];

        foreach ($queries as $description => $query) {
            $startTime = microtime(true);
            $result = $query();
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            $status = $executionTime < 10 ? '✅' : ($executionTime < 50 ? '⚠️' : '❌');
            $this->line("  {$status} {$description}: {$executionTime}ms (结果: {$result})");
        }
    }

    private function testCorrectionQueries()
    {
        $this->info('🔍 测试订单更正查询性能...');
        
        $queries = [
            '按状态查询更正' => function() {
                return OrderCorrection::where('status', 'confirmed')->count();
            },
            '按状态+时间查询更正' => function() {
                return OrderCorrection::where('status', 'pending')
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->count();
            },
            '按确认时间查询更正' => function() {
                return OrderCorrection::whereNotNull('confirmed_at')
                    ->orderBy('confirmed_at', 'desc')
                    ->limit(10)
                    ->count();
            }
        ];

        foreach ($queries as $description => $query) {
            $startTime = microtime(true);
            $result = $query();
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            $status = $executionTime < 10 ? '✅' : ($executionTime < 50 ? '⚠️' : '❌');
            $this->line("  {$status} {$description}: {$executionTime}ms (结果: {$result})");
        }
    }

    private function testBillQueries()
    {
        $this->info('🔍 测试账单查询性能...');
        
        $queries = [
            '按用户+支付状态查询账单' => function() {
                return Bill::where('user_id', 1)->where('payment_status', 'unpaid')->count();
            },
            '按订单+状态查询账单' => function() {
                return Bill::whereNotNull('order_id')->where('status', 'pending')->count();
            },
            '按创建时间查询账单' => function() {
                return Bill::orderBy('created_at', 'desc')->limit(10)->count();
            }
        ];

        foreach ($queries as $description => $query) {
            $startTime = microtime(true);
            $result = $query();
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            $status = $executionTime < 10 ? '✅' : ($executionTime < 50 ? '⚠️' : '❌');
            $this->line("  {$status} {$description}: {$executionTime}ms (结果: {$result})");
        }
    }

    private function testJoinQueries()
    {
        $this->info('🔍 测试关联查询性能...');
        
        $queries = [
            '订单-账单关联查询' => function() {
                return DB::table('orders')
                    ->join('bills', 'orders.id', '=', 'bills.order_id')
                    ->count();
            },
            '订单-更正关联查询' => function() {
                return DB::table('orders')
                    ->join('order_corrections', 'orders.id', '=', 'order_corrections.order_id')
                    ->count();
            },
            '账单-支付记录关联查询' => function() {
                return DB::table('bills')
                    ->join('payment_records', 'bills.id', '=', 'payment_records.bill_id')
                    ->count();
            }
        ];

        foreach ($queries as $description => $query) {
            $startTime = microtime(true);
            $result = $query();
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            $status = $executionTime < 20 ? '✅' : ($executionTime < 100 ? '⚠️' : '❌');
            $this->line("  {$status} {$description}: {$executionTime}ms (结果: {$result})");
        }
    }

    private function runOptimizations()
    {
        $this->info('🔧 运行代码逻辑优化...');
        
        // 1. 清理缓存
        $this->optimizeCache();
        
        // 2. 优化查询
        $this->optimizeQueries();
        
        // 3. 检查索引使用情况
        $this->checkIndexUsage();
        
        // 4. 生成优化建议
        $this->generateOptimizationSuggestions();
    }

    private function optimizeCache()
    {
        $this->info('🗑️ 清理缓存...');
        
        try {
            Cache::flush();
            $this->line('  ✅ 缓存已清理');
        } catch (\Exception $e) {
            $this->line('  ❌ 缓存清理失败: ' . $e->getMessage());
        }
    }

    private function optimizeQueries()
    {
        $this->info('🔍 检查查询优化机会...');
        
        // 检查是否有慢查询
        $this->checkSlowQueries();
        
        // 检查索引使用情况
        $this->checkIndexEffectiveness();
    }

    private function checkSlowQueries()
    {
        $this->line('  📊 检查查询性能...');
        
        $testQueries = [
            "SELECT COUNT(*) FROM orders WHERE status = 'delivered'",
            "SELECT COUNT(*) FROM bills WHERE payment_status = 'unpaid'",
            "SELECT COUNT(*) FROM order_corrections WHERE status = 'pending'",
        ];
        
        foreach ($testQueries as $query) {
            $startTime = microtime(true);
            DB::select($query);
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            if ($executionTime > 50) {
                $this->line("  ⚠️ 慢查询发现: {$executionTime}ms");
                $this->line("     SQL: " . substr($query, 0, 50) . "...");
            }
        }
    }

    private function checkIndexUsage()
    {
        $this->info('📈 检查索引使用情况...');
        
        $indexes = [
            'orders' => ['idx_orders_region_status', 'idx_orders_cod_status'],
            'bills' => ['idx_bills_order_status', 'idx_bills_created_at'],
            'order_corrections' => ['idx_corrections_status_created'],
        ];
        
        foreach ($indexes as $table => $indexList) {
            $this->line("  📋 {$table}表索引:");
            foreach ($indexList as $index) {
                $exists = DB::select("
                    SELECT COUNT(*) as count
                    FROM INFORMATION_SCHEMA.STATISTICS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = ? 
                    AND INDEX_NAME = ?
                ", [$table, $index]);
                
                if ($exists[0]->count > 0) {
                    $this->line("    ✅ {$index}");
                } else {
                    $this->line("    ❌ {$index} (缺失)");
                }
            }
        }
    }

    private function checkIndexEffectiveness()
    {
        $this->line('  🎯 检查索引有效性...');
        
        // 使用EXPLAIN检查关键查询的索引使用情况
        $keyQueries = [
            "SELECT * FROM orders WHERE region_id = 1 AND status = 'paid'",
            "SELECT * FROM bills WHERE user_id = 1 AND payment_status = 'unpaid'",
            "SELECT * FROM order_corrections WHERE status = 'pending' ORDER BY created_at DESC",
        ];
        
        foreach ($keyQueries as $query) {
            try {
                $explain = DB::select("EXPLAIN " . $query);
                $usingIndex = !empty($explain) && isset($explain[0]->key) && $explain[0]->key !== null;
                
                if ($usingIndex) {
                    $this->line("    ✅ 查询使用索引: " . $explain[0]->key);
                } else {
                    $this->line("    ⚠️ 查询未使用索引");
                }
            } catch (\Exception $e) {
                $this->line("    ❌ 查询分析失败");
            }
        }
    }

    private function generateOptimizationSuggestions()
    {
        $this->info('💡 生成优化建议...');
        
        $suggestions = [
            '考虑为热点数据添加Redis缓存',
            '定期分析慢查询日志',
            '考虑对大表进行分区',
            '定期更新表统计信息',
            '监控索引使用情况',
        ];
        
        foreach ($suggestions as $suggestion) {
            $this->line("  💡 {$suggestion}");
        }
    }
}
