<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Region\Models\Region;
use Illuminate\Support\Facades\DB;

class ImportSichuanRegions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'region:import-sichuan {--clear : 清空现有数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导入四川省完整行政区域数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始导入四川省行政区域数据...');

        // 如果指定了清空选项，先清空现有数据
        if ($this->option('clear')) {
            $this->warn('正在清空现有区域数据...');
            
            try {
                // 禁用外键约束检查
                DB::statement('SET FOREIGN_KEY_CHECKS=0;');
                
                // 清空相关表数据
                DB::table('region_prices')->delete();
                DB::table('regions')->delete();
                
                // 重新启用外键约束检查
                DB::statement('SET FOREIGN_KEY_CHECKS=1;');
                
                $this->info('现有数据已清空');
            } catch (\Exception $e) {
                // 确保重新启用外键约束检查
                DB::statement('SET FOREIGN_KEY_CHECKS=1;');
                $this->error('清空数据失败: ' . $e->getMessage());
                return 1;
            }
        }

        DB::beginTransaction();
        
        try {
            // 导入省级数据
            $sichuanProvince = $this->createRegion([
                'name' => '四川省',
                'code' => 'sichuan',
                'level' => 1,
                'parent_id' => 0,
                'sort' => 1,
                'status' => true
            ]);

            $this->info("已创建省级: {$sichuanProvince->name}");

            // 导入市级数据
            $cities = $this->getCitiesData();
            $cityModels = [];
            
            foreach ($cities as $cityData) {
                $city = $this->createRegion([
                    'name' => $cityData['name'],
                    'code' => $cityData['code'],
                    'level' => 2,
                    'parent_id' => $sichuanProvince->id,
                    'sort' => $cityData['sort'],
                    'status' => true
                ]);
                
                $cityModels[$cityData['code']] = $city;
                $this->info("已创建市级: {$city->name}");
            }

            // 导入区县级数据
            $counties = $this->getCountiesData();
            $countyModels = [];
            
            foreach ($counties as $countyData) {
                $parentCity = $cityModels[$countyData['parent_code']] ?? null;
                if (!$parentCity) {
                    $this->warn("找不到父级城市: {$countyData['parent_code']}，跳过 {$countyData['name']}");
                    continue;
                }

                $county = $this->createRegion([
                    'name' => $countyData['name'],
                    'code' => $countyData['code'],
                    'level' => 3,
                    'parent_id' => $parentCity->id,
                    'sort' => $countyData['sort'],
                    'status' => true
                ]);
                
                $countyModels[$countyData['code']] = $county;
                $this->info("已创建区县: {$county->name}");
            }

            // 导入街道/乡镇级数据（示例数据）
            $streets = $this->getStreetsData();
            
            foreach ($streets as $streetData) {
                $parentCounty = $countyModels[$streetData['parent_code']] ?? null;
                if (!$parentCounty) {
                    $this->warn("找不到父级区县: {$streetData['parent_code']}，跳过 {$streetData['name']}");
                    continue;
                }

                $street = $this->createRegion([
                    'name' => $streetData['name'],
                    'code' => $streetData['code'],
                    'level' => 4,
                    'parent_id' => $parentCounty->id,
                    'sort' => $streetData['sort'],
                    'status' => true
                ]);
                
                $this->info("已创建街道/乡镇: {$street->name}");
            }

            DB::commit();
            
            $totalCount = Region::count();
            $this->info("✅ 四川省行政区域数据导入完成！");
            $this->info("📊 统计信息:");
            $this->info("   - 省级: " . Region::where('level', 1)->count() . " 个");
            $this->info("   - 市级: " . Region::where('level', 2)->count() . " 个");
            $this->info("   - 区县级: " . Region::where('level', 3)->count() . " 个");
            $this->info("   - 街道/乡镇级: " . Region::where('level', 4)->count() . " 个");
            $this->info("   - 总计: {$totalCount} 个区域");

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("导入失败: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * 创建区域记录
     */
    private function createRegion(array $data): Region
    {
        return Region::create($data);
    }

    /**
     * 获取四川省市级数据
     */
    private function getCitiesData(): array
    {
        return [
            ['name' => '成都市', 'code' => 'chengdu', 'sort' => 1],
            ['name' => '自贡市', 'code' => 'zigong', 'sort' => 2],
            ['name' => '攀枝花市', 'code' => 'panzhihua', 'sort' => 3],
            ['name' => '泸州市', 'code' => 'luzhou', 'sort' => 4],
            ['name' => '德阳市', 'code' => 'deyang', 'sort' => 5],
            ['name' => '绵阳市', 'code' => 'mianyang', 'sort' => 6],
            ['name' => '广元市', 'code' => 'guangyuan', 'sort' => 7],
            ['name' => '遂宁市', 'code' => 'suining', 'sort' => 8],
            ['name' => '内江市', 'code' => 'neijiang', 'sort' => 9],
            ['name' => '乐山市', 'code' => 'leshan', 'sort' => 10],
            ['name' => '南充市', 'code' => 'nanchong', 'sort' => 11],
            ['name' => '眉山市', 'code' => 'meishan', 'sort' => 12],
            ['name' => '宜宾市', 'code' => 'yibin', 'sort' => 13],
            ['name' => '广安市', 'code' => 'guangan', 'sort' => 14],
            ['name' => '达州市', 'code' => 'dazhou', 'sort' => 15],
            ['name' => '雅安市', 'code' => 'yaan', 'sort' => 16],
            ['name' => '巴中市', 'code' => 'bazhong', 'sort' => 17],
            ['name' => '资阳市', 'code' => 'ziyang', 'sort' => 18],
            ['name' => '阿坝藏族羌族自治州', 'code' => 'aba', 'sort' => 19],
            ['name' => '甘孜藏族自治州', 'code' => 'ganzi', 'sort' => 20],
            ['name' => '凉山彝族自治州', 'code' => 'liangshan', 'sort' => 21],
        ];
    }

    /**
     * 获取区县级数据
     */
    private function getCountiesData(): array
    {
        return [
            // 成都市下属区县
            ['name' => '锦江区', 'code' => 'jinjiang', 'parent_code' => 'chengdu', 'sort' => 1],
            ['name' => '青羊区', 'code' => 'qingyang', 'parent_code' => 'chengdu', 'sort' => 2],
            ['name' => '金牛区', 'code' => 'jinniu', 'parent_code' => 'chengdu', 'sort' => 3],
            ['name' => '武侯区', 'code' => 'wuhou', 'parent_code' => 'chengdu', 'sort' => 4],
            ['name' => '成华区', 'code' => 'chenghua', 'parent_code' => 'chengdu', 'sort' => 5],
            ['name' => '龙泉驿区', 'code' => 'longquanyi', 'parent_code' => 'chengdu', 'sort' => 6],
            ['name' => '青白江区', 'code' => 'qingbaijiang', 'parent_code' => 'chengdu', 'sort' => 7],
            ['name' => '新都区', 'code' => 'xindu', 'parent_code' => 'chengdu', 'sort' => 8],
            ['name' => '温江区', 'code' => 'wenjiang', 'parent_code' => 'chengdu', 'sort' => 9],
            ['name' => '双流区', 'code' => 'shuangliu', 'parent_code' => 'chengdu', 'sort' => 10],
            ['name' => '郫都区', 'code' => 'pidu', 'parent_code' => 'chengdu', 'sort' => 11],
            ['name' => '新津区', 'code' => 'xinjin', 'parent_code' => 'chengdu', 'sort' => 12],
            ['name' => '都江堰市', 'code' => 'dujiangyan', 'parent_code' => 'chengdu', 'sort' => 13],
            ['name' => '彭州市', 'code' => 'pengzhou', 'parent_code' => 'chengdu', 'sort' => 14],
            ['name' => '邛崃市', 'code' => 'qionglai', 'parent_code' => 'chengdu', 'sort' => 15],
            ['name' => '崇州市', 'code' => 'chongzhou', 'parent_code' => 'chengdu', 'sort' => 16],
            ['name' => '简阳市', 'code' => 'jianyang', 'parent_code' => 'chengdu', 'sort' => 17],
            ['name' => '金堂县', 'code' => 'jintang', 'parent_code' => 'chengdu', 'sort' => 18],
            ['name' => '大邑县', 'code' => 'dayi', 'parent_code' => 'chengdu', 'sort' => 19],
            ['name' => '蒲江县', 'code' => 'pujiang', 'parent_code' => 'chengdu', 'sort' => 20],

            // 绵阳市下属区县
            ['name' => '涪城区', 'code' => 'fucheng', 'parent_code' => 'mianyang', 'sort' => 1],
            ['name' => '游仙区', 'code' => 'youxian', 'parent_code' => 'mianyang', 'sort' => 2],
            ['name' => '安州区', 'code' => 'anzhou', 'parent_code' => 'mianyang', 'sort' => 3],
            ['name' => '江油市', 'code' => 'jiangyou', 'parent_code' => 'mianyang', 'sort' => 4],
            ['name' => '三台县', 'code' => 'santai', 'parent_code' => 'mianyang', 'sort' => 5],
            ['name' => '盐亭县', 'code' => 'yanting', 'parent_code' => 'mianyang', 'sort' => 6],
            ['name' => '梓潼县', 'code' => 'zitong', 'parent_code' => 'mianyang', 'sort' => 7],
            ['name' => '北川羌族自治县', 'code' => 'beichuan', 'parent_code' => 'mianyang', 'sort' => 8],
            ['name' => '平武县', 'code' => 'pingwu', 'parent_code' => 'mianyang', 'sort' => 9],

            // 德阳市下属区县
            ['name' => '旌阳区', 'code' => 'jingyang', 'parent_code' => 'deyang', 'sort' => 1],
            ['name' => '罗江区', 'code' => 'luojiang', 'parent_code' => 'deyang', 'sort' => 2],
            ['name' => '广汉市', 'code' => 'guanghan', 'parent_code' => 'deyang', 'sort' => 3],
            ['name' => '什邡市', 'code' => 'shifang', 'parent_code' => 'deyang', 'sort' => 4],
            ['name' => '绵竹市', 'code' => 'mianzhu', 'parent_code' => 'deyang', 'sort' => 5],
            ['name' => '中江县', 'code' => 'zhongjiang', 'parent_code' => 'deyang', 'sort' => 6],

            // 泸州市下属区县
            ['name' => '江阳区', 'code' => 'jiangyang', 'parent_code' => 'luzhou', 'sort' => 1],
            ['name' => '纳溪区', 'code' => 'naxi', 'parent_code' => 'luzhou', 'sort' => 2],
            ['name' => '龙马潭区', 'code' => 'longmatan', 'parent_code' => 'luzhou', 'sort' => 3],
            ['name' => '泸县', 'code' => 'luxian', 'parent_code' => 'luzhou', 'sort' => 4],
            ['name' => '合江县', 'code' => 'hejiang', 'parent_code' => 'luzhou', 'sort' => 5],
            ['name' => '叙永县', 'code' => 'xuyong', 'parent_code' => 'luzhou', 'sort' => 6],
            ['name' => '古蔺县', 'code' => 'gulin', 'parent_code' => 'luzhou', 'sort' => 7],

            // 其他主要城市的部分区县（示例）
            ['name' => '自流井区', 'code' => 'ziliujing', 'parent_code' => 'zigong', 'sort' => 1],
            ['name' => '贡井区', 'code' => 'gongjing', 'parent_code' => 'zigong', 'sort' => 2],
            ['name' => '大安区', 'code' => 'daan', 'parent_code' => 'zigong', 'sort' => 3],
            ['name' => '沿滩区', 'code' => 'yantan', 'parent_code' => 'zigong', 'sort' => 4],
            ['name' => '荣县', 'code' => 'rongxian', 'parent_code' => 'zigong', 'sort' => 5],
            ['name' => '富顺县', 'code' => 'fushun', 'parent_code' => 'zigong', 'sort' => 6],

            ['name' => '东区', 'code' => 'dongqu', 'parent_code' => 'panzhihua', 'sort' => 1],
            ['name' => '西区', 'code' => 'xiqu', 'parent_code' => 'panzhihua', 'sort' => 2],
            ['name' => '仁和区', 'code' => 'renhe', 'parent_code' => 'panzhihua', 'sort' => 3],
            ['name' => '米易县', 'code' => 'miyi', 'parent_code' => 'panzhihua', 'sort' => 4],
            ['name' => '盐边县', 'code' => 'yanbian', 'parent_code' => 'panzhihua', 'sort' => 5],
        ];
    }

    /**
     * 获取街道/乡镇级数据（示例数据）
     */
    private function getStreetsData(): array
    {
        return [
            // 成都市锦江区下属街道
            ['name' => '督院街街道', 'code' => 'duyuanjie', 'parent_code' => 'jinjiang', 'sort' => 1],
            ['name' => '盐市口街道', 'code' => 'yanshikou', 'parent_code' => 'jinjiang', 'sort' => 2],
            ['name' => '春熙路街道', 'code' => 'chunxilu', 'parent_code' => 'jinjiang', 'sort' => 3],
            ['name' => '书院街街道', 'code' => 'shuyuanjie', 'parent_code' => 'jinjiang', 'sort' => 4],
            ['name' => '合江亭街道', 'code' => 'hejiangting', 'parent_code' => 'jinjiang', 'sort' => 5],
            ['name' => '水井坊街道', 'code' => 'shuijingfang', 'parent_code' => 'jinjiang', 'sort' => 6],
            ['name' => '牛市口街道', 'code' => 'niushikou', 'parent_code' => 'jinjiang', 'sort' => 7],
            ['name' => '龙舟路街道', 'code' => 'longzhoulu', 'parent_code' => 'jinjiang', 'sort' => 8],
            ['name' => '双桂路街道', 'code' => 'shuangguilu', 'parent_code' => 'jinjiang', 'sort' => 9],
            ['name' => '莲新街道', 'code' => 'lianxin', 'parent_code' => 'jinjiang', 'sort' => 10],
            ['name' => '沙河街道', 'code' => 'shahe', 'parent_code' => 'jinjiang', 'sort' => 11],
            ['name' => '东光街道', 'code' => 'dongguang', 'parent_code' => 'jinjiang', 'sort' => 12],
            ['name' => '狮子山街道', 'code' => 'shizishan', 'parent_code' => 'jinjiang', 'sort' => 13],
            ['name' => '成龙路街道', 'code' => 'chenglonglu', 'parent_code' => 'jinjiang', 'sort' => 14],
            ['name' => '柳江街道', 'code' => 'liujiang', 'parent_code' => 'jinjiang', 'sort' => 15],
            ['name' => '三圣街道', 'code' => 'sansheng', 'parent_code' => 'jinjiang', 'sort' => 16],
            ['name' => '琉璃场街道', 'code' => 'liulichang', 'parent_code' => 'jinjiang', 'sort' => 17],

            // 成都市青羊区下属街道
            ['name' => '太升路街道', 'code' => 'taishenglu', 'parent_code' => 'qingyang', 'sort' => 1],
            ['name' => '草市街街道', 'code' => 'caoshijie', 'parent_code' => 'qingyang', 'sort' => 2],
            ['name' => '西御河街道', 'code' => 'xiyuhe', 'parent_code' => 'qingyang', 'sort' => 3],
            ['name' => '汪家拐街道', 'code' => 'wangjiaguai', 'parent_code' => 'qingyang', 'sort' => 4],
            ['name' => '少城街道', 'code' => 'shaocheng', 'parent_code' => 'qingyang', 'sort' => 5],
            ['name' => '新华西路街道', 'code' => 'xinhuaxilu', 'parent_code' => 'qingyang', 'sort' => 6],
            ['name' => '草堂路街道', 'code' => 'caotanglu', 'parent_code' => 'qingyang', 'sort' => 7],
            ['name' => '府南街道', 'code' => 'funan', 'parent_code' => 'qingyang', 'sort' => 8],
            ['name' => '光华街道', 'code' => 'guanghua', 'parent_code' => 'qingyang', 'sort' => 9],
            ['name' => '东坡街道', 'code' => 'dongpo', 'parent_code' => 'qingyang', 'sort' => 10],
            ['name' => '金沙街道', 'code' => 'jinsha', 'parent_code' => 'qingyang', 'sort' => 11],
            ['name' => '黄田坝街道', 'code' => 'huangtianba', 'parent_code' => 'qingyang', 'sort' => 12],
            ['name' => '苏坡街道', 'code' => 'supo', 'parent_code' => 'qingyang', 'sort' => 13],
            ['name' => '文家街道', 'code' => 'wenjia', 'parent_code' => 'qingyang', 'sort' => 14],

            // 成都市金牛区下属街道
            ['name' => '西安路街道', 'code' => 'xianlu', 'parent_code' => 'jinniu', 'sort' => 1],
            ['name' => '西华街道', 'code' => 'xihua', 'parent_code' => 'jinniu', 'sort' => 2],
            ['name' => '人民北路街道', 'code' => 'renminbeilu', 'parent_code' => 'jinniu', 'sort' => 3],
            ['name' => '荷花池街道', 'code' => 'hehuachi', 'parent_code' => 'jinniu', 'sort' => 4],
            ['name' => '驷马桥街道', 'code' => 'simaqiao', 'parent_code' => 'jinniu', 'sort' => 5],
            ['name' => '茶店子街道', 'code' => 'chadianzi', 'parent_code' => 'jinniu', 'sort' => 6],
            ['name' => '抚琴街道', 'code' => 'fuqin', 'parent_code' => 'jinniu', 'sort' => 7],
            ['name' => '九里堤街道', 'code' => 'jiulidi', 'parent_code' => 'jinniu', 'sort' => 8],
            ['name' => '五块石街道', 'code' => 'wukuaishi', 'parent_code' => 'jinniu', 'sort' => 9],
            ['name' => '黄忠街道', 'code' => 'huangzhong', 'parent_code' => 'jinniu', 'sort' => 10],
            ['name' => '营门口街道', 'code' => 'yingmenkou', 'parent_code' => 'jinniu', 'sort' => 11],
            ['name' => '金泉街道', 'code' => 'jinquan', 'parent_code' => 'jinniu', 'sort' => 12],
            ['name' => '沙河源街道', 'code' => 'shaheyuan', 'parent_code' => 'jinniu', 'sort' => 13],
            ['name' => '天回镇街道', 'code' => 'tianhuizhen', 'parent_code' => 'jinniu', 'sort' => 14],
            ['name' => '凤凰山街道', 'code' => 'fenghuangshan', 'parent_code' => 'jinniu', 'sort' => 15],
        ];
    }
} 