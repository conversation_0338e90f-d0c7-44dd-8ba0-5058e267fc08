<?php

namespace App\Billing\Services;

use App\Billing\Models\Bill;
use App\Billing\Models\BillItem;
use App\Billing\Models\BillAdjustment;
use App\Billing\Models\PaymentRecord;
use App\Billing\Models\BalanceTransaction;
use App\Models\User;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class BillingService
{
    /**
     * 从送达的订单创建账单（用于货到付款等场景）
     */
    public function createBillFromOrder(Order $order, array $options = []): Bill
    {
        return DB::transaction(function () use ($order, $options) {
            // 检查是否已存在账单
            $existingBill = Bill::where('order_id', $order->id)->first();
            if ($existingBill) {
                throw new Exception('该订单已存在账单，账单号：' . $existingBill->bill_no);
            }

            // 计算账单金额
            $orderAmount = $order->total;

            $bill = Bill::create([
                'bill_no' => Bill::generateBillNo(),
                'bill_type' => Bill::TYPE_ORDER,
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'original_amount' => $orderAmount,
                'adjustment_amount' => $options['adjustment_amount'] ?? 0,
                'final_amount' => $orderAmount + ($options['adjustment_amount'] ?? 0),
                'pending_amount' => $orderAmount + ($options['adjustment_amount'] ?? 0),
                'due_date' => $options['due_date'] ?? now()->addDays(7),
                'status' => Bill::STATUS_PENDING,
                'payment_status' => Bill::PAYMENT_STATUS_UNPAID,
                'created_by' => $options['created_by'] ?? null,
                'notes' => $options['notes'] ?? "订单送达时自动创建账单",
                'metadata' => array_merge([
                    'order_delivery_time' => now()->toISOString(),
                    'order_type' => $order->payment_method === 'cod' ? 'cod' : 'normal',
                    'auto_created_on_delivery' => true,
                ], $options['metadata'] ?? []),
            ]);

            // 创建账单明细
            $this->createBillItemsFromOrder($order, $bill);

            Log::info('从订单创建账单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'amount' => $bill->final_amount,
                'payment_method' => $order->payment_method
            ]);

            return $bill;
        });
    }

    /**
     * 从订单创建账单明细
     */
    protected function createBillItemsFromOrder(Order $order, Bill $bill): void
    {
        foreach ($order->items as $orderItem) {
            // 获取单位信息
            $unitValue = '件'; // 默认单位
            if ($orderItem->unit_id && $orderItem->unit) {
                $unitValue = $orderItem->unit->name ?? '件';
            }

            // 计算明细总价
            $itemTotalPrice = $orderItem->price * $orderItem->quantity;

            BillItem::create([
                'bill_id' => $bill->id,
                'product_id' => $orderItem->product_id,
                'item_name' => $orderItem->product_name,
                'item_type' => BillItem::TYPE_PRODUCT,
                'item_description' => $orderItem->product_specification ?? '',
                'quantity' => $orderItem->quantity,
                'unit' => $unitValue,
                'unit_price' => $orderItem->price,
                'total_price' => $itemTotalPrice,
                'metadata' => json_encode([
                    'order_item_id' => $orderItem->id,
                    'product_id' => $orderItem->product_id,
                    'unit_id' => $orderItem->unit_id,
                ])
            ]);
        }
    }

    /**
     * 从确认的订单创建账单（只有更正确认后的订单才创建正式账单）
     */
    public function createBillFromConfirmedOrder(Order $order, array $options = []): Bill
    {
        // 验证订单是否已确认（更正确认后）
        if (!$this->isOrderConfirmed($order)) {
            throw new Exception('只有确认后的订单才能创建正式账单');
        }

        return DB::transaction(function () use ($order, $options) {
            // 🔥 修复：对于微信支付订单更正，需要区分原始金额和更正后金额
            $isWechatCorrection = $order->payment_method === 'wechat' &&
                                 $order->correction_status === 'confirmed' &&
                                 $order->latest_correction_id;

            if ($isWechatCorrection) {
                // 微信支付订单更正：使用原始金额作为账单基础
                $latestCorrection = \App\Order\Models\OrderCorrection::find($order->latest_correction_id);
                $originalAmount = $latestCorrection ? $latestCorrection->original_total : $order->total;
                $finalAmount = $this->calculateBillAmountForOrder($order); // 更正后金额
            } else {
                // 其他情况：使用当前订单金额
                $originalAmount = $this->calculateBillAmountForOrder($order);
                $finalAmount = $originalAmount;
            }

            // 检查是否已存在账单
            $existingBill = Bill::where('order_id', $order->id)->first();
            if ($existingBill) {
                throw new Exception('该订单已存在账单，账单号：' . $existingBill->bill_no);
            }

            $bill = Bill::create([
                'bill_no' => Bill::generateBillNo(),
                'bill_type' => Bill::TYPE_ORDER,
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'original_amount' => $originalAmount, // 🔥 修复：使用正确的原始金额
                'adjustment_amount' => $options['adjustment_amount'] ?? 0,
                'final_amount' => $finalAmount + ($options['adjustment_amount'] ?? 0), // 🔥 修复：使用更正后金额
                'pending_amount' => $finalAmount + ($options['adjustment_amount'] ?? 0), // 🔥 修复：初始待付金额
                'due_date' => $options['due_date'] ?? now()->addDays(7),
                'status' => Bill::STATUS_PENDING,
                'payment_status' => Bill::PAYMENT_STATUS_UNPAID,
                'created_by' => $options['created_by'] ?? null,
                'notes' => $options['notes'] ?? "订单确认后自动创建账单",
                'metadata' => array_merge([
                    'order_confirmation_time' => now()->toISOString(),
                    'correction_confirmed' => $order->correction_status === 'confirmed',
                    'order_type' => $order->isCashOnDelivery() ? 'cod' : 'normal',
                    'amount_calculation_method' => $this->getBillAmountCalculationMethod($order),
                ], $options['metadata'] ?? []),
            ]);

            // 创建账单明细（使用更正后的数据）
            foreach ($order->items as $orderItem) {
                // 计算总价：如果订单项的total_price为空，则使用数量*单价
                $itemTotalPrice = $orderItem->total_price ?? ($orderItem->quantity * $orderItem->price);
                
                // 处理单位字段：如果是对象则提取display_name，否则使用原值
                $unitValue = '件'; // 默认值
                if ($orderItem->unit) {
                    if (is_string($orderItem->unit)) {
                        $unitValue = $orderItem->unit;
                    } elseif (is_array($orderItem->unit) && isset($orderItem->unit['display_name'])) {
                        $unitValue = $orderItem->unit['display_name'];
                    } elseif (is_object($orderItem->unit) && isset($orderItem->unit->display_name)) {
                        $unitValue = $orderItem->unit->display_name;
                    }
                }
                
                BillItem::create([
                    'bill_id' => $bill->id,
                    'product_id' => $orderItem->product_id,
                    'item_name' => $orderItem->product_name,
                    'item_type' => BillItem::TYPE_PRODUCT,
                    'item_description' => $orderItem->product_specification ?? '',
                    'quantity' => $orderItem->quantity,
                    'unit' => $unitValue,
                    'unit_price' => $orderItem->price,
                    'total_price' => $itemTotalPrice,
                    'final_amount' => $itemTotalPrice,
                    'order_item_id' => $orderItem->id,
                ]);
            }

            // 如果有调整金额，创建调整记录
            if (isset($options['adjustment_amount']) && $options['adjustment_amount'] != 0) {
                $this->createAdjustment($bill, $options['adjustment_amount'], $options['adjustment_reason'] ?? '订单调整');
            }

            Log::info('确认订单账单创建成功', [
                'bill_id' => $bill->id,
                'order_id' => $order->id,
                'order_status' => $order->status,
                'correction_status' => $order->correction_status,
                'payment_method' => $order->payment_method,
                'is_wechat_correction' => $isWechatCorrection,
                'original_amount' => $bill->original_amount, // 🔥 新增：记录原始金额
                'final_amount' => $bill->final_amount,       // 🔥 新增：记录最终金额
                'pending_amount' => $bill->pending_amount,   // 🔥 新增：记录待付金额
                'calculation_method' => $this->getBillAmountCalculationMethod($order)
            ]);

            // 🔥 新增：检查是否需要自动创建累计账单
            $this->checkAndCreateConsolidatedBill($order->user);

            return $bill;
        });
    }

    /**
     * 🔥 新增：计算订单的账单金额
     */
    private function calculateBillAmountForOrder(Order $order): float
    {
        // 对于货到付款订单，始终使用原始订单金额创建账单
        // 更正差异通过后续的账单调整和支付记录来处理
        if ($order->isCashOnDelivery()) {
            // 🔥 修复：货到付款订单使用原始total字段，不使用final_payment_amount
            // final_payment_amount 只是用于订单更正流程中的计算，不应该影响账单的基础金额
            return $order->total;
        }
        
        // 非货到付款订单使用final_payment_amount（如果存在）或total
        return $order->final_payment_amount ?? $order->total;
    }

    /**
     * 🔥 新增：获取账单金额计算方法说明
     */
    private function getBillAmountCalculationMethod(Order $order): string
    {
        if ($order->isCashOnDelivery()) {
            return 'cod_original_total'; // 货到付款使用原始订单金额
        }
        
        if ($order->final_payment_amount && $order->final_payment_amount != $order->total) {
            return 'final_payment_amount'; // 使用更正后金额
        }
        
        return 'order_total'; // 使用订单总金额
    }

    /**
     * 检查订单是否已确认（可以创建正式账单）
     * 修正逻辑：账单应该在订单确认时创建，不应该等到送达
     */
    public function isOrderConfirmed(Order $order): bool
    {
        // 1. 如果订单已有账单，无需重复创建
        if ($order->bills && $order->bills->count() > 0) {
            return false; // 已有账单，不需要再创建
        }

        // 2. 如果有更正记录，检查最新更正的状态
        // 🔧 修复：强制重新查询更正记录，确保获取最新状态
        $latestCorrection = \App\Order\Models\OrderCorrection::where('order_id', $order->id)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestCorrection) {

            Log::info('检查订单更正状态用于账单创建', [
                'order_id' => $order->id,
                'latest_correction_id' => $latestCorrection->id,
                'latest_correction_status' => $latestCorrection->status,
                'order_correction_status' => $order->correction_status,
                'order_status' => $order->status,
                'can_create_bill' => $latestCorrection->status === 'confirmed'
            ]);

            // 🔥 修复：检查最新更正记录的状态，而不是订单的correction_status字段
            return $latestCorrection->status === 'confirmed';
        }

        // 3. 没有更正记录的订单，在确认时就可以创建账单
        // 注意：不需要等到送达，确认后立即创建账单
        return in_array($order->status, ['confirmed', 'paid', 'shipped', 'delivered']);
    }



    /**
     * 处理账单支付
     */
    public function processPayment(Bill $bill, array $paymentData): PaymentRecord
    {
        if (!$bill->canBePaid()) {
            throw new Exception('账单状态不允许支付');
        }

        return DB::transaction(function () use ($bill, $paymentData) {
            // 创建支付记录
            $paymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $paymentData['payment_method'],
                'payment_amount' => $paymentData['payment_amount'],
                'payment_type' => $this->determinePaymentType($bill, $paymentData['payment_amount']),
                'balance_used' => $paymentData['balance_used'] ?? 0,
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'external_payment_no' => $paymentData['external_payment_no'] ?? null,
                'payment_details' => $paymentData['payment_details'] ?? [],
                'status' => PaymentRecord::STATUS_PENDING, // 初始状态为待处理
                'payment_time' => now(),
                'received_by' => $paymentData['received_by'] ?? null,
                'notes' => $paymentData['notes'] ?? '',
            ]);

            // 根据支付方式处理不同的支付逻辑
            switch ($paymentData['payment_method']) {
                case PaymentRecord::METHOD_WECHAT:
                    $this->processWechatPayment($bill, $paymentRecord, $paymentData);
                    break;
                    
                case PaymentRecord::METHOD_ALIPAY:
                    $this->processAlipayPayment($bill, $paymentRecord, $paymentData);
                    break;
                    
                case PaymentRecord::METHOD_BALANCE:
                    $this->processBalancePayment($bill, $paymentRecord, $paymentData['balance_used'] ?? $paymentData['payment_amount']);
                    $paymentRecord->update([
                        'status' => PaymentRecord::STATUS_SUCCESS,
                        'confirmed_at' => now()
                    ]);
                    break;
                    
                case PaymentRecord::METHOD_CASH:
                case PaymentRecord::METHOD_BANK_TRANSFER:
                case PaymentRecord::METHOD_COD:
                    // 线下支付方式直接标记为成功
                    $paymentRecord->update([
                        'status' => PaymentRecord::STATUS_SUCCESS,
                        'confirmed_at' => now()
                    ]);
                    break;
                    
                default:
                    throw new Exception('不支持的支付方式: ' . $paymentData['payment_method']);
            }

            // 如果支付成功，更新账单状态和金额
            if ($paymentRecord->status === PaymentRecord::STATUS_SUCCESS) {
                $bill->updateAmounts();
                $this->syncOrderPaymentStatus($bill);
            }

            Log::info('账单支付处理完成', [
                'bill_id' => $bill->id,
                'payment_record_id' => $paymentRecord->id,
                'payment_method' => $paymentData['payment_method'],
                'payment_amount' => $paymentData['payment_amount'],
                'status' => $paymentRecord->status
            ]);

            return $paymentRecord;
        });
    }

    /**
     * 处理微信支付
     */
    protected function processWechatPayment(Bill $bill, PaymentRecord $paymentRecord, array $paymentData): void
    {
        try {
            // 获取默认的微信支付服务商
            $provider = \App\WechatPayment\Models\WechatServiceProvider::where('is_active', true)->first();
            if (!$provider) {
                throw new Exception('未配置微信支付服务商');
            }

            // 创建微信支付服务实例
            $wechatService = new \App\WechatPayment\Services\WechatServiceProviderPayment($provider);

            // 检查是否提供了openid（JSAPI支付必需）
            if (empty($paymentData['openid'])) {
                throw new Exception('微信支付需要提供用户openid');
            }

            // 🔥 修复：对于微信订单更正补款，使用原始订单而不是临时订单
            if ($bill->order && $bill->order->payment_method === 'wechat' && $paymentRecord->payment_type === 'supplement') {
                // 微信订单更正补款：使用原始订单信息
                $orderForPayment = $bill->order;
                $orderForPayment->total = $paymentData['payment_amount']; // 临时设置为补款金额

                Log::info('微信订单更正补款处理', [
                    'bill_id' => $bill->id,
                    'order_id' => $orderForPayment->id,
                    'supplement_amount' => $paymentData['payment_amount'],
                    'original_order_no' => $orderForPayment->order_no
                ]);
            } else {
                // 其他情况：创建临时订单对象
                $orderForPayment = new \App\Order\Models\Order();
                $orderForPayment->id = $bill->id;
                $orderForPayment->total = $paymentData['payment_amount'];
                $orderForPayment->order_no = $bill->bill_no;
            }

            // 调用微信支付统一下单
            $payParams = $wechatService->createMiniAppPay($orderForPayment, $paymentData['openid']);

            // 🔥 修复：查找创建的微信支付记录并更新关联信息
            $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('out_trade_no', $paymentRecord->payment_no)->first();
            if (!$wechatPayment) {
                // 如果没找到，根据订单类型查找
                if ($bill->order && $bill->order->payment_method === 'wechat' && $paymentRecord->payment_type === 'supplement') {
                    // 微信订单补款：查找最新的微信支付记录
                    $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $orderForPayment->id)
                        ->orderBy('created_at', 'desc')
                        ->first();
                } else {
                    // 其他情况：使用账单ID查找
                    $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $bill->id)
                        ->orderBy('created_at', 'desc')
                        ->first();
                }
            }

            // 更新支付记录
            $paymentRecord->update([
                'external_payment_no' => $payParams['package'] ?? null,
                'payment_details' => [
                    'trade_type' => 'JSAPI',
                    'wechat_pay_params' => $payParams,
                    'provider_id' => $provider->id,
                ],
                'status' => PaymentRecord::STATUS_PENDING,
            ]);

            Log::info('微信支付订单创建成功', [
                'bill_id' => $bill->id,
                'payment_record_id' => $paymentRecord->id,
                'wechat_payment_id' => $wechatPayment ? $wechatPayment->id : null,
                'pay_params' => $payParams,
            ]);

        } catch (\Exception $e) {
            // 微信支付失败，更新支付记录状态
            $paymentRecord->update([
                'status' => PaymentRecord::STATUS_FAILED,
                'notes' => $paymentRecord->notes . "\n微信支付失败: " . $e->getMessage(),
                'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                    'error' => $e->getMessage(),
                    'failed_at' => now()->toISOString(),
                ]),
            ]);

            Log::error('微信支付处理失败', [
                'bill_id' => $bill->id,
                'payment_record_id' => $paymentRecord->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 处理支付宝支付
     */
    protected function processAlipayPayment(Bill $bill, PaymentRecord $paymentRecord, array $paymentData): void
    {
        // TODO: 实现支付宝支付逻辑
        // 这里可以集成支付宝SDK
        
        $paymentRecord->update([
            'status' => PaymentRecord::STATUS_PENDING,
            'notes' => $paymentRecord->notes . "\n支付宝支付待实现",
        ]);
        
        Log::info('支付宝支付待实现', [
            'bill_id' => $bill->id,
            'payment_record_id' => $paymentRecord->id,
        ]);
    }

    /**
     * 处理余额支付
     */
    protected function processBalancePayment(Bill $bill, PaymentRecord $paymentRecord, float $balanceAmount): void
    {
        $user = $bill->user;
        
        if ($user->balance < $balanceAmount) {
            throw new Exception('用户余额不足');
        }

        $balanceBefore = $user->balance;
        $user->decrement('balance', $balanceAmount);
        $balanceAfter = $user->fresh()->balance;

        // 更新支付记录中的余额信息
        $paymentRecord->update([
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
        ]);

        // 创建余额变动记录
        BalanceTransaction::create([
            'user_id' => $user->id,
            'transaction_no' => BalanceTransaction::generateTransactionNo(),
            'amount' => -$balanceAmount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'transaction_type' => BalanceTransaction::TYPE_PAYMENT,
            'source' => 'bill_payment',
            'source_id' => $bill->id,
            'description' => "账单支付: {$bill->bill_no}",
            'bill_id' => $bill->id,
            'payment_record_id' => $paymentRecord->id,
            'transaction_time' => now(),
            'status' => BalanceTransaction::STATUS_SUCCESS,
        ]);
    }

    /**
     * 创建账单调整
     */
    public function createAdjustment(Bill $bill, float $adjustmentAmount, string $reason, array $options = []): BillAdjustment
    {
        Log::info('开始创建账单调整', [
            'bill_id' => $bill->id,
            'bill_no' => $bill->bill_no,
            'adjustment_amount' => $adjustmentAmount,
            'reason' => $reason,
            'options' => $options,
            'bill_status' => $bill->status,
            'bill_payment_status' => $bill->payment_status,
            'bill_original_amount' => $bill->original_amount,
            'bill_current_adjustment' => $bill->adjustment_amount,
            'bill_final_amount' => $bill->final_amount
        ]);

        if (!$bill->canBeAdjusted()) {
            Log::error('账单状态不允许调整', [
                'bill_id' => $bill->id,
                'bill_status' => $bill->status,
                'bill_payment_status' => $bill->payment_status
            ]);
            throw new Exception('账单状态不允许调整');
        }

        return DB::transaction(function () use ($bill, $adjustmentAmount, $reason, $options) {
            // 处理调整类型
            $adjustmentType = $options['adjustment_type'] ?? null;
            
            // 前端adjustment_type值与数据库值的映射
            $typeMapping = [
                'increase' => BillAdjustment::TYPE_PRICE_INCREASE,
                'decrease' => BillAdjustment::TYPE_PRICE_DECREASE,
                'discount' => BillAdjustment::TYPE_DISCOUNT,
                'fee' => BillAdjustment::TYPE_FEE_ADD
            ];

            // 如果前端传递了adjustment_type，根据类型决定金额的正负
            if ($adjustmentType) {
                Log::info('使用前端传递的调整类型', [
                    'adjustment_type' => $adjustmentType,
                    'original_amount' => $adjustmentAmount,
                    'mapped_type' => $typeMapping[$adjustmentType] ?? null
                ]);
                
                // 如果是decrease类型，确保金额为负数
                if ($adjustmentType === 'decrease' && $adjustmentAmount > 0) {
                    $adjustmentAmount = -$adjustmentAmount;
                    Log::info('将增加金额转换为减少金额', ['adjusted_amount' => $adjustmentAmount]);
                }
                
                // 将前端类型映射到数据库类型
                $adjustmentType = $typeMapping[$adjustmentType] ?? BillAdjustment::TYPE_MANUAL_ADJUST;
            } else {
                // 没有传递调整类型，使用金额正负判断
                $adjustmentType = $adjustmentAmount > 0 ? BillAdjustment::TYPE_PRICE_INCREASE : BillAdjustment::TYPE_PRICE_DECREASE;
                Log::info('根据金额正负确定调整类型', ['adjustment_type' => $adjustmentType]);
            }
            
            // 检查调整后的金额是否合法
            $newFinalAmount = $bill->final_amount + $adjustmentAmount;
            if ($newFinalAmount < 0) {
                Log::error('账单调整金额无效', [
                    'bill_id' => $bill->id,
                    'current_final_amount' => $bill->final_amount,
                    'adjustment_amount' => $adjustmentAmount,
                    'new_final_amount' => $newFinalAmount
                ]);
                throw new Exception('调整后的账单金额不能为负数');
            }
            
            // 检查调整后的待付金额是否合法
            $newPendingAmount = $bill->pending_amount + $adjustmentAmount;
            if ($newPendingAmount < 0) {
                Log::error('账单调整后待付金额无效', [
                    'bill_id' => $bill->id,
                    'current_pending_amount' => $bill->pending_amount,
                    'adjustment_amount' => $adjustmentAmount,
                    'new_pending_amount' => $newPendingAmount
                ]);
                throw new Exception('调整后的待付金额不能为负数');
            }
            
            try {
                $adjustment = BillAdjustment::create([
                    'bill_id' => $bill->id,
                    'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                    'adjustment_amount' => $adjustmentAmount,
                    'adjustment_type' => $adjustmentType,
                    'reason' => $reason,
                    'description' => $options['description'] ?? '',
                    'operator_id' => $options['operator_id'] ?? null,
                    'status' => BillAdjustment::STATUS_APPROVED,
                    'applied_at' => now(),
                ]);
                
                Log::info('账单调整记录创建成功', [
                    'adjustment_id' => $adjustment->id,
                    'adjustment_no' => $adjustment->adjustment_no,
                    'adjustment_amount' => $adjustmentAmount
                ]);

                // 更新账单金额
                $originalAdjustmentAmount = $bill->adjustment_amount;
                $originalFinalAmount = $bill->final_amount;
                $originalPendingAmount = $bill->pending_amount;
                
                $bill->logBillStatus('调整前'); // 记录调整前的状态
                
                // 使用安全的方式更新金额，避免直接使用increment可能导致的约束冲突
                $bill->adjustment_amount += $adjustmentAmount;
                $bill->final_amount += $adjustmentAmount;
                $bill->pending_amount += $adjustmentAmount;
                $bill->save();
                
                Log::info('账单金额更新成功', [
                    'bill_id' => $bill->id,
                    'original_adjustment' => $originalAdjustmentAmount,
                    'new_adjustment' => $bill->adjustment_amount,
                    'original_final' => $originalFinalAmount,
                    'new_final' => $bill->final_amount,
                    'original_pending' => $originalPendingAmount,
                    'new_pending' => $bill->pending_amount
                ]);
                
                $bill->updateAmounts(); // 重新计算状态
                $bill->logBillStatus('调整后'); // 记录调整后的状态

                // 同步更新关联订单状态
                $this->syncOrderPaymentStatus($bill);

                Log::info('账单调整创建成功', [
                    'bill_id' => $bill->id,
                    'adjustment_id' => $adjustment->id,
                    'adjustment_amount' => $adjustmentAmount,
                    'final_bill_amount' => $bill->final_amount
                ]);

                return $adjustment;
            } catch (Exception $e) {
                Log::error('账单调整创建失败', [
                    'bill_id' => $bill->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }

    /**
     * 取消账单
     */
    public function cancelBill(Bill $bill, string $reason = '', array $options = []): void
    {
        if (!$bill->canBeCancelled()) {
            throw new Exception('账单状态不允许取消');
        }

        DB::transaction(function () use ($bill, $reason, $options) {
            $bill->update([
                'status' => Bill::STATUS_CANCELLED,
                'cancelled_at' => now(),
                'notes' => $bill->notes . "\n取消原因: {$reason}",
            ]);

            Log::info('账单取消成功', ['bill_id' => $bill->id, 'reason' => $reason]);
        });
    }

    /**
     * 账单退款
     */
    public function refundBill(Bill $bill, float $refundAmount, string $reason = '', array $options = []): PaymentRecord
    {
        if ($bill->paid_amount < $refundAmount) {
            throw new Exception('退款金额不能超过已付金额');
        }

        return DB::transaction(function () use ($bill, $refundAmount, $reason, $options) {
            $refundRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $options['refund_method'] ?? PaymentRecord::METHOD_OTHER,
                'payment_amount' => -$refundAmount,
                'payment_type' => PaymentRecord::TYPE_REFUND,
                'status' => PaymentRecord::STATUS_SUCCESS,
                'payment_time' => now(),
                'confirmed_at' => now(),
                'received_by' => $options['operator_id'] ?? null,
                'notes' => "退款原因: {$reason}",
            ]);

            // 如果是余额退款，需要增加用户余额
            if (($options['refund_method'] ?? '') === PaymentRecord::METHOD_BALANCE) {
                $this->processBalanceRefund($bill, $refundRecord, $refundAmount);
            }

            // 更新账单状态
            $bill->updateAmounts();

            // 同步更新关联订单状态
            $this->syncOrderPaymentStatus($bill);

            Log::info('账单退款处理成功', [
                'bill_id' => $bill->id,
                'refund_amount' => $refundAmount,
                'reason' => $reason
            ]);

            return $refundRecord;
        });
    }

    /**
     * 🔥 新增：批量退款
     */
    public function batchRefundBills(array $billIds, string $reason = '批量退款', ?int $operatorId = null): array
    {
        // 🔒 生成批量退款锁键
        $batchLockKey = 'batch_refund_' . md5(implode(',', $billIds)) . '_' . $operatorId;

        return Cache::lock($batchLockKey, 300)->block(10, function () use ($billIds, $reason, $operatorId) {
            Log::info('开始批量退款处理', [
                'bill_ids' => $billIds,
                'reason' => $reason,
                'operator_id' => $operatorId,
                'lock_key' => substr($batchLockKey, 0, 20) . '...'
            ]);

            $results = [
                'success_count' => 0,
                'fail_count' => 0,
                'success_bills' => [],
                'failed_bills' => [],
                'total_refund_amount' => 0,
            ];

            foreach ($billIds as $billId) {
                // 🔒 为每个账单添加单独的锁
                $billLockKey = "refund_bill_{$billId}";

                try {
                    $lockResult = Cache::lock($billLockKey, 60)->block(5, function () use ($billId, $reason, $operatorId, &$results) {
                        return $this->processSingleBillRefund($billId, $reason, $operatorId, $results);
                    });

                    if ($lockResult === false) {
                        $results['failed_bills'][] = [
                            'bill_id' => $billId,
                            'bill_no' => "ID:{$billId}",
                            'error' => '获取退款锁失败，可能有其他退款正在进行'
                        ];
                        $results['fail_count']++;
                    }

                } catch (\Illuminate\Contracts\Cache\LockTimeoutException $e) {
                    $results['failed_bills'][] = [
                        'bill_id' => $billId,
                        'bill_no' => "ID:{$billId}",
                        'error' => '退款锁超时，账单可能正在被其他操作处理'
                    ];
                    $results['fail_count']++;

                    Log::warning('批量退款锁超时', [
                        'bill_id' => $billId,
                        'lock_key' => $billLockKey
                    ]);
                }
            }

            Log::info('批量退款完成', [
                'total_bills' => count($billIds),
                'success_count' => $results['success_count'],
                'fail_count' => $results['fail_count'],
                'total_refund_amount' => $results['total_refund_amount']
            ]);

            return $results;
        });
    }

    /**
     * 🔒 新增：处理单个账单退款（在锁保护下）
     */
    private function processSingleBillRefund(int $billId, string $reason, ?int $operatorId, array &$results): bool
    {
        try {
            $bill = Bill::with(['order', 'paymentRecords'])->findOrFail($billId);

            Log::info('开始处理单个账单退款', [
                'bill_id' => $billId,
                'bill_no' => $bill->bill_no,
                'payment_status' => $bill->payment_status,
                'paid_amount' => $bill->paid_amount,
                'pending_refund_amount' => $bill->pending_refund_amount,
                'original_payment_method' => $bill->original_payment_method
            ]);

            // 🔒 再次检查账单是否可以退款（在锁内再次验证）
            if (!$this->canRefundBill($bill)) {
                $results['failed_bills'][] = [
                    'bill_id' => $billId,
                    'bill_no' => $bill->bill_no,
                    'error' => '账单状态不允许退款或已有退款记录'
                ];
                $results['fail_count']++;
                return false;
            }

            // 🔥 使用订单更正系统计算的应退金额
            $refundAmount = $bill->pending_refund_amount;

            // 验证应退金额
            if ($refundAmount <= 0) {
                $results['failed_bills'][] = [
                    'bill_id' => $billId,
                    'bill_no' => $bill->bill_no,
                    'error' => '该账单没有待退款金额'
                ];
                $results['fail_count']++;
                return false;
            }

            // 验证应退金额不超过已付金额
            if ($refundAmount > $bill->paid_amount) {
                $results['failed_bills'][] = [
                    'bill_id' => $billId,
                    'bill_no' => $bill->bill_no,
                    'error' => "应退金额 ¥{$refundAmount} 超过已付金额 ¥{$bill->paid_amount}"
                ];
                $results['fail_count']++;
                return false;
            }

            // 确定退款方式
            $refundMethod = $this->determineRefundMethod($bill);

            // 🔒 执行退款（在事务中）
            DB::transaction(function () use ($bill, $refundAmount, $reason, $operatorId, $refundMethod) {
                if ($refundMethod === 'wechat') {
                    // 直接处理微信退款，不通过refundBill方法避免状态冲突
                    $this->processBatchWechatRefundDirect($bill, $refundAmount, $reason, $operatorId);
                } else {
                    // 非微信支付使用原有逻辑
                    $this->refundBill($bill, $refundAmount, $reason, [
                        'refund_method' => $refundMethod,
                        'operator_id' => $operatorId
                    ]);
                }
            });

            $results['success_bills'][] = [
                'bill_id' => $billId,
                'bill_no' => $bill->bill_no,
                'refund_amount' => $refundAmount,
                'refund_no' => 'REF' . date('YmdHis') . mt_rand(1000, 9999)
            ];
            $results['success_count']++;
            $results['total_refund_amount'] += $refundAmount;

            Log::info('单个账单退款成功', [
                'bill_id' => $billId,
                'bill_no' => $bill->bill_no,
                'refund_amount' => $refundAmount
            ]);

            return true;

        } catch (Exception $e) {
            $bill = Bill::find($billId);
            $results['failed_bills'][] = [
                'bill_id' => $billId,
                'bill_no' => $bill->bill_no ?? "ID:{$billId}",
                'error' => $e->getMessage()
            ];
            $results['fail_count']++;

            Log::error('单个账单退款失败', [
                'bill_id' => $billId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * 🔒 新增：计算批量退款总金额
     */
    public function calculateBatchRefundAmount(array $billIds): float
    {
        $totalAmount = 0;

        foreach ($billIds as $billId) {
            $bill = Bill::find($billId);
            if ($bill && $this->canRefundBill($bill)) {
                // 🔥 修正：使用订单更正系统计算的应退金额
                $totalAmount += $bill->pending_refund_amount;
            }
        }

        return $totalAmount;
    }

    /**
     * 🔥 新增：检查账单是否可以退款
     */
    private function canRefundBill(Bill $bill): bool
    {
        // 🔥 重新加载账单数据，确保数据是最新的
        $bill->refresh();

        // 只有已付款的账单才能退款
        if ($bill->payment_status !== 'paid') {
            Log::warning('账单不是已付款状态', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'payment_status' => $bill->payment_status
            ]);
            return false;
        }

        // 已付金额必须大于0
        if ($bill->paid_amount <= 0) {
            Log::warning('账单已付金额为0或负数', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'paid_amount' => $bill->paid_amount
            ]);
            return false;
        }

        // 账单状态必须是有效的
        if (in_array($bill->status, ['cancelled', 'refunded'])) {
            Log::warning('账单状态不允许退款', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'status' => $bill->status
            ]);
            return false;
        }

        // 🔥 只有微信支付的账单才支持自动退款
        if ($bill->original_payment_method !== 'wechat') {
            Log::warning('非微信支付账单不支持自动退款', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'payment_method' => $bill->original_payment_method
            ]);
            return false;
        }

        // 🔥 新增：必须有待退款金额
        if ($bill->pending_refund_amount <= 0) {
            Log::warning('账单没有待退款金额', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'pending_refund_amount' => $bill->pending_refund_amount
            ]);
            return false;
        }

        // 🔒 关键：检查是否已有退款记录（防重复退款）
        if ($this->hasExistingRefund($bill)) {
            Log::warning('账单已有退款记录，防止重复退款', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no
            ]);
            return false;
        }

        return true;
    }

    /**
     * 🔒 新增：检查是否已有退款记录（防重复退款核心方法）
     */
    private function hasExistingRefund(Bill $bill): bool
    {
        // 1. 检查账单系统的退款记录
        $billingRefundExists = $bill->paymentRecords()
            ->where('payment_type', PaymentRecord::TYPE_REFUND)
            ->where('status', PaymentRecord::STATUS_SUCCESS)
            ->where('payment_amount', '<', 0) // 退款是负数
            ->exists();

        if ($billingRefundExists) {
            Log::info('发现账单系统退款记录', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no
            ]);
            return true;
        }

        // 2. 检查微信退款记录
        if ($bill->order_id) {
            $wechatRefundExists = \App\WechatPayment\Models\WechatServiceRefund::where('order_id', $bill->order_id)
                ->whereIn('refund_status', ['SUCCESS', 'PROCESSING'])
                ->exists();

            if ($wechatRefundExists) {
                Log::info('发现微信退款记录', [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'order_id' => $bill->order_id
                ]);
                return true;
            }
        }

        // 3. 检查订单更正相关的退款记录
        if ($bill->order_id) {
            $correctionRefundExists = $bill->paymentRecords()
                ->where('payment_type', PaymentRecord::TYPE_REFUND)
                ->where('status', PaymentRecord::STATUS_SUCCESS)
                ->whereJsonContains('payment_details->correction_id', function($query) use ($bill) {
                    // 检查是否有订单更正相关的退款
                    return true;
                })
                ->exists();

            if ($correctionRefundExists) {
                Log::info('发现订单更正退款记录', [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'order_id' => $bill->order_id
                ]);
                return true;
            }
        }

        return false;
    }

    /**
     * 🔥 新增：确定退款方式
     */
    private function determineRefundMethod(Bill $bill): string
    {
        // 🔥 批量退款只支持微信支付
        $originalPaymentMethod = $bill->original_payment_method;

        if ($originalPaymentMethod === 'wechat') {
            return 'wechat';
        } else {
            // 非微信支付的账单不应该进入批量退款流程
            throw new Exception("账单 {$bill->bill_no} 不是微信支付，无法进行自动退款");
        }
    }

    /**
     * 🔥 新增：处理批量微信退款
     */
    private function processWechatRefundForBatch(Bill $bill, PaymentRecord $refundRecord): void
    {
        try {
            // 查找订单
            $order = $bill->order;
            if (!$order) {
                throw new Exception('未找到关联订单，无法处理微信退款');
            }

            // 🔥 直接调用微信退款API，不使用createRefund避免状态重置
            $this->processDirectWechatRefund($order, abs($refundRecord->payment_amount), $refundRecord);

            Log::info('批量微信退款处理成功', [
                'bill_id' => $bill->id,
                'order_id' => $order->id,
                'refund_amount' => abs($refundRecord->payment_amount)
            ]);

        } catch (Exception $e) {
            // 微信退款失败，更新退款记录状态
            $refundRecord->update([
                'status' => PaymentRecord::STATUS_FAILED,
                'notes' => $refundRecord->notes . "\n微信退款失败: " . $e->getMessage()
            ]);

            Log::error('批量微信退款失败', [
                'bill_id' => $bill->id,
                'refund_record_id' => $refundRecord->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 🔥 新增：直接处理微信退款（不影响订单状态）
     */
    private function processDirectWechatRefund(\App\Order\Models\Order $order, float $refundAmount, PaymentRecord $refundRecord): void
    {
        // 查找原始微信支付记录
        $originalPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
            ->where('trade_state', 'SUCCESS')
            ->first();

        if (!$originalPayment) {
            throw new Exception('未找到原始微信支付记录');
        }

        // 创建微信退款记录
        $wechatRefund = \App\WechatPayment\Models\WechatServiceRefund::create([
            'payment_id' => $originalPayment->id,
            'order_id' => $order->id,
            'out_refund_no' => 'REF' . date('YmdHis') . mt_rand(1000, 9999),
            'refund_fee' => $refundAmount * 100, // 转换为分
            'refund_reason' => '批量退款',
            'refund_status' => 'processing',
            'operator_id' => $refundRecord->received_by,
        ]);

        // 调用微信退款API
        $wechatService = app(\App\WechatPayment\Services\WechatRefundService::class);
        $result = $wechatService->processRefund([
            'out_trade_no' => $originalPayment->out_trade_no,
            'out_refund_no' => $wechatRefund->out_refund_no,
            'total_fee' => $originalPayment->total_fee,
            'refund_fee' => $wechatRefund->refund_fee,
            'refund_desc' => '批量退款'
        ]);

        // 更新退款记录
        $wechatRefund->update([
            'refund_id' => $result['refund_id'] ?? null,
            'refund_status' => 'success'
        ]);

        // 更新支付记录状态为成功
        $refundRecord->update([
            'status' => PaymentRecord::STATUS_SUCCESS,
            'transaction_id' => $wechatRefund->out_refund_no,
            'notes' => $refundRecord->notes . "\n微信退款成功，退款单号：{$wechatRefund->out_refund_no}"
        ]);
    }

    /**
     * 🔥 新增：直接处理批量微信退款（不影响账单状态）
     */
    private function processBatchWechatRefundDirect(Bill $bill, float $refundAmount, string $reason, ?int $operatorId): void
    {
        DB::transaction(function () use ($bill, $refundAmount, $reason, $operatorId) {
            // 1. 创建退款记录
            $refundRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => 'wechat',
                'payment_amount' => -$refundAmount,
                'payment_type' => PaymentRecord::TYPE_REFUND,
                'status' => PaymentRecord::STATUS_PENDING,
                'payment_time' => now(),
                'received_by' => $operatorId,
                'notes' => "批量退款原因: {$reason}",
            ]);

            // 2. 处理微信退款API调用
            $this->processDirectWechatRefund($bill->order, $refundAmount, $refundRecord);

            // 3. 更新账单金额（不改变支付状态）
            $bill->updateAmounts();

            Log::info('批量微信退款直接处理成功', [
                'bill_id' => $bill->id,
                'refund_amount' => $refundAmount,
                'refund_record_id' => $refundRecord->id
            ]);
        });
    }

    /**
     * 🔥 新增：创建退款记录（用于订单取消等场景）
     */
    public function createRefund(array $params): PaymentRecord
    {
        $orderId = $params['order_id'];
        $refundAmount = $params['refund_amount'];
        $refundReason = $params['refund_reason'] ?? '订单取消退款';
        $refundType = $params['refund_type'] ?? 'order_cancellation';
        $operatorId = $params['operator_id'] ?? null;
        $autoProcess = $params['auto_process'] ?? false;

        return DB::transaction(function () use ($orderId, $refundAmount, $refundReason, $refundType, $operatorId, $autoProcess) {
            // 查找订单
            $order = \App\Order\Models\Order::findOrFail($orderId);

            // 查找或创建订单账单
            $bill = $this->findOrCreateOrderBill($order);

            // 检查是否有足够的已付金额可退款
            if ($bill->paid_amount < $refundAmount) {
                throw new Exception("退款金额 ¥{$refundAmount} 超过已付金额 ¥{$bill->paid_amount}");
            }

            // 创建退款记录
            $refundRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $order->payment_method,
                'payment_amount' => -$refundAmount,
                'payment_type' => PaymentRecord::TYPE_REFUND,
                'payment_scenario' => $refundType,
                'status' => $autoProcess ? PaymentRecord::STATUS_PENDING : PaymentRecord::STATUS_SUCCESS,
                'payment_time' => now(),
                'confirmed_at' => $autoProcess ? null : now(),
                'received_by' => $operatorId,
                'notes' => $refundReason,
                'payment_details' => [
                    'refund_type' => $refundType,
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'auto_process' => $autoProcess
                ]
            ]);

            // 如果是微信支付且需要自动处理，调用微信退款API
            if ($autoProcess && $order->payment_method === 'wechat') {
                $this->processWechatRefundForOrder($order, $refundRecord);
            }

            // 更新账单状态
            $bill->updateAmounts();

            Log::info('订单退款记录创建成功', [
                'order_id' => $orderId,
                'order_no' => $order->order_no,
                'refund_amount' => $refundAmount,
                'refund_type' => $refundType,
                'payment_record_id' => $refundRecord->id,
                'auto_process' => $autoProcess
            ]);

            return $refundRecord;
        });
    }

    /**
     * 🔥 新增：处理订单的微信退款（使用新的退款服务）
     */
    private function processWechatRefundForOrder(\App\Order\Models\Order $order, PaymentRecord $refundRecord): void
    {
        try {
            // 🔥 使用新的统一退款服务
            $provider = \App\WechatPayment\Models\WechatServiceProvider::where('is_active', 1)->first();
            if (!$provider) {
                throw new Exception('未找到活跃的微信支付配置');
            }

            // 获取子商户信息（如果有）
            $subMerchant = null;
            if ($order->region_id) {
                $subMerchant = \App\WechatPayment\Models\WechatSubMerchant::where('region_id', $order->region_id)
                    ->where('is_active', 1)
                    ->first();
            }

            // 创建退款服务实例
            $refundService = new \App\WechatPayment\Services\WechatRefundService($provider, $subMerchant);

            // 🔥 调用统一退款处理
            $wechatRefund = $refundService->processRefund([
                'order_id' => $order->id,
                'refund_amount' => abs($refundRecord->payment_amount),
                'refund_reason' => '订单取消退款',
                'operator_id' => $refundRecord->operated_by,
            ]);

            // 🔥 更新支付记录，关联微信退款记录
            $refundRecord->update([
                'status' => PaymentRecord::STATUS_SUCCESS,
                'transaction_id' => $wechatRefund->out_refund_no,
                'notes' => $refundRecord->notes . "\n微信退款处理成功，退款单号: " . $wechatRefund->out_refund_no,
                'extra_data' => array_merge($refundRecord->extra_data ?? [], [
                    'wechat_refund_id' => $wechatRefund->id,
                    'out_refund_no' => $wechatRefund->out_refund_no,
                ])
            ]);

            Log::info('微信退款处理成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'payment_record_id' => $refundRecord->id,
                'wechat_refund_id' => $wechatRefund->id,
                'out_refund_no' => $wechatRefund->out_refund_no,
                'refund_amount' => abs($refundRecord->payment_amount)
            ]);

        } catch (\Exception $e) {
            // 🔥 微信退款失败，更新退款记录状态
            $refundRecord->update([
                'status' => PaymentRecord::STATUS_FAILED,
                'notes' => $refundRecord->notes . "\n微信退款失败: " . $e->getMessage()
            ]);

            Log::error('微信退款处理失败', [
                'order_id' => $order->id,
                'payment_record_id' => $refundRecord->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 🔥 新增：查找或创建订单账单
     */
    private function findOrCreateOrderBill(\App\Order\Models\Order $order): Bill
    {
        // 先尝试查找现有账单
        $bill = Bill::where('order_id', $order->id)->first();

        if ($bill) {
            return $bill;
        }

        // 如果没有账单，创建一个新的
        return $this->createBillForOrder($order);
    }

    /**
     * 处理余额退款
     */
    protected function processBalanceRefund(Bill $bill, PaymentRecord $refundRecord, float $refundAmount): void
    {
        $user = $bill->user;
        $balanceBefore = $user->balance;
        $user->increment('balance', $refundAmount);
        $balanceAfter = $user->fresh()->balance;

        // 创建余额变动记录
        BalanceTransaction::create([
            'user_id' => $user->id,
            'transaction_no' => BalanceTransaction::generateTransactionNo(),
            'amount' => $refundAmount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'transaction_type' => BalanceTransaction::TYPE_REFUND,
            'source' => 'bill_refund',
            'source_id' => $bill->id,
            'description' => "账单退款: {$bill->bill_no}",
            'bill_id' => $bill->id,
            'payment_record_id' => $refundRecord->id,
            'transaction_time' => now(),
            'status' => BalanceTransaction::STATUS_SUCCESS,
        ]);
    }

    /**
     * 获取用户账单列表
     */
    public function getUserBills(User $user, array $filters = [])
    {
        $query = Bill::where('user_id', $user->id)
            ->with(['items', 'paymentRecords', 'adjustments']);

        if (isset($filters['bill_type'])) {
            $query->where('bill_type', $filters['bill_type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * 获取逾期账单
     */
    public function getOverdueBills(array $filters = [])
    {
        $query = Bill::overdue()->with(['user', 'items']);

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['overdue_days'])) {
            $query->where('due_date', '<', now()->subDays($filters['overdue_days']));
        }

        return $query->orderBy('due_date', 'asc');
    }

    /**
     * 计算账单统计信息
     */
    public function getBillStatistics(array $filters = []): array
    {
        $query = Bill::query();

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return [
            'total_bills' => $query->count(),
            'total_amount' => $query->sum('final_amount'),
            'paid_amount' => $query->sum('paid_amount'),
            'pending_amount' => $query->sum('pending_amount'),
            'overdue_bills' => $query->overdue()->count(),
            'overdue_amount' => $query->overdue()->sum('pending_amount'),
        ];
    }

    /**
     * 同步更新关联订单的支付状态
     */
    protected function syncOrderPaymentStatus(Bill $bill): void
    {
        // 只处理订单类型的账单
        if ($bill->bill_type !== Bill::TYPE_ORDER) {
            return;
        }

        // 检查是否为累计账单
        if ($this->isConsolidatedBill($bill)) {
            $this->syncConsolidatedBillOrderStatus($bill);
        } elseif ($bill->order_id) {
            $this->syncSingleOrderStatus($bill);
        }
    }

    /**
     * 检查是否为累计账单
     */
    protected function isConsolidatedBill(Bill $bill): bool
    {
        return $bill->bill_type === Bill::TYPE_CONSOLIDATED;
    }

    /**
     * 同步累计账单关联的所有订单状态
     */
    protected function syncConsolidatedBillOrderStatus(Bill $bill): void
    {
        // 获取累计账单包含的所有订单ID
        $orderIds = $bill->metadata['order_ids'] ?? [];
        if (empty($orderIds)) {
            return;
        }

        $orders = Order::whereIn('id', $orderIds)->get();
        
        foreach ($orders as $order) {
            $this->syncOrderStatusFromBillPaymentStatus($order, $bill);
        }

        Log::info('累计账单订单状态同步完成', [
            'consolidated_bill_id' => $bill->id,
            'order_count' => count($orderIds),
            'bill_payment_status' => $bill->payment_status,
        ]);
    }

    /**
     * 同步单个订单状态
     */
    protected function syncSingleOrderStatus(Bill $bill): void
    {
        $order = $bill->order;
        if (!$order) {
            return;
        }

        $this->syncOrderStatusFromBillPaymentStatus($order, $bill);
    }

    /**
     * 根据账单支付状态同步订单状态
     */
    protected function syncOrderStatusFromBillPaymentStatus(Order $order, Bill $bill): void
    {
        // 🔥 修复：货到付款订单不应该因为账单付款而改变订单状态
        if ($order->payment_method === 'cod') {
            Log::info('货到付款订单跳过状态同步', [
                'order_id' => $order->id,
                'order_status' => $order->status,
                'bill_payment_status' => $bill->payment_status,
                'reason' => '货到付款订单状态由业务流程控制，不受账单付款状态影响'
            ]);
            return;
        }

        $newOrderStatus = null;
        $paidAt = null;
        $transactionId = null;

        switch ($bill->payment_status) {
            case Bill::PAYMENT_STATUS_PAID:
            case Bill::PAYMENT_STATUS_OVERPAID:
                // 账单已付款，订单也应标记为已确认（在线支付直接确认）
                if (in_array($order->status, ['pending', 'pending_payment'])) {
                    // 🔥 修复：在线支付成功直接确认订单
                    $newOrderStatus = 'confirmed';
                    $paidAt = now();

                    // 🔥 修复：获取交易号
                    $latestPaymentRecord = $bill->paymentRecords()
                        ->where('status', \App\Billing\Models\PaymentRecord::STATUS_SUCCESS)
                        ->whereNotNull('transaction_id')
                        ->latest()
                        ->first();

                    if ($latestPaymentRecord && $latestPaymentRecord->transaction_id) {
                        $transactionId = $latestPaymentRecord->transaction_id;
                    }
                }
                break;

            case Bill::PAYMENT_STATUS_UNPAID:
            case Bill::PAYMENT_STATUS_PARTIAL:
                // 账单未付款或部分付款，订单应回到待付款状态
                if (in_array($order->status, ['paid']) && !$order->canBeCancelled()) {
                    // 如果订单已发货或送达，不能回滚状态
                    return;
                }
                if ($order->status === 'paid') {
                    $newOrderStatus = 'pending';
                    $paidAt = null;
                }
                break;
        }

        // 执行订单状态更新
        if ($newOrderStatus && $newOrderStatus !== $order->status) {
            $updateData = ['status' => $newOrderStatus];
            if ($paidAt) {
                $updateData['paid_at'] = $paidAt;
                $updateData['payment_confirmed_at'] = $paidAt;
                // 🔥 新增：如果是确认状态，也设置确认时间
                if ($newOrderStatus === 'confirmed') {
                    $updateData['confirmed_at'] = $paidAt;
                }
                // 🔥 修复：添加交易号
                if ($transactionId) {
                    $updateData['transaction_id'] = $transactionId;
                }
            } elseif ($newOrderStatus === 'pending') {
                $updateData['paid_at'] = null;
                $updateData['payment_confirmed_at'] = null;
                $updateData['confirmed_at'] = null;
                $updateData['transaction_id'] = null;
            }

            $order->update($updateData);

            Log::info('订单支付状态已同步更新', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'old_status' => $order->getOriginal('status'),
                'new_status' => $newOrderStatus,
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'bill_payment_status' => $bill->payment_status,
                'transaction_id' => $transactionId,  // 🔥 新增：记录交易号
                'update_data' => $updateData,  // 🔥 新增：记录更新的数据
                'is_consolidated' => $this->isConsolidatedBill($bill),
            ]);
        }
    }

    /**
     * 确定支付类型
     */
    protected function determinePaymentType(Bill $bill, float $paymentAmount): string
    {
        // 🔥 修复：对于微信订单更正补款，需要特殊判断
        if ($bill->order && $bill->order->payment_method === 'wechat' && $bill->order->correction_status === 'confirmed') {
            // 检查是否已有原始支付记录
            $hasOriginalPayment = $bill->paymentRecords()
                ->where('payment_type', 'payment')
                ->where('status', 'success')
                ->exists();

            if ($hasOriginalPayment && $paymentAmount > 0) {
                // 已有原始支付，当前支付是补款
                return PaymentRecord::TYPE_SUPPLEMENT;
            }
        }

        // 其他情况：根据金额判断
        if ($paymentAmount >= $bill->pending_amount) {
            return PaymentRecord::TYPE_FULL;
        } else {
            return PaymentRecord::TYPE_PARTIAL;
        }
    }

    /**
     * 🔥 重构：处理订单更正确认（统一入口）
     * 接管所有订单更正后的资金相关操作
     */
    public function handleOrderCorrectionConfirmed(OrderCorrection $correction, int $operatedBy): void
    {
        Log::info('账单系统接收订单更正确认', [
            'order_id' => $correction->order_id,
            'correction_type' => $correction->correction_type,
            'difference_amount' => $correction->difference_amount,
            'operator_id' => $operatedBy
        ]);

        // 1. 创建账单（重新加载订单以确保获取最新的更正记录）
        $order = \App\Order\Models\Order::with('corrections')->find($correction->order_id);
        $bill = $this->createBillFromConfirmedOrder($order, [
            'notes' => "订单更正确认后创建，更正单号：{$correction->correction_no}",
            'operator_id' => $operatedBy,
            'metadata' => [
                'correction_info' => [
                    'correction_no' => $correction->correction_no,
                    'correction_type' => $correction->correction_type,
                    'difference_amount' => $correction->difference_amount,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total,
                ]
            ]
        ]);

        // 2. 处理支付差异
        $this->handlePaymentDifference($correction, $bill, $operatedBy);

        Log::info('账单系统处理订单更正完成', [
            'bill_id' => $bill->id,
            'bill_no' => $bill->bill_no
        ]);
    }

    /**
     * 🔥 废弃：原有的记录方法（保留作为参考）
     */
    public function recordOrderCorrectionResult_DEPRECATED($correction, array $options = []): void
    {
        $paymentMethodData = $options['payment_method_data'] ?? [];
        
        Log::info('账单模块记录订单更正结果', [
            'order_id' => $correction->order_id,
            'correction_type' => $correction->correction_type,
            'difference_amount' => $correction->difference_amount,
            'operator_id' => $options['operator_id'] ?? null,
            'payment_method_data' => $paymentMethodData // 🔥 新增：记录支付方式信息
        ]);

        // 对于无修改确认，直接创建账单
        if ($correction->correction_type === 'no_change') {
            Log::info('无修改确认，创建账单并处理支付记录', [
                'order_id' => $correction->order_id,
                'order_payment_method' => $correction->order->payment_method,
                'order_total' => $correction->order->total
            ]);
            
            $bill = $this->createBillFromConfirmedOrder($correction->order, array_merge($options, [
                'notes' => "订单无修改确认后自动创建账单，更正单号：{$correction->correction_no}"
            ]));
            
            // 🔥 关键修复：为微信支付等在线支付订单创建原始支付记录
            if (in_array($correction->order->payment_method, ['wechat', 'alipay'])) {
                $this->createInitialPaymentRecord($bill, $correction->order, $options['operator_id'] ?? null);
            }
            
            return;
        }

        // 🔥 修复：有修改的情况，尝试创建账单并记录更正信息，但不因为账单创建失败而中断流程
        try {
            $billMetadata = [
                'correction_info' => [
                    'correction_no' => $correction->correction_no,
                    'correction_type' => $correction->correction_type,
                    'difference_amount' => $correction->difference_amount,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total,
                    'handled_by_order_module' => $options['metadata']['handled_by_order_module'] ?? true
                ]
            ];
            
            // 🔥 新增：包含支付方式信息
            if (!empty($paymentMethodData)) {
                $billMetadata['payment_method_info'] = $paymentMethodData;
            }
            
            $this->createBillFromConfirmedOrder($correction->order, array_merge($options, [
                'notes' => "订单更正确认后自动创建账单，更正单号：{$correction->correction_no}，差额：{$correction->difference_amount}元",
                'metadata' => $billMetadata
            ]));
            
            Log::info('账单模块记录订单更正结果完成', [
                'order_id' => $correction->order_id,
                'bill_created' => true
            ]);
        } catch (\Exception $e) {
            // 🔥 关键修复：账单创建失败不应该影响订单更正的主要流程
            Log::warning('账单模块状态记录失败，但订单更正流程正常完成', [
                'order_id' => $correction->order_id,
                'error' => $e->getMessage(),
                'note' => '这是非关键错误，不影响订单更正的核心功能'
            ]);
        }
    }

    /**
     * 🔥 新增：处理订单更正支付差异（根据支付方式区分处理）
     */
    public function processOrderCorrectionPaymentDifference($correction, float $differenceAmount, int $operatedBy): void
    {
        $paymentMethod = $correction->order->payment_method;
        
        Log::info('账单系统处理订单更正支付差异', [
            'order_id' => $correction->order_id,
            'difference_amount' => $differenceAmount,
            'payment_method' => $paymentMethod,
            'operator_id' => $operatedBy
        ]);

        // 🔥 正确逻辑：微信支付需要处理差额，货到付款按最终金额收款
        if ($paymentMethod === 'wechat') {
            if ($differenceAmount < 0) {
                // 微信支付 + 金额减少 = 退款
                $this->processWechatRefund($correction, abs($differenceAmount), $operatedBy);
            } else {
                // 微信支付 + 金额增加 = 补款
                $this->processWechatSupplement($correction, $differenceAmount, $operatedBy);
            }
        } else {
            // 货到付款等其他支付方式：直接按更正后的最终金额收款
            $this->processDirectPaymentForCorrection($correction, $operatedBy);
        }
    }

    /**
     * 🔥 新增：处理微信退款
     */
    private function processWechatRefund($correction, float $refundAmount, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $refundAmount, $operatedBy) {
            Log::info('处理微信退款', [
                'refund_amount' => $refundAmount,
                'operator_id' => $operatedBy
            ]);

            // 1. 确保订单有账单
            $bill = $this->ensureOrderHasBill($correction->order);
            
            // 2. 创建账单调整记录
            $adjustment = BillAdjustment::create([
                'bill_id' => $bill->id,
                'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                'adjustment_type' => BillAdjustment::TYPE_MANUAL_ADJUST,
                'adjustment_amount' => -$refundAmount,
                'reason' => '订单更正微信退款',
                'description' => "订单更正导致金额减少，微信退款：{$refundAmount}元，更正单号：{$correction->correction_no}",
                'operator_id' => $operatedBy,
                'status' => BillAdjustment::STATUS_APPROVED,
                'applied_at' => now()
            ]);

            // 3. 创建退款记录
            $paymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => 'wechat',
                'payment_amount' => -$refundAmount,
                'payment_type' => PaymentRecord::TYPE_REFUND,
                'status' => PaymentRecord::STATUS_PENDING,
                'payment_time' => now(),
                'received_by' => $operatedBy,
                'notes' => "订单更正微信退款：{$correction->correction_no}",
                'payment_details' => [
                    'correction_no' => $correction->correction_no,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total,
                    'refund_reason' => '订单更正金额减少'
                ]
            ]);

            // 4. 执行微信退款
            $this->executeWechatRefund($paymentRecord, $correction);
            
            // 5. 更新账单状态
            $bill->updateAmounts();
            
            Log::info('微信退款处理完成', [
                'bill_id' => $bill->id,
                'adjustment_id' => $adjustment->id,
                'payment_record_id' => $paymentRecord->id,
                'refund_amount' => $refundAmount
            ]);
        });
    }

    /**
     * 🔥 修改：处理微信补款 - 统一使用账单系统的付款链接
     */
    private function processWechatSupplement($correction, float $supplementAmount, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $supplementAmount, $operatedBy) {
            Log::info('账单系统处理订单更正补款', [
                'order_id' => $correction->order_id,
                'supplement_amount' => $supplementAmount,
                'payment_method' => $correction->order->payment_method,
                'operator_id' => $operatedBy
            ]);

            // 1. 确保订单有账单
            $bill = $this->ensureOrderHasBill($correction->order);
            
            // 2. 创建账单调整记录
            $adjustment = BillAdjustment::create([
                'bill_id' => $bill->id,
                'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                'adjustment_type' => BillAdjustment::TYPE_MANUAL_ADJUST,
                'adjustment_amount' => $supplementAmount,
                'reason' => '订单更正补款',
                'description' => "订单更正导致金额增加，补款金额：{$supplementAmount}元，更正单号：{$correction->correction_no}",
                'operator_id' => $operatedBy,
                'status' => BillAdjustment::STATUS_APPROVED,
                'applied_at' => now()
            ]);

            // 3. 创建补款记录
            $paymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $correction->order->payment_method,
                'payment_amount' => $supplementAmount,
                'payment_type' => PaymentRecord::TYPE_SUPPLEMENT,
                'status' => PaymentRecord::STATUS_PENDING,
                'payment_time' => now(),
                'received_by' => $operatedBy,
                'notes' => "订单更正补款：{$correction->correction_no}",
                'payment_details' => [
                    'correction_no' => $correction->correction_no,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total,
                    'supplement_reason' => '订单更正金额增加'
                ]
            ]);

            // 4. 执行微信补款
            $this->executeWechatSupplement($paymentRecord, $correction);
            
            // 5. 更新账单状态
            $bill->updateAmounts();
            
            Log::info('账单系统补款处理完成', [
                'bill_id' => $bill->id,
                'adjustment_id' => $adjustment->id,
                'payment_record_id' => $paymentRecord->id,
                'supplement_amount' => $supplementAmount
            ]);
        });
    }





    /**
     * 🔥 修复：处理货到付款订单更正（正确的COD逻辑）
     */
    private function processDirectPaymentForCorrection($correction, int $operatedBy): void
    {
        $order = $correction->order;

        if ($order->payment_method === 'cod') {
            // 货到付款订单使用专门的处理逻辑
            $this->processCodOrderCorrection($correction, $operatedBy);
        } else {
            // 其他支付方式（现金等）
            $this->processOtherPaymentMethodCorrection($correction, $operatedBy);
        }
    }

    /**
     * 🔥 新增：处理货到付款订单更正
     */
    private function processCodOrderCorrection($correction, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $operatedBy) {
            $order = $correction->order;

            Log::info('处理货到付款订单更正', [
                'order_id' => $correction->order_id,
                'order_no' => $order->order_no,
                'original_total' => $correction->original_total,
                'corrected_total' => $correction->corrected_total,
                'cod_status' => $order->cod_status ?? 'unpaid',
                'operator_id' => $operatedBy
            ]);

            // 1. 确保订单有账单（货到付款订单在更正时才创建账单）
            $bill = $this->ensureOrderHasBill($order);

            // 2. 更新账单为更正后的金额
            $correctedAmount = $correction->corrected_total;
            $adjustmentAmount = $correctedAmount - $bill->original_amount;

            if (abs($adjustmentAmount) > 0.01) {
                // 3. 创建账单调整记录
                $adjustment = BillAdjustment::create([
                    'bill_id' => $bill->id,
                    'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                    'adjustment_type' => BillAdjustment::TYPE_MANUAL_ADJUST,
                    'adjustment_amount' => $adjustmentAmount,
                    'reason' => '货到付款订单更正',
                    'description' => "货到付款订单更正，调整金额：{$adjustmentAmount}元，更正单号：{$correction->correction_no}",
                    'operator_id' => $operatedBy,
                    'status' => BillAdjustment::STATUS_APPROVED,
                    'applied_at' => now()
                ]);

                Log::info('货到付款账单调整记录创建', [
                    'bill_id' => $bill->id,
                    'adjustment_id' => $adjustment->id,
                    'adjustment_amount' => $adjustmentAmount
                ]);
            }

            // 4. 🔥 关键修复：货到付款订单的正确账单状态
            $isPaid = in_array($order->cod_status ?? 'unpaid', ['paid', 'confirmed']);

            $bill->update([
                'original_amount' => $order->total, // 保持原始订单金额
                'final_amount' => $correctedAmount, // 更正后的金额
                'adjustment_amount' => $adjustmentAmount,
                'paid_amount' => $isPaid ? $correctedAmount : 0, // 根据COD状态设置已付金额
                'pending_amount' => $isPaid ? 0 : $correctedAmount, // 根据COD状态设置待付金额
                'status' => $isPaid ? Bill::STATUS_PAID : Bill::STATUS_PENDING,
                'payment_status' => $isPaid ? Bill::PAYMENT_STATUS_PAID : Bill::PAYMENT_STATUS_PENDING,
                'notes' => ($bill->notes ?? '') . "\n货到付款订单更正处理完成"
            ]);

            Log::info('✅ 货到付款订单更正处理完成', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'original_amount' => $order->total,
                'final_amount' => $correctedAmount,
                'adjustment_amount' => $adjustmentAmount,
                'pending_amount' => $isPaid ? 0 : $correctedAmount,
                'cod_status' => $order->cod_status ?? 'unpaid',
                'logic' => '货到付款订单更正后按最终金额收款'
            ]);
        });
    }

    /**
     * 🔥 修复：确保订单有对应的账单（微信支付订单优先使用现有账单）
     */
    private function ensureOrderHasBill(Order $order): Bill
    {
        // 先查找现有账单
        $existingBill = Bill::where('order_id', $order->id)->first();
        
        if ($existingBill) {
            Log::info('找到现有账单，直接使用', [
                'order_id' => $order->id,
                'bill_id' => $existingBill->id,
                'bill_no' => $existingBill->bill_no,
                'payment_method' => $order->payment_method
            ]);
            return $existingBill;
        }

        // 🔥 添加详细调试日志
        $hasCorrections = $order->corrections()->exists();
        Log::info('ensureOrderHasBill 调试信息', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'payment_method' => $order->payment_method,
            'payment_method_type' => gettype($order->payment_method),
            'is_wechat' => $order->payment_method === 'wechat',
            'has_corrections' => $hasCorrections,
            'corrections_count' => $order->corrections()->count(),
            'order_status' => $order->status,
            'correction_status' => $order->correction_status ?? 'null'
        ]);

        // 🔥 关键修复：对于微信支付订单，如果没有现有账单，创建简化账单
        if ($order->payment_method === 'wechat') {
            Log::info('微信支付订单缺少账单，创建简化账单用于补款处理', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'payment_method' => $order->payment_method,
                'reason' => '微信支付订单更正补款需要账单基础'
            ]);
            
            return $this->createSimplifiedBillForWechatOrder($order);
        }

        // 🔥 检查是否在更正确认过程中（非微信支付订单）
        if ($hasCorrections) {
            Log::info('非微信支付订单更正过程中创建完整账单', [
                'order_id' => $order->id,
                'order_status' => $order->status,
                'correction_status' => $order->correction_status ?? 'null',
                'corrections_count' => $order->corrections()->count(),
                'payment_method' => $order->payment_method
            ]);
            
            return $this->createBillForCorrectionProcess($order, [
                'notes' => '订单更正确认过程中自动创建账单'
            ]);
        }

        // 普通情况下，使用严格验证
        return $this->createBillFromConfirmedOrder($order, [
            'notes' => '订单更正时自动创建账单'
        ]);
    }

    /**
     * 🔥 新增：为微信支付订单创建简化账单（仅用于补款处理）
     */
    private function createSimplifiedBillForWechatOrder(Order $order): Bill
    {
        return DB::transaction(function () use ($order) {
            // 获取订单的应收金额
            $orderAmount = $order->isCashOnDelivery() && $order->final_payment_amount 
                ? $order->final_payment_amount  
                : $order->total;
            
            // 检查是否已存在账单
            $existingBill = Bill::where('order_id', $order->id)->first();
            if ($existingBill) {
                return $existingBill; // 如果已存在，直接返回
            }
            
            $bill = Bill::create([
                'bill_no' => Bill::generateBillNo(),
                'bill_type' => Bill::TYPE_ORDER,
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'original_amount' => $orderAmount,
                'adjustment_amount' => 0,
                'final_amount' => $orderAmount,
                'pending_amount' => $orderAmount,
                'due_date' => now()->addDays(7),
                'status' => Bill::STATUS_PENDING,
                'payment_status' => Bill::PAYMENT_STATUS_UNPAID,
                'created_by' => null,
                'notes' => "微信支付订单简化账单（用于补款处理）",
                'metadata' => [
                    'simplified_for_wechat' => true,
                    'wechat_correction_process' => true,
                    'created_for_supplement' => true,
                    'creation_time' => now()->toISOString(),
                    'order_type' => $order->isCashOnDelivery() ? 'cod' : 'normal'
                ],
            ]);

            // 🔥 关键：微信支付订单不创建详细明细，只创建一个总计项目
            BillItem::create([
                'bill_id' => $bill->id,
                'product_id' => null,
                'item_name' => '订单总计',
                'item_type' => BillItem::TYPE_PRODUCT,
                'item_description' => "微信支付订单 {$order->order_no} 总计金额",
                'quantity' => 1,
                'unit' => '项',
                'unit_price' => $orderAmount,
                'total_price' => $orderAmount,
                'final_amount' => $orderAmount,
                'order_item_id' => null,
            ]);

            Log::info('微信支付订单简化账单创建成功', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'final_amount' => $orderAmount,
                'is_simplified' => true,
                'purpose' => 'wechat_supplement_processing'
            ]);

            return $bill;
        });
    }

    /**
     * 🔥 新增：执行微信退款
     */
    private function executeWechatRefund(PaymentRecord $paymentRecord, $correction): void
    {
        // 获取原始微信支付记录
        $originalWechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $correction->order_id)
            ->where('trade_state', 'SUCCESS')
            ->first();
            
        if (!$originalWechatPayment) {
            throw new Exception('未找到该订单的微信支付记录，无法处理退款');
        }

        // 调用微信退款API
        $wechatService = app(\App\WechatPayment\Services\WechatServiceProviderPayment::class);
        
        $refundResult = $wechatService->refund([
            'out_trade_no' => $originalWechatPayment->out_trade_no,
            'out_refund_no' => $paymentRecord->payment_no,
            'total_fee' => $originalWechatPayment->total_fee * 100, // 转换为分
            'refund_fee' => abs($paymentRecord->payment_amount) * 100, // 转换为分
            'refund_desc' => '订单更正退款',
        ]);

        // 创建微信退款记录
        $wechatRefund = \App\WechatPayment\Models\WechatServiceRefund::create([
            'payment_id' => $originalWechatPayment->id,
            'order_id' => $correction->order_id,
            'out_refund_no' => $paymentRecord->payment_no,
            'refund_id' => $refundResult['refund_id'] ?? null,
            'transaction_id' => $originalWechatPayment->transaction_id,
            'out_trade_no' => $originalWechatPayment->out_trade_no,
            'total_fee' => $originalWechatPayment->total_fee,
            'refund_fee' => abs($paymentRecord->payment_amount),
            'refund_status' => 'PROCESSING',
            'refund_reason' => '订单更正退款',
            'notify_data' => $refundResult,
        ]);

        // 更新支付记录
        $paymentRecord->update([
            'transaction_id' => $refundResult['refund_id'] ?? null,
            'status' => PaymentRecord::STATUS_PROCESSING,
            'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                'wechat_refund_id' => $wechatRefund->id,
                'refund_initiated_at' => now()->toISOString(),
                'wechat_response' => $refundResult
            ])
        ]);

        Log::info('微信退款API调用成功', [
            'payment_record_id' => $paymentRecord->id,
            'wechat_refund_id' => $wechatRefund->id,
            'refund_amount' => abs($paymentRecord->payment_amount)
        ]);
    }

    /**
     * 🔥 新增：执行货到付款退款
     */
    private function executeCodRefund(PaymentRecord $paymentRecord, $correction): void
    {
        // 货到付款退款：直接标记为成功，最终收款时扣除
        $paymentRecord->update([
            'status' => PaymentRecord::STATUS_SUCCESS,
            'confirmed_at' => now(),
            'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                'cod_refund' => true,
                'refund_processed_at' => now()->toISOString()
            ])
        ]);

        Log::info('货到付款退款记录成功', [
            'payment_record_id' => $paymentRecord->id,
            'refund_amount' => abs($paymentRecord->payment_amount)
        ]);
    }

    /**
     * 🔥 修改：执行微信补款 - 使用账单系统的付款链接服务
     */
    private function executeWechatSupplement(PaymentRecord $paymentRecord, $correction): void
    {
        // 🔥 重要：使用账单系统的付款链接服务，不再使用订单模块
        $paymentLinkService = app(PaymentLinkService::class);
        $bill = $paymentRecord->bill;
        
        $paymentLink = $paymentLinkService->createPaymentLinkForBill($bill, [
            'amount' => $paymentRecord->payment_amount,
            'payment_method' => 'wechat',
            'payment_scenario' => 'correction_supplement',
            'created_by' => $paymentRecord->received_by,
            'business_context' => [
                'correction_no' => $correction->correction_no,
                'original_total' => $correction->original_total,
                'corrected_total' => $correction->corrected_total,
                'payment_record_id' => $paymentRecord->id
            ]
        ]);

        // 更新支付记录，关联付款链接
        $paymentRecord->update([
            'external_payment_no' => $paymentLink->link_no,
            'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                'payment_link_no' => $paymentLink->link_no,
                'payment_link_id' => $paymentLink->id,
                'payment_link_url' => $paymentLink->full_url,
                'supplement_initiated_at' => now()->toISOString(),
                'created_by_billing_system' => true
            ])
        ]);

        Log::info('账单系统微信补款链接生成成功', [
            'payment_record_id' => $paymentRecord->id,
            'payment_link_no' => $paymentLink->link_no,
            'payment_link_id' => $paymentLink->id,
            'supplement_amount' => $paymentRecord->payment_amount,
            'bill_id' => $bill->id
        ]);
    }

    /**
     * 🔥 新增：执行货到付款补款
     */
    private function executeCodSupplement(PaymentRecord $paymentRecord, $correction): void
    {
        // 货到付款补款：保持pending状态，等待最终收款
        $paymentRecord->update([
            'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                'cod_supplement' => true,
                'supplement_recorded_at' => now()->toISOString()
            ])
        ]);

        Log::info('货到付款补款记录成功', [
            'payment_record_id' => $paymentRecord->id,
            'supplement_amount' => $paymentRecord->payment_amount
        ]);
    }

    /**
     * 🔥 新增：为更正确认过程创建账单（跳过严格验证）
     */
    private function createBillForCorrectionProcess(Order $order, array $options = []): Bill
    {
        return DB::transaction(function () use ($order, $options) {
            // 获取订单的应收金额
            $orderAmount = $order->isCashOnDelivery() && $order->final_payment_amount 
                ? $order->final_payment_amount  // 货到付款且有更正金额
                : $order->total;                // 普通订单或货到付款无更正
            
            // 检查是否已存在账单
            $existingBill = Bill::where('order_id', $order->id)->first();
            if ($existingBill) {
                throw new Exception('该订单已存在账单，账单号：' . $existingBill->bill_no);
            }
            
            $bill = Bill::create([
                'bill_no' => Bill::generateBillNo(),
                'bill_type' => Bill::TYPE_ORDER,
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'original_amount' => $orderAmount,
                'adjustment_amount' => $options['adjustment_amount'] ?? 0,
                'final_amount' => $orderAmount + ($options['adjustment_amount'] ?? 0),
                'pending_amount' => $orderAmount + ($options['adjustment_amount'] ?? 0),
                'due_date' => $options['due_date'] ?? now()->addDays(7),
                'status' => Bill::STATUS_PENDING,
                'payment_status' => Bill::PAYMENT_STATUS_UNPAID,
                'created_by' => $options['created_by'] ?? null,
                'notes' => $options['notes'] ?? "订单更正确认过程中自动创建账单",
                'metadata' => array_merge([
                    'order_correction_process' => true,
                    'correction_process_time' => now()->toISOString(),
                    'order_type' => $order->isCashOnDelivery() ? 'cod' : 'normal',
                    'amount_source' => $order->isCashOnDelivery() && $order->final_payment_amount ? 'final_payment_amount' : 'total',
                ], $options['metadata'] ?? []),
            ]);

            // 创建账单明细（使用更正后的数据）
            foreach ($order->items as $orderItem) {
                // 计算总价：如果订单项的total_price为空，则使用数量*单价
                $itemTotalPrice = $orderItem->total_price ?? ($orderItem->quantity * $orderItem->price);
                
                // 处理单位字段：如果是对象则提取display_name，否则使用原值
                $unitValue = '件'; // 默认值
                if ($orderItem->unit) {
                    if (is_string($orderItem->unit)) {
                        $unitValue = $orderItem->unit;
                    } elseif (is_array($orderItem->unit) && isset($orderItem->unit['display_name'])) {
                        $unitValue = $orderItem->unit['display_name'];
                    } elseif (is_object($orderItem->unit) && isset($orderItem->unit->display_name)) {
                        $unitValue = $orderItem->unit->display_name;
                    }
                }
                
                BillItem::create([
                    'bill_id' => $bill->id,
                    'product_id' => $orderItem->product_id,
                    'item_name' => $orderItem->product_name,
                    'item_type' => BillItem::TYPE_PRODUCT,
                    'item_description' => $orderItem->product_specification ?? '',
                    'quantity' => $orderItem->quantity,
                    'unit' => $unitValue,
                    'unit_price' => $orderItem->price,
                    'total_price' => $itemTotalPrice,
                    'final_amount' => $itemTotalPrice,
                    'order_item_id' => $orderItem->id,
                ]);
            }

            // 如果有调整金额，创建调整记录
            if (isset($options['adjustment_amount']) && $options['adjustment_amount'] != 0) {
                $this->createAdjustment($bill, $options['adjustment_amount'], $options['adjustment_reason'] ?? '订单调整');
            }

            Log::info('更正确认过程中账单创建成功', [
                'bill_id' => $bill->id, 
                'order_id' => $order->id,
                'order_status' => $order->status,
                'final_amount' => $orderAmount,
                'is_correction_process' => true
            ]);

            return $bill;
        });
    }

    /**
     * 获取账单列表
     */
    public function getBillList(array $params): array
    {
        $query = Bill::with(['user', 'order', 'items', 'paymentRecords'])
            ->where('bill_type', '!=', 'consolidated'); // 🔥 排除累计账单

        // 搜索过滤
        if (!empty($params['search'])) {
            $search = $params['search'];
            $query->where(function ($q) use ($search) {
                $q->where('bill_no', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // 账单类型过滤
        if (!empty($params['bill_type'])) {
            $query->where('bill_type', $params['bill_type']);
        }

        // 状态过滤
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 用户过滤
        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        // 🔥 新增：排除累计账单
        if (isset($params['exclude_consolidated']) && $params['exclude_consolidated']) {
            $query->where('bill_type', '!=', 'consolidated');
        }

        // 🔥 新增：支付方式过滤
        if (!empty($params['payment_method'])) {
            $query->whereHas('paymentRecords', function ($q) use ($params) {
                $q->where('payment_method', $params['payment_method'])
                  ->where('status', 'success');
            });
        }

        // 支付状态过滤
        if (!empty($params['payment_status'])) {
            $query->where('payment_status', $params['payment_status']);
        }

        // 日期范围过滤
        if (!empty($params['start_date'])) {
            $query->whereDate('created_at', '>=', $params['start_date']);
        }
        if (!empty($params['end_date'])) {
            $query->whereDate('created_at', '<=', $params['end_date']);
        }

        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 20;

        return [
            'data' => $query->orderBy('created_at', 'desc')->paginate($perPage, ['*'], 'page', $page),
            'summary' => $this->getBillStatistics($params)
        ];
    }

    /**
     * 🔥 新增：获取累计账单列表
     */
    public function getConsolidatedBillList(array $params): array
    {
        $query = Bill::with(['user', 'items', 'paymentRecords'])
            ->where('bill_type', 'consolidated'); // 只获取累计账单

        // 用户筛选
        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        // 状态筛选
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 付款状态筛选
        if (!empty($params['payment_status'])) {
            $query->where('payment_status', $params['payment_status']);
        }

        // 日期范围筛选
        if (!empty($params['date_from'])) {
            $query->whereDate('created_at', '>=', $params['date_from']);
        }
        if (!empty($params['date_to'])) {
            $query->whereDate('created_at', '<=', $params['date_to']);
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 分页
        $perPage = $params['per_page'] ?? 20;
        $bills = $query->paginate($perPage);

        // 计算汇总信息
        $summary = $this->calculateConsolidatedBillSummary($params);

        return [
            'data' => $bills,
            'summary' => $summary
        ];
    }

    /**
     * 🔥 新增：计算累计账单汇总信息
     */
    private function calculateConsolidatedBillSummary(array $params): array
    {
        $query = Bill::where('bill_type', 'consolidated');

        // 应用相同的筛选条件
        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }
        if (!empty($params['payment_status'])) {
            $query->where('payment_status', $params['payment_status']);
        }
        if (!empty($params['date_from'])) {
            $query->whereDate('created_at', '>=', $params['date_from']);
        }
        if (!empty($params['date_to'])) {
            $query->whereDate('created_at', '<=', $params['date_to']);
        }

        return [
            'total_bills' => $query->count(),
            'total_amount' => $query->sum('final_amount'),
            'paid_amount' => $query->sum('paid_amount'),
            'pending_amount' => $query->sum('pending_amount'),
            'overdue_bills' => $query->where('due_date', '<', now())
                ->where('payment_status', '!=', 'paid')->count(),
            'overdue_amount' => $query->where('due_date', '<', now())
                ->where('payment_status', '!=', 'paid')->sum('pending_amount'),
        ];
    }

    /**
     * 获取账单报告
     */
    public function getBillReports(array $params): array
    {
        $startDate = $params['start_date'];
        $endDate = $params['end_date'];
        $chartType = $params['chart_type'] ?? 'day';

        $query = Bill::whereBetween('created_at', [$startDate, $endDate]);

        // 根据图表类型分组统计
        $groupBy = match($chartType) {
            'week' => "DATE_FORMAT(created_at, '%Y-%u')",
            'month' => "DATE_FORMAT(created_at, '%Y-%m')",
            default => "DATE(created_at)"
        };

        $reports = $query->selectRaw("
            {$groupBy} as period,
            COUNT(*) as bill_count,
            SUM(final_amount) as total_amount,
            SUM(paid_amount) as paid_amount,
            SUM(pending_amount) as pending_amount
        ")
        ->groupByRaw($groupBy)
        ->orderByRaw($groupBy)
        ->get();

        // 创建新的查询实例来获取汇总数据
        $summaryQuery = Bill::whereBetween('created_at', [$startDate, $endDate]);

        return [
            'chart_data' => $reports,
            'summary' => [
                'total_bills' => $summaryQuery->count(),
                'total_amount' => $summaryQuery->sum('final_amount'),
                'paid_amount' => $summaryQuery->sum('paid_amount'),
                'pending_amount' => $summaryQuery->sum('pending_amount'),
            ]
        ];
    }

    /**
     * 获取合并统计
     */
    public function getConsolidatedStats(array $params): array
    {
        $startDate = $params['start_date'] ?? now()->startOfMonth();
        $endDate = $params['end_date'] ?? now()->endOfMonth();

        return [
            'revenue' => [
                'total_revenue' => Bill::whereBetween('created_at', [$startDate, $endDate])->sum('final_amount'),
                'paid_revenue' => Bill::whereBetween('created_at', [$startDate, $endDate])->sum('paid_amount'),
                'pending_revenue' => Bill::whereBetween('created_at', [$startDate, $endDate])->sum('pending_amount'),
            ],
            'bills' => [
                'total_bills' => Bill::whereBetween('created_at', [$startDate, $endDate])->count(),
                'paid_bills' => Bill::whereBetween('created_at', [$startDate, $endDate])->where('payment_status', 'paid')->count(),
                'pending_bills' => Bill::whereBetween('created_at', [$startDate, $endDate])->where('payment_status', 'pending')->count(),
                'overdue_bills' => Bill::whereBetween('created_at', [$startDate, $endDate])->overdue()->count(),
            ],
            'customers' => [
                'total_customers' => Bill::whereBetween('created_at', [$startDate, $endDate])->distinct('user_id')->count(),
                'paying_customers' => Bill::whereBetween('created_at', [$startDate, $endDate])->where('payment_status', 'paid')->distinct('user_id')->count(),
            ]
        ];
    }



    /**
     * 获取支付记录列表
     */
    public function getPaymentRecords(array $params): array
    {
        $query = PaymentRecord::with(['bill', 'bill.user']);

        // 搜索过滤
        if (!empty($params['search'])) {
            $search = $params['search'];
            $query->where(function ($q) use ($search) {
                $q->where('payment_no', 'like', "%{$search}%")
                  ->orWhere('external_payment_no', 'like', "%{$search}%");
            });
        }

        // 支付方式过滤
        if (!empty($params['payment_method'])) {
            $query->where('payment_method', $params['payment_method']);
        }

        // 支付类型过滤
        if (!empty($params['payment_type'])) {
            $query->where('payment_type', $params['payment_type']);
        }

        // 状态过滤
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 用户过滤
        if (!empty($params['user_id'])) {
            $query->whereHas('bill', function ($billQuery) use ($params) {
                $billQuery->where('user_id', $params['user_id']);
            });
        }

        // 账单类型过滤
        if (!empty($params['bill_type'])) {
            $query->whereHas('bill', function ($billQuery) use ($params) {
                $billQuery->where('bill_type', $params['bill_type']);
            });
        }

        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 20;

        return [
            'data' => $query->orderBy('created_at', 'desc')->paginate($perPage, ['*'], 'page', $page)
        ];
    }

    /**
     * 获取支付记录统计
     */
    public function getPaymentStatistics(array $params = []): array
    {
        $query = PaymentRecord::query();

        if (!empty($params['start_date'])) {
            $query->whereDate('created_at', '>=', $params['start_date']);
        }
        if (!empty($params['end_date'])) {
            $query->whereDate('created_at', '<=', $params['end_date']);
        }

        return [
            'total_payments' => $query->count(),
            'total_amount' => $query->sum('payment_amount'),
            'success_payments' => $query->where('status', 'success')->count(),
            'failed_payments' => $query->where('status', 'failed')->count(),
            'pending_payments' => $query->where('status', 'pending')->count(),
            'by_method' => $query->selectRaw('payment_method, COUNT(*) as count, SUM(payment_amount) as amount')
                                ->groupBy('payment_method')
                                ->get(),
            'by_type' => $query->selectRaw('payment_type, COUNT(*) as count, SUM(payment_amount) as amount')
                              ->groupBy('payment_type')
                              ->get(),
        ];
    }

    /**
     * 获取支付报告
     */
    public function getPaymentReports(array $params): array
    {
        $startDate = $params['start_date'];
        $endDate = $params['end_date'];

        $query = PaymentRecord::whereBetween('created_at', [$startDate, $endDate]);

        // 支付方式过滤
        if (!empty($params['payment_method'])) {
            $query->where('payment_method', $params['payment_method']);
        }

        // 支付类型过滤
        if (!empty($params['payment_type'])) {
            $query->where('payment_type', $params['payment_type']);
        }

        $dailyReports = $query->selectRaw("
            DATE(created_at) as date,
            COUNT(*) as payment_count,
            SUM(payment_amount) as total_amount,
            SUM(CASE WHEN status = 'success' THEN payment_amount ELSE 0 END) as success_amount,
            COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count
        ")
        ->groupByRaw('DATE(created_at)')
        ->orderByRaw('DATE(created_at)')
        ->get();

        // 创建新的查询实例来获取汇总数据
        $summaryQuery = PaymentRecord::whereBetween('created_at', [$startDate, $endDate]);

        // 应用相同的过滤条件
        if (!empty($params['payment_method'])) {
            $summaryQuery->where('payment_method', $params['payment_method']);
        }
        if (!empty($params['payment_type'])) {
            $summaryQuery->where('payment_type', $params['payment_type']);
        }

        $totalCount = $summaryQuery->count();

        return [
            'daily_reports' => $dailyReports,
            'summary' => [
                'total_payments' => $totalCount,
                'total_amount' => $summaryQuery->sum('payment_amount'),
                'success_rate' => $totalCount > 0 ? ($summaryQuery->where('status', 'success')->count() / $totalCount * 100) : 0,
            ]
        ];
    }

    /**
     * 重试支付
     */
    public function retryPayment(int $paymentRecordId): PaymentRecord
    {
        $paymentRecord = PaymentRecord::findOrFail($paymentRecordId);
        
        if ($paymentRecord->status !== 'failed') {
            throw new Exception('只有失败的支付记录才能重试');
        }

        // 更新状态为处理中
        $paymentRecord->update(['status' => PaymentRecord::STATUS_PENDING]);

        // 这里可以添加实际的重试逻辑
        // 例如重新调用支付API等

        return $paymentRecord;
    }

    /**
     * 取消支付
     */
    public function cancelPayment(int $paymentRecordId): PaymentRecord
    {
        $paymentRecord = PaymentRecord::findOrFail($paymentRecordId);
        
        if (!in_array($paymentRecord->status, ['pending', 'processing'])) {
            throw new Exception('只有待处理或处理中的支付记录才能取消');
        }

        $paymentRecord->update([
            'status' => 'cancelled',
            'cancelled_at' => now()
        ]);

        return $paymentRecord;
    }

    /**
     * 导出支付记录
     */
    public function exportPaymentRecords(array $params): array
    {
        $query = PaymentRecord::with(['bill', 'bill.user']);

        // 应用相同的过滤条件
        if (!empty($params['payment_method'])) {
            $query->where('payment_method', $params['payment_method']);
        }
        if (!empty($params['start_date'])) {
            $query->whereDate('created_at', '>=', $params['start_date']);
        }
        if (!empty($params['end_date'])) {
            $query->whereDate('created_at', '<=', $params['end_date']);
        }

        $records = $query->orderBy('created_at', 'desc')->get();

        return [
            'data' => $records->map(function ($record) {
                return [
                    '支付单号' => $record->payment_no,
                    '账单号' => $record->bill->bill_no ?? '',
                    '用户名' => $record->bill->user->name ?? '',
                    '支付方式' => $record->payment_method,
                    '支付类型' => $record->payment_type,
                    '支付金额' => $record->payment_amount,
                    '状态' => $record->status,
                    '支付时间' => $record->payment_time?->format('Y-m-d H:i:s'),
                    '创建时间' => $record->created_at->format('Y-m-d H:i:s'),
                ];
            }),
            'filename' => '支付记录_' . now()->format('Y-m-d_H-i-s') . '.xlsx'
        ];
    }

    /**
     * 支付账单（简化接口）
     */
    public function payBill(int $billId, array $params): array
    {
        $bill = Bill::findOrFail($billId);

        // 🔥 修复：支持前端PaymentModal发送的数据格式
        $paymentData = [
            'payment_method' => $params['payment_method'],
            'payment_amount' => $params['payment_amount'] ?? $params['amount'] ?? 0,
            'balance_used' => $params['balance_used'] ?? 0,
            'transaction_id' => $params['transaction_id'] ?? null,
            'notes' => $params['notes'] ?? '',
            'payment_details' => $params['payment_details'] ?? [],
        ];

        Log::info('BillingService::payBill 处理支付', [
            'bill_id' => $billId,
            'original_params' => $params,
            'processed_data' => $paymentData
        ]);

        $paymentRecord = $this->processPayment($bill, $paymentData);

        return [
            'payment_record' => $paymentRecord,
            'bill' => $bill->fresh()
        ];
    }



    /**
     * 取消账单（简化接口）
     */
    public function cancelBillById(int $billId): array
    {
        $bill = Bill::findOrFail($billId);
        $this->cancelBill($bill, '用户取消');

        return [
            'bill' => $bill->fresh()
        ];
    }

    /**
     * 处理微信支付通知回调
     */
    public function handleWechatPaymentNotify(array $notifyData): array
    {
        return DB::transaction(function () use ($notifyData) {
            // 查找对应的支付记录
            $paymentRecord = PaymentRecord::where('payment_no', $notifyData['out_trade_no'])->first();
            
            if (!$paymentRecord) {
                throw new Exception('未找到对应的支付记录: ' . $notifyData['out_trade_no']);
            }

            // 检查是否已经处理过
            if ($paymentRecord->status === PaymentRecord::STATUS_SUCCESS) {
                Log::info('支付记录已处理，跳过重复通知', [
                    'payment_record_id' => $paymentRecord->id,
                    'out_trade_no' => $notifyData['out_trade_no']
                ]);
                return ['code' => 'SUCCESS', 'message' => '已处理'];
            }

            // 验证支付结果
            if ($notifyData['result_code'] !== 'SUCCESS') {
                // 支付失败
                $paymentRecord->update([
                    'status' => PaymentRecord::STATUS_FAILED,
                    'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                        'wechat_notify' => $notifyData,
                        'failed_reason' => $notifyData['err_code_des'] ?? '支付失败',
                        'failed_at' => now()->toISOString(),
                    ]),
                ]);

                Log::error('微信支付失败通知', [
                    'payment_record_id' => $paymentRecord->id,
                    'error_code' => $notifyData['err_code'] ?? '',
                    'error_desc' => $notifyData['err_code_des'] ?? '',
                ]);

                return ['code' => 'SUCCESS', 'message' => '支付失败已记录'];
            }

            // 支付成功，更新支付记录
            $paymentRecord->update([
                'status' => PaymentRecord::STATUS_SUCCESS,
                'transaction_id' => $notifyData['transaction_id'] ?? null,
                'confirmed_at' => now(),
                'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                    'wechat_notify' => $notifyData,
                    'transaction_id' => $notifyData['transaction_id'] ?? null,
                    'paid_at' => $notifyData['time_end'] ?? now()->toISOString(),
                ]),
            ]);

            // 更新微信支付记录
            $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('out_trade_no', $notifyData['out_trade_no'])->first();
            if ($wechatPayment) {
                $wechatPayment->update([
                    'transaction_id' => $notifyData['transaction_id'] ?? null,
                    'trade_state' => 'SUCCESS',
                    'pay_time' => now(),
                    'notify_data' => $notifyData,
                ]);
            }

            // 更新账单状态
            $bill = $paymentRecord->bill;
            if ($bill) {
                $bill->updateAmounts();
                $this->syncOrderPaymentStatus($bill);

                // 🔥 新增：微信订单更正补款成功后的特殊处理
                if ($bill->order &&
                    $bill->order->payment_method === 'wechat' &&
                    $bill->order->correction_status === 'confirmed' &&
                    $paymentRecord->payment_type === 'supplement') {

                    Log::info('微信订单更正补款成功', [
                        'bill_id' => $bill->id,
                        'order_id' => $bill->order->id,
                        'supplement_amount' => $paymentRecord->payment_amount,
                        'bill_status' => $bill->status,
                        'payment_status' => $bill->payment_status
                    ]);

                    // 可以在这里添加补款成功后的额外处理逻辑
                    // 比如发送通知、更新订单状态等
                }

                Log::info('微信支付成功，账单状态已更新', [
                    'bill_id' => $bill->id,
                    'bill_no' => $bill->bill_no,
                    'payment_record_id' => $paymentRecord->id,
                    'payment_amount' => $paymentRecord->payment_amount,
                    'bill_payment_status' => $bill->fresh()->payment_status,
                ]);
            }

            return ['code' => 'SUCCESS', 'message' => '处理成功'];
        });
    }

    /**
     * 🔥 新增：为在线支付订单创建原始支付记录
     */
    private function createInitialPaymentRecord(Bill $bill, Order $order, ?int $operatedBy): PaymentRecord
    {
        Log::info('为在线支付订单创建原始支付记录', [
            'bill_id' => $bill->id,
            'order_id' => $order->id,
            'payment_method' => $order->payment_method,
            'amount' => $order->total
        ]);

        $paymentRecord = PaymentRecord::create([
            'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
            'payment_method' => $order->payment_method,
            'original_payment_method' => $order->payment_method,
            'payment_amount' => $order->total,
            'payment_type' => PaymentRecord::TYPE_FULL,
            'payment_scenario' => PaymentRecord::SCENARIO_INITIAL_PAYMENT,
            'count_as_paid' => true, // 🔥 关键：计入已付金额
            'status' => PaymentRecord::STATUS_SUCCESS,
            'payment_time' => $order->created_at, // 使用订单创建时间作为支付时间
            'confirmed_at' => now(),
            'received_by' => $operatedBy,
            'notes' => "在线支付订单原始支付记录，订单号：{$order->order_no}",
            'payment_context' => [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'created_for' => 'initial_payment_record',
                'original_payment_time' => $order->created_at,
                'reason' => '为无修改确认的在线支付订单创建原始支付记录'
            ]
        ]);

        // 更新账单金额状态
        $bill->updateAmounts();
        
        Log::info('原始支付记录创建成功', [
            'payment_record_id' => $paymentRecord->id,
            'bill_paid_amount' => $bill->fresh()->paid_amount,
            'bill_payment_status' => $bill->fresh()->payment_status
        ]);

        return $paymentRecord;
    }

    /**
     * 🔥 新增：处理货到付款订单更正收款
     */
    public function processCodOrderCorrectionPayment($correction, array $paymentData, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $paymentData, $operatedBy) {
            Log::info('账单系统处理货到付款订单更正收款', [
                'order_id' => $correction->order_id,
                'payment_data' => $paymentData,
                'operator_id' => $operatedBy
            ]);

            // 1. 确保订单有账单
            $bill = $this->ensureOrderHasBill($correction->order);
            
            // 2. 处理实际收款
            $this->processCodActualPayment($correction, $bill, $paymentData, $operatedBy);
            
            Log::info('货到付款订单更正收款处理完成', [
                'bill_id' => $bill->id,
                'payment_method' => $paymentData['payment_method'],

            ]);
        });
    }

    /**
     * 🔥 修复：处理货到付款实际收款
     */
    private function processCodActualPayment($correction, $bill, array $paymentData, int $operatedBy): void
    {
        $actualReceived = $paymentData['actual_received_amount'] ?? $correction->corrected_total;
        $discountAmount = $paymentData['discount_amount'] ?? 0;
        $extraAmount = $paymentData['extra_amount'] ?? 0;
        $paymentMethod = $paymentData['payment_method'];

        Log::info('处理货到付款实际收款', [
            'bill_id' => $bill->id,
            'original_bill_amount' => $bill->original_amount,
            'corrected_total' => $correction->corrected_total,
            'actual_received' => $actualReceived,
            'discount_amount' => $discountAmount,
            'extra_amount' => $extraAmount,
            'payment_method' => $paymentMethod
        ]);

        // 🔥 修复：计算需要调整的账单金额
        $adjustmentAmount = $correction->corrected_total - $bill->original_amount;

        // 1. 如果有金额差异，创建账单调整记录
        if (abs($adjustmentAmount) > 0.01) {
            $adjustment = BillAdjustment::create([
                'bill_id' => $bill->id,
                'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                'adjustment_type' => $adjustmentAmount > 0 ? BillAdjustment::TYPE_PRICE_INCREASE : BillAdjustment::TYPE_PRICE_DECREASE,
                'adjustment_amount' => $adjustmentAmount,
                'reason' => '货到付款订单更正金额调整',
                'description' => "订单更正实际收款，调整账单金额：原始 {$bill->original_amount} 元 → 更正后 {$correction->corrected_total} 元，更正单号：{$correction->correction_no}",
                'operator_id' => $operatedBy,
                'status' => BillAdjustment::STATUS_APPROVED,
                'applied_at' => now()
            ]);

            Log::info('创建账单调整记录（实际收款）', [
                'adjustment_id' => $adjustment->id,
                'adjustment_amount' => $adjustmentAmount,
                'from_amount' => $bill->original_amount,
                'to_amount' => $correction->corrected_total
            ]);
        }

        // 2. 🔥 修复：计算实际应该收到的净金额（扣除抹零优惠）
        $netReceivedAmount = $actualReceived - $discountAmount;
        
        // 创建主收款记录，正确设置新字段
        $paymentRecord = PaymentRecord::create([
            'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
            'payment_method' => $paymentMethod,
            'payment_amount' => $netReceivedAmount, // 🔥 关键：使用净收款金额
            'payment_type' => $this->determinePaymentType($bill, $netReceivedAmount),
            'status' => PaymentRecord::STATUS_SUCCESS,
            'payment_time' => now(),
            'confirmed_at' => now(),
            'received_by' => $operatedBy,
            'notes' => "货到付款订单更正实际收款：{$correction->correction_no}",
            'payment_details' => [
                'correction_no' => $correction->correction_no,
                'original_total' => $correction->original_total,
                'corrected_total' => $correction->corrected_total,
                'actual_received' => $actualReceived,
                'discount_amount' => $discountAmount,
                'net_received' => $netReceivedAmount,
                'extra_amount' => $extraAmount,
                'discount_reason' => $paymentData['discount_reason'] ?? null,
                'cod_correction_payment' => true,
                'actual_payment' => true // 标记为实际收款
            ]
        ]);

        // 3. 如果有抹零优惠，创建单独的调整记录
        if ($discountAmount > 0) {
            $discountAdjustment = BillAdjustment::create([
                'bill_id' => $bill->id,
                'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                'adjustment_type' => BillAdjustment::TYPE_DISCOUNT,
                'adjustment_amount' => -$discountAmount,
                'reason' => '抹零优惠',
                'description' => $paymentData['discount_reason'] ?? "抹零优惠：{$discountAmount}元",
                'operator_id' => $operatedBy,
                'status' => BillAdjustment::STATUS_APPROVED,
                'applied_at' => now()
            ]);

            Log::info('创建抹零优惠调整记录', [
                'adjustment_id' => $discountAdjustment->id,
                'discount_amount' => $discountAmount
            ]);
        }

        // 4. 如果有多收金额，创建额外收款记录
        if ($extraAmount > 0) {
            $extraPaymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $paymentMethod,
                'payment_amount' => $extraAmount,
                'payment_type' => PaymentRecord::TYPE_SUPPLEMENT,
                'status' => PaymentRecord::STATUS_SUCCESS,
                'payment_time' => now(),
                'confirmed_at' => now(),
                'received_by' => $operatedBy,
                'notes' => "货到付款多收金额：{$correction->correction_no}",
                'payment_details' => [
                    'extra_payment' => true,
                    'related_payment_id' => $paymentRecord->id,
                    'extra_reason' => '客户多付款项'
                ]
            ]);

            Log::info('创建多收金额记录', [
                'extra_payment_id' => $extraPaymentRecord->id,
                'extra_amount' => $extraAmount
            ]);
        }

        // 5. 🔥 关键：更新账单状态，确保正确反映付款情况
        // 重新计算账单的调整金额
        $totalAdjustment = $adjustmentAmount - $discountAmount;
        
        $bill->update([
            'adjustment_amount' => ($bill->adjustment_amount ?? 0) + $totalAdjustment,
            'final_amount' => $correction->corrected_total - $discountAmount, // 最终应收（扣除抹零）
        ]);

        // 6. 重新计算账单的付款状态
        $bill->updateAmounts(); // 这会重新计算 paid_amount, pending_amount 和 payment_status
        $this->syncOrderPaymentStatus($bill);

        Log::info('货到付款实际收款处理完成', [
            'bill_id' => $bill->id,
            'payment_record_id' => $paymentRecord->id,
            'net_received_amount' => $netReceivedAmount,
            'discount_amount' => $discountAmount,
            'extra_amount' => $extraAmount,
            'final_bill_amount' => $bill->fresh()->final_amount,
            'paid_amount' => $bill->fresh()->paid_amount,
            'pending_amount' => $bill->fresh()->pending_amount,
            'payment_status' => $bill->fresh()->payment_status,
            'payment_method' => $paymentMethod
        ]);
    }



    /**
     * 🔥 新增：处理微信支付订单更正补款（选择支付方式）
     */
    public function processWechatOrderCorrectionSupplement($correction, array $paymentData, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $paymentData, $operatedBy) {
            $supplementAmount = $correction->difference_amount;
            $paymentMethodForSupplement = $paymentData['payment_method_for_supplement'];

            Log::info('账单系统处理微信订单更正补款（选择支付方式）', [
                'order_id' => $correction->order_id,
                'supplement_amount' => $supplementAmount,
                'payment_method_for_supplement' => $paymentMethodForSupplement,
                'operator_id' => $operatedBy
            ]);

            // 1. 确保订单有账单
            $bill = $this->ensureOrderHasBill($correction->order);
            
            // 2. 创建账单调整记录
            $adjustment = BillAdjustment::create([
                'bill_id' => $bill->id,
                'adjustment_no' => BillAdjustment::generateAdjustmentNo(),
                'adjustment_type' => BillAdjustment::TYPE_MANUAL_ADJUST,
                'adjustment_amount' => $supplementAmount,
                'reason' => '微信订单更正补款',
                'description' => "微信订单更正补款，使用{$paymentMethodForSupplement}收取差额：{$supplementAmount}元，更正单号：{$correction->correction_no}",
                'operator_id' => $operatedBy,
                'status' => BillAdjustment::STATUS_APPROVED,
                'applied_at' => now()
            ]);

            // 3. 创建补款记录，正确设置新字段
            $paymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                // 🔥 新增
                'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $paymentMethodForSupplement,
                'original_payment_method' => 'wechat', // 🔥 新增：原订单支付方式
                'payment_amount' => $supplementAmount,
                'payment_type' => PaymentRecord::TYPE_SUPPLEMENT,
                'payment_scenario' => PaymentRecord::SCENARIO_SUPPLEMENT, // 🔥 新增：补款场景
                'count_as_paid' => true, // 🔥 关键：补款计入已付金额
                'status' => $paymentMethodForSupplement === 'wechat' ? PaymentRecord::STATUS_PENDING : PaymentRecord::STATUS_SUCCESS,
                'payment_time' => now(),
                'confirmed_at' => $paymentMethodForSupplement !== 'wechat' ? now() : null,
                'received_by' => $operatedBy,
                'notes' => "微信订单更正补款（{$paymentMethodForSupplement}）：{$correction->correction_no}",
                'payment_details' => [
                    'correction_no' => $correction->correction_no,
                    'original_payment_method' => 'wechat',
                    'supplement_payment_method' => $paymentMethodForSupplement,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total,
                    'supplement_reason' => '微信订单更正金额增加'
                ],
                'payment_context' => [ // 🔥 新增：支付上下文
                    'order_id' => $correction->order_id,
                    'order_payment_method' => 'wechat',
                    'correction_scenario' => 'wechat_supplement',
                    'supplement_method' => $paymentMethodForSupplement,
                    'supplement_reason' => '微信订单更正金额增加',
                    'original_amount' => $correction->original_total,
                    'supplement_amount' => $supplementAmount,
                    'payment_method_chosen' => true, // 标识用户选择了补款方式
                    'operator_id' => $operatedBy,
                ]
            ]);

            // 4. 根据补款支付方式执行相应操作
            if ($paymentMethodForSupplement === 'wechat') {
                // 微信补款：生成支付链接
                $this->executeWechatSupplement($paymentRecord, $correction);
            } elseif ($paymentMethodForSupplement === 'cash') {
                // 现金补款：直接标记成功
                $paymentRecord->update([
                    'status' => PaymentRecord::STATUS_SUCCESS,
                    'confirmed_at' => now(),
                    'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                        'cash_supplement' => true,
                        'collected_at' => now()->toISOString()
                    ])
                ]);
            } elseif ($paymentMethodForSupplement === 'alipay') {
                // 支付宝补款：生成支付链接（待实现）
                $paymentRecord->update([
                    'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                        'alipay_supplement' => true,
                        'payment_pending' => true
                    ])
                ]);
            } elseif ($paymentMethodForSupplement === 'bank_transfer') {
                // 银行转账：保持待确认状态
                $paymentRecord->update([
                    'payment_details' => array_merge($paymentRecord->payment_details ?? [], [
                        'bank_transfer_supplement' => true,
                        'transfer_pending' => true
                    ])
                ]);
            }
            
            // 5. 更新账单状态
            $bill->updateAmounts();
            
            Log::info('微信订单更正补款（选择支付方式）处理完成', [
                'bill_id' => $bill->id,
                'adjustment_id' => $adjustment->id,
                'payment_record_id' => $paymentRecord->id,
                'supplement_amount' => $supplementAmount,
                'payment_method' => $paymentMethodForSupplement
            ]);
        });
    }

    /**
     * 🔥 新增：检查并自动创建累计账单
     */
    private function checkAndCreateConsolidatedBill(User $user): void
    {
        try {
            // 获取用户所有待付款的账单（排除已合并的）
            $pendingBills = Bill::where('user_id', $user->id)
                ->where('bill_type', Bill::TYPE_ORDER)
                ->whereIn('status', [Bill::STATUS_PENDING, Bill::STATUS_PARTIAL_PAID])
                ->whereNull('parent_bill_id') // 排除已经是子账单的
                ->where('order_id', '!=', null) // 只处理有关联订单的账单
                ->get();

            // 如果有2个或以上待付款账单，自动创建累计账单
            if ($pendingBills->count() >= 2) {
                Log::info('检测到用户有多个待付款账单，自动创建累计账单', [
                    'user_id' => $user->id,
                    'pending_bills_count' => $pendingBills->count(),
                    'bill_ids' => $pendingBills->pluck('id')->toArray()
                ]);

                // 调用累计账单服务
                $consolidatedService = app(\App\Billing\Services\ConsolidatedBillingService::class);
                $consolidatedBill = $consolidatedService->createConsolidatedBillFromExistingBills(
                    $user, 
                    $pendingBills->pluck('id')->toArray(),
                    [
                        'created_by' => null, // 系统自动创建
                        'notes' => "系统自动创建累计账单，包含 {$pendingBills->count()} 个账单",
                        'metadata' => [
                            'consolidation_type' => 'auto', // 自动创建
                            'trigger_reason' => 'multiple_pending_bills',
                            'auto_created_at' => now()->toISOString(),
                        ]
                    ]
                );

                Log::info('自动累计账单创建成功', [
                    'user_id' => $user->id,
                    'consolidated_bill_id' => $consolidatedBill->id,
                    'consolidated_bill_no' => $consolidatedBill->bill_no,
                    'original_bills_count' => $pendingBills->count(),
                    'total_amount' => $consolidatedBill->final_amount
                ]);
            }

        } catch (\Exception $e) {
            // 累计账单创建失败不影响主流程
            Log::error('自动创建累计账单失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 🔥 完全重写：处理订单更正反审/撤回 - 正确的销毁账单逻辑
     */
    public function handleCorrectionReversal(OrderCorrection $correction, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $operatedBy) {
            Log::info('🔄 开始处理订单更正反审/撤回 - 正确的账单销毁逻辑', [
                'correction_no' => $correction->correction_no,
                'order_id' => $correction->order_id,
                'operator_id' => $operatedBy,
                'correction_status' => $correction->status
            ]);

            // 1. 查找相关账单
            $bill = Bill::where('order_id', $correction->order_id)->first();
            if (!$bill) {
                Log::info('✅ 未找到相关账单，无需处理', [
                    'order_id' => $correction->order_id,
                    'correction_id' => $correction->id
                ]);
                return;
            }

            Log::info('📋 找到需要处理的账单', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'bill_status' => $bill->status,
                'payment_status' => $bill->payment_status,
                'original_amount' => $bill->original_amount,
                'final_amount' => $bill->final_amount,
                'paid_amount' => $bill->paid_amount,
                'pending_amount' => $bill->pending_amount
            ]);

            // 2. 🔥 分析账单状态，决定处理策略
            $this->analyzeAndProcessBillReversal($bill, $correction, $operatedBy);

            Log::info('✅ 订单更正反审/撤回的账单处理完成', [
                'order_id' => $correction->order_id,
                'bill_id' => $bill->id,
                'operator_id' => $operatedBy
            ]);
        });
    }

    /**
     * 🔥 核心方法：分析并处理账单撤销
     */
    private function analyzeAndProcessBillReversal(Bill $bill, OrderCorrection $correction, int $operatedBy): void
    {
        // 获取所有有效的支付记录
        $activePayments = $bill->paymentRecords()
            ->where('status', PaymentRecord::STATUS_SUCCESS)
            ->where('count_as_paid', true)
            ->where('payment_amount', '>', 0)
            ->get();

        $totalRealPayments = $activePayments->sum('payment_amount');
        


        // 获取所有账单调整记录
        $activeAdjustments = $bill->adjustments()
            ->where('status', BillAdjustment::STATUS_APPROVED)
            ->get();

        Log::info('📊 账单状态分析', [
            'bill_id' => $bill->id,
            'total_real_payments' => $totalRealPayments,
            'active_payments_count' => $activePayments->count(),

            'active_adjustments_count' => $activeAdjustments->count(),
            'bill_original_amount' => $bill->original_amount,
            'bill_final_amount' => $bill->final_amount
        ]);

        // 场景1：无实际收款 - 直接标记撤销
        if ($totalRealPayments == 0) {
            Log::info('🎯 场景1：无实际收款，直接标记撤销', [
                'bill_id' => $bill->id
            ]);
            $this->processPureBillReversal($bill, $correction, $operatedBy, $activeAdjustments);
            return;
        }

        // 🔥 场景2：有实际收款 - 需要处理退款
        if ($totalRealPayments > 0) {
            Log::info('🎯 场景2：存在实际收款，需要处理退款', [
                'bill_id' => $bill->id,
                'total_real_payments' => $totalRealPayments
            ]);
            $this->processRealPaymentBillReversal($bill, $correction, $operatedBy, $activePayments, $activeAdjustments);
            return;
        }
    }

    /**
     * 处理无实际收款账单的撤销
     */
    private function processPureBillReversal(Bill $bill, OrderCorrection $correction, int $operatedBy, $activeAdjustments): void
    {
        Log::info('🔄 开始处理无实际收款账单撤销', [
            'bill_id' => $bill->id,
            'bill_no' => $bill->bill_no,
            'adjustments_count' => $activeAdjustments->count(),
            'bill_original_amount' => $bill->original_amount,
            'bill_final_amount' => $bill->final_amount,
            'bill_paid_amount' => $bill->paid_amount,
        ]);

        // 在事务外部记录账单信息
        $billId = $bill->id;
        $billNo = $bill->bill_no;
        $revokedAdjustmentCount = 0;

        try {
            // 🔥 使用数据库事务确保所有操作的原子性
            \DB::transaction(function () use ($bill, $correction, $operatedBy, $activeAdjustments, &$revokedAdjustmentCount) {

                // 1. 撤销所有账单调整


                foreach ($activeAdjustments as $adjustment) {
                    try {
                        $this->revokeAdjustment($adjustment, $correction, $operatedBy);
                        $revokedAdjustmentCount++;

                        Log::info('✅ 账单调整撤销成功', [
                            'adjustment_id' => $adjustment->id,
                            'amount' => $adjustment->amount,
                        ]);
                    } catch (\Exception $e) {
                        Log::error('❌ 账单调整撤销失败', [
                            'adjustment_id' => $adjustment->id,
                            'error' => $e->getMessage(),
                        ]);
                        throw $e;
                    }
                }

                // 3. 将账单标记为撤销状态（实际是删除）
                $this->markBillAsRevoked($bill, $correction, $operatedBy, 'no_payment');

                Log::info('✅ 账单撤销事务完成', [
                    'bill_id' => $bill->id,
                    'revoked_adjustments' => $revokedAdjustmentCount,
                ]);
            });

            Log::info('✅ 账单撤销完成', [
                'bill_id' => $billId,
                'bill_no' => $billNo,
                'operation' => 'deleted',
                'revoked_adjustments' => $revokedAdjustmentCount,
            ]);

        } catch (\Exception $e) {
            Log::error('❌ 账单撤销失败', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new \Exception("账单撤销失败：{$e->getMessage()}");
        }
    }

    /**
     * 🔥 处理有实际收款账单的撤销
     */
    private function processRealPaymentBillReversal(Bill $bill, OrderCorrection $correction, int $operatedBy, $activePayments, $activeAdjustments): void
    {
        Log::info('🔄 开始处理有实际收款账单撤销', [
            'bill_id' => $bill->id,
            'active_payments_count' => $activePayments->count(),
            'total_amount' => $activePayments->sum('payment_amount')
        ]);

        // 1. 为每个实际收款创建对应的退款记录
        foreach ($activePayments as $payment) {
            $this->createRefundForPayment($payment, $correction, $operatedBy);
        }

        // 2. 撤销所有账单调整
        foreach ($activeAdjustments as $adjustment) {
            $this->revokeAdjustment($adjustment, $correction, $operatedBy);
        }

        // 3. 将账单标记为撤销状态
        $this->markBillAsRevoked($bill, $correction, $operatedBy, 'with_refund');

        Log::info('✅ 有实际收款账单撤销完成', [
            'bill_id' => $bill->id,
            'processed_refunds' => $activePayments->count(),
            'revoked_adjustments' => $activeAdjustments->count()
        ]);
    }



    /**
     * 🔥 为实际收款创建退款记录
     */
    private function createRefundForPayment(PaymentRecord $payment, OrderCorrection $correction, int $operatedBy): void
    {
        // 创建退款记录
        $refundRecord = PaymentRecord::create([
            'bill_id' => $payment->bill_id,
            'payment_no' => PaymentRecord::generatePaymentNo(),
            'payment_method' => $payment->payment_method, // 使用原支付方式退款
            'original_payment_method' => $payment->payment_method,
            'payment_amount' => -$payment->payment_amount, // 负数表示退款
            'payment_type' => PaymentRecord::TYPE_REFUND,
            'payment_scenario' => PaymentRecord::SCENARIO_SUPPLEMENT, // 🔥 修复：使用现有的枚举值
            'status' => PaymentRecord::STATUS_SUCCESS,
            'count_as_paid' => true, // 退款计入支付记录
            'payment_time' => now(),
            'confirmed_at' => now(),
            'received_by' => $operatedBy,
            'notes' => "订单更正反审/撤回自动退款 - 原支付记录: {$payment->payment_no}",
            'payment_context' => [
                'refund_type' => 'correction_reversal',
                'original_payment_id' => $payment->id,
                'original_payment_no' => $payment->payment_no,
                'correction_no' => $correction->correction_no,
                'refund_reason' => '订单更正反审/撤回',
                'refund_amount' => $payment->payment_amount,
                'refund_method' => $payment->payment_method,
                'auto_processed' => true,
                'processed_at' => now()->toISOString(),
                'processed_by' => $operatedBy
            ]
        ]);

        // 同时标记原支付记录为已退款
        $payment->update([
            'status' => PaymentRecord::STATUS_REFUNDED,
            'count_as_paid' => false, // 已退款的不计入已付金额
            'notes' => ($payment->notes ?? '') . "\n[已退款] 订单更正反审/撤回，对应退款记录: {$refundRecord->payment_no} - " . now()->format('Y-m-d H:i:s'),
            'payment_context' => array_merge($payment->payment_context ?? [], [
                'refunded_due_to_correction_reversal' => true,
                'refunded_at' => now()->toISOString(),
                'refund_record_id' => $refundRecord->id,
                'refund_record_no' => $refundRecord->payment_no
            ])
        ]);

        Log::info('💰 创建退款记录完成', [
            'original_payment_id' => $payment->id,
            'original_payment_no' => $payment->payment_no,
            'refund_record_id' => $refundRecord->id,
            'refund_record_no' => $refundRecord->payment_no,
            'refund_amount' => $payment->payment_amount,
            'refund_method' => $payment->payment_method
        ]);
    }

    /**
     * 🔥 撤销账单调整记录
     */
    private function revokeAdjustment(BillAdjustment $adjustment, OrderCorrection $correction, int $operatedBy): void
    {
        $originalDescription = $adjustment->description ?? '';
        
        $adjustment->update([
            'status' => BillAdjustment::STATUS_REJECTED,
            'description' => $originalDescription . "\n[撤销] 订单更正反审/撤回，调整记录已撤销 - " . now()->format('Y-m-d H:i:s'),
        ]);

        Log::info('📝 账单调整记录已撤销', [
            'adjustment_id' => $adjustment->id,
            'adjustment_no' => $adjustment->adjustment_no,
            'adjustment_amount' => $adjustment->adjustment_amount,
            'reason' => $adjustment->reason,
            'correction_id' => $correction->id
        ]);
    }

    /**
     * 🔥 直接删除账单（反审时彻底清理）- 基于实际表结构
     */
    private function markBillAsRevoked(Bill $bill, OrderCorrection $correction, int $operatedBy, string $revokeType): void
    {
        $originalStatus = $bill->status;
        $originalPaymentStatus = $bill->payment_status;

        Log::info('🔄 开始删除账单（反审彻底清理）', [
            'bill_id' => $bill->id,
            'bill_no' => $bill->bill_no,
            'original_status' => $originalStatus,
            'original_payment_status' => $originalPaymentStatus,
            'revoke_type' => $revokeType,
            'transaction_level' => \DB::transactionLevel(),
        ]);

        try {
            // 🔥 使用数据库事务确保原子性
            \DB::transaction(function () use ($bill, $correction, $operatedBy, $revokeType) {

                $billId = $bill->id;
                $billNo = $bill->bill_no;
                $orderId = $bill->order_id;

                // 1. 记录删除前的账单信息（用于审计）
                $billInfo = [
                    'bill_id' => $billId,
                    'bill_no' => $billNo,
                    'order_id' => $orderId,
                    'original_amount' => $bill->original_amount,
                    'final_amount' => $bill->final_amount,
                    'paid_amount' => $bill->paid_amount,
                    'status' => $bill->status,
                    'payment_status' => $bill->payment_status,
                    'deleted_due_to_correction_reversal' => true,
                    'deleted_at' => now()->toISOString(),
                    'deleted_by' => $operatedBy,
                    'correction_no' => $correction->correction_no,
                    'revoke_type' => $revokeType,
                    'delete_reason' => '订单更正反审/撤回',
                ];

                // 2. 删除账单调整记录（基于实际模型关系）
                $deletedAdjustments = 0;
                try {
                    $deletedAdjustments = $bill->adjustments()->delete();
                    Log::info('🗑️ 删除账单调整记录', [
                        'bill_id' => $billId,
                        'deleted_adjustments' => $deletedAdjustments,
                    ]);
                } catch (\Exception $e) {
                    Log::warning('⚠️ 删除账单调整记录失败', [
                        'bill_id' => $billId,
                        'error' => $e->getMessage(),
                    ]);
                }

                // 3. 删除支付记录（基于实际模型关系）
                $deletedPaymentRecords = 0;
                try {
                    $deletedPaymentRecords = $bill->paymentRecords()->delete();
                    Log::info('🗑️ 删除支付记录', [
                        'bill_id' => $billId,
                        'deleted_payment_records' => $deletedPaymentRecords,
                    ]);
                } catch (\Exception $e) {
                    Log::warning('⚠️ 删除支付记录失败', [
                        'bill_id' => $billId,
                        'error' => $e->getMessage(),
                    ]);
                }

                // 4. 删除账单明细（基于实际模型关系）
                $deletedBillItems = 0;
                try {
                    $deletedBillItems = $bill->items()->delete();
                    Log::info('🗑️ 删除账单明细', [
                        'bill_id' => $billId,
                        'deleted_bill_items' => $deletedBillItems,
                    ]);
                } catch (\Exception $e) {
                    Log::warning('⚠️ 删除账单明细失败', [
                        'bill_id' => $billId,
                        'error' => $e->getMessage(),
                    ]);
                }

                // 5. 删除余额变动记录（如果存在）
                $deletedBalanceTransactions = 0;
                try {
                    if (method_exists($bill, 'balanceTransactions')) {
                        $deletedBalanceTransactions = $bill->balanceTransactions()->delete();
                        Log::info('🗑️ 删除余额变动记录', [
                            'bill_id' => $billId,
                            'deleted_balance_transactions' => $deletedBalanceTransactions,
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::warning('⚠️ 删除余额变动记录失败', [
                        'bill_id' => $billId,
                        'error' => $e->getMessage(),
                    ]);
                }

                // 6. 最后删除账单本身
                $deletedBill = $bill->delete();

                if (!$deletedBill) {
                    throw new \Exception('账单删除失败');
                }

                Log::info('🗑️ 账单删除成功', [
                    'bill_id' => $billId,
                    'bill_no' => $billNo,
                    'deleted_bill' => $deletedBill,
                ]);

                // 7. 清除订单的账单相关状态（只更新 updated_at）
                if ($orderId) {
                    $orderUpdateResult = \DB::table('orders')
                        ->where('id', $orderId)
                        ->update(['updated_at' => now()]);

                    Log::info('🔄 更新订单时间戳', [
                        'order_id' => $orderId,
                        'update_result' => $orderUpdateResult,
                    ]);
                }

                // 8. 记录删除操作的详细信息
                Log::info('📋 账单删除详情记录', $billInfo);
            });

            Log::info('✅ 账单删除完成', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'revoke_type' => $revokeType,
                'operation' => 'deleted',
            ]);

        } catch (\Exception $e) {
            Log::error('❌ 账单删除失败', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new \Exception("账单删除失败：{$e->getMessage()}");
        }
    }

    /*
    |--------------------------------------------------------------------------
    | 用户端专用业务逻辑方法
    |--------------------------------------------------------------------------
    | 以下方法专门为小程序用户端提供优化的业务逻辑
    */

    /**
     * 获取用户账单（包含更正信息）
     */
    public function getUserBillsWithCorrections(User $user, array $filters = [])
    {
        $query = Bill::where('user_id', $user->id)
            ->with([
                'items',
                'paymentRecords',
                'adjustments',
                'order.corrections' => function ($q) {
                    $q->latest();
                }
            ]);

        // 基础过滤
        if (isset($filters['bill_type'])) {
            $query->where('bill_type', $filters['bill_type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        // 更正过滤
        if (isset($filters['has_correction'])) {
            if ($filters['has_correction']) {
                $query->whereHas('order.corrections');
            } else {
                $query->whereDoesntHave('order.corrections');
            }
        }

        // 日期过滤
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * 获取用户账单详情
     */
    public function getUserBillDetail(User $user, int $billId): ?Bill
    {
        return Bill::where('user_id', $user->id)
            ->where('id', $billId)
            ->with([
                'items.product',
                'paymentRecords',
                'adjustments',
                'order.corrections.items.orderItem.product'
            ])
            ->first();
    }

    /**
     * 获取账单更正轨迹
     */
    public function getBillCorrectionTrail(int $billId): array
    {
        $bill = Bill::with('order.corrections.items.orderItem.product')->find($billId);

        if (!$bill || !$bill->order) {
            return [];
        }

        $corrections = $bill->order->corrections()->with('items.orderItem.product')->get();

        return $corrections->map(function ($correction) {
            return [
                'id' => $correction->id,
                'correction_no' => $correction->correction_no,
                'status' => $correction->status,
                'correction_type' => $correction->correction_type,
                'original_amount' => $correction->original_total_amount,
                'corrected_amount' => $correction->corrected_total_amount,
                'adjustment_amount' => $correction->corrected_total_amount - $correction->original_total_amount,
                'correction_reason' => $correction->correction_reason,
                'created_at' => $correction->created_at,
                'confirmed_at' => $correction->confirmed_at,
                'items_count' => $correction->items->count(),
                'changed_items_count' => $correction->items->filter(function ($item) {
                    return $item->corrected_total != $item->original_total;
                })->count(),
            ];
        })->toArray();
    }

    /**
     * 获取用户账单统计
     */
    public function getUserBillStatistics(User $user, array $params = []): array
    {
        $query = Bill::where('user_id', $user->id);

        // 时间范围过滤
        if (isset($params['date_from'])) {
            $query->whereDate('created_at', '>=', $params['date_from']);
        }

        if (isset($params['date_to'])) {
            $query->whereDate('created_at', '<=', $params['date_to']);
        }

        // 基础统计
        $totalBills = $query->count();
        $pendingAmount = $query->where('status', 'pending')->sum('final_amount');
        $paidAmount = $query->where('status', 'paid')->sum('paid_amount');
        $refundedAmount = $query->where('payment_status', 'refunded')->sum('paid_amount');
        $pendingBillsCount = $query->where('status', 'pending')->count();

        // 支付方式统计
        $paymentMethods = PaymentRecord::whereHas('bill', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->where('status', 'success')
        ->selectRaw('payment_method, COUNT(*) as count, SUM(payment_amount) as total_amount')
        ->groupBy('payment_method')
        ->get()
        ->mapWithKeys(function ($item) {
            return [$item->payment_method => [
                'count' => $item->count,
                'total_amount' => $item->total_amount,
            ]];
        })->toArray();

        // 月度趋势（最近6个月）
        $monthlyTrend = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $monthData = Bill::where('user_id', $user->id)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->selectRaw('
                    COUNT(*) as bills_count,
                    SUM(final_amount) as total_amount,
                    SUM(paid_amount) as paid_amount
                ')
                ->first();

            $monthlyTrend[] = [
                'month' => $month->format('Y-m'),
                'month_text' => $month->format('Y年m月'),
                'bills_count' => $monthData->bills_count ?? 0,
                'total_amount' => $monthData->total_amount ?? 0,
                'paid_amount' => $monthData->paid_amount ?? 0,
            ];
        }

        return [
            'total_bills' => $totalBills,
            'pending_amount' => $pendingAmount,
            'paid_amount' => $paidAmount,
            'refunded_amount' => $refundedAmount,
            'pending_bills_count' => $pendingBillsCount,
            'payment_methods' => $paymentMethods,
            'monthly_trend' => $monthlyTrend,
        ];
    }

    /**
     * 获取用户更正统计
     */
    public function getUserCorrectionStatistics(User $user, array $params = []): array
    {
        $query = OrderCorrection::whereHas('order', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        });

        // 时间范围过滤
        if (isset($params['date_from'])) {
            $query->whereDate('created_at', '>=', $params['date_from']);
        }

        if (isset($params['date_to'])) {
            $query->whereDate('created_at', '<=', $params['date_to']);
        }

        $totalCorrections = $query->count();
        $totalOrders = Order::where('user_id', $user->id)->count();
        $correctionRate = $totalOrders > 0 ? ($totalCorrections / $totalOrders) * 100 : 0;

        // 计算平均调整金额和总节省金额
        $corrections = $query->get();
        $totalAdjustment = $corrections->sum(function ($correction) {
            return $correction->corrected_total_amount - $correction->original_total_amount;
        });
        $averageAdjustment = $totalCorrections > 0 ? $totalAdjustment / $totalCorrections : 0;
        $totalSaved = $corrections->where('corrected_total_amount', '<', 'original_total_amount')
            ->sum(function ($correction) {
                return $correction->original_total_amount - $correction->corrected_total_amount;
            });

        // 最近更正记录
        $recentCorrections = $query->with('order')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($correction) {
                return [
                    'id' => $correction->id,
                    'order_no' => $correction->order->order_no,
                    'adjustment_amount' => $correction->corrected_total_amount - $correction->original_total_amount,
                    'created_at' => $correction->created_at,
                ];
            });

        return [
            'total_corrections' => $totalCorrections,
            'correction_rate' => round($correctionRate, 2),
            'average_adjustment' => round($averageAdjustment, 2),
            'total_saved' => round($totalSaved, 2),
            'recent_corrections' => $recentCorrections,
        ];
    }

    /**
     * 处理用户支付
     */
    public function processUserPayment(Bill $bill, array $params): array
    {
        $paymentMethod = $params['payment_method'];
        $paymentType = $params['payment_type'] ?? 'full';
        $amount = $paymentType === 'partial' ? $params['amount'] : $bill->final_amount - $bill->paid_amount;
        $remark = $params['remark'] ?? '';

        // 验证支付金额
        if ($amount <= 0 || $amount > ($bill->final_amount - $bill->paid_amount)) {
            throw new Exception('支付金额无效');
        }

        return DB::transaction(function () use ($bill, $paymentMethod, $amount, $remark) {
            // 创建支付记录
            $paymentRecord = PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => PaymentRecord::generatePaymentNo(),
                'payment_method' => $paymentMethod,
                'payment_type' => 'user_payment',
                'amount' => $amount,
                'status' => PaymentRecord::STATUS_PENDING,
                'remark' => $remark,
                'created_by' => $bill->user_id,
            ]);

            // 根据支付方式处理
            switch ($paymentMethod) {
                case 'wechat':
                    return $this->processWechatPaymentForUser($paymentRecord);
                case 'alipay':
                    return $this->processAlipayPaymentForUser($paymentRecord);
                case 'cash':
                case 'bank_transfer':
                    // 现金和银行转账需要人工确认
                    return [
                        'payment_id' => $paymentRecord->id,
                        'status' => 'pending_confirmation',
                        'message' => '支付记录已创建，等待确认',
                        'next_action' => 'wait_confirmation',
                    ];
                default:
                    throw new Exception('不支持的支付方式');
            }
        });
    }

    /**
     * 获取用户最近更正账单
     */
    public function getUserRecentCorrections(User $user, int $limit = 5)
    {
        return Bill::where('user_id', $user->id)
            ->whereHas('order.corrections')
            ->with(['order.corrections' => function ($q) {
                $q->latest();
            }])
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 获取用户待付款账单
     */
    public function getUserPendingBills(User $user, int $limit = 10, string $sort = 'due_date_asc')
    {
        $query = Bill::where('user_id', $user->id)
            ->where('status', 'pending')
            ->whereIn('payment_status', ['unpaid', 'partial'])
            ->with(['order', 'paymentRecords']);

        // 排序
        switch ($sort) {
            case 'amount_asc':
                $query->orderBy('final_amount', 'asc');
                break;
            case 'amount_desc':
                $query->orderBy('final_amount', 'desc');
                break;
            case 'due_date_desc':
                $query->orderBy('due_date', 'desc');
                break;
            case 'created_asc':
                $query->orderBy('created_at', 'asc');
                break;
            case 'created_desc':
                $query->orderBy('created_at', 'desc');
                break;
            default: // due_date_asc
                $query->orderBy('due_date', 'asc');
                break;
        }

        return $query->limit($limit)->get();
    }

    /**
     * 处理微信支付（用户端）
     */
    private function processWechatPaymentForUser(PaymentRecord $paymentRecord): array
    {
        // 这里应该调用微信支付API
        // 暂时返回模拟数据
        return [
            'payment_id' => $paymentRecord->id,
            'status' => 'pending_payment',
            'payment_url' => 'weixin://wxpay/bizpayurl?pr=' . base64_encode($paymentRecord->payment_no),
            'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            'expire_time' => now()->addMinutes(30),
            'message' => '请在30分钟内完成支付',
            'next_action' => 'open_wechat_pay',
        ];
    }

    /**
     * 处理支付宝支付（用户端）
     */
    private function processAlipayPaymentForUser(PaymentRecord $paymentRecord): array
    {
        // 这里应该调用支付宝API
        // 暂时返回模拟数据
        return [
            'payment_id' => $paymentRecord->id,
            'status' => 'pending_payment',
            'payment_url' => 'alipays://platformapi/startapp?saId=10000007&qrcode=' . urlencode($paymentRecord->payment_no),
            'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            'expire_time' => now()->addMinutes(30),
            'message' => '请在30分钟内完成支付',
            'next_action' => 'open_alipay',
        ];
    }

    /**
     * 🔥 新增：处理支付差异（核心方法）
     */
    private function handlePaymentDifference($correction, Bill $bill, int $operatedBy): void
    {
        $differenceAmount = $correction->difference_amount;
        $paymentMethod = $correction->order->payment_method;

        Log::info('处理支付差异', [
            'difference_amount' => $differenceAmount,
            'payment_method' => $paymentMethod,
            'correction_type' => $correction->correction_type
        ]);

        if (abs($differenceAmount) < 0.01) {
            // 无差异，处理原始支付记录
            $this->handleNoPaymentDifference($correction, $bill, $operatedBy);
        } elseif ($differenceAmount > 0) {
            // 需要补款
            $this->handleSupplementPayment($correction, $bill, $differenceAmount, $operatedBy);
        } else {
            // 需要退款
            $this->handleRefundPayment($correction, $bill, abs($differenceAmount), $operatedBy);
        }
    }

    /**
     * 🔥 新增：处理无支付差异的情况
     */
    private function handleNoPaymentDifference($correction, Bill $bill, int $operatedBy): void
    {
        $order = $correction->order;

        Log::info('无支付差异，处理原始支付记录', [
            'order_payment_method' => $order->payment_method,
            'order_total' => $order->total,
            'corrected_total' => $correction->corrected_total
        ]);

        if ($order->payment_method === 'wechat') {
            // 🔥 修复：微信支付无差异情况需要特殊处理
            $this->processWechatCorrectionNoDifference($correction, $bill, $operatedBy);
        } elseif ($order->payment_method === 'alipay') {
            // 支付宝支付处理
            $this->createInitialPaymentRecord($bill, $order, $operatedBy);
        }
        // 货到付款订单无需特殊处理，账单已创建
    }

    /**
     * 🔥 新增：处理微信支付订单更正无差异情况
     */
    private function processWechatCorrectionNoDifference($correction, Bill $bill, int $operatedBy): void
    {
        $order = $correction->order;
        
        Log::info('🔧 处理微信支付订单更正无差异情况', [
            'bill_id' => $bill->id,
            'order_id' => $order->id,
            'corrected_total' => $correction->corrected_total
        ]);
        
        DB::transaction(function() use ($correction, $bill, $operatedBy, $order) {
            // 1. 获取微信支付记录
            $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
                ->where('trade_state', 'SUCCESS')
                ->first();
                
            if (!$wechatPayment) {
                throw new \Exception('未找到该订单的微信支付记录');
            }
            
            $originalPaidAmount = $wechatPayment->total_fee; // 原始支付金额
            
            // 2. 创建原始支付记录（如果不存在）
            $existingPayment = PaymentRecord::where('bill_id', $bill->id)
                ->where('payment_type', 'payment')
                ->where('payment_method', 'wechat')
                ->first();
                
            if (!$existingPayment) {
                PaymentRecord::create([
                    'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => 'PAY' . date('YmdHis') . mt_rand(1000, 9999),
                    'payment_method' => 'wechat',
                    'payment_amount' => $originalPaidAmount,
                    'payment_type' => PaymentRecord::TYPE_PAYMENT,
                    'status' => PaymentRecord::STATUS_SUCCESS,
                    'payment_time' => $bill->created_at ?? now(),
                    'confirmed_at' => $bill->created_at ?? now(),
                    'notes' => '微信支付原始付款记录',
                    'payment_details' => [
                        'wechat_payment_id' => $wechatPayment->id,
                        'original_wechat_payment' => true
                    ]
                ]);
                
                Log::info('创建微信支付原始付款记录', [
                    'bill_id' => $bill->id,
                    'payment_amount' => $originalPaidAmount
                ]);
            }
            
            // 3. 🔥 关键修复：正确更新账单状态（无差异情况）
            $bill->update([
                'original_amount' => $correction->corrected_total, // 账单金额为更正后金额
                'final_amount' => $correction->corrected_total,
                'paid_amount' => $correction->corrected_total, // 已付金额等于更正后金额
                'pending_amount' => 0, // 待付金额为0
                'status' => Bill::STATUS_PAID,
                'payment_status' => Bill::PAYMENT_STATUS_PAID,
                'paid_at' => now(),
                'notes' => ($bill->notes ?? '') . "\n微信支付订单更正无差异处理完成"
            ]);
            
            Log::info('✅ 微信支付订单更正无差异处理完成', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'final_bill_amount' => $correction->corrected_total,
                'paid_amount' => $correction->corrected_total,
                'pending_amount' => 0,
                'payment_status' => 'paid'
            ]);
        });
    }

    /**
     * 🔥 新增：处理补款
     */
    private function handleSupplementPayment($correction, Bill $bill, float $supplementAmount, int $operatedBy): void
    {
        $order = $correction->order;
        
        Log::info('处理补款', [
            'supplement_amount' => $supplementAmount,
            'payment_method' => $order->payment_method
        ]);

        if ($order->payment_method === 'wechat') {
            // 🔥 修复：微信支付补款需要特殊处理账单状态
            $this->processWechatCorrectionSupplement($correction, $bill, $supplementAmount, $operatedBy);
        } else {
            // 其他支付方式使用原有逻辑
            $this->createSupplementPaymentRecord($bill, $supplementAmount, [
                'operator_id' => $operatedBy,
                'reason' => '订单更正补款',
                'payment_scenario' => \App\Billing\Models\PaymentRecord::SCENARIO_SUPPLEMENT
            ]);
        }
    }

    /**
     * 🔥 新增：处理微信支付订单更正补款
     */
    private function processWechatCorrectionSupplement($correction, Bill $bill, float $supplementAmount, int $operatedBy): void
    {
        $order = $correction->order;
        
        Log::info('🔧 开始处理微信支付订单更正补款', [
            'bill_id' => $bill->id,
            'order_id' => $order->id,
            'supplement_amount' => $supplementAmount,
            'corrected_total' => $correction->corrected_total,
            'original_total' => $order->total
        ]);
        
        DB::transaction(function() use ($correction, $bill, $supplementAmount, $operatedBy, $order) {
            // 1. 获取微信支付记录
            $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
                ->where('trade_state', 'SUCCESS')
                ->first();
                
            if (!$wechatPayment) {
                throw new \Exception('未找到该订单的微信支付记录');
            }
            
            $originalPaidAmount = $wechatPayment->total_fee; // 原始支付金额
            
            // 2. 创建原始支付记录（如果不存在）
            $existingPayment = PaymentRecord::where('bill_id', $bill->id)
                ->where('payment_type', 'payment')
                ->where('payment_method', 'wechat')
                ->first();
                
            if (!$existingPayment) {
                PaymentRecord::create([
                    'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => 'PAY' . date('YmdHis') . mt_rand(1000, 9999),
                    'payment_method' => 'wechat',
                    'payment_amount' => $originalPaidAmount,
                    'payment_type' => PaymentRecord::TYPE_PAYMENT,
                    'status' => PaymentRecord::STATUS_SUCCESS,
                    'payment_time' => $bill->created_at ?? now(),
                    'confirmed_at' => $bill->created_at ?? now(),
                    'notes' => '微信支付原始付款记录',
                    'payment_details' => [
                        'wechat_payment_id' => $wechatPayment->id,
                        'original_wechat_payment' => true
                    ]
                ]);
                
                Log::info('创建微信支付原始付款记录', [
                    'bill_id' => $bill->id,
                    'payment_amount' => $originalPaidAmount
                ]);
            }
            
            // 3. 创建补款记录
            PaymentRecord::create([
                'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => 'SUP' . date('YmdHis') . mt_rand(1000, 9999),
                'payment_method' => 'pending', // 等待用户选择支付方式
                'payment_amount' => $supplementAmount,
                'payment_type' => PaymentRecord::TYPE_SUPPLEMENT,
                'status' => PaymentRecord::STATUS_PENDING,
                'payment_time' => now(),
                'notes' => '订单更正补款',
                'payment_details' => [
                    'operator_id' => $operatedBy,
                    'supplement_reason' => '订单更正金额增加',
                    'original_amount' => $order->total,
                    'corrected_amount' => $correction->corrected_total
                ]
            ]);
            
            // 4. 🔥 修复：正确更新账单状态 - 保持原始金额，只增加补款部分
            $bill->update([
                'original_amount' => $order->total, // ✅ 保持原始订单金额
                'final_amount' => $correction->corrected_total, // 更正后的总金额
                'paid_amount' => $originalPaidAmount, // 已付金额为原始支付金额
                'pending_amount' => $supplementAmount, // 待付金额仅为补款金额
                'status' => Bill::STATUS_PARTIAL_PAID,
                'payment_status' => Bill::PAYMENT_STATUS_PARTIAL,
                'notes' => ($bill->notes ?? '') . "\n微信支付订单更正补款处理完成"
            ]);
            
            Log::info('✅ 微信支付订单更正补款处理完成', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'original_bill_amount' => $order->total, // 原始账单金额
                'final_bill_amount' => $correction->corrected_total, // 更正后账单金额
                'paid_amount' => $originalPaidAmount, // 已支付金额
                'supplement_amount' => $supplementAmount, // 补款金额
                'pending_amount' => $supplementAmount, // 待付金额（仅补款部分）
                'payment_status' => 'partial',
                'logic_fix' => '修复：保持原始账单金额，待付金额仅为补款部分'
            ]);
        });
    }

    /**
     * 🔥 新增：处理退款
     */
    private function handleRefundPayment($correction, Bill $bill, float $refundAmount, int $operatedBy): void
    {
        $order = $correction->order;

        Log::info('处理退款', [
            'refund_amount' => $refundAmount,
            'payment_method' => $order->payment_method
        ]);

        if ($order->payment_method === 'wechat') {
            // 🔥 修复：微信支付退款需要特殊处理账单状态
            $this->processWechatCorrectionRefund($correction, $bill, $refundAmount, $operatedBy);
        } elseif ($order->payment_method === 'cod') {
            // 货到付款退款（记录负数账单调整）
            $this->recordCodRefund($bill, $refundAmount, $operatedBy);
        }
    }

    /**
     * 🔥 新增：创建补款支付记录
     */
    private function createSupplementPaymentRecord(Bill $bill, float $amount, array $options = []): void
    {
        $paymentRecord = \App\Billing\Models\PaymentRecord::create([
            'payment_no' => 'SUP' . date('YmdHis') . mt_rand(1000, 9999),
            'bill_id' => $bill->id,
            'user_id' => $bill->user_id,
            'payment_amount' => $amount, // 🔥 修复：使用正确的字段名
            'payment_method' => 'pending', // 等待用户选择支付方式
            'payment_type' => PaymentRecord::TYPE_SUPPLEMENT,
            'payment_scenario' => $options['payment_scenario'] ?? \App\Billing\Models\PaymentRecord::SCENARIO_SUPPLEMENT,
            'status' => \App\Billing\Models\PaymentRecord::STATUS_PENDING,
            'business_type' => 'correction_supplement',
            'payment_time' => now(), // 🔥 修复：添加支付时间字段
            'notes' => $options['reason'] ?? '订单更正补款',
            'payment_details' => [
                'correction_id' => $options['correction_id'] ?? null,
                'operator_id' => $options['operator_id'] ?? null,
                'created_at' => now()->toISOString()
            ]
        ]);

        Log::info('补款支付记录创建成功', [
            'payment_record_id' => $paymentRecord->id,
            'payment_no' => $paymentRecord->payment_no,
            'amount' => $amount
        ]);
    }

    /**
     * 🔥 新增：记录货到付款退款
     */
    private function recordCodRefund(Bill $bill, float $refundAmount, int $operatedBy): void
    {
        // 创建负数调整记录
        \App\Billing\Models\BillAdjustment::create([
            'bill_id' => $bill->id,
            'adjustment_type' => 'refund',
            'amount' => -$refundAmount,
            'reason' => '订单更正退款',
            'created_by' => $operatedBy,
            'metadata' => [
                'refund_type' => 'cod_correction',
                'created_at' => now()->toISOString()
            ]
        ]);

        // 更新账单金额
        $bill->final_amount -= $refundAmount;
        $bill->pending_amount = max(0, $bill->final_amount - $bill->paid_amount);
        $bill->save();

        Log::info('货到付款退款记录创建成功', [
            'bill_id' => $bill->id,
            'refund_amount' => $refundAmount,
            'new_final_amount' => $bill->final_amount
        ]);
    }

    /**
     * 🔥 修复：处理微信支付订单更正退款（正确的账单逻辑）
     */
    private function processWechatCorrectionRefund($correction, Bill $bill, float $refundAmount, int $operatedBy): void
    {
        $order = $correction->order;
        
        Log::info('🔧 开始处理微信支付订单更正退款', [
            'bill_id' => $bill->id,
            'order_id' => $order->id,
            'refund_amount' => $refundAmount,
            'corrected_total' => $correction->corrected_total,
            'original_total' => $order->total
        ]);
        
        DB::transaction(function() use ($correction, $bill, $refundAmount, $operatedBy, $order) {
            // 1. 获取微信支付记录
            $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
                ->where('trade_state', 'SUCCESS')
                ->first();
                
            if (!$wechatPayment) {
                throw new \Exception('未找到该订单的微信支付记录');
            }
            
            $originalPaidAmount = $wechatPayment->total_fee; // 原始支付金额
            
            // 2. 创建原始支付记录（如果不存在）
            $existingPayment = PaymentRecord::where('bill_id', $bill->id)
                ->where('payment_type', 'payment')
                ->where('payment_method', 'wechat')
                ->first();
                
            if (!$existingPayment) {
                PaymentRecord::create([
                    'bill_id' => $bill->id,
                    // 🔥 修复：直接设置correction_id字段
                    'payment_no' => 'PAY' . date('YmdHis') . mt_rand(1000, 9999),
                    'payment_method' => 'wechat',
                    'payment_amount' => $originalPaidAmount,
                    'payment_type' => PaymentRecord::TYPE_PAYMENT,
                    'status' => PaymentRecord::STATUS_SUCCESS,
                    'payment_time' => $bill->created_at ?? now(),
                    'confirmed_at' => $bill->created_at ?? now(),
                    'notes' => '微信支付原始付款记录',
                    'payment_details' => [
                        'wechat_payment_id' => $wechatPayment->id,
                        'original_wechat_payment' => true
                    ]
                ]);
                
                Log::info('创建微信支付原始付款记录', [
                    'bill_id' => $bill->id,
                    'payment_amount' => $originalPaidAmount
                ]);
            }
            
            // 3. 执行微信退款API调用
            $this->executeWechatRefundForCorrection($order, $refundAmount, [
                'bill_id' => $bill->id,
                'reason' => '订单更正退款'
            ]);
            
            // 4. 创建退款记录
            PaymentRecord::create([
                'bill_id' => $bill->id,
                // 🔥 修复：直接设置correction_id字段
                'payment_no' => 'REF' . date('YmdHis') . mt_rand(1000, 9999),
                'payment_method' => 'wechat',
                'payment_amount' => $refundAmount,
                'payment_type' => PaymentRecord::TYPE_REFUND, // 🔥 修复：使用常量
                'status' => PaymentRecord::STATUS_SUCCESS, // 🔥 修复：使用常量
                'payment_time' => now(),
                'confirmed_at' => now(),
                'notes' => '订单更正退款',
                'metadata' => [
                    'refund_reason' => '订单更正金额减少',
                    'original_amount' => $order->total,
                    'corrected_amount' => $correction->corrected_total
                ]
            ]);
            
            // 5. 🔥 修复：正确更新账单状态 - 退款情况下保持原始金额
            $bill->update([
                'original_amount' => $order->total, // ✅ 保持原始订单金额
                'final_amount' => $correction->corrected_total, // 更正后的金额
                'paid_amount' => $correction->corrected_total, // 实际收到的金额（原始支付 - 退款）
                'pending_amount' => 0, // 待付金额为0（已完成退款）
                'status' => Bill::STATUS_PAID,
                'payment_status' => Bill::PAYMENT_STATUS_PAID,
                'paid_at' => now(),
                'notes' => ($bill->notes ?? '') . "\n微信支付订单更正退款处理完成"
            ]);
            
            Log::info('✅ 微信支付订单更正退款处理完成', [
                'bill_id' => $bill->id,
                'bill_no' => $bill->bill_no,
                'original_bill_amount' => $order->total, // 原始账单金额
                'final_bill_amount' => $correction->corrected_total, // 更正后账单金额
                'paid_amount' => $correction->corrected_total, // 实际收到金额
                'refund_amount' => $refundAmount, // 退款金额
                'pending_amount' => 0, // 待付金额
                'payment_status' => 'paid',
                'logic_fix' => '修复：保持原始账单金额，实际收到金额为更正后金额'
            ]);
        });
    }

    /**
     * 🔥 新增：执行微信退款API调用（订单更正专用）
     */
    private function executeWechatRefundForCorrection(Order $order, float $refundAmount, array $options = []): void
    {
        try {
            // 获取微信支付配置
            $provider = \App\WechatPayment\Models\WechatServiceProvider::first();
            if (!$provider) {
                throw new \Exception('未找到微信支付服务商配置');
            }
            
            // 获取子商户配置（如果存在）
            $subMerchant = \App\WechatPayment\Models\WechatSubMerchant::first();
            
            // 获取微信支付记录
            $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
                ->where('trade_state', 'SUCCESS')
                ->first();
            
            if (!$wechatPayment) {
                throw new \Exception('未找到该订单的微信支付记录');
            }
            
            $actualTotalFee = intval($wechatPayment->total_fee * 100); // 转换为分
            $refundFee = intval($refundAmount * 100); // 转换为分
            
            // 验证退款金额
            if ($refundFee > $actualTotalFee) {
                throw new \Exception("退款金额({$refundFee}分)不能超过实际支付金额({$actualTotalFee}分)");
            }
            
            // 调用微信退款API
            $wechatService = new \App\WechatPayment\Services\WechatServiceProviderPayment($provider, $subMerchant);
            $result = $wechatService->refund([
                'out_trade_no' => $order->payment_no,
                'out_refund_no' => 'REF' . date('YmdHis') . mt_rand(1000, 9999),
                'total_fee' => $actualTotalFee,
                'refund_fee' => $refundFee,
                'refund_desc' => $options['reason'] ?? '订单更正退款'
            ]);

            Log::info('微信退款API调用成功', [
                'order_id' => $order->id,
                'refund_amount' => $refundAmount,
                'wechat_refund_id' => $result['refund_id'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('微信退款API调用失败', [
                'order_id' => $order->id,
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 🔥 新增：处理订单更正的微信退款（旧方法，保持兼容性）
     */
    private function processWechatRefundForCorrection(Order $order, float $refundAmount, int $operatedBy, array $options = []): void
    {
        try {
            // 创建退款记录
            $refundRecord = \App\Billing\Models\PaymentRecord::create([
                'payment_no' => 'REF' . date('YmdHis') . mt_rand(1000, 9999),
                'bill_id' => $options['bill_id'] ?? null,
                'user_id' => $order->user_id,
                'payment_amount' => $refundAmount, // 🔥 修复：使用正确的字段名
                'payment_method' => 'wechat',
                'payment_type' => PaymentRecord::TYPE_REFUND,
                'payment_scenario' => \App\Billing\Models\PaymentRecord::SCENARIO_SUPPLEMENT, // 🔥 修复：使用现有的枚举值
                'status' => \App\Billing\Models\PaymentRecord::STATUS_PROCESSING,
                'business_type' => 'correction_refund',
                'payment_time' => now(), // 🔥 修复：添加支付时间字段
                'notes' => $options['reason'] ?? '订单更正退款',
                'metadata' => [
                    'correction_id' => $options['correction_id'] ?? null,
                    'operator_id' => $operatedBy,
                    'original_order_id' => $order->id,
                    'refund_reason' => $options['reason'] ?? '订单更正退款',
                    'refund_type' => 'correction_reversal' // 🔥 在metadata中记录具体类型
                ]
            ]);

            // 调用微信退款API
            // 获取微信支付配置
            $provider = \App\WechatPayment\Models\WechatServiceProvider::first();
            if (!$provider) {
                throw new \Exception('未找到微信支付服务商配置');
            }
            
            // 获取子商户配置（如果存在）
            $subMerchant = \App\WechatPayment\Models\WechatSubMerchant::first();
            
            // 获取微信支付记录中的实际支付金额
            $wechatPayment = \App\WechatPayment\Models\WechatServicePayment::where('order_id', $order->id)
                ->where('trade_state', 'SUCCESS')
                ->first();
            
            if (!$wechatPayment) {
                throw new \Exception('未找到该订单的微信支付记录');
            }
            
            // 确定实际的支付金额（分为单位）
            // 微信支付记录中的 total_fee 是以元为单位存储的，需要转换为分
            $actualTotalFee = intval($wechatPayment->total_fee * 100);
            
            // 检查是否已有退款记录，避免重复退款
            $existingRefunds = \App\Billing\Models\PaymentRecord::where('payment_type', 'refund')
                ->where('payment_method', 'wechat')
                ->where('notes', 'like', '%订单更正退款%')
                ->whereIn('status', ['processing', 'success', 'transferred'])
                ->get();
            
            $totalRefunded = $existingRefunds->sum('payment_amount');
            $newRefundAmount = $refundAmount;
            
            if ($totalRefunded > 0) {
                \Log::warning('检测到已有退款记录', [
                    'order_id' => $order->id,
                    'existing_refunds_count' => $existingRefunds->count(),
                    'total_refunded' => $totalRefunded,
                    'new_refund_amount' => $newRefundAmount
                ]);
                
                // 如果已退款金额加上新退款金额超过实际支付金额，则拒绝退款
                if (($totalRefunded + $newRefundAmount) > ($actualTotalFee / 100)) {
                    throw new \Exception("累计退款金额(" . ($totalRefunded + $newRefundAmount) . "元)不能超过实际支付金额(" . ($actualTotalFee / 100) . "元)");
                }
            }
            
            \Log::info('微信退款金额计算', [
                'order_id' => $order->id,
                'wechat_total_fee_yuan' => $wechatPayment->total_fee,
                'wechat_total_fee_fen' => $actualTotalFee,
                'order_total' => $order->total,
                'refund_amount' => $refundAmount,
                'existing_refunds' => $totalRefunded
            ]);
            
            $refundFee = intval($refundAmount * 100);
            
            // 验证退款金额不能超过实际支付金额
            if ($refundFee > $actualTotalFee) {
                throw new \Exception("退款金额({$refundFee}分)不能超过实际支付金额({$actualTotalFee}分)");
            }
            
            $wechatService = new \App\WechatPayment\Services\WechatServiceProviderPayment($provider, $subMerchant);
            $result = $wechatService->refund([
                'out_trade_no' => $order->payment_no,
                'out_refund_no' => $refundRecord->payment_no,
                'total_fee' => $actualTotalFee, // 使用微信支付记录中的实际金额
                'refund_fee' => $refundFee, // 转换为分
                'refund_desc' => $options['reason'] ?? '订单更正退款'
            ]);

            // 更新退款状态
            $refundRecord->update([
                'status' => \App\Billing\Models\PaymentRecord::STATUS_SUCCESS,
                'external_payment_no' => $result['refund_id'] ?? null,
                'completed_at' => now()
            ]);

            // 🔥 新增：更新账单支付状态为多付/待退款状态
            if (isset($options['bill_id'])) {
                $bill = Bill::find($options['bill_id']);
                if ($bill) {
                    $bill->update([
                        'payment_status' => 'overpaid',
                        'pending_refund_amount' => $refundAmount,
                        'refund_status' => 'processing'
                    ]);
                    
                    Log::info('账单支付状态已更新为多付状态', [
                        'bill_id' => $bill->id,
                        'bill_no' => $bill->bill_no,
                        'pending_refund_amount' => $refundAmount,
                        'payment_status' => 'overpaid'
                    ]);
                }
            }

            Log::info('微信退款处理成功', [
                'refund_record_id' => $refundRecord->id,
                'refund_amount' => $refundAmount,
                'wechat_refund_id' => $result['refund_id'] ?? null,
                'bill_status_updated' => isset($options['bill_id'])
            ]);

        } catch (\Exception $e) {
            Log::error('微信退款处理失败', [
                'order_id' => $order->id,
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}