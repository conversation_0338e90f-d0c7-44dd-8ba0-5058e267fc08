<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Printing\Http\Controllers\PrintController;
use App\Printing\Models\PrintRecord;
use App\Order\Models\Order;
use Illuminate\Http\Request;

class TestPrintCallback extends Command
{
    protected $signature = 'test:print-callback';
    protected $description = '测试前端打印回调问题';

    public function handle()
    {
        $this->info('=== 测试前端打印回调问题 ===');
        $this->newLine();

        // 1. 获取一个测试订单
        $order = Order::first();
        if (!$order) {
            $this->error('❌ 没有找到测试订单');
            return 1;
        }

        $this->info("📋 使用测试订单: ID {$order->id}, 订单号: {$order->order_no}");
        $this->newLine();

        // 2. 模拟前端打印配送单API调用
        $this->info('🖨️ 步骤1: 模拟前端打印配送单API调用');
        try {
            $printController = app(PrintController::class);
            $request = new Request([
                'driver' => 'clodop',
                'copies' => 1,
                'paper_size' => 'A4'
            ]);
            
            $response = $printController->printDelivery($request, $order->id);
            $responseData = json_decode($response->getContent(), true);
            
            if ($responseData['success']) {
                $printRecordId = $responseData['print_record_id'];
                $this->info("✅ 打印记录创建成功: ID {$printRecordId}");
                $this->info("📄 打印脚本长度: " . strlen($responseData['script']) . " 字符");
            } else {
                $this->error("❌ 打印记录创建失败: " . $responseData['error']);
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ 打印API调用异常: " . $e->getMessage());
            return 1;
        }

        $this->newLine();

        // 3. 检查打印记录状态
        $this->info('🔍 步骤2: 检查打印记录状态');
        $printRecord = PrintRecord::find($printRecordId);
        if ($printRecord) {
            $this->info('📋 打印记录详情:');
            $this->line("  - ID: {$printRecord->id}");
            $this->line("  - 类型: {$printRecord->print_type}");
            $this->line("  - 状态: {$printRecord->status}");
            $this->line("  - 驱动: {$printRecord->driver}");
            $this->line("  - 创建时间: {$printRecord->created_at}");
            $this->line("  - 打印时间: " . ($printRecord->printed_at ?? '未设置'));
        } else {
            $this->error('❌ 找不到打印记录');
            return 1;
        }

        $this->newLine();

        // 4. 模拟前端打印完成回调
        $this->info('📞 步骤3: 模拟前端打印完成回调');
        try {
            $callbackRequest = new Request([
                'print_record_id' => $printRecordId,
                'status' => 'completed'
            ]);
            
            $callbackResponse = $printController->printCompleted($callbackRequest);
            $callbackData = json_decode($callbackResponse->getContent(), true);
            
            if ($callbackData['success']) {
                $this->info('✅ 打印完成回调成功');
            } else {
                $this->error('❌ 打印完成回调失败: ' . ($callbackData['message'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            $this->error('❌ 打印回调异常: ' . $e->getMessage());
        }

        $this->newLine();

        // 5. 再次检查打印记录状态
        $this->info('🔍 步骤4: 检查回调后的打印记录状态');
        $printRecord->refresh();
        $this->info('📋 更新后的打印记录详情:');
        $this->line("  - ID: {$printRecord->id}");
        $this->line("  - 类型: {$printRecord->print_type}");
        $this->line("  - 状态: {$printRecord->status}");
        $this->line("  - 创建时间: {$printRecord->created_at}");
        $this->line("  - 打印时间: " . ($printRecord->printed_at ?? '未设置'));
        $this->line("  - 打印人ID: " . ($printRecord->printed_by ?? '未设置'));

        $this->newLine();

        // 6. 测试批量获取打印状态API
        $this->info('📊 步骤5: 测试批量获取打印状态API');
        try {
            $statusRequest = new Request([
                'order_ids' => [$order->id]
            ]);
            
            $statusResponse = $printController->batchGetOrderPrintStatus($statusRequest);
            $statusData = json_decode($statusResponse->getContent(), true);
            
            if ($statusData['success']) {
                $this->info('✅ 批量获取打印状态成功');
                $this->info('📋 打印状态数据:');
                
                $orderStatus = $statusData['data'][$order->id] ?? null;
                if ($orderStatus && $orderStatus['success']) {
                    $printStats = $orderStatus['data']['print_stats'];
                    
                    $this->line('  配送单:');
                    $this->line('    - 是否已打印: ' . ($printStats['delivery']['is_printed'] ? '是' : '否'));
                    $this->line('    - 总打印次数: ' . ($printStats['delivery']['total_prints'] ?? 0));
                    $this->line('    - 成功打印次数: ' . ($printStats['delivery']['completed_prints'] ?? 0));
                    $this->line('    - 失败打印次数: ' . ($printStats['delivery']['failed_prints'] ?? 0));
                    
                    $this->line('  小票:');
                    $this->line('    - 是否已打印: ' . ($printStats['receipt']['is_printed'] ? '是' : '否'));
                    $this->line('    - 总打印次数: ' . ($printStats['receipt']['total_prints'] ?? 0));
                    $this->line('    - 成功打印次数: ' . ($printStats['receipt']['completed_prints'] ?? 0));
                    $this->line('    - 失败打印次数: ' . ($printStats['receipt']['failed_prints'] ?? 0));
                } else {
                    $this->error('❌ 订单打印状态获取失败');
                    if ($orderStatus && !$orderStatus['success']) {
                        $this->error('错误信息: ' . ($orderStatus['error'] ?? '未知错误'));
                    }
                }
            } else {
                $this->error('❌ 批量获取打印状态失败: ' . ($statusData['message'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            $this->error('❌ 获取打印状态异常: ' . $e->getMessage());
        }

        $this->newLine();

        // 7. 检查前端可能遇到的问题
        $this->info('🔧 步骤6: 诊断前端可能遇到的问题');

        // 检查CORS设置
        $this->info('🌐 检查CORS设置:');
        $corsConfig = config('cors');
        if ($corsConfig) {
            $this->line('  - CORS已配置');
            $this->line('  - 允许的源: ' . implode(', ', $corsConfig['allowed_origins'] ?? ['*']));
            $this->line('  - 允许的方法: ' . implode(', ', $corsConfig['allowed_methods'] ?? ['*']));
        } else {
            $this->warn('  - ⚠️ CORS配置可能有问题');
        }

        // 检查API路由
        $this->info('🛣️ 检查API路由:');
        $routes = app('router')->getRoutes();
        $printRoutes = [];
        foreach ($routes as $route) {
            $uri = $route->uri();
            if (strpos($uri, 'api/print') !== false) {
                $printRoutes[] = $route->methods()[0] . ' ' . $uri;
            }
        }

        if (!empty($printRoutes)) {
            $this->line('  - 找到打印相关路由:');
            foreach ($printRoutes as $route) {
                $this->line("    * {$route}");
            }
        } else {
            $this->warn('  - ⚠️ 没有找到打印相关路由');
        }

        $this->newLine();
        $this->info('✅ 测试完成');
        
        return 0;
    }
} 